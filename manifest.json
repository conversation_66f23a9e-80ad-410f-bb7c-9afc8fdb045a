{
    "name" : "着调儿",
    "appid" : "__UNI__92D9658",
    "description" : "",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    "app-plus" : {
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "compilerVersion" : 3,
        "modules" : {},
        "distribute" : {
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            "ios" : {
                "dSYMs" : false
            },
            "sdkConfigs" : {
                "ad" : {}
            }
        },
        "uniStatistics" : {
            "enable" : true
        }
    },
    "quickapp" : {},
    "uniStatistics" : {
        "enable" : true, //全局关闭统计分析  
        "version" : "2"
    },
    "mp-weixin" : {
        "__usePrivacyCheck__" : false, //关闭用户隐私保护指引配置， 9.15后基础库会自动适配开启且无法关闭
        "usingComponents" : true,
        "appid" : "wxcd079aad6de5cc78",
        "navigateToMiniProgramAppIdList" : [ "wxe95c67489c17edcf" ], //要跳转小程序白名单
        "setting" : {
            "urlCheck" : true,
            "es6" : true,
            "minified" : true
        },
        "plugins" : {
            "live-player-plugin" : {
                "version" : "1.3.5", // 注意填写该直播组件最新版本号，微信开发者工具调试时可获取最新版本号（复制时请去掉注释）
                "provider" : "wx2b03c6e691cd7370" // 必须填该直播组件appid，该示例值即为直播组件appid（复制时请去掉注释）
            }
        },
        // "live-player-plugin" : {
        //     "version" : "1.1.5", // 注意填写该直播组件最新版本号，微信开发者工具调试时可获取最新版本号（复制时请去掉注释）
        //     "provider" : "wx2b03c6e691cd7370" // 必须填该直播组件appid，该示例值即为直播组件appid（复制时请去掉注释）
        // },
        // "myPlugin" : {
        //     "version" : "1.0.7",
        //     "provider" : "wx738958e0f4c894f9"
        // }
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于获取周边门店信息" // 周边门店信息
            }
        },
        "requiredPrivateInfos" : [ "getLocation", "chooseAddress", "chooseLocation" ],
        "sitemapLocation" : "sitemap.json",
        "uniStatistics" : {
            "enable" : true //微信平台关闭统计 
        },
        "usingShopPlugin" : true,
        "requiredBackgroundModes" : [ "audio" ]
    },
    "h5" : {
        "usingComponents" : true,
        "router" : {
            "mode" : "history",
            "base" : "/h5/"
        },
        "optimization" : {
            "treeShaking" : {
                "enable" : true
            }
        },
        "template" : "template.h5.html",
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "MK4BZ-3XFLG-LPTQF-IKYWM-XVJYO-JDF5P"
                }
            }
        },
        "domain" : "https://shop.arthorize.com",
        "title" : "着调儿",
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-toutiao" : {
        "esModuleInterop" : true,
        "allowSyntheticDefaultImports" : true,
        "usingComponents" : true,
        "appid" : "ttfe348889611e7951",
        "setting" : {
            "es6" : true,
            "urlCheck" : false,
            "minified" : true,
            "postcss" : true
        },
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-alipay" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-baidu" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-qq" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : true
        }
    },
    "networkTimeout" : {
        // "uploadFile" : 300000 ,// 5分钟
        "uploadFile" : 1560000 // 26分钟
    },
    "quickapp-webview-union" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-jd" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-kuaishou" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-lark" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "quickapp-webview-huawei" : {
        "uniStatistics" : {
            "enable" : true
        }
    }
}
