{"bsonType": "object", "required": [], "permission": {"read": "'READ_UNI_STAT_PAY' in auth.permission", "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}, "provider": {"title": "支付供应商", "bsonType": "string", "enum": [{"text": "微信支付", "value": "wxpay"}, {"text": "支付宝", "value": "alipay"}, {"text": "苹果应用内支付", "value": "appleiap"}], "description": "支付供应商 如 wxpay alipay 参考 https://uniapp.dcloud.net.cn/api/plugins/provider.html#"}, "provider_pay_type": {"title": "支付方式", "bsonType": "string", "description": "支付供应商的支付类型（插件内部标记支付类型的标识，不需要用户传）", "trim": "both"}, "uni_platform": {"title": "应用平台", "bsonType": "string", "description": "uni客户端平台,如：web、mp-weixin、mp-alipay、app等", "trim": "both"}, "status": {"title": "订单状态", "bsonType": "int", "enum": [{"text": "已关闭", "value": -1}, {"text": "未支付", "value": 0}, {"text": "已支付", "value": 1}, {"text": "已部分退款", "value": 2}, {"text": "已全额退款", "value": 3}], "description": "订单状态 -1 已关闭 0：未支付 1：已支付 2：已部分退款 3：已全额退款", "defaultValue": 0}, "type": {"title": "订单类型", "bsonType": "string", "description": "订单类型 goods：订单付款 recharge：余额充值付款 vip：vip充值付款 等等，可自定义", "trim": "both"}, "order_no": {"title": "业务系统订单号", "bsonType": "string", "minLength": 20, "maxLength": 28, "description": "业务系统订单号，控制在20-28位（不可以是24位,24位在阿里云空间可能会有问题，可重复，代表1个业务订单会有多次付款的情况）", "trim": "both"}, "out_trade_no": {"title": "支付插件订单号", "bsonType": "string", "description": "支付插件订单号（需控制唯一，不传则由插件自动生成）", "trim": "both"}, "transaction_id": {"title": "交易单号", "bsonType": "string", "description": "交易单号（支付平台订单号，由支付平台控制唯一）", "trim": "both"}, "user_id": {"title": "用户ID", "bsonType": "string", "description": "用户id，参考uni-id-users表", "foreignKey": "uni-id-users._id"}, "nickname": {"title": "用户昵称", "bsonType": "string", "description": "用户昵称冗余", "trim": "both"}, "device_id": {"bsonType": "string", "description": "客户端设备ID"}, "client_ip": {"title": "客户端IP", "bsonType": "string", "description": "创建支付的客户端ip", "trim": "both"}, "openid": {"title": "openid", "bsonType": "string", "description": "发起支付的用户openid", "trim": "both"}, "description": {"title": "支付描述", "bsonType": "string", "description": "支付描述，如：uniCloud个人版包月套餐", "trim": "both"}, "err_msg": {"title": "支付失败原因", "bsonType": "string", "description": "支付失败原因", "trim": "both"}, "total_fee": {"title": "订单总金额", "bsonType": "int", "description": "订单总金额，单位为分，100等于1元"}, "refund_fee": {"title": "订单总退款金额", "bsonType": "int", "description": "订单总退款金额，单位为分，100等于1元"}, "refund_count": {"title": "当前退款笔数", "bsonType": "int", "description": "当前退款笔数 (退款单号为 out_trade_no-refund_count)"}, "refund_list": {"title": "退款详情", "bsonType": "array", "description": "退款详情"}, "provider_appid": {"title": "开放平台appid", "bsonType": "string", "description": "公众号appid，小程序appid，app开放平台appid 等", "trim": "both"}, "appid": {"title": "DCloud AppId", "bsonType": "string", "description": "dcloud_appid", "trim": "both"}, "user_order_success": {"title": "回调状态", "bsonType": "bool", "description": "用户异步通知逻辑是否全部执行完成，且无异常（建议前端通过此参数是否为true来判断是否支付成功）"}, "custom": {"title": "自定义数据", "bsonType": "object", "description": "自定义数据（用户自定义数据）"}, "original_data": {"title": "异步通知原始数据", "bsonType": "object", "description": "异步回调通知返回的原始数据，微信v2是xml转json后的数据，微信v3和支付宝是原始json"}, "create_date": {"title": "创建时间", "bsonType": "timestamp", "description": "创建时间", "forceDefaultValue": {"$env": "now"}}, "pay_date": {"title": "支付时间", "bsonType": "timestamp", "description": "支付时间"}, "notify_date": {"title": "异步通知时间", "bsonType": "timestamp", "description": "订单通知支付成功时间"}, "cancel_date": {"title": "取消时间", "bsonType": "timestamp", "description": "订单取消时间"}, "refund_date": {"title": "最近退款时间", "bsonType": "timestamp", "description": "最近退款时间"}, "stat_data": {"title": "uni统计相关数据", "bsonType": "object", "description": "uni统计相关数据", "properties": {"platform": {"bsonType": "string", "description": "与uni_platform唯一区别是APP区分 android 和 ios"}, "app_version": {"bsonType": "string", "description": "客户端版本号 （字符串形式）如1.0.0"}, "app_version_code": {"bsonType": "string", "description": "客户端版本号（数字形式） 如100"}, "app_wgt_version": {"bsonType": "string", "description": "客户端热更新版本号"}, "os": {"bsonType": "string", "description": "设备的操作系统 如 android ios"}, "ua": {"bsonType": "string", "description": "客户端userAgent"}, "channel": {"bsonType": "string", "description": "客户端渠道"}, "scene": {"bsonType": "string", "description": "小程序场景值"}}}}}