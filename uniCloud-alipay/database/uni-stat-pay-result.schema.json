{"bsonType": "object", "description": "存储统计汇总的支付数据", "required": [], "permission": {"read": "'READ_UNI_STAT_PAY' in auth.permission", "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}, "appid": {"bsonType": "string", "description": "应用ID，对应opendb-app-list.appid", "foreignKey": "opendb-app-list.appid"}, "platform_id": {"bsonType": "string", "description": "应用平台ID，对应uni-stat-app-platforms._id", "foreignKey": "uni-stat-app-platforms._id"}, "channel_id": {"bsonType": "string", "description": "渠道/场景值ID，对应uni-stat-app-channels._id", "foreignKey": "uni-stat-app-channels._id"}, "version_id": {"bsonType": "string", "description": "应用版本ID，对应opendb-app-versions._id", "foreignKey": "opendb-app-versions._id"}, "pay_total_amount": {"bsonType": "int", "description": "支付金额：统计时间内，成功支付的订单金额之和（不剔除退款订单）。单位分。"}, "pay_order_count": {"bsonType": "int", "description": "支付笔数：统计时间内，成功支付的订单数，一个订单对应唯一一个订单号。（不剔除退款订单。）"}, "pay_user_count": {"bsonType": "int", "description": "支付人数：统计时间内，成功支付的人数（不剔除退款订单）。"}, "pay_device_count": {"bsonType": "int", "description": "支付设备数：统计时间内，成功支付的设备数（不剔除退款订单）。"}, "create_total_amount": {"bsonType": "int", "description": "下单金额：统计时间内，成功下单的订单金额（不剔除退款订单）。单位分。"}, "create_order_count": {"bsonType": "int", "description": "下单笔数：统计时间内，成功下单的订单笔数（不剔除退款订单）。"}, "create_user_count": {"bsonType": "int", "description": "下单人数：统计时间内，成功下单的客户数，一人多次下单记为一人（不剔除退款订单）。"}, "create_device_count": {"bsonType": "int", "description": "下单设备数：统计时间内，成功下单的设备数，一台设备多次访问被计为一台（不剔除退款订单）。"}, "refund_total_amount": {"bsonType": "int", "description": "成功退款金额：统计时间内，成功退款的金额。以成功退款时间点为准。单位分。"}, "refund_order_count": {"bsonType": "int", "description": "成功退款订单数：统计时间内，成功退款的订单数。以成功退款时间点为准。"}, "refund_user_count": {"bsonType": "int", "description": "成功退款人数：统计时间内，成功退款的人数（不剔除退款订单）。"}, "refund_device_count": {"bsonType": "int", "description": "成功退款设备数：统计时间内，成功退款的设备数（不剔除退款订单）。"}, "activity_user_count": {"bsonType": "int", "description": "访问人数：统计时间内，访问人数，一人多次访问被计为一人（只统计已登录的用户）。"}, "activity_device_count": {"bsonType": "int", "description": "访问设备数：统计时间内，访问设备数，一台设备多次访问被计为一台（包含未登录的用户）。"}, "new_user_count": {"bsonType": "int", "description": "新增注册人数：统计时间内，注册人数。"}, "new_device_count": {"bsonType": "int", "description": "新增新设备数：统计时间内，新设备数。"}, "new_user_create_order_count": {"bsonType": "int", "description": "新用户下单人数：统计时间内，新增注册人数中下单的人数。"}, "new_user_pay_order_count": {"bsonType": "int", "description": "新用户支付人数：统计时间内，新增注册人数中下成功支付的人数。"}, "dimension": {"bsonType": "string", "description": "统计范围 hour：按小时统计，day：按天统计，week：按周统计，month：按月统计 quarter：按季度统计 year：按年统计", "enum": [{"text": "年", "value": "year"}, {"text": "季度", "value": "quarter"}, {"text": "月", "value": "month"}, {"text": "周", "value": "week"}, {"text": "天", "value": "day"}, {"text": "小时", "value": "hour"}]}, "create_date": {"bsonType": "timestamp", "description": "创建时间"}, "start_time": {"bsonType": "timestamp", "description": "统计开始时间"}, "end_time": {"bsonType": "timestamp", "description": "统计结束时间"}, "stat_date": {"bsonType": "object", "description": "统计日期参数", "properties": {"date_str": {"bsonType": "string", "description": "如：2021-07-27"}, "year": {"bsonType": "int", "description": "年"}, "month": {"bsonType": "int", "description": "月"}, "day": {"bsonType": "int", "description": "日"}, "hour": {"bsonType": "int", "description": "时"}}}}}