{"name": "uni-stat-receiver", "dependencies": {"uni-stat": "file:../common/uni-stat", "uni-id-common": "file:../../../uni_modules/uni-id-common/uniCloud/cloudfunctions/common/uni-id-common"}, "cloudfunction-config": {"concurrency": 1, "memorySize": 128, "timeout": 60, "triggers": []}, "extensions": {"uni-cloud-jql": {}}, "plugin-dev-name": "uni-template-admin", "plugin-version": "2.4.17", "origin-plugin-dev-name": "uni-template-admin", "origin-plugin-version": "2.4.17"}