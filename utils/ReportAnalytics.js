// 自定义事件分析
// uni.report 是uni的统计,所有平台，但不能在小程序后台查看，小程序查看需要使用官方
// wx.reportAnalytics 需要在小程序管理后台自定义分析中新建事件，配置好事件名与字段。
// 事件名 小程序不支持驼峰 以小程序为准 
// 小程序中可直接选在配置方式 填写配置，配置页面路径及触发的类名选择器即可触发
// 这里选在api上报方式,微信小程序需要配置字段

import {
	IS_DEV
} from '@/config.js';

export function reportSearch(data) {
	if (IS_DEV) return;
	uni.report('search', data);
	// #ifdef MP-WEIXIN
	wx.reportAnalytics('search', data);
	// #endif
}

// 搜索点击标签
export function reportSearchLabel(data) {
	if (IS_DEV) return;
	uni.report('search_label', data);
	// #ifdef MP-WEIXIN
	wx.reportAnalytics('search_label', data);
	// #endif
}

export function reportSearchResultClick(data) {
	if (IS_DEV) return;
	uni.report('search_result_click', data);
	// #ifdef MP-WEIXIN
	wx.reportAnalytics('search_result_click', data);
	// #endif
}

export function reportShare(data) {
	if (IS_DEV) return;
	uni.report('share', data);
	// #ifdef MP-WEIXIN
	wx.reportAnalytics('share', data);
	// #endif
}
// 获取小程序启动相关参数
export function reportEnterOptions(data) {
	if (IS_DEV) return;
	const {
		path,
		scene,
		referrerInfo = {
			appId: '',
			extraData: {}
		}
	} = data;
	let obj = {
		path,
		scene

	};
	uni.report('enter_options', obj);
	// #ifdef MP-WEIXIN
	console.log(data)
	console.log(referrerInfo)
	obj.referrerinfo_appid = referrerInfo.appId || '';
	obj.referrerinfo_extradata = referrerInfo.extraData ? JSON.parse(referrerInfo.extraData) : '';
	wx.reportAnalytics('enter_options', obj);
	// #endif
}


export function reportEvaluateDetail(data) {
	if (IS_DEV) return;
	uni.report('evaluate_detail', data);
	// #ifdef MP-WEIXIN
	wx.reportAnalytics('evaluate_detail', data);
	// #endif
}

export function reportWishDetail(data) {
	if (IS_DEV) return;
	uni.report('wish_detail', data);
	// #ifdef MP-WEIXIN
	wx.reportAnalytics('wish_detail', data);
	// #endif
}

export function reportWishMake(data) {
	if (IS_DEV) return;
	uni.report('wish_make', data);
	// #ifdef MP-WEIXIN
	wx.reportAnalytics('wish_make', data);
	// #endif
}

export function reportEvaluateAttention(data) {
	if (IS_DEV) return;
	uni.report('evaluate_attention', data);
	// #ifdef MP-WEIXIN
	wx.reportAnalytics('evaluate_attention', data);
	// #endif
}
export function reportEvaluateUnAttention(data) {
	if (IS_DEV) return;
	uni.report('evaluate_un_attention', data);
	// #ifdef MP-WEIXIN
	wx.reportAnalytics('evaluate_un_attention', data);
	// #endif
}


export function reportWishAttention(data) {
	if (IS_DEV) return;
	uni.report('wish_attention', data);
	// #ifdef MP-WEIXIN
	wx.reportAnalytics('wish_attention', data);
	// #endif
}

export function reportWishUnAttention(data) {
	if (IS_DEV) return;
	uni.report('wish_un_attention', data);
	// #ifdef MP-WEIXIN
	wx.reportAnalytics('wish_un_attention', data);
	// #endif
}

export function reportUserFollow(data) {
	if (IS_DEV) return;
	uni.report('user_follow', data);
	// #ifdef MP-WEIXIN
	wx.reportAnalytics('user_follow', data);
	// #endif
}

export function reportUserUnFollow(data) {
	if (IS_DEV) return;
	uni.report('user_un_follow', data);
	// #ifdef MP-WEIXIN
	wx.reportAnalytics('user_un_follow', data);
	// #endif
}

export function reportUserHome(data) {
	if (IS_DEV) return;
	uni.report('visit_user_home', data);
	// #ifdef MP-WEIXIN
	wx.reportAnalytics('visit_user_home', data);
	// #endif
}


export function reportWebView(data) {
	if (IS_DEV) return;
	uni.report('webview', data);
	// #ifdef MP-WEIXIN
	wx.reportAnalytics('webview', data);
	// #endif
}

export function reportActiveDetail(data) {
	if (IS_DEV) return;
	uni.report('active_detail', data);
	// #ifdef MP-WEIXIN
	wx.reportAnalytics('active_detail', data);
	// #endif
}