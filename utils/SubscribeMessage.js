import {
	SUBSCRIBE_MESSAGE
} from '@/config.js';

export function auth() {
	// #ifdef MP-WEIXIN
	let tmplIds = {};
	let messageTmplIds = uni.getStorageSync(SUBSCRIBE_MESSAGE);
	tmplIds = messageTmplIds ? JSON.parse(messageTmplIds) : {};
	
	console.log('tmplIds',tmplIds)
	return tmplIds;
	// #endif
	// #ifdef H5
	return false
	// #endif
}

/**
 * 支付成功后订阅消息id
 * 订阅  确认收货通知 订单支付成功  新订单管理员提醒 
 */
export function openPaySubscribe() {
	let tmplIds = auth();
	if(!tmplIds) return subscribe();
	return subscribe([
		tmplIds.oreder_takever,
		tmplIds.order_pay_success,
		tmplIds.order_new,
	])
}

/**
 * 订单相关订阅消息
 * 送货 发货 取消订单 
 */
export function openOrderSubscribe() {
	let tmplIds = auth();
	if(!tmplIds) return subscribe();
	return subscribe([
		tmplIds.order_deliver_success,
		tmplIds.order_postage_success,
		tmplIds.order_clone
	])
}

/**
 * 提现消息订阅
 * 成功 和 失败 消息
 */
export function openExtrctSubscribe() {
	let tmplIds = auth();
	if(!tmplIds) return subscribe();
	return subscribe([
		tmplIds.user_extract
	]);
}

/**
 * 拼团成功
 */
export function openPinkSubscribe() {
	let tmplIds = auth();
	if(!tmplIds) return subscribe();
	return subscribe([
		tmplIds.pink_true
	]);
}

/**
 * 砍价成功
 */
export function openBargainSubscribe() {
	let tmplIds = auth();
	if(!tmplIds) return subscribe();
	return subscribe([
		tmplIds.bargain_success
	]);
}

/**
 * 订单退款
 */
export function openOrderRefundSubscribe() {
	let tmplIds = auth();
	if(!tmplIds) return subscribe();
	return subscribe([tmplIds.order_refund]);
}

/**
 * 充值成功
 */
export function openRechargeSubscribe() {
	let tmplIds = auth();
	if(!tmplIds) return subscribe();
	return subscribe([tmplIds.recharge_success]);
}

/**
 * 提现
 */
export function openEextractSubscribe() {
	let tmplIds = auth();
	if(!tmplIds) return subscribe();
	return subscribe([tmplIds.user_extract]);
}

// ，活动相关消息通知
export function openCommunityActiveSubscribe() {
	let tmplIds = auth();
	if(!tmplIds) return subscribe();
	return subscribe([tmplIds.activity_canceled,tmplIds.upcoming_activitys]);
}
// 定金，尾款相关消息通知
export function openCommunitySubscribe() {
	let tmplIds = auth();
	if(!tmplIds) return subscribe();
	return subscribe([tmplIds.pending_payent,tmplIds.final_payment,tmplIds.refund]);
}
	// return subscribe([tmplIds.pending_payent,tmplIds.activity_canceled,tmplIds.upcoming_activitys,tmplIds.final_payment,tmplIds.refund]);
/**
 * 调起订阅界面
 * array tmplIds 模板id
 */
export function subscribe(tmplIds) {
	console.log('tmplIds',tmplIds)
	return new Promise((reslove) => {
		// #ifdef MP
		uni.requestSubscribeMessage({
			tmplIds: tmplIds,
			success(res) {
				// console.log('res',res)
				return reslove(res);
			},
			fail(res) {
				// console.log('err',res)
				return reslove(res);
			},
			complete: function(res) {
				return reslove(res);
			},
		})
		// #endif
		// #ifdef H5
		return reslove(true);
		// #endif
		// #ifdef MP-TOUTIAO
		return reslove(true);
		// #endif
	});
}


