import store from "../store";
import {
	uniNavigator,
	uniGetImageInfo
} from './uni_api'
import {
	login,
	getUserInfo
} from '@/api/user.js';
import {
	wechatAuth
} from '@/api/public.js';
import storage from '@/utils/storage.js'
// #ifdef H5
import {
	isWeixin
} from "./validate.js";
let _isWeixin = isWeixin();
import {
	toAuth
} from './wechat/auth.js';
import {         
	REDIRECT_URI,
	WECHAT_LOGIN,
	WECHAT_AUTH_BACK_URL
} from '@/config.js'
// #endif
// 自动去执行授权登录
export function autoAuth() {
	store.commit("LOGOUT");
	// #ifdef H5
	if (_isWeixin && WECHAT_LOGIN) {
		// 微信公众号授权
        // storage.set(WECHAT_AUTH_BACK_URL, location.href)
		if (checkLogin()) {
			console.log('微信公众号已授权')
			return false;
		}
		// 公众号授权测试
		// let expires_time = 1598223013000,
		// token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzaG9wLmFydGhvcml6ZS5jb20iLCJhdWQiOiJzaG9wLmFydGhvcml6ZS5jb20iLCJpYXQiOjE1OTgyMzk2MjksIm5iZiI6MTU5ODIzOTYyOSwiZXhwIjoxNTk4MjUwNDI5LCJqdGkiOnsiaWQiOjIsInR5cGUiOiJ3ZWNoYXQifX0.lPJ0Ocn6SO3RplMkpk1Z3QQ4z_Z6oWMEwl1ACCg1exQ';	
		// updateToken(token, expires_time);
		// return;

		toAuth();
		console.log('当前为公众号环境', location.href)
	} else {
		console.log('当前为H5环境')
		if (!store.state.isGOAuth) {
			store.commit("SET_GO_AUTH", true);
			uniNavigator('/pages/user/Login')
		} else {
			console.log('路由已存在，不再重复跳转')
		}

	}
	// #endif
	// #ifdef  MP
	// 微信授权信息
	store.commit("SHOW_AUTH_POPUP_SHOW");
    
    // #ifdef MP-WEIXIN
    uni.getPrivacySetting({
        success: (res) => {
            // 如果是false，则说明之前授权过无需弹出隐私协议
            if(res.needAuthorization == true){
                store.commit("SHOW_AGREEMENT_POPUP_SHOW");
                wx.requirePrivacyAuthorize({
                      success: () => {
                        // 用户同意授权
                        // 继续小程序逻辑
                      },
                      fail: () => {}, // 用户拒绝授权
                      complete: () => {}
                    })
            }else {
                store.commit("HIDE_AGREEMENT_POPUP_SHOW");
            }
        },
        fail: () => {},
        complete: () => {},
    });
    // #endif
	console.log('当前为小程序环境')
	// #endif
}

// 登录 授权之后执行
export function toLogin(data, callback) {
    let _this = this;
	const {
		type
	} = data;
	let request = '';
	if (type === 'wechat') {
		// let url = storage.get(WECHAT_AUTH_BACK_URL) || '/index',
			let spid = store.state.spid;
		request = wechatAuth(data.code, spid, 'wechat')
	} else {
		request = login(data);
	}
	request.then(res => {
		console.log('login', res)
		let data = res.data;
        // #ifdef MP-WEIXIN
        storage.set('userInfo_phone',res.data.userInfo.phone)
        // #endif
		let token = data.token,
			expires_time = 1593591798; // store.state.expires_time || 1593522851;
           
		// #ifdef H5
		expires_time = data.expires_time.substring(0, 19);
		expires_time = expires_time.replace(/-/g, "/");
		expires_time = new Date(expires_time).getTime() - 28800000;
        
		updateToken(token, expires_time, data)
		if (_isWeixin && WECHAT_LOGIN) {
			// 微信公众号授权
			console.log('公众号授权登录后若有则返回缓存地址，没有则返回首页')
			getuserInfo().then(res => {
                console.log('store.state.auth_back_url',store.state.auth_back_url)
				// location.href = data.url || '/'
                // location.href = storage.get(WECHAT_AUTH_BACK_URL) || '/'; 
                if(store.state.auth_back_url){
                    location.href = store.state.auth_back_url;
                }else {
                    location.href = data.url || '/'
                }
                // location.href = 'https://shop.arthorize.com/h5/course/detail?id=0&sid=6&uid=1539&invitationId=6&invite_code=UN6HFM'
			});
		} else {
			console.log('H5登录获取信息成功，返回上一级', expires_time)
			uniNavigator(1, "navigateBack")
		}
		// #endif
		// #ifdef  MP
		// 小程序授权信息
		expires_time = data.expires_time;
		if (data.cache_key) {
			storage.set('cache_key', data.cache_key)
		}
		updateToken(token, expires_time, data)
		setTimeout(() => {
			callback(data)
		}, 500)
		console.log('当前为小程序环境')
		// #endif

	}).catch(err => {
		console.log('login', err)
	})
}
// 跳转前需要授权验证的
export function authNavigator(url, type = 'navigateTo', args = {}) {
    console.log('跳转路径', url)
	console.log('是否登录', checkLogin())
	if (checkLogin()) {
		uniNavigator(url, type, args = {})
	} else {
		autoAuth()
	}
}

// 点击需要授权验证的
export function zxauthNavigator() {
	console.log('是否登录', checkLogin())
	if(!checkLogin()){
      autoAuth()  
      return false;
    }else {
        return true;
    }
    
}


export function updateToken(token, expires_time, data,update=false) {
	store.commit("LOGIN", {
		token,
		expires_time
	});
	if (token && expires_time) {
		getuserInfo()
	}
}

export async function getuserInfo() {
	let res = await getUserInfo();
	store.commit("UPDATE_USERINFO", res.data);
	return res;
}

export async function PosterCanvas(arr2, store_name, price, _this, successFn) {
	// arr2[背景图,产品图,产品二维码]
    console.log('--------------',arr2)
	uni.showLoading({
		title: '海报生成中',
		mask: true
	});
	const ctx = uni.createCanvasContext('myCanvas', _this);
    console.log('ctx-',ctx)
	// ctx.clearRect(0, 0, 0, 0);
	const {
		width,
		height,
		path
	} = await uniGetImageInfo(arr2[0]);
    
    
	const WIDTH = width,
		HEIGHT = height,
		marginT = 60;
	// 背景图
	ctx.drawImage(arr2[0], 0, 0, WIDTH, HEIGHT);
	// 商品图
	ctx.drawImage(arr2[1], 0, 0, WIDTH, WIDTH);
	ctx.save()
	// 商品名称
	const CONTENT_ROW_LENGTH = 40;
	let [contentLeng, contentArray, contentRows] = textByteLength(store_name, CONTENT_ROW_LENGTH);
    
	if (contentRows > 2) {
		contentRows = 2;
		let textArray = contentArray.slice(0, 2);
		textArray[textArray.length - 1] += '……';
		contentArray = textArray;
	}
	ctx.setTextAlign('center');
	ctx.setFontSize(32);
	let titleH = 32 * 1.3,
		titleX = WIDTH / 2,
		titleY = WIDTH + marginT;
	for (let m = 0; m < contentArray.length; m++) {
		ctx.fillText(contentArray[m], titleX, titleY + titleH * m);
	}

    
	// 价格显示
	let priceH = 32 * 1.3,
		priceX = WIDTH / 2,
		priceY = titleY + contentArray.length * titleH + marginT;
	ctx.setTextAlign('center');
	ctx.setFontSize(48);
	ctx.setFillStyle('red');
	ctx.fillText('￥' + price, priceX, priceY);

	// 二维码
	let scanH = 200,
		scanX = 40,
		scanY = priceY + marginT;

	// 裁剪圆形区域		
	ctx.save()
	// #ifdef MP
	ctx.arc(scanX + scanH / 2, scanY + scanH / 2, scanH / 2, 0, 2 * Math.PI);
	ctx.setFillStyle('green')
	ctx.fill()
	ctx.clip(); //一旦剪切了某个区域，则所有之后的绘图都会被限制在被剪切的区域内（不能访问画布上的其他区域）。可以在使用 clip() 方法前通过使用 save() 方法对当前画布区域进行保存，并在以后的任意时间对其进行恢复（通过 restore() 方法）。
	// #endif
	// 绘制二维码
	ctx.drawImage(arr2[2], scanX, scanY, scanH, scanH);
	// ctx.fillRect(50, 50, 150, 100)
	// 商二维码右侧文字
	ctx.restore()
	let desH = 32 * 1.3,
		desX = scanX + scanH + 30,
		desY = scanY + (scanH + desH) / 2;
	ctx.setTextAlign('left');
	ctx.setFillStyle('#000');
	ctx.setFontSize(34);
	// #ifdef H5
	ctx.fillText('长按识别二维码 立即购买', desX, desY);
	// #endif
	// #ifdef MP 
	ctx.fillText('长按识别小程序 立即购买', desX, desY);
	// #endif
	ctx.draw(true, function() {
		console.log('xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx')
		uni.canvasToTempFilePath({
			canvasId: 'myCanvas',
			fileType: 'png',
			destWidth: WIDTH,
			destHeight: HEIGHT,
			success: function(res) {
				uni.hideLoading();
				console.log('xxxx', res)
				successFn && successFn(res.tempFilePath);
			},
			fail: function(err) {
				console.log('err',err)
			}
		}, _this)
	});
}

export async function SharePoster(arr2, store_name, desc, _this, successFn) {
	// arr2[背景图,产品图,产品二维码]
    console.log('--------------',arr2)
	uni.showLoading({
		title: '海报生成中',
		mask: true
	});
	const ctx = uni.createCanvasContext('myCanvas', _this);
    console.log('ctx-',ctx)
	// ctx.clearRect(0, 0, 0, 0);
	const {
		width,
		height,
		path
	} = await uniGetImageInfo(arr2[0]);
    
    
	const WIDTH = width,
		HEIGHT = height,
		marginT = 60;
	// 背景图
	ctx.drawImage(arr2[0], 0, 0, WIDTH, HEIGHT);
	// 商品图
	ctx.drawImage(arr2[1], 80, 200, WIDTH * 0.8, WIDTH * 0.8);
    // ctx.fillRect(50, 50, 150, 100)
	ctx.save()
	// 好友名称
	const CONTENT_ROW_LENGTH = 40;
	let [contentLeng, contentArray, contentRows] = textByteLength(store_name, CONTENT_ROW_LENGTH);
    
	if (contentRows > 2) {
		contentRows = 2;
		let textArray = contentArray.slice(0, 2);
		textArray[textArray.length - 1] += '……';
		contentArray = textArray;
	}
	ctx.setTextAlign('left');
	ctx.setFontSize(26);
	let titleH = 32 * 1.3,
		titleX = WIDTH / 20,
		titleY = marginT ;
	for (let m = 0; m < contentArray.length; m++) {
		ctx.fillText(contentArray[m], titleX + 30, titleY + 40);
	}

    
	// 描述显示
	let priceH = 32 * 1.3,
		descX = WIDTH / 20,
		descY = marginT * 2 ;
	ctx.setTextAlign('left');
	ctx.setFontSize(26);
	// ctx.setFillStyle('red');
	ctx.fillText(desc, descX + 30, descY + 40);

	// 二维码
	let scanH = 200,
		scanX = 40,
		scanY = descY + marginT;

	// 裁剪圆形区域		
	ctx.save()
	// 绘制二维码
	ctx.drawImage(arr2[2], scanX , scanY * 5 + 80, scanH, scanH);
	// ctx.fillRect(50, 50, 150, 100)
	// 商二维码右侧文字
	ctx.restore()
	let desH = 32 * 1.3,
		desX = scanX + scanH + 30,
		desY = scanY + (scanH + desH) *4 - 60;
	ctx.setTextAlign('left');
	ctx.setFillStyle('#000');
	ctx.setFontSize(28);
	// #ifdef H5
	ctx.fillText('长按识别二维码了解课程', desX, desY);
	// #endif
	
    console.log('yyyyyyyyy',WIDTH,HEIGHT)
	ctx.draw(true, function() {
		console.log('xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx')
		uni.canvasToTempFilePath({
			canvasId: 'myCanvas',
			fileType: 'png',
			destWidth: WIDTH,
			destHeight: HEIGHT,
			success: function(res) {
				uni.hideLoading();
				console.log('xxxx', res)
				successFn && successFn(res.tempFilePath);
			},
			fail: function(err) {
				console.log('err',err)
			}
		}, _this)
	});
}


const textByteLength = (text, num) => {
	let strLength = 0;
	let rows = 1;
	let str = 0;
	let arr = [];
	for (let j = 0; j < text.length; j++) {
		if (text.charCodeAt(j) > 255) {
			strLength += 2;
			if (strLength > rows * num) {
				strLength++;
				arr.push(text.slice(str, j));
				str = j;
				rows++;
			}
		} else {
			strLength++;
			if (strLength > rows * num) {
				arr.push(text.slice(str, j));
				str = j;
				rows++;
			}
		}
	}
	arr.push(text.slice(str, text.length));
	return [strLength, arr, rows] //  [处理文字的总字节长度，每行显示内容的数组，行数]
}


export function checkLogin(update = true) {
	let token = storage.get('token') || null,
		expires_time = storage.get('expires_time') || null;
	let newTime = Date.parse(new Date()) / (String(expires_time).length === 13 ? 1 : 1000)
	let login = !(expires_time < newTime) && token;
	if (!login) {
		updateToken(null, null, {})
	}
	return login;
}

export function updateTitle(title) {
	uni.setNavigationBarTitle({
		title: title
	});
}

export function throttle(fn, time, obj = {
	leading: true,
	trailing: false
}) {
	let prevTime = 0,
		_this = this,
		timId;
	// 第三个参数生效
	return function() {
		let nowTime = new Date().getTime();
		if (obj.leading === false && !prevTime) {
			// 禁用第一次执行
			prevTime = nowTime;
		}
		if (nowTime - prevTime > time) {
			if (timId) {
				clearTimeout(timId);
				timId = null;
			}
			fn.apply(this, arguments);
			prevTime = nowTime;
		} else if (!timId && obj.trailing) {
			timId = setTimeout(() => {
				prevTime = new Date().getTime();
				timId = null;
				fn.apply(_this, arguments);
			}, time);
		}
	};
}
// 函数防抖 延迟函数执行，并且不管触发多少次都只执行最后一次
export function debounce(fn, time = 500,immediate) {
	let timId, result;
	let debFun = function() {
		let _this = this;
		timId && clearTimeout(timId);
		if (immediate) {
			// 立即执行
			let isImmediate = !timId;
			timId = setTimeout(() => {
				timId = null;
			}, time);
			if (isImmediate) {
				result = fn.apply(_this, arguments);
			}
		} else {
			timId = setTimeout(() => {
				fn.apply(_this, arguments);
			}, time);
		}
	
		return result;
	};
	// 取消防抖函数执行
	debFun.cancel = function() {
		clearTimeout(timId);
		timId = null;
	};
	return debFun;
}

// 保存图片至本地
export function saveImageToPhotosAlbum(path, filename, callback) {
	// #ifdef H5
	var xhr = window.XMLHttpRequest ?
		new XMLHttpRequest() :
		new ActiveXObject("Microsoft.XMLHTTP");
	xhr.open("get", path, true);
	//监听进度事件
	xhr.addEventListener(
		"progress",
		function(evt) {
			console.log(evt)
			if (evt.lengthComputable) {
				var percentComplete = evt.loaded / evt.total;
				if (percentComplete === 1) {
					callback && callback(true)
				}
			}
		},
		true
	);
	xhr.responseType = "blob";
	xhr.onreadystatechange = function() {
		if (xhr.readyState === 4 && xhr.status === 200) {
			// loading.close();
			const linkHtml = document.createElement("a");
			// linkHtml.href = response.path;
			linkHtml.href = window.URL.createObjectURL(xhr.response);
			linkHtml.download = filename;
			linkHtml.click();
			// notify.close()
			// _this.$showElSuccessMessage("下载完成");
		}
	};
	xhr.onerror = function() {
		callback && callback(false)
	};
	xhr.send();

	// #endif

	// #ifdef MP
	uni.saveImageToPhotosAlbum({
		filePath: path,
		success: function() {
			callback && callback(true)
		},
		fail() {
			callback && callback(false)
		}
	});
	// #endif
}

// 获取用户微信地址信息 已判断在微信环境（公众号或小程序）
export function authGetAddress() {
	return new Promise((resolve, rejext) => {
		// #ifdef H5
		if (_isWeixin) {
			const module = () => import("@/utils/wechat/common.js");
			module().then(Module => {
				Module.wechatEvevt('openAddress').then(res => {
					resolve(res)
				}).catch(err => {
					reject(err)
				})
			});
		}
		// #endif

		// #ifdef MP
		uni.authorize({
			scope: 'scope.address',
			success: function(res) {
				uni.chooseAddress({
					success: function(res) {
						resolve(res)
					},
					fail: function(res) {
                        console.log('选择地址失败',res)
						if (res.errMsg == 'chooseAddress:cancel') {
							uni.showToast({
								title: '取消选择'
							})
						}
					},
				})
			},
			fail: function(res) {
				uni.showModal({
					title: '您已拒绝导入微信地址权限',
					content: '是否进入权限管理，调整授权？',
					success(res) {
						if (res.confirm) {
							uni.openSetting({
								success: function(res) {
									console.log(res.authSetting)
								}
							});
						} else if (res.cancel) {
							uni.showToast({
								title: '已取消'
							})
						}
					}
				})
			},
		})
		// #endif
	})
}
// 使用应用内置地图查看位置。
export function authOpenLocation(config) {
	return new Promise((resolve, rejext) => {
		// #ifdef H5
		if (_isWeixin) {
			const module = () => import("@/utils/wechat/common.js");
			module().then(Module => {
				Module.wechatEvevt('openLocation', config).then(res => {
					resolve(res)
				}).catch(err => {

					if (err.is_ready) {
						err.wx.openLocation(config);
					} else {
						reject(err)
					}

				})
			});
		}
		// #endif

		// #ifdef MP
		uni.openLocation({
			latitude: parseFloat(config.latitude),
			longitude: parseFloat(config.longitude),
			scale: 8,
			name: config.name,
			address: config.address,
			success: function(res) {
				resolve(res)
			},
		});
		// #endif
	})
}

// 授权获取位置信息
export function authGetLocation(type = 'wgs84') {
	return new Promise((resolve, reject) => {
		// #ifdef MP
		uni.getSetting({
			success: function(res) {
				if (!res.authSetting['scope.userLocation']) {
					uni.getLocation({
						type: type,
						success: function(res) {
							resolve(res)
						},
						fail(err) {
							if (err.errMsg == "getLocation:fail auth deny") {
								uni.showModal({
									title: '您已经拒绝授权地理位置',
									content: '是否需要开启权限',
									success: function(res) {
										if (res.cancel) {
											reject(res);
										} else if (res.confirm) {
											uni.openSetting({
												success: function(res) {
													if (res
														.authSetting[
															"scope.userLocation"
															]) {
														uni.showToast({
															title: '授权成功',
															icon: 'success',
															duration: 1000
														})
														uni.getLocation({
															success: function(
																res
																) {

																resolve
																	(
																		res);
															}
														})
													} else {
														uni.showToast({
															title: '授权失败',
															icon: 'none',
															duration: 1000
														})
													}
												},
												fail: function(err) {
													console.log(
														'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
														err)
												}
											})
										}
									},
								})
							}
						}
					});
				} else {
					uni.getLocation({
						type: type,
						success: function(res) {
							resolve(res)
						},
						fail(err) {
							reject(err)
						}
					});
				}
			}
		});
		// #endif
		// #ifdef H5
		console.log('ccccccccwwc')
		uni.getLocation({
			type: type,
			success: function(res) {
				resolve(res)
			},
			fail(err) {
				reject(err)
			}
		});
		// #endif

	})
}

// 授权获取位置信息
export function authChooseLocation() {
	return new Promise((resolve, reject) => {
		// #ifdef MP
		uni.authorize({
			scope: 'scope.userLocation',
			success() {
				uni.chooseLocation({
					success: function(res) {
						resolve(res)
					},
					fail(err) {
						reject(err)
					}
				});
			},
			fail(err) {
				uni.showModal({
					title: '提示',
					content: '请允许使用您的位置',
					confirmText: '去允许',
					success: function(res) {
						if (res.confirm) {
							uni.openSetting({
								success(res) {
									console.log(res.authSetting);
								}
							});
						} else if (res.cancel) {
							reject()
						}
					}
				});
			}
		})

		// #endif
		// #ifdef H5
		uni.chooseLocation({
			success: function(res) {
				resolve(res)
			},
			fail(err) {
				reject(err)
			}
		});
		// #endif
	})
}
// 获取用户微信地址信息 已判断在微信环境（公众号或小程序）
export function authOpenQRCode() {
	return new Promise((resolve, reject) => {
		// #ifdef H5
		if (_isWeixin) {
			const module = () => import("@/utils/wechat/common.js");
			module().then(Module => {
				Module.wechatEvevt('scanQRCode', {
					needResult: 1,
					scanType: ["qrCode", "barCode"]
				}).then(res => {
					if (res.resultStr) {
						resolve(res)
					} else {
						uni.showToast({
							title: '没有扫描到什么！'
						})
					}
				}).catch(err => {
					if (res.is_ready) {
						res.wx.scanQRCode({
							needResult: 1,
							scanType: ["qrCode", "barCode"],
							success: function(res) {
								resolve(res)
							},
							fail: function(res) {
								if (res.errMsg == "scanQRCode:permission denied") {
									uni.showToast({
										title: '没有权限'
									})
								}
								reject(res)
							}
						});
					} else {
						reject(err)
					}
				})
			});
		}
		// #endif

		// #ifdef MP
		uni.scanCode({
			success: function(res) {
				console.log('scanCode', res)
				resolve(res)
			},
			fail(err) {
				console.log('scanCodeerr', err)
				reject(err)
			}
		});
		// #endif
	})
}
// 设置剪切板内容 复制
export function setClipboardData(val) {
	return new Promise((resolve, reject) => {
		// #ifdef MP
		uni.setClipboardData({
			data: val,
			success: function(res) {
				uni.showToast({
					title: '复制成功'
				})
				resolve(true)
			},
			fail() {
				uni.showToast({
					title: '复制失败'
				})
				reject(false)
			}
		});
		// #endif
		// #ifdef H5
		if (!document.queryCommandSupported('copy')) { //为了兼容有些浏览器 queryCommandSupported 的判断
			// 不支持
			uni.showToast({
				title: '浏览器不支持'
			})
		}
		let textarea = document.createElement("textarea")
		textarea.value = val
		textarea.readOnly = "readOnly"
		document.body.appendChild(textarea)
		textarea.select() // 选择对象
		textarea.setSelectionRange(0, val.length) //核心
		let result = document.execCommand("copy") // 执行浏览器复制命令
		if (result) {
			uni.showToast({
				title: '复制成功'
			})
			resolve(true)
		} else {
			uni.showToast({
				title: '复制失败'
			})
			reject(false)
		}

		textarea.remove()
		// #endif
	})
}
// 获取剪切板内容 粘贴
export function getClipboardData() {
	return new Promise((resolve, reject) => {
		uni.getClipboardData({
			success: function(res) {
				resolve(res.data)
			}
		});
	})
}

export function openWeChatCustomerService(weiXinCustomerServiceUrl = '',corpId = '',showMessageCard = false, sendMessageTitle = '', sendMessagePath ='',sendMessageImg = '') {
	// if (!weiXinCustomerServiceUrl || !corpId){
	//     uni.showToast({
	//         title: '请配置好客服链接或者企业'
	//     })
	//     return
	// }
	wx.openCustomerServiceChat({
	    // 客服信息
	    extInfo: {
	        url: weiXinCustomerServiceUrl, // 客服链接 https://work.weixin.qq.com/xxxxxxxx
	    },
	    corpId, // 企业ID wwed1ca4d3597eXXXX
	    showMessageCard, // 是否发送小程序气泡消息
	    sendMessageTitle, // 气泡消息标题
	    sendMessagePath, // 气泡消息小程序路径（一定要在小程序路径后面加上“.html”，如：pages/index/index.html）
	    sendMessageImg, // 气泡消息图片
	    success(res) {
	        console.log("success", JSON.stringify(res));
	    },
	    fail(err) {
            uni.showToast({
                title: err.errMsg
            })
            return;
	    },
	});
}


export function getUrlParams(param, k, p) {
	if (typeof param != 'string') return {};
	k = k ? k : '&'; //整体参数分隔符
	p = p ? p : '='; //单个参数分隔符
	var value = {};
	if (param.indexOf(k) !== -1) {
		param = param.split(k);
		for (var val in param) {
			if (param[val].indexOf(p) !== -1) {
				var item = param[val].split(p);
				value[item[0]] = item[1];
			}
		}
	} else if (param.indexOf(p) !== -1) {
		var item = param.split(p);
		value[item[0]] = item[1];
	} else {
		return param;
	}
	return value;
}

export function initTime(val, type, format = '-') {
	const date = val ? new Date(val) : new Date();
	let y = date.getFullYear(),
		m = date.getMonth() + 1,
		d = date.getDate();
	let h = date.getHours(),
		i = date.getMinutes(),
		s = date.getSeconds();
	m = m > 9 ? m : '0' + m;
	d = d > 9 ? d : '0' + d;
	h = h > 9 ? h : '0' + h;
	i = i > 9 ? i : '0' + i;
	s = s > 9 ? s : '0' + s;

	const year = `${y}${format}${m}${format}${d}`;
	const time = `${h}:${i}:${s}`;
	if (type === 'ymd') {
		return `${year}`
	} else if (type === 'his') {
		return `${time}`
	} else {
		return `${year} ${time}`
	}



}
