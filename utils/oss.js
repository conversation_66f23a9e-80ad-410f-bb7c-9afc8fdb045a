const getImageInfo = async function(path) {
	return new Promise((resolve, reject) => {
		uni.request({ //需要在小程序后台配置 request域名
			url: path + '?x-oss-process=image/info',
			success: (res) => {
				resolve({
					w: Number(res.data.ImageWidth.value),
					h: Number(res.data.ImageHeight.value)
				});
			},
			fail: (err) => {
				uni.getImageInfo({
					src: path,
					success: function(image) {
						resolve({
							w: Number(image.width),
							h: Number(image.height)
						});
					},
					fail(err1) {
						console.log('err', err1)
						reject(err1);
					}
				});
			}
		})
	})
}
// 裁剪图片
const handleImg = async function(path, params) {
	if (!path) {
		return uni.showToast({
			title: '传入地址无效',
			icon: 'none'
		})
	}
	let reg = /\.(png|jpg|gif|jpeg|webp)$/,
		regRes = reg.test(path);
	if (params.m === 'fill' && regRes) {
		// 缩放模式
		let info = await getImageInfo(path);
		if (info) {
			let w = info.w,
				h = info.h;
			if (info.w < info.h) {
				h = params.w / (info.w / info.h); //宽固定的情况下 实际能裁剪的最大高度
				params.h = parseInt(h)
			} else {
				w = (params.w / params.h) * info.h; //高固定的情况下 实际能裁剪的最大宽度
				params.w = parseInt(w);
				params.h = info.h;
			}
		}
	}
	return regRes ? path + ossImgParams(params) : path;
}

const ossImgParams = function(params) {
	// let { pixelRatio } = uni.getSystemInfoSync();
	// params.w = params.w ? (params.w / 2 * pixelRatio) : 700; //高dpr设备会使宽高超出控制
	// params.h = params.h ? (params.h / 2 * pixelRatio): 700;
	params.w = params.w ? params.w : 700;
	params.h = params.h ? params.h : 700;
	params.type = params.type ? params.type : 'resize';
	params.m = params.m ? params.m : 'lfit';
	params.limit = params.limit ? params.limit : "1";
	let ossType = '?x-oss-process=image/' + params.type,
		ossM = ',m_' + params.m,
		ossX = params.x ? ',x_' + parseInt(params.x) : '',
		ossY = params.y ? ',y_' + parseInt(params.y) : '',
		ossH = params.h ? ',h_' + parseInt(params.h) : '',
		ossQ = params.q ? '/quality,q_' + params.q : '',
		ossW = params.w ? ',w_' + parseInt(params.w) : '',
		ossG = params.g ? ',g_' + params.g : '',
		ossLimit = ',limit_' + params.limit,
		ossFormat = params.format ? '/format,' + params.format : '',
		ossWatermark = params.watermark ?
		'/watermark,image_d2F0ZXJtYXJrX25ldy5wbmd4LW9zcy1wcm9jZXNzPWltYWdlL3Jlc2l6ZSxwXzIwMC9icmlnaHQsMTAwL2NvbnRyYXN0LDEwMA==,t_80,g_west,y_10,x_100' :
		''; //水印为oss根目录之后的路径转为base64之后的值
	return ossType + ossM + ossX + ossY + ossH + ossW + ossLimit + ossQ + ossG + ossFormat + ossWatermark;
}


export {
	handleImg,
	ossImgParams,
	getImageInfo
}
