import {
	PATH_INDEX_URL
} from '@/config.js'
import {
	debounce
} from '@/utils/common.js';
import store from "../store";
// 对uni中的交互反馈相关api组件进行封装  showToast、showModal
export function uniShowToast(title = '未登录', icon = "none", args = {}) {
	const {
		image = '',
			mask = false,
			duration = 1500,
			position,
			success, fail, complete
	} = args;
	let toast = {
		title,
		icon: icon == 'error' ? 'none' : icon,
		duration
	};
	// #ifdef APP-PLUS || H5 || MP-WEIXIN || MP-BAIDU
	if (image) {
		toast['image'] = image
	}
	// #endif
	// #ifdef H5 || MP-WEIXIN 
	if (mask) {
		toast['mask'] = mask
	}
	// #endif
	// #ifdef APP-PLUS
	if (position) {
		toast["position"] = position
	}
	// #endif
	uni.showToast({
		...toast,
		...callBack(toast, {
			success,
			fail,
			complete
		})
	})
}
export function uniHideToast() {
	uni.hideToast()
}
export function uniHideLoading() {
	uni.hideLoading()
}
export function successToast(title = '成功', args = {}) {
	uniShowToast(title, "success", args)
}
export function loadingToast(title = '加载中', args = {}) {
	const {
		mask = false,
			success, fail, complete
	} = args;
	let loading = {
		title
	};
	if (mask) {
		loading['mask'] = mask
	}
	uni.showLoading({
		...loading,
		...callBack(loading, {
			success,
			fail,
			complete
		})
	});
}

export function uniShowModal(title, content, args = {}) {
	const {
		showCancel = true, cancelText = "取消", cancelColor, confirmText = "确定", confirmColor, success, fail, complete
	} = args;
	let modal = {
		title,
		content,
		showCancel,
		cancelText,
		confirmText
	}
	// #ifdef H5 || MP-WEIXIN || MP-BAIDU
	if (cancelColor) {
		modal['cancelColor'] = cancelColor
	}
	if (confirmColor) {
		modal['confirmColor'] = confirmColor
	}
	// #endif
	uni.showModal({
		...modal,
		...callBack(modal, {
			success,
			fail,
			complete
		})
	})
}
// 跳转
export const uniNavigator = debounce(function(url, type = 'navigateTo', args = {}) {
	const {
		userInfo
	} = store.state;
	const {
		endtime = Number(url) ? 600 : 200,
			animationType,
			animationDuration,
			success,
			fail,
			complete
	} = args;
	if (!!Number(url)) {
		type = 'navigateBack'
	} else {
		url = url.trim();
		if (type !== 'switchTab') {
			const split1 = url.split('?');
			url = url + (!!split1[1] ? '&' : '?') + 'uid=' + (userInfo && userInfo.uid ? userInfo.uid : -
			1); //追加公共参数
		}

	}
	// #ifdef H5
	if (ROUTES.ROUTES_ARRAY.length) {
		if (!!Number(url)) {
			url = Number(url)
		} else {
			const split = url.split('?');
			let aliasPath = ROUTES.ROUTES_ARRAY.filter((item) => {
				return item.path === split[0]
			})
			url = (!!split[1] ? aliasPath[0].aliasPath + `?${split[1]}` : aliasPath[0].aliasPath)
		}
	}
	// #endif
	// #ifdef MP
	if (type !== 'navigateBack') {
		url = (url.indexOf('pages') < 0) ? pages[url].path : url;
	}
	// #endif
	let obj = {
		url
	};

	// #ifdef APP-PLUS
	if (animationType) {
		obj['animationType'] = animationType
	}
	if (animationDuration) {
		obj['animationDuration'] = animationDuration
	}
	// #endif
	obj = callBack(obj, {
		success,
		fail,
		complete
	})
	// console.log('type',type)
 //    console.log('url',url)
 //    console.log('obj',obj)
    
	switch (type) {
		case 'navigateTo':
			setTimeout(function() {
				// if (this._isWidescreen) { //若为宽屏，则触发右侧详情页的自定义事件
				//          uni.$emit('updateDetail', {
				//            detail: encodeURIComponent(JSON.stringify(detail))
				//          })
				//        } else {
				//          uni.navigateTo({
				//            url: '/pages/detail/detail?query=' + encodeURIComponent(JSON.stringify(detail))
				//          });
				//        }
				uni.navigateTo(obj)
			}, endtime);
			break;
		case 'redirectTo':
			setTimeout(function() {
				uni.redirectTo(obj)
			}, endtime);
			break;
		case 'reLaunch':
			setTimeout(function() {
				uni.reLaunch(obj)
			}, endtime);
			break;
		case 'switchTab':
			setTimeout(function() {
				uni.switchTab(obj)
			}, endtime);
			break;
		case 'navigateBack':
			setTimeout(function() {
				// #ifdef H5
				const urlToNum = Number(url);
				history.go(urlToNum > 0 ? 0 - Number(url) : urlToNum)
				// #endif
				// #ifdef MP
				const history = getCurrentPages();
				if (history.length) {
					uni.navigateBack({
						delta: Math.abs(Number(url))
					})
				} else {
					uni.switchTab({
						url: PATH_INDEX_URL,
						complete: function(res) {
							console.log(res)
						}
					})
				}
				// #endif
			}, endtime);

			break;

	}
}, 500, true)


export function uniStartPullDownRefresh(args = {}) {
	uni.startPullDownRefresh(args);
}

export function uniStopPullDownRefresh(endtime = 1000) {
	setTimeout(function() {
		uni.stopPullDownRefresh();
	}, endtime);


}

export function uniGetImageInfo(path) {
	return new Promise((resolve, reject) => {
		uni.getImageInfo({
			src: path,
			success: function(res) {
				console.log(res)
				resolve(res)
			},
			fail: function(err) {
				console.log(err)
				reject(err)
			},
		})
	})

}
export function uniSelectorQueryInfo(selector, _this) {
	return new Promise((resolve, reject) => {
		const query = uni.createSelectorQuery().in(_this);
		query.select(selector).boundingClientRect(res => {
			// 获取节点坐标
			resolve(res)
		}).exec();
	})

}
export function uniSetNavigationBarColor(backgroundColor = "#e93323", frontColor = "#ffffff") {
	uni.setNavigationBarColor({
		frontColor,
		backgroundColor,
		animation: {
			duration: 400,
			timingFunc: 'easeIn'
		}
	})
}
// 跳转小程序
export function uniNavigateToMiniProgram(appId, args = {}) {
	// #ifdef MP
	const {
		path,
		extraData = {
			from: '着调儿'
		},
		envVersion,
		success,
		fail,
		complete
	} = args;
	let obj = {
		path,
		extraData,
		envVersion
	}
	obj = callBack(obj, {
		success,
		fail,
		complete
	})
	uni.navigateToMiniProgram({
		appId: '',
		...obj
	})
	// #endif

}
// 返回上一个小程序
export function uniNavigateBackMiniProgram(args = {}) {
	// #ifdef MP
	const {
		extraData = {
				from: '着调儿'
			},
			success,
			fail,
			complete
	} = args;

	let obj = {
		extraData
	}
	obj = callBack(obj, {
		success,
		fail,
		complete
	})
	uni.navigateBackMiniProgram({
		...obj
	})
	// #endif
}

function callBack(name = {}, options = {}) {
	const {
		success,
		fail,
		complete
	} = options;
	if (success) {
		name['success'] = (res) => {
			success(res)
		}
	}
	if (fail) {
		name['fail'] = (err) => {
			fail(err)
		}
	}
	if (complete) {
		name['complete'] = (res) => {
			complete(res)
		}
	}
	return name
}
