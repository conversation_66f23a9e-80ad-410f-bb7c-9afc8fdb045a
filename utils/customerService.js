const sign = '37ef9b97802051c07740cfb81fb7b26aa0032e14529356eb64e7c87b9a5d908afc5ec3e629fd66271d349286af5c5c43d00e2bfe';
export function initService(obj, selector, typ = "_self") {
	//参数说明
	//sign：公司渠道唯一标识，复制即可，无需改动
	//uid：用户唯一标识，如果没有则不填写，默认为空
	//data：用于传递用户信息，最多支持5个，参数名分别为c1,c2,c3,c4,c5；默认为空
	//selector：css选择器(document.querySelector, 如#btnid .chat-btn等)，用于替换默认的常驻客服入口
	//callback(type, data): 回调函数,type表示事件类型， data表示事件相关数据
	//type支持的类型：newmsg有新消息，error云智服页面发生错误， close聊天窗口关闭
	// d1: '', //商品描述
	// d2: '', //价格
	// d3: '', //原价格
	// d4: '', //展示商品图片链接
	// d5: '', //商品跳转链接
	// d6: '', //商品id

	// 当前界面小窗口方式接入
	if (selector) {
		let data = Object.assign({
			c1: '',
			c2: '',
			c3: '',
			c4: '',
			c5: ''
		}, obj)
		var elem = document.createElement("script");
		elem.src = '/chat/yzf_chat.js';
		document.body.appendChild(elem);
		elem.onload = elem.onreadystatechange = function() {
			window.yzf && window.yzf.init({
				sign: sign,
				uid:  '',
				data: data,
				selector: selector,
				callback: function(type, data) {}
			})
		}
	} else {
		let url = 'https://yzf.qq.com/xv/web/static/chat/index.html?',
			data = Object.assign({
				sign: sign,
				uid:  '',
				c1:'',
				c2:'',
				c3: '',
				c4: '',
				c5: ''
			}, obj);
		data = Object.keys(data)
			.map(function(key) {
				return encodeURIComponent(key) + '=' + encodeURIComponent(data[key]);
			})
			.join('&');
		url = url + data;
        window.open(url, typ);
	}
}
