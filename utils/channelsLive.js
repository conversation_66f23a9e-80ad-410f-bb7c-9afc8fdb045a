import {
	isWeixin
} from "./validate.js";
// import {
// 	Wechat,
// 	MiniProgram
// } from "wechat-jssdk";
// console.log('Wechat', Wechat)

// 获取视频号直播预告信息
export const getChannelsLiveNoticeInfo = (finderUserName = "sphewD9HMy4BfJ1", ) => {
	return new Promise((resolve, reject) => {
		wx.getChannelsLiveNoticeInfo({
			finderUserName,
			success(res) {
				resolve(res)
			},
			fail(err) {
				reject(err)
			}
		})
	})
}
// 获取视频号直播信息
export const getChannelsLiveInfo = (finderUserName = "sphewD9HMy4BfJ1", ) => {
	return new Promise((resolve, reject) => {
		wx.getChannelsLiveInfo({
			finderUserName,
			success(res) {
				resolve(res)
			},
			fail(err) {
				reject(err)
			}
		})
	})
}

// 预约视频号直播  getChannelsLiveNoticeInfo 接口获取  noticeId
export const reserveChannelsLive = (noticeId) => {
	wx.reserveChannelsLive({
		noticeId
	})
}

// 打开视频号直播
export const openChannelsLive = (finderUserName, feedId, nonceId) => {
	return new Promise((resolve, reject) => {
		wx.openChannelsLive({
			finderUserName,
			feedId,
			nonceId,
			success(res) {
				resolve(res)
			},
			fail(err) {
				reject(err)
			}
		})
	})
}

// 打开视频号视频
export const openChannelsActivity = (finderUserName = "", feedId = "", url) => {
	// #ifndef MP
	return new Promise((resolve, reject) => {
		if (url.indexOf('weixin://') > -1) {
			resolve({
				xtype: 'mp'
			});
		} else {
			uni.showModal({
				title: '提示',
				content: '请前往微信搜索 着调 小程序',
				success: function(res) {
					if (res.confirm) {
						resolve('用户点击确定');
					} else if (res.cancel) {
						reject('用户点击取消');
					}
				}
			});
		}

	})
	// #endif
	// #ifdef MP
	return new Promise((resolve, reject) => {
		wx.openChannelsActivity({
			finderUserName,
			feedId,
			success(res) {
				resolve(res)
			},
			fail(err) {
				console.log(finderUserName, feedId)
				reject(err)
			}
		})
	})
	// #endif

}
