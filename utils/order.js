import {
	uniNavigator,
	loadingToast,
	uniShowToast,
	uniHideToast,
	uniHideLoading
} from './uni_api'
import {
	cancelOrder,
	takeOrder,
	delOrder,
	payOrder
} from "@/api/order";
import {
	activityCancelOrder,
	activityTakeOrder,
	activityDelOrder,
	activityPayOrder
} from "@/api/community";
// #ifdef H5
import {
	pay
} from '@/utils/wechat/pay.js';
// #endif
export function goShopDetail(item) {
	console.log(item)
	// return
	// return new Promise(resolve => {
	if (item.activity && item.activity.type === '1') {
		uniNavigator(`/pages/activity/SeckillDetails?id=${item.activity.id}&time=${item.activity.time}&status=1`);
	} else if (item.activity && item.activity.type === '2') {
		uniNavigator(`/pages/activity/DargainDetails?id=${item.activity.id}`);
	} else if (item.activity && item.activity.type === '3') {
		uniNavigator(`/pages/activity/GroupDetails?id=${item.activity.id}`);
	} else {
		uniNavigator(`/pages/shop/GoodsCon?id=${item.id}`);
	}
}

export function payOrderHandle(orderId, type, from,source) {
	return new Promise((resolve, reject) => {
		loadingToast('支付中')
		let req = '';
		if(source==='community'){
			req = activityPayOrder;
		}else{
			req = payOrder;
		}
		req(orderId, type, from)
			.then(res => {
				const data = res.data;
				let url = '/pages/order/PaymentStatus?orderId=' + orderId + '&msg=' + res.msg;
				if(source==='community'){
					url = url + '&source=community'
				}
				uniHideToast();
				switch (data.status) {
					case "ORDER_EXIST":
					case "EXTEND_ORDER":
					case "PAY_ERROR":
					case "PAY_DEFICIENCY":
						uniShowToast(res.msg);
						reject(data, url + '&status=0');
						break;
					case "SUCCESS":
						uniShowToast(res.msg);
						// #ifdef H5
						url = url + '&status=1'
						// #endif
						resolve(data, url);
						break;
					case "WECHAT_H5_PAY":
                    case "BYTEDANCE_PAY":
						// #ifndef MP-TOUTIAO
						setTimeout(() => {
							location.replace(data.result.jsConfig.mweb_url);
						}, 100);
						resolve(data, url + '&status=2');
						// #endif
						// #ifdef MP-TOUTIAO
                        
						let tt_res = res.data.result;
                        console.log('jsConfig--',tt_res)
                        tt.pay({
                            orderInfo: {
                                order_id: tt_res.orderId,
                                order_token: tt_res.orderToken,
                            },
                            service: 5,
                            _debug: 1,
                            success(res) {
                                if (res.code == 0) {
                                    uniShowToast('支付成功');
                                    resolve(res, url)
                                }
                            },
                            fail(res) {
                                console.log('字节支付失败', res)
                                uniShowToast('取消支付');
                                reject(err, url + '&status=2');
                            },
                        });
              
						// #endif
						break;
					case "WECHAT_PAY":
						// #ifdef H5
						pay(data.result.jsConfig).then(() => {
							resolve(data, url + '&status=4');
						});
						// #endif
						// #ifdef MP-WEIXIN
						let jsConfig = res.data.result.jsConfig;
                        //  requestOrderPayment
                        wx.requestPayment({
							timeStamp: jsConfig.timestamp,
							nonceStr: jsConfig.nonceStr,
							package: jsConfig.package,
							signType: jsConfig.signType,
							paySign: jsConfig.paySign,
							success: function(res) {
								uniShowToast('支付成功');
								resolve(res, url)
							},
							fail: function(err) {
								uniShowToast('取消支付');
								reject(err, url + '&status=2');
							},
							complete: function(e) {
								//关闭当前页面跳转至订单状态
								if (res.errMsg == 'requestPayment:cancel') {
									reject(e, url + '&status=2');
									return uniShowToast('取消支付');
								}
							}
						});
						// #endif
				}
			})
			.catch(err => {
				console.log(err)
				uniShowToast(err.msg || err || "订单支付失败");
				reject(err)
			});
	});
}

export function cancelOrderHandle(orderId,source) {
	return new Promise((resolve, reject) => {
		uni.showModal({
			title: '提示',
			content: '确认取消该订单',
			success(res) {
				if (res.confirm) {
					let req = '';
					if(source==='community'){
						req = activityCancelOrder;
					}else{
						req = cancelOrder;
					}
					req(orderId)
						.then(res => {
							uniShowToast("取消成功");
							resolve(res);
						})
						.catch(err => {
							uniShowToast("取消失败");
							reject(err);
						});
				} else if (res.cancel) {

				}
			}
		})
	});
}

export function takeOrderHandle(orderId,source) {
	return new Promise((resolve, reject) => {
		let req = '';
		if(source==='community'){
			req = activityTakeOrder;
		}else{
			req = takeOrder;
		}
		req(orderId)
			.then(res => {
				resolve(res);
			})
			.catch(err => {
				uniShowToast("收货失败");
				reject(err);
			});
	});
}
export function delOrderHandle(orderId,source) {
	return new Promise((resolve, reject) => {
		uni.showModal({
			title: '提示',
			content: '确认删除该订单',
			success(res) {
				if (res.confirm) {
					let req = '';
					if(source==='community'){
						req = activityDelOrder;
					}else{
						req = delOrder;
					}
					req(orderId)
						.then(res => {
							uniShowToast("删除成功");
							resolve(res);
						})
						.catch(err => {
							uniShowToast("删除失败");
							reject(err);
						});
				} else if (res.cancel) {

				}
			}
		})
	});
}
