import {
	uniShowToast,
	successToast,
	loadingToast,
	uniHideToast,
	uniShowModal
} from './uni_api'

export default function cloudRequest(name, data = {}, options = {}) {
	const {
		loading = true
	} = options;
	loadingStatus(loading, true)
	return new Promise((resolve, reject) => {
		uniCloud.callFunction({
			name: name,
			data: data,
		}).then((res) => {
			loadingStatus(loading, false)
			if(res.success){
				resolve(res.result)
			}else{
				reject(err)
			}
			
		}).catch((err) => {
			loadingStatus(loading, false)
			reject(err)
		})
	})
}

// 定义请求提示
const loadingStatus = (loading, type) => {
	if (loading) {
		if (type) {
			loadingToast('正在请求中');
		} else {
			uniHideToast();
		}
	} else {
		return
	}
}
