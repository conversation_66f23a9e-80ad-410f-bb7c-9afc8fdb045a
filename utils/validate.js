// 校验是否是手机端
export function isMobile() {
	const userAgent = navigator.userAgent;
	return userAgent.match(
		/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
	);
}
// 校验微信环境
export function isWeixin() {
	// const userAgent = navigator.userAgent;
	// return userAgent.toLowerCase().indexOf("micromessenger") !== -1;
     var ua = window.navigator.userAgent.toLowerCase();
        if(ua.match(/MicroMessenger/i) == 'micromessenger'){
            return true;
        }else{
            return false;
        }
}

// 输入金额校验
export function RegMoney(val) {
	let reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
	return reg.test(val)
}
// 手机号码校验
export function RegPhone(val) {
	let reg = /^1[3|4|5|7|8][0-9]\d{8}$/;
	return reg.test(val);
}
// 固定电话号码校验
export function RegFixedPhone(val) {
	let reg = /^([0][1-9]{2,3}-)?[0-9]{7,8}$/;
	if(val.indexOf('-')<0){
	reg = /^([0][1-9]{2,3})?[0-9]{7,8}$/;
	}
	return reg.test(val)
}
// 手机号加固定座机总校验
export function RegPhoneAndFixed(val) {
	let reg = /^(((\d{3,4}-)?[0-9]{7,8})|(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8})$/;
	return reg.test(val);
}
 
// 邮箱校验
export function regEmail(val) {
	const reg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/;
	return reg.test(val)
}
// 校验url
export function regHref(val) {
	const reg = /^((https?|ftp|file):\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
	return reg.test(val)
}

// 界面是否在可视范围内
// export function elementIsVisibleInViewport(el, partiallyVisible = false) {
// 	const {
// 		top,
// 		left,
// 		bottom,
// 		right
// 	} = el.getBoundingClientRect();
// 	const {
// 		innerHeight,
// 		innerWidth
// 	} = window;
// 	return partiallyVisible ?
// 		((top > 0 && top < innerHeight) || (bottom > 0 && bottom < innerHeight)) &&
// 		((left > 0 && left < innerWidth) || (right > 0 && right < innerWidth)) :
// 		top >= 0 && left >= 0 && bottom <= innerHeight && right <= innerWidth;
// }
