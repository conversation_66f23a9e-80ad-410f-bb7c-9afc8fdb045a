import WechatJSSDK from "wechat-jssdk/dist/client.umd"; //WechatJSSDK(result)
import {
	getWechatConfig
} from '@/api/public';

function toPromise(fn, config = {}) {
	return new Promise((resolve, reject) => {
		fn({
			...config,
			success(res) {
				resolve(res);
			},
			fail(err) {
				reject(err);
			},
			complete(err) {
				reject(err);
			},
			cancel(err) {
				reject(err);
			}
		});
	});
}
export function pay(config) {
	return new Promise((resolve, reject) => {
		getWechatConfig().then(res => {
			res.data.customUrl = '//res.wx.qq.com/open/js/jweixin-1.6.0.js';
			let wechatObj = WechatJSSDK(res.data);
			wechatObj.initialize()
				.then(w => {
					let instance = w.wx;
					instance.ready(() => {
						instance.chooseWXPay({
							...config,
							success(res) {
								resolve(res);
							},
							fail(err) {
								reject(err);
							},
							complete(err) {
								// reject(err);
							},
							cancel(err) {
								reject(err);
							}
						})
					});
				})
				.catch(err => {
					console.error(err);
				});

		})
	})
}
