import WechatJSSDK from "wechat-jssdk/dist/client.umd"; //WechatJSSDK(result)
import {
	getWechatConfig
} from '@/api/public';

export function wechatEvevt(name, config) {
	return new Promise((resolve, reject) => {
		let configDefault = {
			fail(err) {
				reject(err)
			},
			success(res) {
				resolve(res);
			},
			cancel(err) {
				reject(err);
			},
			complete(err) {
				reject(err);
			}
		};
		Object.assign(configDefault, config);
		getWechatConfig().then(res => {
			res.data.customUrl = '//res.wx.qq.com/open/js/jweixin-1.6.0.js';
			let wechatObj = WechatJSSDK(res.data);
			wechatObj.initialize()
				.then(w => {
					let instance = w.wx;
					instance.ready(() => {
						instance[name] && instance[name](configDefault)
					});
				})
				.catch(err => {
					console.error(err);
				});

		})

	});
}
