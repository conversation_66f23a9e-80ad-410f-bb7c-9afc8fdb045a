// 分享规则,传入地址为当前页面路径 已pages开头,最终咋在config拼接 主域名 + 页面路径 + 当前用户uid


import WechatJSSDK from "wechat-jssdk/dist/client.umd"; //WechatJSSDK(result)
import {
	getWechatConfig
} from '@/api/public';
import {
	VUE_APP_URL
} from '@/config.js';
import store from "@/store";
import {
	getUrlParams,
	setClipboardData
} from '@/utils/common.js'
const config1 = {
	//前4个是微信验证签名必须的参数，第2-4个参数为类似上面 '/get-signature' 从node端获取的结果
	'appId': 'xxx',
	'nonceStr': 'xxx',
	'signature': 'xxx',
	'timestamp': 'xxx',
	//下面为可选参数
	'debug': true, //开启 debug 模式
	'jsApiList': [], //设置所有想要使用的微信jsapi列表, 默认值为 ['onMenuShareTimeline', 'onMenuShareAppMessage']，分享到朋友圈及聊天记录
	'customUrl': '', //自定义微信jssdk链接
}
export function openShareAll(config = {}, copy = false) {
	let userInfo = store.state.userInfo || {};
	if (config.link) {
		const paths = config.link.split('?')
		const aliasPath = ROUTES.ROUTES_ARRAY.filter((item) => {
			return item.path === paths[0]
		})
		let link = `${VUE_APP_URL}${aliasPath[0].aliasPath}?spid=${userInfo.uid || -1}`;
		if (paths[1]) {
			link = `${link}&${paths[1]}`
		}
		config.link = link;
        console.log('config',config)
		if (copy) {
			setClipboardData(link, '地址已复制');
			setTimeout(() => {
				uni.showToast({
					title: '可点击右上角分享',
					icon: 'icon'
				}, 1500)
			})

			return
		}
	}

	config = Object.assign({
		type: 'link',
		title: '着调儿',
		link: VUE_APP_URL,
		imgUrl: VUE_APP_URL + '/logo.png',
		desc: 'description',
		success: function() {},
		cancel: function() {}
	}, config)

	console.log(config)
	return new Promise((resolve, reject) => {
		getWechatConfig().then(res => {
			res.data.customUrl = '//res.wx.qq.com/open/js/jweixin-1.6.0.js';
			let wechatObj = WechatJSSDK(res.data);
			console.log(wechatObj)
			wechatObj.initialize()
				.then(w => {
					let instance = w.wx;
					console.log(instance)
					instance.ready(() => {
						// wechatObj.shareOnChat(config);
						// wechatObj.shareOnMoment(config); //使用的onMenuShareAppMessage onMenuShareTimeline 接口已被废弃
						instance.onMenuShareAppMessage(config);
						instance.onMenuShareTimeline(config);
						instance.updateAppMessageShareData(config);
						instance.updateTimelineShareData(config);
					});
				})
				.catch(err => {
					console.error(err);
				});

		})
	})
}
