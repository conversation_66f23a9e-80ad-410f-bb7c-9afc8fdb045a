import WechatJSSDK from "wechat-jssdk/dist/client.umd"; //WechatJSSDK(result)
import {
	getWechatConfig
} from '@/api/public';

export function openAddress() {
	return new Promise((resolve, reject) => {		
		// uni.request({
		// 	url:'https://www.xyzgy.xyz/webconfig/jssdk/share.php',
		// 	data:{
		// 		url:document.location.href
		// 	},
		// 	success(res) {
		// 		console.log(res)
		// 		let wechatObj = WechatJSSDK(res.data);
		// 		wechatObj.initialize()
		// 			.then(w => {
		// 				let instance = w.wx;
		// 				instance.openAddress({
		// 					success(res) {
		// 						resolve(res)
		// 					},
		// 					fail(err) {
		// 						reject(err);
		// 					}
		// 				});
		// 			})
		// 			.catch(err => {
		// 				console.error(err);
		// 			});
		// 	}
		// })
		getWechatConfig().then(res => {
			console.log(res)
			res.data.customUrl = '//res.wx.qq.com/open/js/jweixin-1.6.0.js';
			let wechatObj = WechatJSSDK(res.data);
			wechatObj.initialize()
				.then(w => {
					let instance = w.wx;
					instance.ready(() => {
						instance.openAddress({
							success(res) {
								resolve(res);
							},
							fail(err) {
								reject(err);
							},
							complete(err) {
								reject(err);
							},
							cancel(err) {
								reject(err);
							}
						})
					});				
				})
				.catch(err => {
					console.error(err);
				});

		})

	});
}
