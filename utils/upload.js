import store from "../store";
import {
	VUE_APP_API_URL
} from '../config.js';
import {
	autoAuth
} from '@/utils/common.js'
export const uploadFile = async function(path, name = 'img', abort = false) {
	console.log(path)
	return new Promise((resolve, reject) => {
		let uploadTask = uni.uploadFile({
			url: `${VUE_APP_API_URL}/upload/image`, //接口地址
			filePath: path,
			name: name,
			formData: {
				'filename': name
			},
			header: {
				// #ifdef MP
				"Content-Type": "multipart/form-data",
				// #endif
				["Authori-zation"]: "Bearer " + store.state.token
			},
			success: (res) => {
				if (res.statusCode === 200) {
					let data = JSON.parse(res.data);
					if (data.status == 200) {
						return resolve(data.data);
					} else if ([410000, 410001, 410002].indexOf(data.status) !== -1) {
						autoAuth();
					} else {
						return reject(data.msg || '系统错误');
					}
				} else {
					return reject(res.data);
				}

			},
			fail(err) {
				if (abort) {
					// 异常中断上传
					uploadTask.abort()
				}
				console.log(err)
				reject(err)
			}
		});
	})

}
export const uploadImg = async function(path = null, loading = true) {
	loading && uni.showLoading({
		title: '图片上传中'
	})
    
	if (typeof path === 'string') {
		let res = await uploadFile(path);
		loading && uni.hideLoading()
		return [res.url];
	} else if (typeof path === 'object') {
		if (path.length) {
			let arr = [];
			for (let i = 0; i < path.length; i++) {
				let res = await uploadFile(path[i]).catch(err => {
					loading &&  uni.hideLoading()
				});
				arr.push(res.url)
				if (i === path.length - 1) {
					loading && uni.hideLoading()
				}
			}
			return arr;
		} else {
			loading && uni.hideLoading()
			return [];
		}
	} else {
		loading && uni.hideLoading();
		return [];
	}
}
