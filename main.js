import Vue from 'vue'
import App from './App'
    
// #ifdef H5
import {
    router,
    RouterMount
} from './router'
Vue.use(router)
// #endif

import uView from "uview-ui";
Vue.use(uView);

import {
    uniShowToast,
    successToast,
    loadingToast,
    uniHideToast,
    uniHideLoading,
    uniShowModal,
    uniNavigator,
    uniStartPullDownRefresh,
    uniStopPullDownRefresh,
    uniSetNavigationBarColor
} from './utils/uni_api'
import storage from './utils/storage.js'
import store from './store'
import {
    systemMessageStatus
} from '@/api/yuanshi/user.js'

import Authorize from '@/components/x-authorize/x-authorize.vue'
import Home from '@/components/x-home/x-home.vue'
// #ifdef MP-WEIXIN
import Agreement from '@/components/privacy-agreement/privacy-agreement.vue'
// #endif

import {
    updateTitle,
    authNavigator,
    getUrlParams
} from './utils/common.js'

import "@/static/iconfont/iconfont.css";

Vue.config.productionTip = false


Vue.prototype.$showToast = uniShowToast;
Vue.prototype.$successToast = successToast;
Vue.prototype.$loadingToast = loadingToast;
Vue.prototype.$hideToast = uniHideToast;
Vue.prototype.$hideLoading = uniHideLoading;
Vue.prototype.$showModal = uniShowModal;
Vue.prototype.$navigator = uniNavigator;
Vue.prototype.$authNavigator = authNavigator;
Vue.prototype.$startPullRefresh = uniStartPullDownRefresh;
Vue.prototype.$stopPullRefresh = uniStopPullDownRefresh;
Vue.prototype.$store = store;
Vue.prototype.$storage = storage;
Vue.prototype.$updateTitle = updateTitle;
Vue.prototype.$setNavigationBarColor = uniSetNavigationBarColor;
// console.log(ROUTES)
Vue.prototype.$linkpress = (obj) => {
    // console.log('obj---',obj)
    let {
        ignore,
        href
    } = obj;
    if (typeof obj === 'string') {
        href = obj;
    }
    let pathType = 'navigateTo',
        link = href.split('?')[0].split('h5')[1],
        path = ROUTES.ROUTES_OBJECT[link]?.path;
    if (path) {
        const params = href.split('?')[1];
        if (params) {
            const {
                openType = 'navigateTo'
            } = getUrlParams(params);
            pathType = openType;
            path = path + '?' + params;
        }
        console.log(path, pathType)
        uniNavigator(path, pathType)
    } else {
        uniNavigator('/pages/webview/webview?url=' + href)
    }
};


Vue.component('x-authorize', Authorize)
Vue.component('x-home', Home)
// #ifdef MP-WEIXIN
Vue.component('x-agreement', Agreement)
// #endif

Vue.mixin({
    data() {
        return {
            parseStyle: {
                div: 'max-width:100% !important;white-space: normal !important;word-wrap: break-word;word-break: break-all;',
                p: 'max-width:100% !important;white-space: normal !important;word-wrap: break-word;word-break: break-all;',
                span: 'white-space: normal !important;',
                img: 'max-width:100% !important;',
                video: 'max-width:100% !important;',
            },
            systemMessageStatus: true,
            commentMessageNumber:0, // 我参与的评论未读数量
            systemMessageNumber:0, // 我的消息未读数量
        }
    },
    methods: {
        getSystemMessageStatus() {
            return new Promise((resolve, reject) => {
                systemMessageStatus().then(res => {
                    resolve(res.data)
                }).catch(err => {
                    reject(err)
				})
            }).catch((err) => {});
        },
        async setTabBarIndex(index) {
            // console.log('setTabBarIndex---', index, store)
            if (index === 0 || index === 2) {
                store.commit('UPDATE_SOURCE_SEARCH_ID', 0)
                store.commit('UPDATE_WX_ANIMATE1')
            }
            if (index === 1) {
                store.commit('UPDATE_WX_ANIMATE')
            }
            // let is_reading_comment = false;  // 我参与的评论红点待使用
            let data = await this.getSystemMessageStatus();
            if(typeof(data) != "undefined"){
                this.systemMessageStatus = data.is_reading;
                this.commentMessageNumber = data.comment_message_number;
                this.systemMessageNumber = data.system_message_number + data.user_message_number;
            }
            if(this.commentMessageNumber){
                this.systemMessageStatus = false;
            }
            // #ifdef H5
            // 小圆点的实现  小程序 更改 showDot
            if (index === 1) {
                document.querySelectorAll(".uni-tabbar .uni-tabbar__item")[1].classList.remove('animate')
            }
            console.log('H5-is_reading', data)
            if (this.systemMessageStatus) {
                document.querySelectorAll(".uni-tabbar .uni-tabbar__item")[2].classList.remove('active')
            } else {
                document.querySelectorAll(".uni-tabbar .uni-tabbar__item")[2].classList.add('active')
            }
            // #endif
            // #ifdef MP-WEIXIN
            // console.log('store', store)
            // console.log('Mp-is_reading', is_reading)
            // console.log('---store.state.isWxTabarAnimate', store.state.isWxTabarAnimate)
            if (typeof this.$mp.page.getTabBar === 'function' &&
                this.$mp.page.getTabBar()) {
                console.log(this.$mp.page)
                this.$mp.page.getTabBar().setData({
                    selected: index,
                    showDot: !this.systemMessageStatus, // is_reading 是否满足
                    showAnimate: store.state.isWxTabarAnimate
                })
            }
            // #endif
        }
    }
})

App.mpType = 'app'

const app = new Vue({
    ...App
})
// #ifdef H5
import {
    openShareAll
} from '@/utils/wechat/share.js';
import {
    isWeixin
} from "@/utils/validate.js";
if (isWeixin()) {
    openShareAll()
}
RouterMount(app, router, '#app')
// #endif
// #ifndef H5
app.$mount();
// #endif
