自定义tarbar组件
H5 直接修改样式
.uni-tabbar{
	height: 128rpx;
	.uni-tabbar__item{
		&:nth-child(3){
			.uni-tabbar__bd{
				width: 160rpx;
				height: 120rpx;
				background: #FFFFFF;
				box-shadow: 0px 0px 20rpx rgba(0, 0, 0, 0.1);
				opacity: 1;
				border-radius: 4rpx 4rpx 24rpx 24rpx;
				margin-top: -10rpx;
				margin-bottom: 12rpx;
				background: #fff;
				.uni-tabbar__icon{
					width: 124rpx;
					height: 60rpx;
				}
			}
		}
	}
}

微信小程序  custom-tab-bar必须在根目录，不能存放wxcomponents 样式与h5同步即可
官方示例去掉cover,正常使用view image就可以，否则造成样式闪烁 
注释 this.setData({ selected: data.index }) 
main.js 注册全局方法
Vue.mixin({
  methods:{
    setTabBarIndex(index){
      if (typeof this.$mp.page.getTabBar === 'function' &&
        this.$mp.page.getTabBar()) {
        this.$mp.page.getTabBar().setData({
          selected:index
        })
      }
    }
  }
})

onShow(){
	this.setTabBarIndex(0); //index为当前tab的索引
}