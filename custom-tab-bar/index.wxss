.tab-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: calc(constant(safe-area-inset-bottom) + 128rpx);
	height: calc(env(safe-area-inset-bottom) + 128rpx);
	background: white;
	display: flex;
	border-radius: 8rpx 8rpx 0px 0px;
	box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
}

.tab-bar-border {
	/* 	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 1px;
	transform: scaleY(0.5); */
}

.tab-bar-item {
	flex: 1;
	text-align: center;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
}

.tab-bar-item image {
	width: 52rpx;
	height: 52rpx;
}

.tab-bar-item view {
	font-size: 10px;

}

.tab-bar-item:nth-child(3) {
	margin-bottom: 12rpx;
	margin-top: -8rpx;
}

.tab-bar-item:nth-child(3) .wrap {
	width: 156rpx !important;
	height: 156rpx !important;
	border-radius: 50%;
	background: #fff;
	opacity: 1;
	/* border-radius: 4rpx 4rpx 24rpx 24rpx; */
	display: flex;
	align-items: center;
	justify-content: center;
}

.tab-bar-item:nth-child(3) .wrap image {
	/* width: 124rpx;
	height: 52rpx; */
	width: 156rpx !important;
	height: 156rpx !important;
}

.tab-bar-item:nth-child(4) .wrap {
	position: relative;
}

.tab-bar-item:nth-child(4) .wrap .dot {
	position: absolute;
	width: 14rpx;
	height: 14rpx;
	top: -2rpx;
	right: 2rpx;
	border-radius: 50%;
	background-color: red;
}


.tab-bar-item .animate {
	animation: huXi 5s linear infinite;
}
@keyframes rotate{
		from {
			transform: rotate(0);
		}
		30%{
			transform: rotate(90deg);
		}
		50%{
			transform: rotate(180deg);
		}
		70%{
			transform: rotate(270deg);
		}
		to{
			transform: rotate(360deg);
		}
	}
    
@keyframes rotateY {
	from {
		transform: rotateY(0);
	}

	30% {
		transform: rotateY(0);
	}

	50% {
		transform: rotateY(360deg);
	}

	70% {
		transform: rotateY(0);
	}

	to {
		transform: rotateY(0);
	}
}

@keyframes huXi {
	from {
		transform: scale(1);
	}

	30% {
		transform: scale(1.1);
	}

	40% {
		transform: scale(0.8);
	}

	50% {
		transform: scale(0.6);
	}

	60% {
		transform: scale(0.8);
	}

	70% {
		transform: scale(1.1);
	}

	to {
		transform: scale(1);
	}
}
