<!--miniprogram/custom-tab-bar/index.wxml-->
<view class="tab-bar">
	<view class="tab-bar-border"></view>
	<view wx:for="{{list}}" wx:key="index" class="tab-bar-item" data-path="{{item.pagePath}}" data-index="{{index}}"
	 bindtap="switchTab">
		<view class="wrap" >
			<view class=" {{showAnimate&&index===1?'animate':''}}">
				<image src="{{selected === index ? item.selectedIconPath : item.iconPath}}"></image>
				<view style="color: {{selected === index ? selectedColor : color}}">{{item.text}}</view>
				<view class="dot" wx:if="{{showDot}}"></view>
			</view>
		</view>
	</view>
</view>
