<script>
    import {
        checkLogin,
        updateToken
    } from '@/utils/common.js';
    import {
        reportEnterOptions
    } from '@/utils/ReportAnalytics.js';
    import {
        SUBSCRIBE_MESSAGE
    } from '@/config.js';
    import {
        getTemlIds
    } from '@/api/user';
    export default {
        onLaunch: function(options) {
            // // #ifdef MP-WEIXIN
            // miniShopPlugin.initApp(this.$mp.app, wx); 
            // miniShopPlugin.initHomePath('/pages/tabBar/index/index');
            // // #endif
            // #ifdef MP-TOUTIAO
            uni.hideTabBar()
            // #endif
            console.log('App Launch', options);
            // #ifdef APP-PLUS
            this.checkVersion();
            // #endif
            this.initData();
            this.$store.commit('UPDATE_SPID', options.query.spid || 0);
            // #ifdef H5
            document.querySelectorAll(".uni-tabbar .uni-tabbar__item")[1].classList.add('animate')
            this.$store.commit('SET_SELF', this);
            let stytemInfo = this.$store.state.stytemInfo;
            // if(stytemInfo.windowWidth>420 && !window.top.isPC && !/iOS|Android/i.test(stytemInfo.system)){
            //             window.location.pathname = '/h5/static/pc.html';
            //         }
            // #endif

            // #ifdef MP
            let messageTmplIds = this.$storage.get(SUBSCRIBE_MESSAGE);
            if (!messageTmplIds) {
                getTemlIds().then(res => {
                    // console.log('获取消息模板id-',res);
                    if (res.data) this.$storage.set(SUBSCRIBE_MESSAGE, JSON.stringify(res.data));
                });
            }
            // let obj = options.query,xurl =options.path ;
            // if( Object.keys(obj).length){
            // 	xurl = xurl + Object.keys(obj).map(key => key + '=' + obj[key]).join('&')
            // }

            // console.log(xurl)
            if ([1154].indexOf(options.scene) > -1) {
                console.log('已配置场景值')
                if (options.scene === 1154) {
                    this.$store.commit('UPDATE_FROM_TIMELINE', true);
                }
                let obj = options.query,
                    xurl = options.path;
                if (Object.keys(obj).length) {
                    xurl = xurl + Object.keys(obj).map(key => key + '=' + obj[key]).join('&')
                }
                this.$navigator(xurl)
            } else {
                const updateManager = uni.getUpdateManager();
                updateManager.onCheckForUpdate(function(res) {
                    // 请求完新版本信息的回调
                });
                updateManager.onUpdateReady(function() {
                    uni.showModal({
                        title: '更新提示',
                        content: '新版本已经准备好，是否重启应用？',
                        success: function(res) {
                            if (res.confirm) {
                                // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                                updateManager.applyUpdate();
                            }
                        }
                    });
                });
                updateManager.onUpdateFailed(function() {
                    return that.Tips({
                        title: '新版本下载失败'
                    });
                });
            }
            // #endif
            reportEnterOptions(options)
        },
        onShow: function(options) {
            console.log('AppShow---', options);
        },
        onHide: function() {
            // console.log('App Hide');
        },
        methods: {
            initData() {
                let storage = this.$storage;
                let token = storage.get('token') || null,
                    expires_time = storage.get('expires_time') || null,
                    // expires_time = null,
                    userInfo = storage.get('userInfo') || {};
                this.$store.commit('UPDATE_USERINFO', userInfo);
                if (checkLogin()) {
                    // this.$store.commit('LOGIN', { token, expires_time });
                    updateToken(token, expires_time)
                }
            },
            // #ifdef APP-PLUS
            // app更新检测
            checkVersion() {
                // 获取应用版本号
                let version = plus.runtime.version;
                //检测当前平台，如果是安卓则启动安卓更新
                uni.getSystemInfo({
                    success: res => {
                        this.updateHandler(res.platform, version);
                    }
                });
            },
            // 更新操作
            updateHandler(platform, version) {
                let data = {
                    platform: platform,
                    version: version
                };
                let _this = this;
                // plus.runtime.openURL('最新版本更新地址')
            }
            // #endif
        }
    };
</script>

<style lang="scss">
    @import './static/iconfont1/iconfont.css';
    @import './static/css/app.css';
    @import './static/css/base.scss';
    @import "uview-ui/index.scss";
    // 表情包
    @import url('./static/emoji/emojione.css');

    .uni-popup {
        z-index: 999 !important;
    }

    /* 解决头条小程序组件内引入字体不生效的问题 */
    /* #ifdef MP-TOUTIAO */
    @font-face {
        font-family: uniicons;
        src: url('/static/uni.ttf');
    }


    /* #endif */
    /* #ifdef H5 */
    uni-page-head {
        display: none;
    }

    body::-webkit-scrollbar,
    html::-webkit-scrollbar {
        display: none;
    }

    @media (min-width: 750px) {
        uni-app {
            width: 750px;
            margin: 0 auto;
        }
    }

    // 修改tarbar样式 
    .uni-tabbar {
        height: 128rpx;
        box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
        border-radius: 8rpx 8rpx 0px 0px;

        .uni-tabbar__item {
            .uni-tabbar__icon {
                width: 52rpx;
                height: 52rpx;

            }

            &.animate {
                .uni-tabbar__icon {
                    animation: huXi 5s linear infinite;
                }
            }

            &:nth-child(3) {
                margin-bottom: 12rpx;
                margin-top: -8rpx;

                .uni-tabbar__bd {
                    width: 156rpx !important;
                    height: 156rpx !important;
                    border-radius: 50%;
                    background: #ffffff;
                    // box-shadow: 0px 0px 20rpx rgba(0, 0, 0, 0.1);
                    opacity: 1;

                    // border-radius: 4rpx 4rpx 24rpx 24rpx;
                    .uni-tabbar__icon {
                        width: 156rpx !important;
                        height: 156rpx !important;

                    }
                }
            }

            &.active {
                .uni-tabbar__bd {
                    .uni-tabbar__icon {
                        &::after {
                            position: absolute;
                            content: '';
                            top: -2rpx;
                            right: 2rpx;
                            width: 14rpx;
                            height: 14rpx;
                            border-radius: 50%;
                            background-color: red;
                        }
                    }

                }
            }
        }
    }

    @keyframes rotateY {
        from {
            transform: rotateY(0);
        }

        30% {
            transform: rotateY(0);
        }

        50% {
            transform: rotateY(360deg);
        }

        70% {
            transform: rotateY(0);
        }

        to {
            transform: rotateY(0);
        }
    }

    @keyframes rotate {
        from {
            transform: rotate(0);
        }

        30% {
            transform: rotate(90deg);
        }

        50% {
            transform: rotate(180deg);
        }

        70% {
            transform: rotate(270deg);
        }

        to {
            transform: rotate(360deg);
        }
    }

    @keyframes huXi {

        // from {
        // 	transform: scale(1);
        // }
        // 10%{
        // 	transform: scale(1.2);
        // }
        // 20%{
        // 	transform: scale(1);
        // }
        // 30%{
        // 	transform: scale(0.95);
        // }
        // to{
        // 	transform: scale(1);
        // }
        from {
            transform: scale(1);
        }

        30% {
            transform: scale(1.1);
        }

        40% {
            transform: scale(0.8);
        }

        50% {
            transform: scale(0.6);
        }

        60% {
            transform: scale(0.8);
        }

        70% {
            transform: scale(1.1);
        }

        to {
            transform: scale(1);
        }
    }

    /* 	body {
		    max-width:375px;
		    margin:0 auto !important;
		} */
    /* #endif */
    page {
        background: #f7f8fa;
        padding-bottom: 150rpx;
        // min-height: 100vh;
    }
</style>
