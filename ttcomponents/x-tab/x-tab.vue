<template>
	<view class="tab">
		<scroll-view scroll-x style="white-space:nowrap;" :scroll-with-animation="true" :scroll-left="scrollLeft">
			<view class="wrap" :style="{ fontSize: fontSize }">
				<view
					class="item"
					v-for="(item, index) in arr"
					:key="index"
					@click="swichTab(index, item)"
					:class="index == current ? activeClass : ''"
					:style="{ padding: padding }"
					:id="`t${index}`"
				>
					<view class="title" :style="{ height: height, lineHeight: height, color: index == current ? activeColor : color, fontWeight: index == current ? bold : 'normal' }">
						<!-- 头条小程序不支持slot-scope 解构 -->
						<block v-if="name === 'scroll-product'">
							<view class="swiper-tab scroll-product" @click="goPages(`/pages/shop/GoodsList?id=${item.id}&title=${item.cate_name}`)">
								<view class="img-box"><image :src="item.pic" mode="" alt="img"></image></view>
								<view class="pro-info line1">{{ item.cate_name }}</view>
							</view>
						</block>
						<block v-if="name === 'newProducts'">
							<view @click="goDetail(item)" class="swiper-tab newProducts" style="line-height:normal">
								<view class="img-box">
									<image :src="item.image" alt="img"></image>
									<span class="pictrue_log_medium pictrue_log_class" v-if="item.activity && item.activity.type === '1'">秒杀</span>
									<span class="pictrue_log_medium pictrue_log_class" v-if="item.activity && item.activity.type === '2'">砍价</span>
									<span class="pictrue_log_medium pictrue_log_class" v-if="item.activity && item.activity.type === '3'">拼团</span>
								</view>
								<view class="pro-info line1">{{ item.store_name }}</view>
								<view class="money font-color-red">￥{{ item.price }}</view>
							</view>
						</block>
					</view>
					<view :class="lineClass" :style="{ width: lineWidth, backgroundColor: index == current ? lineColor : '' }"></view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>
<script>
export default {
	props: {
		arr: {
			type: Array,
			default() {
				return [];
			}
		},
		active: {
			type: Number,
			default: 0
		},
		title: {
			//显示title的字段
			type: String,
			default: 'title'
		},
		color: {
			type: String,
			default: '#000'
		},
		activeColor: {
			type: String,
			default: 'red'
		},
		fontSize: {
			type: String,
			default: '30rpx'
		},
		activeClass: {
			type: String,
			default: 'on'
		},
		lineClass: {
			type: String,
			default: 'line'
		},
		lineWidth: {
			type: String,
			default: '30rpx'
		},
		lineColor: {
			type: String,
			default: 'red'
		},
		padding: {
			type: String,
			default: '0 20rpx'
		},
		bold: {
			type: String,
			default: 'bold'
		},
		height: {
			type: String,
			default: '70rpx'
		},
		name: {
			type: String,
			default: 'scroll-product'
		}
	},
	data() {
		return {
			current: 0,
			scrollLeft: 0,
			leftArr: [],
			init: false
		};
	},
	methods: {
		goPages(path, type) {
			this.$navigator(path, type);
		},
		uniSelectorQueryInfo(selector, _this) {
			return new Promise((resolve, reject) => {
				const query = uni.createSelectorQuery().in(_this);
				query
					.select(selector)
					.boundingClientRect(res => {
						// 获取节点坐标
						resolve(res);
					})
					.exec();
			});
		},
		swichTab(idx, item) {
			if (this.current === idx) {
				return;
			}
			this.current = idx;
			this.scrollLeft = this.leftArr[idx] - this.leftArr[0];
			this.$emit('change', idx, item);
		},
		async initScrollInfo() {
			let leftArr = [];
			for (let i = 0; i < this.arr.length; i++) {
				const { left } = await this.uniSelectorQueryInfo(`#t${i}`, this);
				leftArr.push(left);
			}
			this.leftArr = leftArr;
			if (this.current > 0) {
				this.scrollLeft = this.leftArr[this.current] - this.leftArr[0];
			}
		}
	},
	beforeUpdate() {
		const _this = this;
		if (this.init) {
			return;
		}
		this.$nextTick(function() {
			_this.current = _this.active;
			_this.initScrollInfo();
			_this.init = true;
		});
	}
};
</script>
<style lang="scss" scoped>
.flex_around {
	justify-content: space-around;
}
.tab {
	.wrap {
		display: flex;
		.item {
			.title {
			}
			.line {
				transition: all 0.15s linear;
				height: 4rpx;
				margin: 0 auto;
			}
			&.on {
				.line {
					background-color: red;
				}
			}
		}
	}
}
.swiper-tab {
	display: inline-block;
	margin-right: 20rpx;
	box-shadow: 0 15rpx 15rpx -10rpx #eee;
	-webkit-box-shadow: 0 15rpx 15rpx -10rpx #eee;
	-moz-box-shadow: 0 15rpx 15rpx -10rpx #eee;
	-o-box-shadow: 0 15rpx 15rpx -10rpx #eee;

	&:nth-last-child(1) {
		margin-right: 0;
	}

	.img-box image {
		width: 100%;
		height: 100%;
	}

	&.scroll-product {
		width: 180rpx;
		.img-box {
			width: 100%;
			height: 180rpx;
			image {
				border-radius: 6rpx 6rpx 0 0;
			}
		}
		.pro-info {
			font-size: 24rpx;
			color: #282828;
			text-align: center;
			height: 60rpx;
			line-height: 60rpx;
			border: 1px solid #f5f5f5;
			border-bottom: 0;
			border-top: 0;
			padding: 0 10rpx;
		}
	}
	&.newProducts {
		width: 243rpx;
		border: 1px solid #eee;
		border-radius: 12rpx;
		margin-right: 10rpx;
		.img-box {
			width: 100%;
			height: 240rpx;
			image {
				border-radius: 12rpx 12rpx 0 0;
			}
		}
		.pro-info {
			font-size: 28rpx;
			color: #333;
			text-align: center;
			padding: 15rpx 10rpx 0 10rpx;
		}
		.money {
			padding: 0 10rpx 18rpx 10rpx;
			text-align: center;
			font-size: 26rpx;
			font-weight: bold;
		}
	}
}
</style>
