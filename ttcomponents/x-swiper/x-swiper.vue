<template>
	<view class="swiper relative">
		<swiper
			:indicator-dots="indicatorDots"
			:indicator-color="indicatorColor"
			:indicator-active-color="indicatorActiveColor"
			:display-multiple-items="displayMultipleItems > arr.length ? arr.length : displayMultipleItems"
			:previous-margin="previousMargin"
			:next-margin="nextMargin"
			:autoplay="autoplay"
			:vertical="vertical"
			:interval="interval"
			:duration="duration"
			:circular="circular"
			@change="dotsChange"
			:style="{ width: '100%', height: height }"
		>
			<swiper-item v-for="(item, index) in arr" :key="index" @click="swiperClick(item)" :class="xClass" :style="{ padding: padding }">
				<!-- 头条小程序不支持slot-scope 解构 -->
				<block v-if="name === 'boutique'">
					<view class="ttswiper boutique" @click="goPages(item.wap_link ? item.wap_link : '')"><image :src="item.img" mode="" alt="img"></image></view>
				</block>
				<block v-if="name === 'news'">
					<view @click="goPages(item.wap_url ? item.wap_url : '')" class="news acea-row row-between-wrapper">
						<view class="text acea-row row-between-wrapper">
							<view class="label" v-if="item.show === '是'">最新</view>
							<view class="newsTitle line1">{{ item.info }}</view>
						</view>
						<view class="iconfont icon-xiangyou"></view>
					</view>
				</block>
				<block v-if="name === 'swiperNews'">
					<view class="ttswiper swiperNews" @click="goPages(`/pages/shop/news/NewsDetail?id=${item.id}`)">
						<image :src="item.image_input[0]" mode="" alt="img" class="slide-image"></image>
					</view>
				</block>
				<block v-if="name === 'product'">
					<view
						class="ttswiper product"
						:style="{
							background: 'url(' + item + ') no-repeat center center',
							'background-size': '100% 100%'
						}"
					>
						<image src="@/static/images/videos.png" class="video_play" @click="videoPlay(index)" v-if="videolines && index === 0" />
					</view>
				</block>
				<block v-if="name === 'superior'">
					<view class="ttswiper superior acea-row row-middle">
						<view class="item" v-for="val in item.list" :key="val.image" @click="goGoods(val)">
							<view class="pictrue">
								<image :src="val.image" />
								<span class="pictrue_log pictrue_log_class" v-if="val.activity && val.activity.type === '1'">秒杀</span>
								<span class="pictrue_log pictrue_log_class" v-if="val.activity && val.activity.type === '2'">砍价</span>
								<span class="pictrue_log pictrue_log_class" v-if="val.activity && val.activity.type === '3'">拼团</span>
							</view>
							<view class="name line1">{{ val.store_name }}</view>
							<view class="money font-color-red">¥{{ val.price }}</view>
						</view>
					</view>
				</block>
			</swiper-item>
		</swiper>

		<view class="absolute dots" v-if="dots">
			<view class="acea-row row-around" v-if="dotsType == 'line'">
				<view class="acea-row dots_line">
					<view v-for="(item, index) in arr" :key="index" :style="{ background: current == index ? indicatorActiveColor : indicatorColor }"></view>
				</view>
			</view>
			<view class="acea-row row-right" v-if="dotsType == 'block'">
				<view class="dots_block">{{ current + 1 }} / {{ arr.length }}</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		indicatorDots: {
			type: Boolean,
			default: false
		},
		indicatorColor: {
			type: String,
			default: '#C7C7C7'
		},
		indicatorActiveColor: {
			type: String,
			default: '#ffffff'
		},
		displayMultipleItems: {
			type: Number,
			default: 1
		},
		previousMargin: {
			type: String,
			default: '0rpx'
		},
		nextMargin: {
			type: String,
			default: '0rpx'
		},
		autoplay: {
			type: Boolean,
			default: true
		},
		vertical: {
			type: Boolean,
			default: false
		},
		interval: {
			type: Number,
			default: 4000
		},
		duration: {
			type: Number,
			default: 500
		},
		circular: {
			type: Boolean,
			default: true
		},
		scale: {
			type: Boolean,
			default: false
		},
		padding: {
			type: String,
			default: '0rpx'
		},
		arr: {
			type: Array,
			default() {
				return [];
			}
		},
		height: {
			type: String,
			default: '100%'
		},
		dots: {
			type: Boolean,
			default: true
		},
		dotsType: {
			type: String,
			default: 'line'
		},
		xClass: {
			type: String,
			default: 'swiper_item'
		},
		slotProp: {
			type: String,
			default: 'item'
		},
		srcName: {
			type: String,
			default: 'pic'
		},
		name: {
			type: String,
			default: 'boutique'
		},

		videoline: {
			type: String,
			default: () => ''
		}
	},
	data() {
		return {
			current: 0,
			videolines: this.videoline
		};
	},
	watch: {
		videoline: {
			handler(newName) {
				this.videolines = newName;
			},
			deep: true
		}
	},
	methods: {
		goPages(path, type) {
			path = path.replace('/1', '?type=1');
			this.$navigator(path, type === '1' ? 'switchTab' : '');
		},
		dotsChange(e) {
			this.current = e.detail.current;
			this.$emit('change', this.current);
		},
		swiperClick(item) {
			this.$emit('swiperClick', item);
		},
		videoPlay(idx) {
			this.$emit('play', idx);
		}
	}
};
</script>

<style scoped="" lang="less">
.swiper {
	height: 100%;
	image {
		width: 100%;
		height: 100%;
	}
	.scale {
		transform: scaleY(0.9);
		transform-origin: center center;
	}
	swiper-item {
		box-sizing: border-box;
	}
}
.dots {
	width: 100%;
	bottom: 10rpx;
	.dots_line {
		> view {
			width: 16rpx;
			height: 4rpx;
			// background: rgba(49, 48, 59, 1);
			&.dot_active {
				// background:#fff;
			}
		}
	}
	.dots_block {
		background: #fff;
		padding: 5rpx 10rpx;
		margin-right: 10rpx;
		font-size: 24rpx;
	}
}

.ttswiper {
	width: 100%;
	height: 100%;
	image {
		width: 100%;
		height: 100%;
	}
}

.boutique {
	width: 690rpx;
	height: 290rpx;
	margin: 28rpx auto 0 auto;
	image {
		height: 260rpx;
	}
}

.news {
	width: 523rpx;
	overflow: hidden;
	height: 77rpx;
	border-top: 1px solid #f4f4f4;
	padding: 0 30rpx;
	box-shadow: 0 10rpx 30rpx #f5f5f5;
	.pictrue {
		width: 124rpx;
		height: 28rpx;
		border-right: 1px solid #ddd;
		padding-right: 23rpx;
		box-sizing: content-box;
		image {
			display: block;
		}
	}
	.text {
		width: 420rpx;
		height: 77rpx;
		.label {
			font-size: 20rpx;
			color: #ff4c48;
			width: 68rpx;
			height: 34rpx;
			border-radius: 20rpx;
			text-align: center;
			line-height: 34rpx;
			border: 2rpx solid #ff4947;
		}
	}
	.newsTitle {
		width: 397rpx;
		font-size: 24rpx;
		color: #666;
	}
	.iconfont {
		font-size: 28rpx;
		color: #888;
	}
}

.swiperNews {
	width: 690rpx;
	height: 367rpx;
	margin: 0 auto;
	image {
		border-radius: 6rpx;
	}
}

.product {
	.video_play {
		position: absolute;
		width: 100rpx;
		height: 100rpx;
		left: 45%;
		top: 45%;
		z-index: 11;
	}
}
.superior {
	width: 100%;
	.item {
		width: 215rpx;
		margin: 0 22rpx 30rpx 0;
		font-size: 26rpx;
		&:nth-of-type(3n) {
			margin-right: 0;
			
		}
		.pictrue {
			width: 100%;
			height: 215rpx;
			position: relative;
			image {
				width: 100%;
				height: 100%;
				border-radius: 6rpx;
			}
		}
	}
	.name {
		color: #282828;
		margin-top: 12rpx;
	}
}
</style>
