view {
	box-sizing: border-box;
}
image {
	width: 100%;
	height: 100%;
	will-change: transform;
}


// 公共样式
.common_wrap{
	padding:0 40rpx;
}
/* #ifdef MP */
::-webkit-scrollbar { //隐藏scroll-view滚动条 会造成pc端滚动无效
	display: none;
	width: 0;
	height: 0;
	color: transparent;
}
/* #endif */
.flex {
	display: flex;
}
.flex_align_center {
	align-items: center;
}
.flex_align_end {
	align-items: flex-end;
}
.flex_between {
	justify-content: space-between;
}
.flex_around {
	justify-content: space-around;
}
.flex_end {
	justify-content: flex-end;
}
.flex_row {
	flex-direction: row;
}
.flex_column {
	flex-direction: column;
}
.flex_column_reverse {
	flex-direction: column-reverse;
}
.flex_wrap {
	flex-wrap: wrap;
}
.flex_nowrap {
	flex-wrap: nowrap;
}
.float_left {
	float: left;
}
.flex_line_height {
	// box-sizing: border-box 模式下,line-height不垂直居中(减去border值即可)  content-box生效
	display: flex;
	align-items: center;
	justify-content: space-around;
}
.relative {
	position: relative;
}
.absolute {
	position: absolute !important;
	width: 100%;
}
.fixed {
	position: fixed;
	width: 100%;
}
.text_center {
	text-align: center;
}
.text_right {
	text-align: right;
}
.text_left {
	text-align: left;
}
.text_indent {
	text-indent: 2em;
}
.text_indent2 {
	text-indent: 4em;
}
.text_indent3 {
	text-indent: 6em;
}
.text_ellipsis {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.text_line_ellipsis {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	flex-direction: column;
}
.text_line_ellipsis_show {
	-webkit-line-clamp: 5; //超出多少行显示...
}
.text_line_ellipsis_hide {
	-webkit-line-clamp: 0; //全部显示
}
.font_size20 {
	display: inline-block;
	font-size: 24rpx;
	transform: scale(0.834); //只对block生效
	// width: 118%;  //会影响布局，视情况使用
	// transform-origin: left;
}
.over_hidden {
	overflow: hidden;
}
.over_scroll {
	overflow: scroll;
}
.inline_block {
	display: inline-block;
}
.pointer {
	cursor: pointer;
}
.no_wrap {
	width: 100%;
	word-wrap: break-word; //截断单词并换行
	word-break: break-all; //不截断单词，位置不够整体换行
}
.init_width {
	width: 100%;
}
.xyz_btn {
	width: 100%;
}
.none {
	display: none;
}
.font_bold {
	font-weight: bold;
}
.font_bolder {
	font-weight: bolder;
}

.rote90_shun {
	transform: rotate(90deg);
	display: inline-block;
}
.rote180 {
	transform: rotate(180deg);
	display: inline-block;
}
.rote90_ni {
	transform: rotate(-90deg);
	display: inline-block;
}

// uni.scss生效
// @mixin fixed_footer($height:110rpx) {
//     position: fixed;
//     bottom: 0;
//     left: 0;
//     right: 0;
//     height: calc(constant(safe-area-inset-bottom) + $height) !important;
//     height: calc(env(safe-area-inset-bottom) + $height) !important;
//     padding-bottom: constant(safe-area-inset-bottom) !important;
//     padding-bottom: constant(safe-area-inset-bottom) !important;
// }

// 感叹号
.say_feeling {
	position: relative;
	&::after {
		content: '!';
		display: inline-block;
		position: absolute;
		right: -10rpx;
		top: -10rpx;
		box-sizing: border-box;
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		border: 4rpx solid #999999;
		text-align: center;
		line-height: 72rpx;
		margin-left: 8rpx;
		color: #999;
		font-size: 48rpx;
		transform: scale(0.4);
		transform-origin: right top;
	}
}
