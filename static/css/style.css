/* #ifdef MP-WEIXIN */
.wx-checkbox-input,
.wx-radio-input {
	width: 40rpx;
	height: 40rpx;
	margin-right: 0 !important;
	border-radius: 50% !important;
	transform: scale(0.8);
	border-color: #d1d1d1 !important;
}

.wx-checkbox-input.wx-checkbox-input-checked,
.wx-radio-input.wx-radio-input-checked {
	background: #eb0909 !important;
	width: 44rpx !important;
	height: 44rpx !important;
	border-color: #eb0909 !important;
	color: #fff !important;
}

/*swtich整体大小及背景色*/
.wx-switch-input {
	transform: scale(0.7);
	margin-right: 0 !important;
	transform-origin: right;
	/* height: 54rpx !important; */
}

/*白色样式（false的样式）*/
.wx-switch-input::before {
	/* height: 50rpx !important; */
}

/*绿色样式（true的样式）*/
.wx-switch-input::after {
	/* width: 50rpx !important;
	height: 50rpx !important; */
}

/* #endif */
/* #ifndef MP-WEIXIN */
>>>.uni-checkbox-input,
>>>.uni-radio-input {
	width: 40rpx;
	height: 40rpx;
	margin-right: 0 !important;
	border-radius: 50% !important;
	transform: scale(0.8);
	border-color: #d1d1d1 !important;
}

>>>.uni-checkbox-input.uni-checkbox-input-checked,
>>>.uni-radio-input.uni-radio-input-checked {
	background: #eb0909 !important;
	width: 45rpx !important;
	height: 45rpx !important;
	border-color: #eb0909 !important;
	color: #fff !important;
}

>>>.uni-switch-input {
	transform: scale(0.7);
	margin-right: 0 !important;
	transform-origin: right;
	/* height: 54rpx !important; */
}

>>>.uni-switch-input::before {
	/* height: 50rpx !important; */
}

>>>.uni-switch-input::after {
	/* width: 50rpx !important;
	height: 50rpx !important; */
}

/* #endif */


/*所有产品的详情公用部分*/
.product-con .wrapper {
	background-color: #fff;
}

.product-con .wrapper .share {
	margin: 0 30rpx;
	padding-top: 25rpx;
}

.product-con .wrapper .share .money {
	font-size: 28rpx;
	font-weight: bold;
}

.product-con .wrapper .share .money .num {
	font-size: 48rpx;
}

.product-con .wrapper .share .money .vip-money {
	color: #282828;
	margin-left: 13rpx;
}

.product-con .wrapper .share .money .image {
	width: 46rpx;
	height: 21rpx;
	margin-left: 7rpx;
}

.product-con .wrapper .share .money .vip-money {
	color: #282828;
	margin-left: 13rpx;
}

.product-con .wrapper .share .iconfont {
	color: #515151;
	font-size: 40rpx;
	margin-bottom: 10rpx;
}

.product-con .wrapper .introduce {
	font-size: 32rpx;
	font-weight: bold;
	margin: 10rpx 30rpx 0 30rpx;
}

.product-con .wrapper .label {
	margin: 22rpx 30rpx 0 30rpx;
	font-size: 24rpx;
	color: #82848f;
	padding-bottom: 25rpx;
}

.product-con .wrapper .coupon {
	padding: 0 30rpx;
	border-top: 1px solid #f5f5f5;
	height: 80rpx;
	font-size: 26rpx;
	color: #82848f;
}

.product-con .wrapper .coupon .hide {
	width: 540rpx;
	height: 80rpx;
	line-height: 80rpx;
}

.product-con .wrapper .coupon .activity {
	height: 40rpx;
	padding: 0 20rpx;
	border: 1px solid #f2857b;
	color: #e93323;
	font-size: 24rpx;
	line-height: 40rpx;
	position: relative;
	margin: 19rpx 0 19rpx 15rpx;
}

.product-con .wrapper .coupon .activity:before {
	content: ' ';
	position: absolute;
	width: 7rpx;
	height: 10rpx;
	border-radius: 0 7rpx 7rpx 0;
	border: 1px solid #f2857b;
	background-color: #fff;
	bottom: 50%;
	left: -2rpx;
	margin-bottom: -7rpx;
	border-left-color: #fff;
}

.product-con .wrapper .coupon .activity:after {
	content: ' ';
	position: absolute;
	width: 7rpx;
	height: 10rpx;
	border-radius: 7rpx 0 0 7rpx;
	border: 1px solid #f2857b;
	background-color: #fff;
	right: -2rpx;
	bottom: 50%;
	margin-bottom: -5rpx;
	border-right-color: #fff;
}

.product-con .wrapper .coupon .iconfont {
	color: #7a7a7a;
	font-size: 28rpx;
}

.product-con .attribute {
	background-color: #fff;
	padding: 0 30rpx;
	font-size: 26rpx;
	color: #82848f;
	margin-top: 20rpx;
	height: 80rpx;
}

.product-con .attribute .atterTxt {
	font-size: 28rpx;
	color: #282828;
}

.product-con .attribute .iconfont {
	font-size: 28rpx;
	color: #7a7a7a;
}

.product-con .userEvaluation {
	margin-top: 20rpx;
	background-color: #fff;
}

.product-con .userEvaluation .title {
	height: 86rpx;
	border-bottom: 1px solid #eee;
	font-size: 28rpx;
	color: #282828;
	margin-left: 30rpx;
	padding-right: 30rpx;
}

.product-con .userEvaluation .title .praise {
	font-size: 28rpx;
	color: #808080;
}

.product-con .userEvaluation .title .praise .iconfont {
	color: #7a7a7a;
	font-size: 28rpx;
	vertical-align: 1rpx;
	margin-left: 8rpx;
}

.product-con .product-intro {
	margin-top: 20rpx;
	width: 100%;
	overflow: hidden;
}

.product-con .product-intro .title {
	font-size: 30rpx;
	color: #282828;
	height: 86rpx;
	width: 100%;
	background-color: #fff;
	text-align: center;
	line-height: 86rpx;
}

.product-con .product-intro .conter {
	width: 100%;
	overflow: hidden;
}

.product-con .product-intro .conter image {
	width: 100% !important;
	display: block !important;
}



.product-con .header {
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 96rpx;
	font-size: 30rpx !important;
	color: #050505;
	background-color: #fff;
	z-index: 11;
	border-bottom: 1px solid #eee;
}

.product-con .header .item {
	position: relative;
	margin: 0 35rpx;
	font-size: 30rpx !important;
}

.product-con .header .item.on:before {
	position: absolute;
	width: 60rpx;
	height: 5rpx;
	background-repeat: no-repeat;
	content: '';
	background: linear-gradient(to left, #ff3366 0%, #ff6533 100%);
	background: -webkit-linear-gradient(to left, #ff3366 0%, #ff6533 100%);
	background: -moz-linear-gradient(to left, #ff3366 0%, #ff6533 100%);
	bottom: -10rpx;
}

.product-con .footer {
	width: 100%;
	padding: 0 20rpx 0 30rpx;
	position: fixed;
	left: 0;
	bottom: 0;
	height: 100rpx;
	background-color: #fff;
	z-index: 777;
	border-top: 1px solid #f0f0f0;
}

.product-con .footer .item {
	font-size: 18rpx;
	color: #666;
	margin-top: 7rpx;
}

.product-con .footer .item .iconfont {
	text-align: center;
	font-size: 40rpx;
	height: 40rpx;
	line-height: 40rpx;
}


.product-con .bnt {
	width: 444rpx;
	height: 76rpx;
	color: #fff;
	font-size: 28rpx;
}

.product-con .bnt>view {
	width: 222rpx;
	text-align: center;
	line-height: 76rpx;
}


.product-con .bnt .joinCart {
	border-radius: 50rpx 0 0 50rpx;
	background-image: linear-gradient(to right, #fea10f 0%, #fa8013 100%);
	background-image: -webkit-linear-gradient(to right, #fea10f 0%, #fa8013 100%);
	background-image: -moz-linear-gradient(to right, #fea10f 0%, #fa8013 100%);
}

.product-con .bnt .buy {
	border-radius: 0 50rpx 50rpx 0;
	background-image: linear-gradient(to right, #fa6514 0%, #e93323 100%);
	background-image: -webkit-linear-gradient(to right, #fa6514 0%, #e93323 100%);
	background-image: -moz-linear-gradient(to right, #fa6514 0%, #e93323 100%);
}

.noscroll {
	height: 100%;
	overflow: hidden;
}




















/*优惠券公用样式*/
.condition .line-title {
	width: 90rpx;
	height: 30rpx;
	line-height: 30rpx;
	padding: 0 10rpx;
	box-sizing: border-box;
	background: rgba(255, 247, 247, 1);
	border: 1px solid rgba(232, 51, 35, 1);
	opacity: 1;
	border-radius: 22rpx;
	font-size: 20rpx !important;
	color: #e83323;
	margin-right: 12rpx;
}

.coupon-list .pic-num {
	color: #ffffff !important;
	font-size: 24rpx !important;
}

/*优惠券列表公共*/
.coupon-list {
	padding: 0 30rpx;
	padding-top: 25rpx;
}

.coupon-list .item {
	width: 100%;
	height: 170rpx;
	margin-bottom: 16rpx;
}

.coupon-list .item .money {
	background-image: url('data:image/png;base64,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');
	background-repeat: no-repeat;
	background-size: 100% 100%;
	width: 240rpx;
	height: 100%;
	color: #fff;
	font-size: 36rpx;
	font-weight: bold;
	text-align: center;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.coupon-list .item .money.moneyGray {
	background-image: url('data:image/png;base64,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');
}

.coupon-list .item .money .num {
	font-size: 60rpx;
}

.coupon-list .item .text {
	width: 450rpx;
	padding: 0 17rpx 0 24rpx;
	background-color: #fff;
}

.coupon-list .item .text .condition {
	font-size: 30rpx;
	color: #282828;
	height: 93rpx;
	line-height: 93rpx;
	border-bottom: 1px solid #f0f0f0;
}

.coupon-list .item .text .data {
	font-size: 24rpx;
	color: #999;
	height: 76rpx;
}

.coupon-list .item .text .data .bnt {
	width: auto;
	padding: 0 15rpx;
	height: 44rpx;
	border-radius: 22rpx;
	font-size: 22rpx;
	color: #fff;
	text-align: center;
	line-height: 44rpx;
}

.coupon-list .item .text .data .bnt.gray {
	background-color: #ccc;
}


/*所有推广头部样式*/
.promoterHeader {
	width: 100%;
	height: 220rpx;
}

.promoterHeader .headerCon {
	width: 100%;
	height: 100%;
	padding: 0 88rpx 0 55rpx;
	font-size: 28rpx;
	color: #fff;
	background-image: url('@/static/images/transparent.png');
	background-repeat: no-repeat;
	background-size: 100% 100%;
}

.promoterHeader .headerCon .name {
	margin-bottom: 2rpx;
}

.promoterHeader .headerCon .num {
	font-size: 50rpx;
}

.promoterHeader .headerCon .iconfont {
	font-size: 125rpx;
}

/* 记录相关公共代码 */


.sign-record .list .item .data {
	height: 80rpx;
	line-height: 80rpx;
	padding: 0 30rpx;
	font-size: 24rpx;
	color: #666;
}

.sign-record .list .item .listn {
	background-color: #fff;
	font-size: 24rpx;
	color: #999;
}

.sign-record .list .item .listn .itemn {
	height: 120rpx;
	border-bottom: 1px solid #eee;
	padding-right: 30rpx;
	margin-left: 30rpx;
}

.sign-record .list .item .listn .itemn .name {
	width: 390rpx;
	font-size: 28rpx;
	color: #282828;
	margin-bottom: 6rpx;
}

.sign-record .list .item .listn .itemn .num {
	font-size: 36rpx;
	font-family: 'GuildfordProBook 5';
	color: #16ac57;
}


/* 管理员 */
.public-wrapper .title {
	font-size: 30rpx;
	color: #282828;
	padding: 0 30rpx;
	margin-bottom: 20rpx;
}

.public-wrapper .title .iconfont {
	color: #2291f8;
	font-size: 40rpx;
	margin-right: 13rpx;
	vertical-align: middle;
}

.public-wrapper {
	margin: 18rpx auto 0 auto;
	width: 690rpx;
	background-color: #fff;
	border-radius: 10rpx;
	padding-top: 25rpx;
}

.public-wrapper .nav {
	padding: 0 30rpx;
	height: 70rpx;
	line-height: 70rpx;
	font-size: 24rpx;
	color: #999;
}

.public-wrapper .data {
	width: 210rpx;
	text-align: left;
}

.public-wrapper .browse {
	width: 192rpx;
	text-align: right;
}

.public-wrapper .turnover {
	width: 227rpx;
	text-align: right;
}

.public-wrapper .conter {
	padding: 0 30rpx;
}

.public-wrapper .conter .item {
	border-bottom: 1px solid #f7f7f7;
	height: 70rpx;
	font-size: 24rpx;
}

.public-wrapper .conter .item .turnover {
	color: #d84242;
}

/*商户管理公共样式*/
.pos-order-goods {
	padding: 0 30rpx;
	background-color: #fff;
}

.pos-order-goods .goods {
	height: 185rpx;
}

.pos-order-goods .goods~.goods {
	border-top: 1px dashed #e5e5e5;
}

.pos-order-goods .goods .picTxt {
	width: 515rpx;
}

.pos-order-goods .goods .picTxt .pictrue {
	width: 130rpx;
	height: 130rpx;
}

.pos-order-goods .goods .picTxt .pictrue image {
	width: 100%;
	height: 100%;
	border-radius: 6rpx;
}

.pos-order-goods .goods .picTxt .text {
	width: 365rpx;
	height: 130rpx;
}

.pos-order-goods .goods .picTxt .text .info {
	font-size: 28rpx;
	color: #282828;
}

.pos-order-goods .goods .picTxt .text .attr {
	font-size: 20rpx;
	color: #999;
}

.pos-order-goods .goods .money {
	width: 164rpx;
	text-align: right;
	font-size: 28rpx;
}

.pos-order-goods .goods .money .x-money {
	color: #282828;
}

.pos-order-goods .goods .money .num {
	color: #ff9600;
	margin: 5rpx 0;
}

.pos-order-goods .goods .money .y-money {
	color: #999;
	text-decoration: line-through;
}

.public-total {
	font-size: 28rpx;
	color: #282828;
	border-top: 1px solid #eee;
	height: 92rpx;
	line-height: 92rpx;
	text-align: right;
	padding: 0 30rpx;
	background-color: #fff;
}

.public-total .money {
	color: #ff4c3c;
}

.priceChange {
	position: fixed;
	width: 580rpx;
	height: 670rpx;
	background-color: #fff;
	border-radius: 10rpx;
	top: 50%;
	left: 50%;
	margin-left: -290rpx;
	margin-top: -335rpx;
	z-index: 666;
	transition: all 0.3s ease-in-out 0s;
	-webkit-transition: all 0.3s ease-in-out 0s;
	-o-transition: all 0.3s ease-in-out 0s;
	-moz-transition: all 0.3s ease-in-out 0s;
	-webkit-transform: scale(0);
	-o-transform: scale(0);
	-moz-transform: scale(0);
	-ms-transform: scale(0);
	transform: scale(0);
	opacity: 0;
}

.priceChange.on {
	opacity: 1;
	transform: scale(1);
	-webkit-transform: scale(1);
	-o-transform: scale(1);
	-moz-transform: scale(1);
	-ms-transform: scale(1);
}

.priceChange .priceTitle {
	background-image: url('@/static/images/pricetitle.jpg');
	background-repeat: no-repeat;
	background-size: 100% 100%;
	width: 100%;
	height: 160rpx;
	border-radius: 10rpx 10rpx 0 0;
	text-align: center;
	font-size: 40rpx;
	color: #fff;
	line-height: 160rpx;
	position: relative;
}

.priceChange .priceTitle .iconfont {
	position: absolute;
	font-size: 40rpx;
	right: 26rpx;
	top: 23rpx;
	width: 40rpx;
	height: 40rpx;
	line-height: 40rpx;
}

.priceChange .listChange {
	width: 100%;
	padding: 0 40rpx;
}

.priceChange .listChange .item {
	height: 103rpx;
	border-bottom: 1px solid #e3e3e3;
	font-size: 32rpx;
	color: #333;
}

.priceChange .listChange .item .money {
	color: #666;
	width: 300rpx;
	text-align: right;
}

.priceChange .listChange .item .money .iconfont {
	font-size: 32rpx;
	margin-left: 20rpx;
}

.priceChange .listChange .item .money input {
	width: 100%;
	height: 100%;
	text-align: right;
	color: #ccc;
}

.priceChange .listChange .item .money input.on {
	color: #666;
}

.priceChange .modify {
	font-size: 32rpx;
	color: #fff;
	width: 490rpx;
	height: 90rpx;
	text-align: center;
	line-height: 90rpx;
	border-radius: 45rpx;
	background-color: #2291f8;
	margin: 53rpx auto 0 auto;
}

.priceChange .modify1 {
	font-size: 32rpx;
	color: #312b2b;
	width: 490rpx;
	height: 90rpx;
	text-align: center;
	line-height: 90rpx;
	border-radius: 45rpx;
	background-color: #eee;
	margin: 30rpx auto 0 auto;
}

/* 订单详情 */
.order-details .header {
	padding: 0 30rpx;
	height: 150rpx;
}

.order-details .header.on {
	background-color: #666 !important;
}

.order-details .header .pictrue {
	width: 110rpx;
	height: 110rpx;
}

.order-details .header .pictrue image {
	width: 100%;
	height: 100%;
}

.order-details .header .data {
	color: rgba(255, 255, 255, 0.8);
	font-size: 24rpx;
	margin-left: 27rpx;
}

.order-details .header.on .data {
	margin-left: 0;
}

.order-details .header .data .state {
	font-size: 30rpx;
	font-weight: bold;
	color: #fff;
	margin-bottom: 7rpx;
}

.order-details .header .data .time {
	margin-left: 20rpx;
}

.order-details .nav {
	background-color: #fff;
	font-size: 26rpx;
	color: #282828;
	padding: 25rpx 0;
}

.order-details .nav .navCon {
	padding: 0 40rpx;
}

.order-details .nav .navCon .on {
	font-weight: bold;
	color: #e93323;
}

.order-details .nav .progress {
	padding: 0 65rpx;
	margin-top: 10rpx;
}

.order-details .nav .progress .line {
	width: 100rpx;
	height: 2rpx;
	background-color: #939390;
}

.order-details .nav .progress .iconfont {
	font-size: 25rpx;
	color: #939390;
	margin-top: -2rpx;
	width: 30rpx;
	height: 30rpx;
	line-height: 33rpx;
	text-align: center;
	margin-right: 0 !important;
}

.order-details .address {
	font-size: 26rpx;
	color: #868686;
	background-color: #fff;
	padding: 25rpx 30rpx 30rpx 30rpx;
}

.order-details .address .name {
	font-size: 30rpx;
	color: #282828;
	margin-bottom: 10rpx;
}

.order-details .address .name .phone {
	margin-left: 40rpx;
}

.order-details .line {
	width: 100%;
	height: 3rpx;
}

.order-details .line image {
	width: 100%;
	height: 100%;
	display: block;
}

.order-details .wrapper {
	background-color: #fff;
	margin-top: 12rpx;
	padding: 30rpx;
}

.order-details .wrapper .item {
	font-size: 28rpx;
	color: #282828;
}

.order-details .wrapper .item~.item {
	margin-top: 20rpx;
}

.order-details .wrapper .item .conter {
	color: #868686;
	width: 500rpx;
	text-align: right;
}

.order-details .wrapper .item .conter .copy {
	font-size: 20rpx;
	color: #333;
	border-radius: 3rpx;
	border: 1px solid #666;
	padding: 3rpx 15rpx;
	margin-left: 24rpx;
}

.order-details .wrapper .actualPay {
	border-top: 1rpx solid #eee;
	margin-top: 30rpx;
	padding-top: 30rpx;
}

.order-details .wrapper .actualPay .money {
	font-weight: bold;
	font-size: 30rpx;
}

.order-details .footer {
	width: 100%;
	height: 100rpx;
	position: fixed;
	bottom: 0;
	left: 0;
	background-color: #fff;
	padding: 0 30rpx;
	border-top: 1px solid #eee;
}

.order-details .footer .bnt {
	width: auto;
	height: 60rpx;
	text-align: center;
	line-height: 60rpx;
	border-radius: 50rpx;
	color: #fff;
	font-size: 27rpx;
	padding: 0 3%;
}

.order-details .footer .bnt.cancel {
	color: #aaa;
	border: 1px solid #ddd;
}

.order-details .footer .bnt.default {
	color: #444;
	border: 1px solid #444;
}

.order-details .footer .bnt~.bnt {
	margin-left: 18rpx;
}



.goodsStyle {
	background-color: #fff;
	padding: 22rpx 30rpx;
}

.goodsStyle .pictrue {
	width: 120rpx;
	height: 120rpx;
}

.goodsStyle .pictrue .image {
	width: 100%;
	height: 100%;
	border-radius: 6rpx;
}

.goodsStyle .text {
	width: 545rpx;
	font-size: 28rpx;
	color: #999;
}

.goodsStyle .text .name {
	width: 360rpx;
	color: #282828;
}

.goodsStyle .text .money {
	text-align: right;
}

.goodsStyle .text .money .num {
	margin-top: 7rpx;
}


.register {
	background-image: linear-gradient(to bottom, #eb5447 0%, #ff8e3b 100%);
	background-image: -webkit-linear-gradient(to bottom, #eb5447 0%, #ff8e3b 100%);
	background-image: -moz-linear-gradient(to bottom, #eb5447 0%, #ff8e3b 100%);
	width: 100%;
	height: 100vh;
}

.register .shading {
	background-image: url("@/static/images/registerw.png");
	background-repeat: no-repeat;
	background-size: 100% 100%;
	width: 100%;
	height: 286rpx;
	padding-top: 70rpx;
}

.register .shading .pictrue {
	width: 172rpx;
	height: 172rpx;
	border-radius: 50%;
	background-color: rgba(255, 255, 255, 0.8);
	margin: 0 auto;
}

.register .shading .pictrue image {
	width: 164rpx;
	height: 164rpx;
	border-radius: 50%;
	display: block;
}

.register .whiteBg {
	width: 620rpx;
	border-radius: 16rpx;
	background-color: #fff;
	margin: 30rpx auto 0 auto;
	padding: 45rpx 30rpx 0 30rpx;
}

.register .whiteBg .title {
	font-size: 36rpx;
	color: #282828;
	text-align: center;
	font-weight: bold;
}

.register .whiteBg .title .item~.item {
	margin-left: 85rpx;
}

.register .whiteBg .title .item {
	color: #999999;
	border-bottom: 5rpx solid #fff;
	padding-bottom: 10rpx;
}

.register .whiteBg .title .item.on {
	color: #282828;
	border-bottom-color: #f35749;
}

.register .whiteBg .list .item {
	border-bottom: 1px solid #ededed;
	padding: 47rpx 0 13rpx 0;
	position: relative;
}

.register .whiteBg .list .item .name {
	font-size: 26rpx;
	color: #2d3342;
	margin-bottom: 27rpx;
	text-align: left;
}

.register .whiteBg .list .item .icon {
	font-size: 35rpx;
	margin-right: 32rpx;
}

.register .whiteBg .list .item input {
	font-size: 33rpx;
	width: 490rpx;
}

.register .whiteBg .list .item input::placeholder {
	color: #cccccc;
}

.register .whiteBg .list .item .codeIput {
	width: 250rpx;
}

.register .whiteBg .list .item .code {
	position: absolute;
	width: 150rpx;
	height: 50rpx;
	background-color: #f35446;
	border-radius: 30rpx;
	color: #fff;
	line-height: 50rpx;
	text-align: center;
	bottom: 17rpx;
	right: 0;
	font-size: 25rpx;
}

.register .whiteBg .list .item .code.on {
	background-color: #bbbbbb;
}

.register .whiteBg .list .forgetPwd {
	text-align: right;
	font-size: 28rpx;
	color: #666;
	margin-top: 20rpx;
}

.register .whiteBg .list .forgetPwd .iconfont {
	font-size: 30rpx;
	margin-right: 10rpx;
	vertical-align: middle;
}

.register .whiteBg .logon {
	font-size: 34rpx;
	color: #fff;
	font-weight: bold;
	height: 86rpx;
	border-radius: 43rpx;
	background: linear-gradient(to right, #f35447 0%, #ff8e3c 100%);
	background: -webkit-linear-gradient(to right, #f35447 0%, #ff8e3c 100%);
	background: -moz-linear-gradient(to right, #f35447 0%, #ff8e3c 100%);
	text-align: center;
	line-height: 86rpx;
	margin-top: 47rpx;
}

.register .whiteBg .tip {
	height: 110rpx;
	text-align: center;
	line-height: 105rpx;
	font-size: 30rpx;
	color: #cccccc;
}

.register .bottom {
	background-image: url('@/static/images/registerB.png');
	background-repeat: no-repeat;
	background-size: 100% 100%;
	width: 620rpx;
	height: 36rpx;
	margin: 0 auto;
}

/*商品标签公用样式*/
.pictrue_log_class {
	background: linear-gradient(90deg, rgba(246, 122, 56, 1) 0%, rgba(241, 27, 9, 1) 100%);
	opacity: 1;
	position: absolute;
	top: 0;
	left: 0;
	color: #fff;
	text-align: center;
}

.pictrue_log {
	width: 80rpx;
	height: 40rpx;
	border-radius: 6rpx 0 12rpx 0;
	line-height: 40rpx;
	font-size: 24rpx;
}

.pictrue_log_medium {
	width: 80rpx;
	height: 44rpx;
	border-radius: 12rpx 0 16rpx 0;
	line-height: 44rpx;
	text-align: center;
	font-size: 26rpx;
}

.pictrue_log_big {
	width: 100rpx;
	height: 46rpx;
	line-height: 46rpx;
	border-radius: 10rpx 0 16rpx 0;
	font-size: 28rpx;
}

.bargain .goodsDetails .conter div {
	width: auto !important;
	height: auto !important;
}

.bg-color-hui {
	background: #bbbbbb !important;
	color: #fff !important;
}

.bg-color-huic {
	background: #F5F5F5 !important;
	color: #BBBBBB !important;
	border: 1px solid #BBBBBB !important;
}

.bg-color-check {
	color: #e83323 !important;
	background: #FFF7F7 !important;
	border: 1px solid #E83323 !important;
}
