const TransformPages = require('uni-read-pages')
const {
	webpack
} = new TransformPages()
module.exports = {
	configureWebpack: {
		plugins: [
			new webpack.DefinePlugin({
				ROUTES: webpack.DefinePlugin.runtimeValue(() => {
					const tfPages = new TransformPages({
						includes: ['path', 'name', 'aliasPath']
					});
					
					const routes = tfPages.routes,
						infos = {};
					
					routes.forEach(item => {
						infos[item.aliasPath] = item;
					})
					
					return JSON.stringify({
						ROUTES_ARRAY: routes,
						ROUTES_OBJECT: infos
					})
					
				}, true)
			})
		]
	},
    chainWebpack: (config) => {
        // 发行或运行时启用了压缩时会生效
        config.optimization.minimizer('terser').tap((args) => {
            const compress = args[0].terserOptions.compress
            // 非 App 平台移除 console 代码(包含所有 console 方法，如 log,debug,info...)
            compress.drop_console = true
            compress.pure_funcs = [
                '__f__', // App 平台 vue 移除日志代码
                'console.log' // 可移除指定的 console 方法
            ]
            return args
        })
    }
}