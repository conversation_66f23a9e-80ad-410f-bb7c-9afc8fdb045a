### C2.0.19
* 大文件上传，增加网络判断，追加失败原因分析
* 商品详情页评论加载逻辑调整，仅在上传界面返回时，刷新评论数据
* 多页面跳转时音频多个同时播放
* 文件上传失败，删除文件重新选择再次上传该类型文件，弹窗逻辑处理

### C2.0.18
* 无效音频的提示
* 评论提交时，上传失败的类型提醒用户，并允许再次上传
* 订阅消息
* 评论详情页无视频或图片时的判断
* 评论详情页浏览量显示
* 商品详情页评论数量显示
* 商品详情页按钮文字修改
* 首页滚动区域高度调整
* 剩余名额字段调整
* 详情页折扣价显示

### C2.0.17
* 剩余名额为0时，禁止报名
* 活动说明截止时间
* 支付状态页金额显示
* 订单详情页 订单金额显示

### C2.0.16
* 评论删除
* 评论加精
* 首页刷新
* 报名页剩余名额计算
* 单规格价格计算异常
* 剩余次数逻辑调整

### C2.0.15
* 详情页已报名列表显示
* 同账号报名人数限制
* 同账号买票总数限制

### C2.0.14
* 增加添加群聊入口
* 增加单人单次购买数量限制
* 支付状态页文字显示调整
* 首页无活动时区域显示
* 编译时控制台提示非组件模式

### C2.0.13
* 修复代码调整带来公众号授权死循环问题
* 微信浏览器与微信小程序地图跳转问题
* 详情页活动状态文字说明

### C2.0.12
* 接口调试及逻辑调整

### C2.0.11
* ui完善及接口调试

### C2.0.10
* 修复商品评价跳转地址错误
* 订单详情页余额未返回
* 订单提交页自提时。个人信息填写
* 划线价格显示

### C2.0.9
* 新品推荐封面图去除oss裁剪
* 移除无用页面路径

### C2.0.8
* oss读取图片信息失败时，调用本地api读取
* 小程序首页增加公众号关注组件

### C2.0.7
* 详情页轮播图调整增加返回首页
* oss裁剪图片增减对地址是否为图片校验
* 微信支付回调失败时，增加返回首页

### C2.0.6
* 首页跳转
* 商品详情页至购物车页面跳转
* 普通商品详情页分享功能完善

### C2.0.5
* 订单结算
* 购物车不同类型商品详情跳转
* 订单详情页不同类型商品跳转

### C2.0.4
* 普通商品选择面板相关逻辑调整
* 普通商品详情页弹窗优先级调整
* 余额支付前对剩余金额判断
* 完善添加购物车逻辑

### C2.0.3
* 首页图片去除图片裁剪，后期使用统一规格图片，前端不再处理
* 增加ycommunity，存放新版本相关页面
* 增加x-file-picker与x-recorder组件
* 普通商品相关逻辑
* 客服消息中商品详情跳转

### C2.0.2
* 路由相关逻辑调整  (主要对h5)
* 路由追加公共参数
* uni组件使用easycom
* 更新测评详情页 parse解析插件
* 客服相关字段补充
* 联系客服时商品图片的裁剪
 
### V2.0.1
* 图片替换

### V2.0.0
* 沙龙活动
* getUserProfile接口更新
* 简单布局优化

### C0.0.1
* 原有商城功能移植并uni化
* 接入腾讯云智服
* 部分组件兼容头条小程序
* 微信小程序增加请求异常时日志上传

### C0.0.2
* 商品规格小程序端选中样式丢失
* 新闻详情富文本图片显示超出区域
* 小程序查看门店地图显示异常
* 推广订单列表布局调整
* 调整分包，压缩图片
* 砍价界面相关逻辑调整，条件判断与小程序一致
* 活动相关海报二维码与后端联调
* 商品详情二维码相关逻辑调整
* 分享二维码解析参数调整
* 更换小程序appid，去除直播插件

### C0.0.3
* 初始化全局spid
* 样式微调
* 首页路由配置调整
* 公众号授权登录

### C0.0.4
* 空图片上传数据拼接问题
* 订单详情、订单列表，数据不能及时更新问题
* 会员购买失败
* 会员购买成功相关信息展示
* 调整涉及用户购买会员之后所涉及返回界面信息刷新

### C0.0.5
* 用户信息页手机号绑定跳转异常
* 支付状态页未能返回首页
* 多账户切换问题
* 修改密码页面无法跳转
* 修改密码验证码验证不通过
* 用户优惠券

### C0.0.6
* 未登录或授权过期重新授权前清除本地相关信息,之后再自动授权
* 增加yuanshi目录
* user目录下增加yuanshi目录
* 相关流程、交互打通及界面简单实现

### C0.0.7
* 首页与看板页ui设计稿实现
* 增加评价组件
* 登录去除默认账号与密码

### C0.0.8
* 添加模板消息
* 过期token的移除

### C0.0.9
* 接口调试
* 服务器增加  wximage目录。存放相关资源
* 部分图片移至服务器，通过外链加载
* iconfont目录下部分文件移至服务器，通过外链加载
* 已完成ui优化
* 未登录状态显示
* 五角星进度显示

### C0.0.10
* ui界面调试
* 会员展示，购买，权益介绍，规则等界面
* 调整小程序会员涉及纯文字展示界面跳转至H5
* 原有商品详情页与现有测评详情页去购买时相关支付功能通过mixins引入
* 接口调试，测试正常，具体细节待定
* 界面文字等细节微调
* 404页面调整

### C0.0.11
* 关于界面及其它ui界面的调整
* 列表相关操作小程序端的登录跳转完善
* tab切换的闪动
* 详情页分享功能的完善
* 心愿单编辑bug修复

### C0.0.12
* 测评详情页秒杀商品显示与隐藏
* 涉及源未指数超出5之后星星的显示数量
* banner中http网页跳转

### C0.0.13
* 小程序端变量值显示undefined
* 小程序中文字修改
* 我要测评页ui及交互逻辑修改

### C0.0.14
* 个人心愿api调整
* 个人心愿状态显示
* 评论输入框的隐藏优化
* 我要测评小程序显示问题（循环整数，浏览器从1开始，小程序0，小程序在非循环中不能监听数据.length变化）

### C0.0.15
* 订单状态逻辑及文字调整
* 订单列表及订单详情重新支付调整
* 评测与心愿单 评论区点赞api调试
* 心愿单按钮调整
* 小程序部分字段undefined

### C0.0.16
* 修改会员支付逻辑，会员支付后直接到相关展示页面
* 部分列表页价格展示
* banner跳转地址逻辑完善
* 会员等级文字化

### C0.0.17
* 首页增加地址授权请求
* 地址管理界面图片初始化宽高，预防闪屏
* 地址管理地址全部删除后，布局错乱
* 会员升级界面取消h5跳转
* 个人中心手机号判断
* 个人中心图标更新
* 优化测评详情页与心愿单详情页title显示
* 完善测评详情页与心愿单详情页授权之后数据请求
* 推荐商品已在当前页面增加提示，不跳转

### C0.0.18
* 列表相关状态显示
* 测评区增加大图查看
* 测评详情页、心愿详情页无数据区域的显示与隐藏
* 搜索页跳转与价格显示

### C0.0.19
* 个人中心关注相关操作
* 测评区无图片时逻辑调整
* 删除无用文件

### C0.0.20
* 测评提交之后数据刷新
* 个人中心我的关注取消之后移除操作及显示数量的更新
* 会员升级规则更新
* 涉及推荐显示源未指数小数点问题各页面单独处理，不再统一处理
* 官评地址不存在时相关逻辑完善

### C0.0.21
* 详情页白色图片时，显示不明显，增加模板层
* 测评区域移除无评论时的评论提示
* 主页ta的关注相关逻辑调整

### C0.0.22
* 标签分的显示与隐藏
* 测评区无数据显示
* 会员等级背景图显示
* vip协议去除H5跳转

### C0.0.23
* 用户主页的跳转限制
* 推荐商品文字修改
* vip相关卡片显示组件化
* vip支付界面及逻辑调整
* 测评页面补充也感兴趣
* 测评区，单标签搜索
* 小程序端搜索结果无法跳转 (v-for key赋值错误，不报错，但函数无法接收参数)

### C0.0.24
* 公众后授权之后重定向后用户信息丢失
* vip相关卡片组件化后会员权益无限跳转问题

### C0.0.25
* 秒杀相关逻辑调整
* 部分组件大屏适配
* 用户信息相关调整使用common方法中getuserinfo,部分界面使用store.state.userinfo更改为commpute及时更新用户信息
* 解决会员购买成功后跳转问题(全局更新用户信息，并直接返回上一页面)

### C0.0.26
* 活动商品价格问题
* 提交订单页增加选择联系人
* 测评打分交互问题修复
* 我的测评查看、编辑与删除

### C0.0.27
* 文字及价钱显示字段修改
* 修复分享主页不能访问
* 核销页调整使用说明
* 测评详情页增加活动使用说明
* 搜索页空标签隐藏

### C0.0.28
* 订单详情页 活动商品跳转错误
* banner跳转

### C0.0.29
* 轮播图增加智能裁剪
* 聊天输入框高度调整
* config.js增加开发环境与生产环境配置
* 首页朋友圈分享

### C0.0.30
* oss图片根据场景进行压缩，裁剪等处理,减小图片大小（至少压缩一半），提高页面加载速度
* 增加统计

### C0.0.31
* 修复新版本工具后h5部分css不生效
* 规格相关ui调整
* 详情页标签显示ui调整
* 版本更新提示
* 优化整个授权流程，代码简化
* 输入框自动弹起
* 测评增加自建标签
* 评分滑动过程增加分数提示
* 详情页标签隐藏
* 详情页按钮等ui调整

### C0.0.32
* 标签打分，快速滑动，分数值显示不准确
* 修复自建标签真机预选字 过长时会全部显示
* 修复自建标签文字过长换行问题
* 自建标签的删除真机无效 （多层数据在wxml中直接splice，编译结果会多一层）
* webview分享设置

### C0.0.33
* 自建标签相关样式调整
* 搜索样式及功能调整
* 搜索样式优化及功能调整
* 输入位空时，禁止相关操作
* 标签提示的显示与隐藏

### C0.0.34
* 搜索框点击时误触banner
* 列表想测状态提示
* 感叹号链接调整及设计ui调整
* 测评区域移除自建标签的显示

### C0.0.35
* 测评提交增加门店列表
* 测评区增加店铺显示
* 搜索页推荐列表样式调整
* 移除前期测试将expires_time初始为空

### C0.0.36
* 想测表单提交提示文字修改
* 想测表单移除时间选项
* 订单结算后提示数据错误

### C0.0.37
* 测评编辑状态下店铺的选择
* 图片删除时提示无修改
* 去除体验版本的数据统计

### C0.0.38
* 测评首页增加说明提示跳转
* 修复门店选中不生效
* 优化测评提交页输入框的显示与隐藏逻辑
* 调整输入框回车换行逻辑
* 测评表单页数据的缓存

### C0.0.39
* 新增源未指数说明页
* 增加源未指数跳转
* 想测及说明页 按钮样式调整
* 调整各个感叹号大小及位置
* 想测评论区只看我的隐藏