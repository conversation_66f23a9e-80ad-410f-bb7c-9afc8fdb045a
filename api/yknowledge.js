import request from "@/utils/request";

/**
 * 最新课程
 */
export function getSpecialLatest(from) {
	return request.get("/special/latest" + '?from=' + from , {}, {
		login: false
	});
}

/**
 * 学习记录
 */             
export function getSpecialMyLearningRecords(data={}) {
	return request.get("/special/my_learning_records", data, {
		login: true
	});
}
/**
 * 我的收藏
 */
export function getSpecialCollection(data={}) {
	return request.get("/special/my_collection", data, {
		login: true
	});
}
/**
 * 我的课程
 */
export function getSpecialMyCourses(data={}) {
	return request.get("/special/my_courses", data, {
		login: true
	});
}
/**
 * 分类
 */
export function getSpecialCategory(data={}) {
	return request.get("/special/category", data, {
		login: false
	});
}

/**
 * 课程列表
 */
export function getSpecials(data={}) {
	return request.get("/specials", data, {
		login: false
	});
}




/**
 * 再次支付课程订单
 */
export function specialOrderPay(data={}) {
	return request.post("/special/order/pay", data, {
		login: true
	});
}
/**
 * 取消课程订单
 */
export function specialOrderCancel(data={}) {
	return request.post("/special/order/cancel", data, {
		login: true
	});
}


/**
 * 最新一条未支付的课程订单
 */
export function getSpecialLastOrder(id) {
	return request.get("/special/order/latest_one", {id}, {
		login: true
	});
}


/**
 * 获取问卷
 */
export function getPollster(data={}) {
	return request.get("/pollster", data, {
		login: true
	});
}


/**
 * 提交学员问卷
 */
export function pollsterCreate(data={}) {
	return request.post("/pollster/create", data, {
		login: true
	});
}



/**
 * 课程详情
 */
export function getSpecialDetail(data={}) {
	return request.get("/special/detail", data, {
		login: false
	});
}



/**
 * 生成邀请码
 */
export function getCreatedInvitationCode(data={}) {
	return request.post("/special/created_invitation_code", data, {
		login: true
	});
}

/**
 * 领取邀请码
 */
export function getReceiveInvitationCode(data={}) {
	return request.get("/special/receive_invitation_code", data, {
		login: true
	});
}

/**
 * 获取课程邀请码信息
 */
export function specialGetCodeInfo(data={}) {
	return request.get("/special/get_code_info", data, {
		login: false
	});
}


/**
 * 兑换课程激活码
 */
export function specialExchangeCode(data={}) {
	return request.get("/special/exchange_code", data, {
		login: false
	});
}



/**
 * 课程收藏
 */
export function getSpecialCollect(data={}) {
	return request.get("/special/collect", data, {
		login: true
	});
}

// export function getSpecialCollect(id) {
// 	return request.get("/special/collect", {id}, {
// 		login: true
// 	});
// }


/**
 * 课程目录二级（升级版）
 */
export function getSpecialCourseCatalogList(data={}) {
	return request.get("/special/get_course_catalog_list", data, {
		login: false
	});
}

/**
 * 课程目录
 */
export function getSpecialCourseList(data={}) {
	return request.get("/special/get_course_list", data, {
		login: false
	});
}

/**
 * 课程目录详情
 */
export function getSpecialCourseListDetail(data={}) {
	return request.get("/special/source_detail", data, {
		login: false
	});
}

/**
 * 发表留言
 */
export function getSpecialMessageAdd(data={}) {
	return request.post("/special/message/add", data, {
		login: true
	});
}
/**
 * 留言列表
 */
export function getSpecialMessage(data={}) {
	return request.get("/special/message", data, {
		login: false
	});
}

export function getSpecialMessageDetail(data={}) {
	return request.get("/special/message/detail", data, {
		login: false
	});
}

/**
 * 取消留言点赞
 */
export function getSpecialMessageUnlike(data={}) {
	return request.get("/special/message/unlike", data, {
		login: true
	});
}
/**
 * 留言点赞
 */
export function getSpecialMessageLike(data={}) {
	return request.get("/special/message/like", data, {
		login: true
	});
}


/**
 * 发表评论
 */
export function getSpecialCommentAdd(data={}) {
	return request.post("/special/comment/add", data, {
		login: true
	});
}
/**
 * 评论区
 */
export function getSpecialComment(data={}) {
	return request.get("/special/comment", data, {
		login: false
	});
}



/**
 * 管理员管理帖子类型
 */
export function userUpdateMessageType(data={}) {
	return request.get("/user/update_message_type", data, {
		login: true
	});
}

/**
 * 取消评论点赞
 */
export function getSpecialCommentUnlike(obj={}) {
	return request.get("/special/comment/unlike", obj, {
		login: true
	});
}
/**
 * 评论点赞
 */
export function getSpecialCommentLike(obj={}) {
	return request.get("/special/comment/like", obj, {
		login: true
	});
}
/**
 * 支付
 */
export function getSpecialOrderCreate(data={}) {
	return request.post("/special/order/create", data, {
		login: true
	});
}

/**
 * 计算实时金额
 */
export function upSpecialOrderComputed(data={}) {
	return request.post("/special/order/computed", data, {
		login: true
	});
}

/**
 * 记录学习人数
 */
export function getSpecialJoinLearning(id) {
	return request.get("/special/join_learning", {id}, {
		login: true
	});
}
/**
 * 记录学习人数
 */
export function getSpecialLearningRecords(id) {
	return request.get("/special/learning_records", {id}, {
		login: true
	});
}

/**
 * 赠送课程
 */

export function getSpecialMyGive(data={}) {
	return request.get("/special/my_gift", data, {
		login: true
	});
}


/**
 * 领取课程
 */
export function getSpecialReceiveGift(order_id) {
	return request.get("/special/receive_gift", {order_id}, {
		login: true
	});
}

/**
 * 领取记录
 */

export function getSpecialGiftReceive(order_id) {
	return request.get("/special/gift_receive", {order_id}, {
		login: true
	});
}


/**
 * 支付状态
 */

export function getSpecialPayOrderStatuts(order_id) {
	return request.get("/special/order/pay_status", {order_id}, {
		login: true
	});
}

/**
 * 解锁课程的惊喜项目
 */
export function specialUnlockSurprise(data={}) {
	return request.post("/special/unlock_surprise", data, {
		login: true
	});
}
