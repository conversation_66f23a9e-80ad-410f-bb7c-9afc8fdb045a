import request from "@/utils/request";

// 最新活动
export function activityLatest(data,from) {
	return request.get("/activity/latest" + '?from=' + from, data, {
		login: false
	});
}
// 往期活动
export function activityPast(data,from) {
	return request.get("/activity/past" + '?from=' + from, data, {
		login: false
	});
}
// 活动详情
export function activityDetail(id, from , invite_uid) {
	return request.get("/activity/detail/" + id + '?from=' + from + '&invite_uid=' + invite_uid, {}, {
		login: false
	});
}

// 管理员审核留言
export function activityMessageReview(data) {
	return request.get("/user/activity_message_review", data,{
        login: true
    });
}

// 管理员删除留言
export function deleteActivityMessage(obj = {}) {
	return request.get("/user/delete_activity_message", obj, {
		login: true
	});
}
// 管理员设置精华
export function setupActivityRefining(id) {
	return request.get("/user/setup_activity_refining", {
		id
	}, {
		login: true
	});
}


// 在线营销活动详情
export function activityOnlineDetail(id, from ,) {
	return request.get("/activity_online/detail?id=" + id + '&from=' + from , {}, {
		login: false
	});
}
// 提交预约
export function activityOnlinePartake(data) {
	return request.post("/activity_online/partake", data, {
		login: true
	});
}


/**
 * 发表留言
 */
export function activityOnlineMessageAdd(data={}) {
	return request.post("/activity_online/message_add", data, {
		login: true
	});
}

/**
 * 修改留言
 */
export function editMyActivityMessage(data={}) {
	return request.post("/user/edit_my_activity_message", data, {
		login: true
	});
}

/**
 * 留言列表
 */
export function activityOnlineMessage(data={}) {
	return request.get("/activity_online/message", data, {
		login: false
	});
}

/**
 * 我的留言评论列表
 */
export function activityOnlineMyMessage(data={}) {
	return request.get("/activity_online/my_message", data, {
		login: false
	});
}

/**
 * 删除我的留言
 */
export function delActivityMyMessage(data={}) {
	return request.get("/user/del_activity_my_message", data, {
		login: true
	});
}


/**
 * 留言详情
 */
export function activityOnlineMessageDetail(data={}) {
	return request.get("/activity_online/message_detail", data, {
		login: true
	});
}

/**
 * 取消留言点赞
 */
export function activityOnlineMessageUnlike(data={}) {
	return request.get("/activity_online/message_unlike", data, {
		login: true
	});
}
/**
 * 留言点赞
 */
export function activityOnlineMessageLike(data={}) {
	return request.get("/activity_online/message_like", data, {
		login: true
	});
}



/**
 * 发表评论
 */
export function activityOnlineCommentAdd(data={}) {
	return request.post("/activity_online/comment_add", data, {
		login: true
	});
}
/**
 * 评论列表
 */
export function activityOnlineComment(data={}) {
	return request.get("/activity_online/comment", data, {
		login: false
	});
}


/**
 * 取消评论点赞
 */
export function activityOnlineCommentUnlike(obj={}) {
	return request.get("/activity_online/comment_unlike", obj, {
		login: true
	});
}
/**
 * 评论点赞
 */
export function activityOnlineCommentLike(obj={}) {
	return request.get("/activity_online/comment_like", obj, {
		login: true
	});
}


// （用户）添加面试预约
export function userInterviewsAdd(data) {
	return request.post("/user/interviews/add", data, {
		login: false
	});
}
// （用户）编辑面试预约
export function userInterviewsEdit(data) {
	return request.post("/user/interviews/edit", data, {
		login: false
	});
}
// （用户）用户取消面试预约
export function userInterviewsCancel(data) {
	return request.post("/user/interviews/cancel", data, {
		login: false
	});
}
// （用户）我的面试预约列表
export function userInterviewsmyList(data) {
	return request.get("/user/interviews/my_list", data, {
		login: false
	});
}
// （用户）面试预约计划详情
export function userProgramDetails(data) {
	return request.get("/user/interviews/program_detail", data, {
		login: false
	});
}
// （用户）面试预约详情
export function userDetails(data) {
	return request.get("/user/interviews/details", data, {
		login: false
	});
}
// （用户）是否有权限面试
export function userIsConditions(data) {
	return request.get("/user/interviews/is_conditions", data, {
		login: false
	});
}


// （管理）添加活动面试预约
export function eventAdd(data) {
	return request.post("/admin/event_center_stage/add", data, {
		login: true
	});
}
// （管理）面试预约计划详情-用于修改
export function eventProgramDetail(data) {
	return request.get("/admin/event_center_stage/program_detail", data, {
		login: false
	});
}
// （管理）下载已约清单
export function eventGetAppointedFile(data) {
	return request.get("/admin/event_center_stage/get_appointed_file", data, {
		login: false
	});
}


// （管理）修改活动面试预约
export function eventEdit(data) {
	return request.post("/admin/event_center_stage/edit", data, {
		login: true
	});
}
// （管理）面试预约计划列表
export function eventProgramList(data) {
	return request.get("/admin/event_center_stage/program_list", data, {
		login: false
	});
}
// （管理）获取已报名付款成功的订单列表
export function eventOrderList(data) {
	return request.get("/admin/event_center_stage/order_list", data, {
		login: false
	});
}
// （管理）已约清单
export function eventAppointedList(data) {
	return request.get("/admin/event_center_stage/appointed_list", data, {
		login: false
	});
}
// （管理）面试计划详情
export function eventDetail(data) {
	return request.get("/admin/event_center_stage/detail", data, {
		login: false
	});
}
// 报名活动列表及信息
export function eventApplicationSituation(data) {
	return request.get("/admin/event_center_stage/application_situation", data, {
		login: false
	});
}

// 签到台-活动订单列表
export function checkInDeskOrderList(data) {
	return request.get("/admin/check_in_desk/order_list", data, {
		login: false
	});
}
// 活动订单签到
export function activityBatchOrderVerific(data) {
	return request.post("/activity/order/batch_order_verific", data, {
		login: true
	});
}



//评论
export function activityComment(data) {
	return request.get("/activity/comment", data, {
		login: true
	});
}

export function activityCommentLike(data) {
	return request.get("/activity/comment/like", data, {
		login: true
	});
}
export function activityCommentUnLike(data) {
	return request.get("/activity/comment/unlike", data, {
		login: true
	});
}
export function activityCommentAdd(data) {
	return request.post("/activity/comment/add", data, {
		login: true
	});
}


// 活动心愿
export function activityAddWish(data) {
	return request.post("/activity/add_wish", data, {
		login: true
	});
}

// 留言
export function activityMessage(data) {
	return request.get("/activity/message", data, {
		login: true
	});
}
export function activityMessageDetail(id) {
	return request.get("/activity/message/detail/" + id, {}, {
		login: false
	});
}
export function activityMessageLike(data) {
	return request.get("/activity/message/like", data, {
		login: true
	});
}
export function activityMessageUnLike(data) {
	return request.get("/activity/message/unlike", data, {
		login: true
	});
}
export function activityMessageAdd(data) {
	return request.post("/activity/message/add", data, {
		login: true
	});
}

export function activityMessageDel(id) {
	return request.post("/activity/message/del", {id}, {
		login: true
	});
}

// 支付
// 创建
export function activityOrderCreate(id, data) {
	return request.post("/activity/order/create/" + id, data, {
		login: true
	});
}
// 加入购物车
export function activityCartAdd(data) {
	return request.post("/activity/cart/add", data, {
		login: true
	});
}
// 确认
export function activityOrderConfirm(data) {
	return request.post("/activity/order/confirm", data, {
		login: true
	});
}
// 订单列表
export function activityOrderList(data) {
	return request.get("/activity/order/list", data, {
		login: true
	});
}

// 订单详情
export function activityOrderDetail(id,invite_uid) {
	return request.get("/activity/order/detail/" + id, {}, {
		login: true
	});
}

// 订单计算
export function activityOrderComputed(id, data) {
	return request.post("/activity/order/computed/" + id, data, {
		login: true
	});
}


/**
 * 取消订单
 * @returns {*}
 */
export function activityCancelOrder(id) {
  return request.post("/activity/order/cancel", { id });
}

/**
 * 删除订单
 * @returns {*}
 */
export function activityDelOrder(uni) {
  return request.post("/activity/order/del", { uni });
}
/**
 * 支付
 * @returns {*}
 */
export function activityPayOrder(uni, paytype, from) {
  return request.post("/activity/order/pay", { uni, paytype, from });
}
/**
 * 订单核销
 * @returns {*}
 */
export function activityOrderVerific(verify_code, is_confirm) {
  return request.post("/admin//activity_order/order_verific", { verify_code, is_confirm });
}

/**
 * 提交退款
 * @returns {*}
 */
export function activityOrderRefund(data) {
  return request.post("/activity/order/refund/verify", data);
}


export function activityOrderAgain(uni) {
  return request.post("/activity/order/again",  { uni: uni });
}

/**
 * 优惠吗校验
 * @returns {*}
 */
export function activityCheckPromocode(id) {
  return request.get("/activity/check_promocode/"+id, {});
}


export function activityCheckBuylimit(id) {
  return request.get("/activity/get_buylimit/"+id, {});
}