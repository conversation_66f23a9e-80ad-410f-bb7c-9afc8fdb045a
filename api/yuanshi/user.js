import request from "@/utils/request";
import store from "@/store";
import {
	reportUserFollow,
	reportUserUnFollow,
	reportUserHome
} from "@/utils/ReportAnalytics.js"



// 用户主页 1
export function userIndexBase(id) {
	if (store.state.userInfo) {
		reportUserHome({
			uid: store.state.userInfo,
			to_uid: id
		})
	}
	return request.get("/user/index_base", {
		user_id: id
	}, {
		login: true
	});
}

// 我的
export function user(data) {
	return request.get("/user", data, {
		login: true
	});
}

// 我的心愿
export function userWishList(data) {
	return request.get("/wish/list", data, {
		login: false
	});
}


// 我的粉丝 
export function userFans(data) {
	return request.get("/user/fans", data, {
		login: true
	});
}

// 我的跟随 
export function userFollowList(data) {
	return request.get("/user/follow", data, {
		login: true
	});
}

// 跟随粉丝数量查询
export function getBaseNum(data) {
	return request.get("/user/relation_number", {}, {
		login: true
	});
}

// // 我的动态
// export function userDynamic(data) {
// 	return request.get("/user/dynamic", data, {
// 		login: false
// 	});
// }


// 跟随
export function userFollow(id) {
	if (store.state.userInfo) {
		reportUserFollow({
			uid: store.state.userInfo,
			to_uid: id
		})
	}
	return request.post("/user/follow", {
		to_uid: id
	}, {
		login: true
	});
}

// 取消跟随
export function userUnFollow(id) {
	if (store.state.userInfo) {
		reportUserUnFollow({
			uid: store.state.userInfo,
			to_uid: id
		})
	}
	return request.post("/user/follow_del", {
		to_uid: id
	}, {
		login: true
	});
}

// 好友聊天记录 1
export function messageRecord(to_uid, data) {
	return request.get("/user/message/record/" + to_uid, data, {
		login: true
	});
}
// 好友列表 1
export function messageList(data) {
	return request.get("/user/message/list", data, {
		login: true
	});
}

/*
 * 管理员审核留言
 * */
export function messageReview(data) {
	// return request.get("/user/service/list");
	return request.get("/user/message_review", data);
}

// 新增触发消息阅读接口
export function systemeeadingRrecord(data) {
	return request.get("/user/system/reading_record", data, {
		login: true
	});
}

// 系统消息
export function systemMessageList(data) {
	return request.get("/user/system/message", data, {
		login: true
	});
}
// 系统消息阅读状态

export function systemMessageStatus(data) {
	return request.get("/user/system/message_status", data, {
		login: true
	});
}

export function getAttention(data) {
	return request.get("/user/attention", data, {
		login: true
	});
}


export function getFootprint(data) {
	return request.get("/user/footprint", data, {
		login: true
	});
}
