import request from "@/utils/request";
import store from "@/store";
import {reportEvaluateAttention,reportEvaluateUnAttention} from "@/utils/ReportAnalytics.js"
// export function evaluationList(data) {
// 	return request.get("/wish/list", data, {
// 		login: false
// 	});
// }


export function evaluationDetail(data) {
	return request.get("/evaluation/detail", data, {
		login: false
	});
}


// 测评标签
export function evaluationLabels(id) {
	return request.get("/evaluation/labels", {product_id:id}, {
		login: false
	});
}

// 添加测评
export function evaluationAdd(data) {
	return request.post("/evaluation/add", data, {
		login: true
	});
}
// 关注
export function evaluationAttention(id) {
	if(store.state.userInfo){
		reportEvaluateAttention({uid:store.state.userInfo,product_id:id})
	}
	return request.get("/product/attention", {product_id:id}, {
		login: true
	});
}
// 取消关注
export function evaluationUnAttention(id) {
	if(store.state.userInfo){
		reportEvaluateUnAttention({uid:store.state.userInfo,product_id:id})
	}
	return request.get("/product/unattention", {product_id:id}, {
		login: true
	});
}

//评论
export function evaluationComment(data) {
	return request.get("/evaluation/area", data, {
		login: true
	});
}


// // 测评点赞
export function evaluationLike(data) {
	return request.get("/evaluation/like", data, {
		login: true
	});
}
export function evaluationUnLike(data) {
	return request.get("/evaluation/unlike", data, {
		login: true
	});
}

// 查看用户单条评测详情内容
export function evaluationInfo(id) {
	return request.get("/evaluation/info", {id:id}, {
		login: true
	});
}
// 只查看我的
export function evaluationMe(data) {
	return request.get("/evaluation/me", data, {
		login: true
	});
}
// 删除评测
export function evaluationDel(id) {
	return request.get("/evaluation/del", {id:id}, {
		login: true
	});
}
// 编辑评测
export function evaluationEdit(data) {
	return request.post("/evaluation/edit", data, {
		login: true
	});
}


/**
 * 门店列表
 */
export function evaluationStoreList(data) {
  return request.get("/evaluation/stores", data, { login: false });
}