import request from "@/utils/request";
// 用户主页 1
export function yIndex(from) {
	return request.get("/index" + '?from=' + from, {}, {
		login: false
	});
}
// 测评精选
export function yFeatured(data) {
	return request.get("/featured", data, {
		login: false
	});
}


// 测评、心愿 评论
export function yComment(data) {
	return request.get("/comment", data, {
		login: false
	});
}

export function yCommentMe(data) {
	return request.get("/comment", data, {
		login: false
	});
}

// 生成短链
export function createdUrlScheme(data) {
	return request.post("/wechat/created_url_scheme", data, {
		login: true
	});
}



// 发表留言
export function yCommentAdd(data) {
	return request.post("/comment/add", data, {
		login: true
	});
}

// // 留言板点赞
export function commentLike(data) {
	return request.get("/comment/like", data, {
		login: true
	});
}
export function commentUnLike(data) {
	return request.get("/comment/unlike", data, {
		login: true
	});
}


// 搜索标签组
export function searchLabel() {
	return request.get("/search/label_group", {}, {
		login: false
	});
}
// 搜索服务
export function searchGo(data) {
	return request.get("/search",data, {
		login: false
	});
}

export function vipExclusive(data) {
	return request.get("/vip/exclusive",{}, {
		login: false
	});
}

export function memberDesc(data) {
	return request.get("/member_desc",{}, {
		login: false
	});
}


// 
export function recommendWantToTest(data) {
	return request.get("/recommend/want_to_test", data, {
		login: false
	});
}
