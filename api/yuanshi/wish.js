import request from "@/utils/request";
import store from "@/store";
import {reportWishAttention,reportWishUnAttention} from "@/utils/ReportAnalytics.js"
// banner
export function wishIndex(id) {
	return request.get("/wish/index", {}, {
		login: false
	});
}
// 关注
export function wishAttention(id) {
	if(store.state.userInfo){
		reportWishAttention({uid:store.state.userInfo,wish_id:id})
	}
	return request.get("/wish/attention", {wish_id:id}, {
		login: true
	});
}
// 取消关注
export function wishUnAttention(id) {
	if(store.state.userInfo){
		reportWishUnAttention({uid:store.state.userInfo,wish_id:id})
	}
	return request.get("/wish/unattention", {wish_id:id}, {
		login: true
	});
}

// 想测 1
export function wishWant(id) {
	return request.get("/wish/want_test", {wish_id:id}, {
		login: true
	});
}

// 取消想测 1
export function wishUnWant(id) {
	return request.get("/wish/unwant_test", {wish_id:id}, {
		login: true
	});
}
// 发表留言
export function wishComment(data) {
	return request.post("/wish/comment", data, {
		login: false
	});
}
// 心愿详情
export function wishDetail(id) {
	return request.get("/wish/detail/"+id,{}, {
		login: false
	});
}

// 心愿单 1
export function wish(data) {
	return request.get("/wish", data, {
		login: false
	});
}

// 心愿单数量
export function wishNum() {
	return request.get("/wish/number", {}, {
		login: false
	});
}
// 编辑心愿
export function wishEdit(data) {
	return request.post("/wish/edit", data, {
		login: true
	});
}
// 发起心愿
export function wishAdd(data) {
	return request.post("/wish/add", data, {
		login: true
	});
}


