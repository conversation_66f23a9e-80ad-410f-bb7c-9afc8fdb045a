import request from "@/utils/request";

// 管理员禁言
export function handleUserMute(uid) {
	return request.get("/user/mute", {
		uid
	}, {
		login: true
	});
}
// 管理员删除评论
export function handleDelUserMute(obj = {}) {
	return request.get("/user/delete_message", obj, {
		login: true
	});
}
// 设置精华
export function handleSetupRefining(id) {
	return request.get("/user/setup_refining", {
		id
	}, {
		login: true
	});
}

export function getMyAllMessage(obj = {}) {
	return request.get("/user/my_message", obj, {
		login: true
	});
}

export function handleDelMyMessage(id) {
	return request.get("/user/del_my_message", {
		id
	}, {
		login: true
	});
}

export function handleDelMyComment(id) {
	return request.get("/user/del_my_comment", {
		id
	}, {
		login: true
	});
}


// 获取
export function getMyMessageDetail(id) {
	return request.get("/user/my_message_detail", {
		id
	}, {
		login: true
	});
}

// 编辑
export function editMyMessageDetail(obj={}) {
	return request.post("/user/edit_my_message", obj, {
		login: true
	});
}
