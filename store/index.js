import Vue from 'vue'
import Vuex from 'vuex'
import storage from '@/utils/storage.js'
import {
	getUserInfo
} from "@/api/user";

import {
	WECHAT_AUTH_BACK_URL
} from '@/config.js'

Vue.use(Vuex)

const store = new Vuex.Store({
	state: {
		token: null,
		userInfo: null,
		expires_time: null,
		spid: null, //推广人id
		code: null,
		home: false, //x-home组件是否显示
		// #ifdef H5
		nav: null, //仅H5生效
		self: null, //存储vue 仅H5生效
		isGOAuth: false, //是否已跳转至登录界面,防止路由重复注入
        auth_back_url: storage.get(WECHAT_AUTH_BACK_URL) || "",
		// #endif
		goName: storage.get("goName") || "",
        
		authPopupShow: false, //x-authorize 弹窗是否显示
        // #ifdef MP-WEIXIN
        agreementPopupShow: false, //x-agreement 隐私弹窗是否显示
        // #endif
		storeItems: storage.get("storeItems") || {},
		stytemInfo: uni.getSystemInfoSync(),
		navigationBarHeight: 44, //uni中导航栏固定高度为44px，tarbar固定高度为50px
		tarBarHeight: 50,
		isWidescreen: false ,//宽屏适配
		courseSearchId:0,
		isFromTimeline:false,
		isWxTabarAnimate:true,
        isPopPlay:false, //小窗模式播放状态
	},
	mutations: {
		UPDATE_FROM_TIMELINE(state,type){
			state.isFromTimeline = type;
		},
		LOGOUT(state) {
			state.token = null;
			state.expires_time = null;
			state.authPopupShow = false;
            // #ifdef MP-WEIXIN
            state.agreementPopupShow = false;
            // #endif
			// #ifdef H5
			state.isGOAuth = false;
			// #endif
			state.userInfo = {};
			storage.remove('token')
			storage.remove('expires_time')
		},
		LOGIN(state, {
			token,
			expires_time
		}) {
			state.token = token;
			state.expires_time = expires_time;
			// console.log('updata', state)
			storage.set('token', token)
			storage.set('expires_time', expires_time)
		},
		UPDATE_SOURCE_SEARCH_ID(state,id){
			state.courseSearchId = id;
		},
		UPDATE_FROM_TIMELINE(state,type){
			console.log('xxxx',type)
			state.isFromTimeline = type;
		},
		UPDATE_SPID(state, spid) {
			state.spid = spid;
		},
		UPDATE_USERINFO(state, userInfo) {
			state.userInfo = userInfo;
			storage.set('userInfo', userInfo)
		},
		SHOW_HOME(state) {
			state.home = true;
		},
		HIDE_HOME(state) {
			state.home = false;
		},
		// #ifdef H5
		SHOW_NAV(state) {
			state.nav = true;
		},
		HIDE_NAV(state) {
			state.nav = false;
		},
		SET_SELF(state, self) {
			state.self = self;
		},
		SET_GO_AUTH(state, self) {
			state.isGOAuth = self;
		},
		UPDATE_WIDESCREEN(state, val) {
			state.isWidescreen = val;
		},
		// #endif

		SHOW_AUTH_POPUP_SHOW(state) {
			// console.log('state.authPopupShow',state.authPopupShow)
			state.authPopupShow = true;
		},
		HIDE_AUTH_POPUP_SHOW(state) {
			state.authPopupShow = false;
		},
        
        POPPLAY_TRUE(state) {
        	state.isPopPlay = true;
        },
        POPPLAY_FALSE(state) {
        	state.isPopPlay = false;
        },
        
        // #ifdef MP-WEIXIN
        SHOW_AGREEMENT_POPUP_SHOW(state) {
        	// console.log('state.authPopupShow',state.authPopupShow)
        	state.agreementPopupShow = true;
        },
        HIDE_AGREEMENT_POPUP_SHOW(state) {
        	state.agreementPopupShow = false;
        },
        
        // #endif
        
		GET_STORE(state, storeItems) {
			state.storeItems = storeItems;
			storage.set("storeItems", storeItems);
		},
		GET_TO(state, goName) {
			state.goName = goName;
			storage.set("goName", goName);
		},
		UPDATE_WX_ANIMATE(state){
			state.isWxTabarAnimate = false;
		},
        UPDATE_WX_ANIMATE1(state){
            state.isWxTabarAnimate = true;
        }
	},
	actions: {
		USERINFO({
			state,
			commit
		}, force) {
			if (state.userInfo !== null && !force)
				return Promise.resolve(state.userInfo);
			else
				return new Promise(reslove => {
					getUserInfo().then(res => {
						commit("UPDATE_USERINFO", res.data);
						reslove(res.data);
					});
				}).catch(() => {
					uni.showToast({
						title: '信息获取失败'
					})
				});
		}
	},
	getters: {
		token: state => state.token,
		isLogin: state => !!state.token,
        isCache_key: state => !!state.cache_key,
		userInfo: state => state.userInfo || {},
		home: state => state.home,
		expires_time: state => state.expires_time,
		goName: state => state.goName,
		// #ifdef H5	
		nav: state => {
			// console.log("nav", state.nav);
			// state.self.$nextTick(() => {
			// 	let html = document.getElementsByTagName("uni-page-head")[0];
			// 	let display = state.nav ? 'block' : 'none';
			// 	html.style.display = display;
			// })
			return state.nav
		},
		// #endif
		authPopupShow: state => state.authPopupShow,
        // #ifdef MP-WEIXIN
        agreementPopupShow: state => state.agreementPopupShow,
        isPopPlay: state => state.isPopPlay,
        
        // #endif
		storeItems: state => state.storeItems,
		isWidescreen: state => state.isWidescreen,
		courseSearchId: state => state.courseSearchId,
		isFromTimeline: state => state.isFromTimeline,
		isWxTabarAnimate: state => state.isWxTabarAnimate
		
	}
})

export default store
