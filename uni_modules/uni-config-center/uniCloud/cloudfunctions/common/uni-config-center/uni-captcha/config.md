## 配置参数说明

由于json不可以写注释，故在此写说明

```js
{
	"mode": "svg", // 可选 svg bmp 默认使用svg格式验证码，nvue（uniapp X）只支持bmp格式验证码
	"background": "#FFFAE8", //验证码背景色，设置空字符`''`不使用背景颜色
	"size": 4, //验证码长度，最多 6 个字符
	// "expiresDate": 180		//验证码过期时间(s)
	// "noise": 6, 				//验证码干扰线条数
	// 以下是svg专属配置 -----------------------------------------------------------
	"width": 150, //图片宽度
	"height": 50, //图片高度
	// "color": false, 			//字体是否使用随机颜色，当设置`background`后恒为`true`
	// "fontSize": 40, 			//字体大小
	// "ignoreChars": '', 		//忽略那些字符
	// "mathExpr": false, 		//是否使用数学表达式
	// "mathMin": 1, 			//表达式所使用的最小数字
	// "mathMax": 9, 			//表达式所使用的最大数字
	// "mathOperator": ''	 	//表达式所使用的运算符，支持 `+`、`-`。不传随机使用
	// 以下是bmp专属配置 -----------------------------------------------------------
	"level": 2, // 干扰等级 1 低 2 中 3 高 4 极高 推荐使用2或3即可
}
```
