# 项目架构分析文档

## 一、项目结构概览
```
├── App.vue                # 主框架入口
├── main.js                # 应用入口文件
├── pages.json             # 页面配置文件
├── package.json           # 项目依赖配置
├── uni.scss               # 全局样式文件
├── config.js              # 配置文件
├── utils/                 # 工具类库
├── api/                   # 接口服务层
├── components/            # 组件库
├── pages/                 # 页面模块
├── store/                 # 状态管理
└── router/                # 路由配置
```

## 二、核心模块分析
### 1. 接口服务层 (api/)
```bash
├── activity.js          # 活动模块接口
├── admin.js             # 管理后台接口
├── community.js         # 社区模块接口
├── order.js             # 订单模块接口
├── store.js             # 商店模块接口
├── user.js              # 用户模块接口
└── yuanshi/             # 元识相关接口
    ├── evaluate.js
    ├── public.js
    └── wish.js
```

### 2. 组件库 (components/)
```bash
├── AddressWindow.vue    # 地址选择组件
├── CountDown.vue        # 倒计时组件
├── CouponListWindow.vue # 优惠券列表组件
├── lh-nav-scroll/       # 导航滚动组件
├── mp-html/             # 富文本解析组件
├── u-charts/            # 图表组件库
├── x-chat/              # 聊天组件
├── x-file-picker/       # 文件上传组件
└── x-swiper/            # 轮播图组件
```

### 3. 页面模块 (pages/)
```bash
├── activity/            # 活动中心模块
├── auth/                # 认证模块
├── index/               # 首页模块
├── my/                  # 我的页面模块
├── order/               # 订单管理模块
├── shop/                # 商品模块
├── tabBar/              # 底部导航模块
├── user/                # 用户中心模块
└── yuanshi/             # 元识功能模块
```

## 三、技术架构分层
```
┌───────────────────────┐
│       UI层            │
│  Vue组件 + 模板渲染   │
├───────────────────────┤
│      业务逻辑层        │
│  页面逻辑 + 交互处理  │
├───────────────────────┤
│      数据服务层        │
│  API接口 + 状态管理   │
├───────────────────────┤
│     基础设施层        │
│ 工具库 + 第三方服务   │
└───────────────────────┘
```

## 四、关键技术栈
- **框架**: Vue.js + Uni-app
- **状态管理**: Vuex + store模块化管理
- **路由**: Vue Router
- **网络请求**: 自定义uni_request.js封装
- **UI组件**: uView组件库 + 自定义组件
- **工具类**: 
  - bc.js (基础工具)
  - validate.js (验证工具)
  - oss.js (对象存储)
  - wechat.js (微信相关)

## 五、特殊模块说明
1. **自定义tabBar**:
   - 位于custom-tab-bar/目录
   - 支持微信小程序原生tabBar功能

2. **元识系统**:
   - 相关接口: api/yuanshi/
   - 核心组件: components/yuanshi/
   - 涉及模块: yuanshiDetail mixin

3. **云开发支持**:
   - uniCloud-alipay/目录
   - 包含云函数和数据库配置

## 六、项目配置要点
1. **跨域处理**:
   - 在manifest.json中配置模块权限
   - 通过uni_request.js统一处理请求拦截

2. **主题定制**:
   - 使用uni.scss全局样式变量
   - 通过static/css/目录进行样式覆盖

3. **小程序适配**:
   - pages.json配置页面路由
   - template.h5.html提供H5模板
