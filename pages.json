{
    "easycom": {
        "autoscan": true,
        "^u-(.*)": "uview-ui/components/u-$1/u-$1.vue",
        "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
    },
    "pages": [{
            "path": "pages/tabBar/index/index",
            "aliasPath": "/",
            "style": {
                "navigationBarTitleText": "首页",
                "backgroundColor": "#fff",
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/tabBar/course/index",
            "aliasPath": "/course",
            "style": {
                "navigationBarTitleText": "课程首页",
                "backgroundColor": "#fff"
            }
        },
        {
            "path": "pages/tabBar/user/user",
            "aliasPath": "/yuser",
            "style": {
                "navigationBarTitleText": "我的"
            }
        },
        {
            "path": "pages/index/addService",
            "aliasPath": "/addService",
            "style": {
                "navigationBarTitleText": "Yusi音乐审美",
                "navigationStyle": "default",
                "footer": true,
                "backgroundColor": "#fff"
            }
        },
        {
            "path": "pages/test/test",
            "aliasPath": "/test",
            "style": {
                "navigationBarTitleText": "测试用"
            }
        },
        {
            "path": "pages/my/user",
            "aliasPath": "/usercenter",
            "style": {
                "navigationBarTitleText": "个人中心"
            }
        },
        {
            "path": "pages/auth/auth",
            "aliasPath": "/auth",
            "style": {
                "navigationBarTitleText": ""
            }
        },
        {
            "path": "pages/404/404",
            "aliasPath": "/404",
            "style": {
                "navigationBarTitleText": "404"
            }
        },
        {
            "path": "pages/gameWebview/gameWebview",
            "aliasPath": "/webview",
            "style": {
                "navigationBarTitleText": "",
                "pageOrientation": "landscape",
                "navigationStyle": "custom"
            }
        }, 
        {
            "path": "pages/webview/webview",
            "aliasPath": "/webview",
            "style": {
                "navigationBarTitleText": ""
            }
        }, {
            "path": "pages/shopClass/shopClass",
            "aliasPath": "/category",
            "style": {
                "navigationBarTitleText": "产品分类",
                "backgroundColor": "#fff"
            }
        }, {
            "path": "pages/shopCart/shopCart",
            "aliasPath": "/shopcart",
            "style": {
                "navigationBarTitleText": "购物车"
            }
        }, {
            "path": "pages/map/map",
            "aliasPath": "/map",
            "style": {
                "navigationBarTitleText": "地图",
                "enablePullDownRefresh": false
            }

        }
    ],
    "globalStyle": {
        "navigationBarTextStyle": "black",
        "navigationBarTitleText": "着调儿",
        "navigationBarBackgroundColor": "#fff",
        "backgroundColor": "#FFFFFF",
        "rpxCalcMaxDeviceWidth": 960, // rpx 计算所支持的最大设备宽度，单位 px，默认值为 960
        "rpxCalcBaseDeviceWidth": 375, // 设备实际宽度超出 rpx 计算所支持的最大宽度时，rpx计算所采用的固定屏幕宽度，单位 px，默认值为 375
        "rpxCalcIncludeWidth": 750
    },
    // "leftWindow": {
    // 	"path": "responsive/left-window.vue", // 指定 leftWindow 页面文件
    // 	"style": {
    // 		"width": "600px"
    // 	}
    // },
    // "rightWindow": {
    // 	"path": "responsive/right-window.vue", // 指定 rightWindow 页面文件
    // 	"style": {
    // 		"width": "calc(100vw - 600px)" // 页面宽度
    // 	},
    // 	"matchMedia": {
    // 		"minWidth": 768 //生效条件，当窗口宽度大于768px时显示
    // 	}
    // },
    "subPackages": [{
            "root": "pages/yuanshi",
            "name": "YuanShi",
            "pages": [{
                    "path": "wish/detail",
                    "aliasPath": "/wishdetail",
                    "style": {
                        "navigationBarTitleText": "心愿单详情页",
                        "backgroundColor": "#fff"
                    }
                },
                {
                    "path": "wish/wish",
                    "aliasPath": "/ywish",
                    "style": {
                        "navigationBarTitleText": "想测",
                        "backgroundColor": "#fff"
                    }
                },
                {
                    "path": "wish/make",
                    "aliasPath": "/ymake",
                    "style": {
                        "navigationBarTitleText": "想测啥"
                    }
                },
                {
                    "path": "wish/info",
                    "aliasPath": "/winfo",
                    "style": {
                        "navigationBarTitleText": "想测"
                    }
                },
                {
                    "path": "search/search",
                    "aliasPath": "/ysearch",
                    "style": {
                        "navigationBarTitleText": "搜索",
                        "backgroundColor": "#fff"
                    }
                },
                {
                    "path": "user/fans",
                    "aliasPath": "/yfans",
                    "style": {
                        "navigationBarTitleText": "粉丝"
                    }
                },
                {
                    "path": "user/about",
                    "aliasPath": "/yabout",
                    "style": {
                        "navigationBarTitleText": "关于着调儿"
                    }
                },
                {
                    "path": "user/home",
                    "aliasPath": "/yhome",
                    "style": {
                        "navigationBarTitleText": "主页"
                    }
                },
                {
                    "path": "vip/ShowVip",
                    "aliasPath": "/yshowvip",
                    "style": {
                        "navigationBarTitleText": "着调儿卡"
                    }
                }, {
                    "path": "vip/BuyVip",
                    "aliasPath": "/ybuyvip",
                    "style": {
                        "navigationBarTitleText": "确认开通"
                    }
                }, {
                    "path": "vip/protocol",
                    "aliasPath": "/yprotocol",
                    "style": {
                        "navigationBarTitleText": "会员卡服务协议"
                    }
                }, {
                    "path": "vip/equity",
                    "aliasPath": "/yequity",
                    "style": {
                        "navigationBarTitleText": "着调儿卡"
                    }
                }, {
                    "path": "vip/upgrade",
                    "aliasPath": "/yupgrade",
                    "style": {
                        "navigationBarTitleText": "用户等级"
                    }
                },
                {
                    "path": "lotteryDraw",
                    "aliasPath": "/ylotteryDraw",
                    "style": {
                        "navigationBarTitleText": "抽奖",
                        "backgroundColor": "#fff"
                    }
                },
                {
                    "path": "chat/ChatList",
                    "aliasPath": "/ychatList",
                    "style": {
                        "navigationBarTitleText": "我的消息"
                    }
                },
                {
                    "path": "chat/ChatRoom",
                    "aliasPath": "/ychatRoom",
                    "style": {
                        "navigationBarTitleText": "聊天室"
                    }
                },
                {
                    "path": "evaluate/detail",
                    "aliasPath": "/evaluatedetail",

                    "style": {
                        "navigationBarTitleText": "测评详情页"
                    }
                }, {
                    "path": "evaluate/list",
                    "aliasPath": "/ylist",
                    "style": {
                        "navigationBarTitleText": "测评榜"
                    }
                }, {
                    "path": "evaluate/info",
                    "aliasPath": "/einfo",
                    "style": {
                        "navigationBarTitleText": "着调儿指数"
                    }
                }, {
                    "path": "evaluate/submit",
                    "aliasPath": "/ysubmit",
                    "style": {
                        "navigationBarTitleText": "填写测评"
                    }
                }
            ]
        }, {
            "root": "pages/shop",
            "name": "shop",
            "pages": [{
                    "path": "EvaluateList",
                    "aliasPath": "/evaluate_list",

                    "style": {
                        "navigationBarTitleText": "商品评分"
                    }
                },
                {
                    "path": "GoodsCollection",
                    "aliasPath": "/collection",
                    "style": {
                        "navigationBarTitleText": "收藏商品"
                    }
                },
                {
                    "path": "GoodsCon",
                    "aliasPath": "/detail",
                    "style": {
                        "navigationBarTitleText": "商品详情"
                    }
                },
                {
                    "path": "GoodSearch",
                    "aliasPath": "/search",
                    "style": {
                        "navigationBarTitleText": "搜索商品",
                        "backgroundColor": "#fff"
                    }
                },
                {
                    "path": "GoodsEvaluate",
                    "aliasPath": "/goods_evaluate",
                    "style": {
                        "navigationBarTitleText": "商品评价"
                    }
                },
                {
                    "path": "GoodsList",
                    "aliasPath": "/goods_list",
                    "style": {
                        "navigationBarTitleText": "商品列表",
                        "enablePullDownRefresh": true
                    }
                },
                // {
                // 	"path": "GoodsPromotion",
                // "aliasPath": "/promotion",
                // 	"style": {
                // 		"navigationBarTitleText": "促销单品",
                // 	}
                // },
                {
                    "path": "StoreList",
                    "aliasPath": "/shop/storeList",
                    "style": {
                        "navigationBarTitleText": "门店列表"
                    }
                },
                {
                    "path": "news/NewsDetail",
                    "aliasPath": "/news_detail",
                    "style": {
                        "navigationBarTitleText": "新闻详情",
                        "backgroundColor": "#fff"
                    }
                }
                // #ifndef MP-TOUTIAO
                ,
                {
                    "path": "HotNewGoods",
                    "aliasPath": "/hot_new_goods",
                    "style": {
                        "navigationBarTitleText": "热门榜单"
                    }
                },
                {
                    "path": "news/NewsList",
                    "aliasPath": "/news_list",
                    "style": {
                        "navigationBarTitleText": "新闻",
                        "backgroundColor": "#fff"
                    }
                }
                // #endif
            ]
        }, {
            "root": "pages/user",
            "name": "User",
            "pages": [
                // #ifdef H5
                {
                    "path": "ChangePassword",
                    "aliasPath": "/change_password",
                    "style": {
                        "navigationBarTitleText": "修改密码",
                        "backgroundColor": "#fff"
                    }
                },
                {
                    "path": "Login",
                    "aliasPath": "/login",

                    "style": {
                        "navigationBarTitleText": "登录"
                    }
                },
                {
                    "path": "RetrievePassword",
                    "aliasPath": "/retrieve_password",

                    "style": {
                        "navigationBarTitleText": "找回密码"
                    }
                },
                // #endif
                {
                    "path": "CustomerList",
                    "aliasPath": "/customer/list",
                    "style": {
                        "enablePullDownRefresh": true,
                        // "mp-weixin": {
                        //     "usingComponents": {
                        //         "chat": "plugin://myPlugin/chat"
                        //     }
                        // },
                        "navigationBarTitleText": "客服列表"
                        
                    }
                },
                {
                    "path": "studentProfile",
                    "aliasPath": "/studentProfile",
                
                    "style": {
                        "navigationBarTitleText": "学员档案"
                    }
                },
                {
                    "path": "PersonalData",
                    "aliasPath": "/user/data",

                    "style": {
                        "navigationBarTitleText": "个人资料"
                    }
                },
                {
                    "path": "Recharge",
                    "aliasPath": "/user/Recharge",

                    "style": {
                        "navigationBarTitleText": "余额充值"
                    }
                },
                {
                    "path": "BindingPhone",
                    "aliasPath": "/user/binding",

                    "style": {
                        "navigationBarTitleText": "绑定手机",
                        "backgroundColor": "#fff"
                    }
                },

                {
                    "path": "UserAccount",
                    "aliasPath": "/user/account",

                    "style": {
                        "navigationBarTitleText": "我的账户"
                    }
                },
                {
                    "path": "UserBill",
                    "aliasPath": "/user/bill",

                    "style": {
                        "navigationBarTitleText": "账单明细"
                    }
                },
                // {
                // 	"path": "showvip",
                // 	"aliasPath": "/uservip",

                // 	"style": {
                // 		"navigationBarTitleText": "会员中心"
                // 	}
                // },
                {
                    "path": "address/AddAddress",
                    "aliasPath": "/user/add_address",

                    "style": {
                        "navigationBarTitleText": "添加收货地址"
                    }
                },
                {
                    "path": "address/AddressManagement",
                    "aliasPath": "/user/add_manage",

                    "style": {
                        "navigationBarTitleText": "地址管理"
                    }
                },
                {
                    "path": "coupon/GetCoupon",
                    "aliasPath": "/user/get_coupon",

                    "style": {
                        "navigationBarTitleText": "领取优惠券"
                    }
                },
                {
                    "path": "coupon/UserCoupon",
                    "aliasPath": "/user/user_coupon",

                    "style": {
                        "navigationBarTitleText": "我的优惠券"
                    }
                },
                {
                    "path": "promotion/CashAudit",
                    "aliasPath": "/user/audit",

                    "style": {
                        "navigationBarTitleText": "提现审核"
                    }
                },
                {
                    "path": "promotion/CashRecord",
                    "aliasPath": "/user/cashrecord",

                    "style": {
                        "navigationBarTitleText": "提现记录"
                    }
                },
                {
                    "path": "promotion/CommissionDetails",
                    "aliasPath": "/user/commission",

                    "style": {
                        "navigationBarTitleText": "佣金明细"
                    }
                },
                {
                    "path": "promotion/CommissionRank",
                    "aliasPath": "/user/commissionrank",

                    "style": {
                        "navigationBarTitleText": "佣金排行"
                    }
                },
                {
                    "path": "promotion/Poster",
                    "aliasPath": "/user/promotionposter",

                    "style": {
                        "navigationBarTitleText": "分销海报",
                        "backgroundColor": "#a3a3a3"
                    }
                },
                {
                    "path": "promotion/PromoterList",
                    "aliasPath": "/user/promoter_list",
                    "style": {
                        "navigationBarTitleText": "推广人列表"
                    }
                },
                {
                    "path": "promotion/PromoterOrder",
                    "aliasPath": "/user/promoter_order",

                    "style": {
                        "navigationBarTitleText": "推广人订单"
                    }
                },
                {
                    "path": "promotion/PromoterRank",
                    "aliasPath": "/user/promoter_rank",

                    "style": {
                        "navigationBarTitleText": "推广人排行"
                    }
                },
                {
                    "path": "promotion/UserCash",
                    "aliasPath": "/user/user_cash",

                    "style": {
                        "navigationBarTitleText": "提现"
                    }
                },
                {
                    "path": "promotion/UserPromotion",
                    "aliasPath": "/user/user_promotion",

                    "style": {
                        "navigationBarTitleText": "我的推广"
                    }
                },
                {
                    "path": "signIn/Integral",
                    "aliasPath": "/user/integral",

                    "style": {
                        "navigationBarTitleText": "积分详情"
                    }
                },
                {
                    "path": "signIn/Sign",
                    "aliasPath": "/user/sign",

                    "style": {
                        "navigationBarTitleText": "签到"
                    }
                },
                {
                    "path": "signIn/SignRecord",
                    "aliasPath": "/user/sign_record",

                    "style": {
                        "navigationBarTitleText": "签到记录"
                    }
                }
            ]
        }, {
            "root": "pages/order",
            "name": "order",
            "pages": [{
                "path": "GoodsReturn",
                "aliasPath": "/order/refund",

                "style": {
                    "navigationBarTitleText": "申请退货"
                }
            }, {
                "path": "Logistics",
                "aliasPath": "/order/logistics",
                "style": {
                    "navigationBarTitleText": "物流信息"

                }
            }, {
                "path": "MyOrder",
                "aliasPath": "/order/myorder",

                "style": {
                    "navigationBarTitleText": "我的订单"
                }
            }, {
                "path": "OrderDetails",
                "aliasPath": "/order/detail",
                "style": {
                    "navigationBarTitleText": "订单详情"
                }
            }, {
                "path": "OrderSubmission",
                "aliasPath": "/order/submit",
                "style": {
                    "navigationBarTitleText": "提交订单"
                }
            }, {
                "path": "ReturnList",
                "aliasPath": "/order/refundlist",

                "style": {
                    "navigationBarTitleText": "退货列表"
                }
            }, {
                "path": "PaymentStatus",
                "aliasPath": "/order/status",
                "style": {
                    "navigationBarTitleText": "支付结果"
                }
            }]
        },
        {
            "root": "pages/activity",
            "pages": [{
                    "path": "BargainRecord",
                    "aliasPath": "/activity/bargainrecord",
                    "style": {
                        "navigationBarTitleText": "砍价记录"
                    }
                },
                {
                    "path": "DargainDetails",
                    "aliasPath": "/activity/bargaindetail",
                    "style": {
                        "navigationBarTitleText": "砍价详情",
                        "backgroundColor": "#e93323"
                    }
                },
                {
                    "path": "GoodsBargain",
                    "aliasPath": "/activity/goodsbargain",
                    "style": {
                        "navigationBarTitleText": "砍价列表",
                        "backgroundColor": "#e93323"
                    }
                },
                {
                    "path": "GoodsGroup",
                    "aliasPath": "/activity/goodgroup",
                    "style": {
                        "navigationBarTitleText": "拼团列表",
                        "backgroundColor": "#fa533d"
                    }
                },
                {
                    "path": "GoodsSeckill",
                    "aliasPath": "/activity/goodseckill",
                    "style": {
                        "navigationBarTitleText": "限时抢购",
                        "backgroundColor": "#ffffff"
                    }
                },
                {
                    "path": "GroupDetails",
                    "aliasPath": "/activity/groupdetails",
                    "style": {
                        "navigationBarTitleText": "拼团详情"
                    }
                }

                ,
                {
                    "path": "GroupRule",
                    "aliasPath": "/activity/grouprule",
                    "style": {
                        "navigationBarTitleText": "拼团"
                    }
                }

                , {
                    "path": "Poster",
                    "aliasPath": "/activity/poster",
                    "style": {
                        "navigationBarTitleText": "海报",
                        "backgroundColor": "#d22516"
                    }
                }
            ]
        },
        {
            "root": "pages/orderAdmin",
            "name": "admin",
            "pages": [{
                    "path": "AdminOrder",
                    "aliasPath": "/admin/order",
                    "style": {
                        "navigationBarTitleText": "订单详情"
                    }
                },
                {
                    "path": "AdminOrderList",
                    "aliasPath": "/admin/orderlist",
                    "style": {
                        "navigationBarTitleText": "订单列表"
                    }
                },
                {
                    "path": "GoodsDeliver",
                    "aliasPath": "/admin/delivery",
                    "style": {
                        "navigationBarTitleText": "订单发货"
                    }
                },
                {
                    "path": "OrderCancellation",
                    "aliasPath": "/admin/order_cancellation",
                    "style": {
                        "navigationBarTitleText": "订单核销",
                        "backgroundColor": "#fff"

                    }
                },
                {
                    "path": "OrderIndex",
                    "aliasPath": "/admin/index",
                    "style": {
                        "navigationBarTitleText": "订单首页"
                    }
                },
                {
                    "path": "Statistics",
                    "aliasPath": "/admin/statistics",
                    "style": {
                        "navigationBarTitleText": "订单数据统计"
                    }
                }
            ]
        },
        {
            "root": "pages/ycommunity",
            "name": "Ycommunity",
            "pages": [
            {
                "path": "newActivity/solicitActivity",
                "aliasPath": "/solicitActivity",
                "style": {
                    "navigationBarTitleText": "活动详情"
                }
            }, 
            {
                "path": "newActivity/detail",
                "aliasPath": "/ActivityDetail",
                "style": {
                    "navigationBarTitleText": "评论详情"
                }
            }, 
            {
                "path": "newActivity/submit",
                "aliasPath": "/ActivitySubmit",
                "style": {
                    "navigationBarTitleText": "提交评论"
                }
            }, 
            {
                "path": "newActivity/previewAssistant",
                "aliasPath": "/previewAssistant",
                "style": {
                    "navigationBarTitleText": "Yusi音乐审美"
                }
            }, 
            {
                "path": "pastActive/pastActive",
                "aliasPath": "/pastactive",
                "style": {
                    "navigationBarTitleText": "往期活动"
                }
            }, {
                "path": "shop/detail",
                "aliasPath": "/community/shopdetail",
                "style": {
                    "navigationBarTitleText": "活动详情"
                }
            }, {
                "path": "shop/singup",
                "aliasPath": "/community/shopsingup",
                "style": {
                    "navigationBarTitleText": "报名"
                }
            }, {
                "path": "shop/submit",
                "aliasPath": "/community/shopsubmit",
                "style": {
                    "navigationBarTitleText": "订单确认"
                }
            }, {
                "path": "shop/adminIndex",
                "aliasPath": "/community/adminIndex",
                "style": {
                    "navigationBarTitleText": "活动中台"
                }
            },
            {
                "path": "shop/adminDetails",
                "aliasPath": "/community/adminDetails",
                "style": {
                    "navigationBarTitleText": "活动预约"
                }
            },
            {
                "path": "shop/adminEdit",
                "aliasPath": "/community/adminEdit",
                "style": {
                    "navigationBarTitleText": "活动中台",
                    "enablePullDownRefresh": true
                }
            },
            {
                "path": "shop/userSubscribe",
                "aliasPath": "/community/userSubscribe",
                "style": {
                    "navigationBarTitleText": "活动预约"
                }
            },
            {
                "path": "shop/userSuccess",
                "aliasPath": "/community/userSuccess",
                "style": {
                    "navigationBarTitleText": "预约成功"
                }
            },
            {
                "path": "shop/agentAppoint",
                "aliasPath": "/community/agentAppoint",
                "style": {
                    "navigationBarTitleText": "活动预约"
                }
            },
            {
                "path": "order/detail",
                "aliasPath": "/community/orderdetail",
                "style": {
                    "navigationBarTitleText": "订单详情"
                }
            }, {
                "path": "order/list",
                "aliasPath": "/community/orderlist",
                "style": {
                    "navigationBarTitleText": "我的订单"
                }
            }, {
                "path": "comment/detail",
                "aliasPath": "/community/commentdetail",
                "style": {
                    "navigationBarTitleText": "评论详情"
                }
            }, {
                "path": "comment/submit",
                "aliasPath": "/community/commentsubmit",
                "style": {
                    "navigationBarTitleText": "提交评论"
                }
            }]
        },
        {
            "root": "pages/yknowledge",
            "name": "Yknowledge",
            "pages": [{
                "path": "course/receive-equity",
                "aliasPath": "/course/receive-equity",
                "style": {
                    "navigationBarTitleText": "领取课程权益"
                }
            },{
                "path": "course/detail",
                "aliasPath": "/course/detail",
                "style": {
                    "navigationBarTitleText": "课程详情"
                }
            }, {
                "path": "course/detail_list",
                "aliasPath": "/course/list",
                "style": {
                    "navigationBarTitleText": "课程详情",
                    "pageOrientation": "auto"
                }
            },{
                "path": "course/detail_share",
                "aliasPath": "/course/share",
                "style": {
                    "navigationBarTitleText": "分享得课程券"
                }
            }, {
                "path": "course/detail_webview",
                "aliasPath": "/course/webview",
                "style": {
                    "navigationBarTitleText": ""
                }
            }, 
            {
                "path": "course/detail_coupon",
                "aliasPath": "/course/coupon",
                "style": {
                    "navigationBarTitleText": "我的活动优惠券",
                    "pageOrientation": "auto"
                }
            }, {
                "path": "user/course",
                "aliasPath": "/course/course",
                "style": {
                    "navigationBarTitleText": "我的课程"
                }
            }, {
                "path": "user/comment",
                "aliasPath": "/course/ucomment",
                "style": {
                    "navigationBarTitleText": "我参与的评论",
                    // 字节因未申请自定义导航，所以不支持以下custom属性，上传时请注释
                    "navigationStyle": "custom"
                }
            }, {
                "path": "gift/give",
                "aliasPath": "/gift/give",
                "style": {
                    "navigationBarTitleText": "赠送礼物"
                }
            }, {
                "path": "comment/detail",
                "aliasPath": "/yknowledge/commentdetail",
                "style": {
                    "navigationBarTitleText": "评论详情"
                }
            }, {
                "path": "comment/submit",
                "aliasPath": "/yknowledge/commentsubmit",
                "style": {
                    "navigationBarTitleText": "提交评论"
                }
            }, {
                "path": "comment/submitWork",
                "aliasPath": "/yknowledge/commentsubmitwork",
                "style": {
                    "navigationBarTitleText": "提交作业"
                }
            }, {
                "path": "comment/submitWorkReply",
                "aliasPath": "/yknowledge/commentsubmitwork",
                "style": {
                    "navigationBarTitleText": "回复作业贴"
                }
            }]
        }
    ],


    "tabBar": {
        "color": "#282828",
        "selectedColor": "#e93323",
        "borderStyle": "white",
        "backgroundColor": "#ffffff",
        "height": "64rpx",
        "fontSize": "10px",
        "iconWidth": "30px",
        "spacing": "3px",
        "custom": true,
        "list": [
            
            {
                "pagePath": "pages/tabBar/index/index",
                "iconPath": "static/images/1-001.png",
                "selectedIconPath": "static/images/1-002.png"
            }, {
                "pagePath": "pages/tabBar/course/index",
                "iconPath": "static/images/zsff/2-002.png",
                // "selectedIconPath": "static/images/zsff/2-002.png"
                "selectedIconPath": "static/images/zsff/2-002.png"
            }, {
                "pagePath": "pages/tabBar/user/user",
                "iconPath": "static/images/4-001.png",
                "selectedIconPath": "static/images/4-002.png"
            }
        ],
        "copyList": [
            {
            "pagePath": "/pages/tabBar/index/index",
            "iconPath": "/static/images/1-001.png",
            "selectedIconPath": "/static/images/1-002.png"
        }, {
            "pagePath": "/pages/tabBar/course/index",
            "iconPath": "/static/images/zsff/2-002.png",
            // "selectedIconPath": "/static/images/zsff/2-002.png"
            "selectedIconPath": "/static/images/zsff/2-002.png"
        }, {
            "pagePath": "/pages/tabBar/user/user",
            "iconPath": "/static/images/4-001.png",
            "selectedIconPath": "/static/images/4-002.png"
        }
        ],
        "midButton": { //该属性只在app生效
            "width": "80px",
            "height": "50px",
            "text": "文字1111",
            "iconPath": "static/images/2-001.png",
            "iconWidth": "24px",
            "backgroundImage": "static/images/2-002.png"
        }
    }

}
