export default {
	data() {
		return {
			chatInfo: {

			}
		}
	},
	methods: {
		getSubmitInfo() {
			const {
				type,
				item,
				idx1,
				idx2,
				idx3,
				comment = ''
			} = this.chatInfo;
			const {
				special_id,
				id,
				source_id = 0,
				pid = 0
			} = item;
			// console.log(item)
			return {
				special_id,
				message_id: id,
				source_id,
				pid: type === 1 ? 0 : id,
				comment
			}
		},
        
        getActivityOnlineSubmitInfo() {
        	const {
        		type,
        		item,
        		idx1,
        		idx2,
        		idx3,
        		comment = ''
        	} = this.chatInfo;
        	const {
        		activity_id,
                id = 0,
        		message_id = 0,
        		pid = 0
        	} = item;
        	// console.log(item)
        	return {
        		activity_id,
        		message_id: message_id,
        		pid: type === 1 ? 0 : id,
        		comment
        	}
        },
        
		updateAddInfo(data) {
			const {
				type,
				idx1,
				idx2,
				idx3,
			} = this.chatInfo;
            this.$refs.xComment.updateMessage(type, idx1, idx2, data)
            this.$refs.xCommentWork.updateMessage(type, idx1, idx2, data)
		},
		updateLikeStars(item,index){
			this.$refs.xComment.updateLikeStars(item, index)
		}
	}
}
