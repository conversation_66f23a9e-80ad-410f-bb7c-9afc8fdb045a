export default {
    data() {
        return {
            innerAudioContext: null,
            autoplay: false,
            progress: 0,
            duration: '00:00',
            currentTime: '00:00',
            
        };
    },
    methods: {
        initAudioInfo() {
            this.autoplay = false;
            this.progress = 0;
            this.duration = '00:00';
            this.currentTime = '00:00';
            if (!this.innerAudioContext) {
                this.initAudioContext()
            }
        },
        destoryAudioCtx() {
            if (this.innerAudioContext) {
                this.innerAudioContext.pause();
                this.innerAudioContext.stop();
                this.innerAudioContext = null;
                this.initAudioInfo();
            }
        },
        
        initPlay(path,title,img) {
            // console.log('path--',path)
            if (path && this.innerAudioContext) {
                // getBackgroundAudioManager 必须设置 title 才能播放
                this.innerAudioContext.title = title || '音频文件';
                this.innerAudioContext.singer = 'Yusi音乐审美养成';
                this.innerAudioContext.coverImgUrl = img || '';
                this.innerAudioContext.src = path;
            }
        },
        
        playAudio(path,title,img) {
            if (!this.innerAudioContext) {
                this.initAudioContext();
            }
            
            // 如果有新的音频路径，更新音频信息
            if (path && path !== this.innerAudioContext.src) {
                this.innerAudioContext.title = title || '音频文件';
                this.innerAudioContext.singer = 'Yusi音乐审美养成';
                this.innerAudioContext.coverImgUrl = img || '';
                this.innerAudioContext.src = path;
            }
            
            // 切换播放状态
            if (!this.autoplay) {
                this.innerAudioContext.play();
            } else {
                this.innerAudioContext.pause();
            }
        },
        playPause() {
            this.innerAudioContext.pause();
            this.autoplay = false;
            this.$emit("onPause", true)
        },
        initAudioContext() {
            let _this = this;
            this.innerAudioContext = wx.getBackgroundAudioManager();
            this.innerAudioContext.onCanplay(() => {
                const timeId = setTimeout(() => {
                    if (_this.innerAudioContext && _this.innerAudioContext.duration) {
                        console.log('可播放', _this.innerAudioContext.duration)
                        _this.duration = _this.format(_this.innerAudioContext.duration);
                    }
                    clearTimeout(timeId)
                }, 100)
            });
            
            this.innerAudioContext.onPlay(() => {
                _this.autoplay = true;
                this.$emit("onPlay", true)
            });
            //音频暂停事件
            this.innerAudioContext.onPause(() => {
                _this.autoplay = false;
                this.$emit("onPause", true)
            });
            //音频停止事件
            this.innerAudioContext.onStop(() => {
                _this.autoplay = false;
                _this.progress = 0;
                _this.currentTime = '00:00';
                this.$emit("onStop", true);
            });
            //音频自然播放结束事件
            this.innerAudioContext.onEnded(() => {
                _this.autoplay = false;
                _this.progress = 0;
                _this.currentTime = '00:00'
                console.log('播放结束')
                this.$emit("yp_getended", true)
            });
            this.innerAudioContext.onTimeUpdate(() => {
                const {
                    duration,
                    currentTime
                } = this.innerAudioContext;
                
                // 确保duration有效
                if (duration && duration > 0) {
                    if (_this.duration === '00:00' || _this.duration === '0:00') {
                        _this.duration = _this.format(duration);
                    }
                    _this.currentTime = _this.format(currentTime);
                    _this.progress = parseInt((currentTime / duration) * 100);
                    _this.$emit("currentTime_audio", parseFloat(currentTime.toFixed(1)));
                }
            });
            this.innerAudioContext.onError(res => {
                _this.autoplay = false;
                _this.progress = 0;
                _this.currentTime = '00:00';
                console.log('背景音频播放出错---', res);
                _this.$emit("onError", res);
            });
            
        },
        format(interval) {
            interval = interval | 0
            const minute = interval / 60 | 0;
            let second = interval % 60;
            if (second < 10) {
                second = '0' + second;
            }
            return `${minute}:${second}`
        },
        //改变播放进度
        sliderChange(e) {
            // console.log('eee',e)
            const audioCtx = this.innerAudioContext;
            console.log(audioCtx)
            audioCtx && audioCtx.seek(parseInt((audioCtx.duration * this.progress) / 100));
        },
        playVideo() {
            if (this.innerAudioContext) {
                this.innerAudioContext.pause()
            }
        },
    },
    mounted() {
        this.initAudioContext();
    }
};
