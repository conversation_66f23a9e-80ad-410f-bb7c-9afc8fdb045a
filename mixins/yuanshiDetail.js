import {
	evaluationLabels,
	evaluationDetail
} from '@/api/yuanshi/evaluate.js';
import {
	wishDetail,
	wishWant,
	wishUnWant,
	wishLikecomment,
	wishAttention
} from '@/api/yuanshi/wish.js';
export default {
	data() {
		return {
			
		}
	},
	methods: {
		btnClick(type) {
				let wid = this.mixinsParam.wid;
				if (type) {
					this.detail.is_want = false;
					wishUnWant(wid).then(res => {
						this.$showToast('已取消');
					});
				} else {
					this.detail.is_want = true;
					wishWant(wid).then(res => {
						this.$showToast('想去+1');
					});
				}
		},
		
		goFollow(type, id, idx) {
			if (type) {
				userUnFollow(id).then(res => {
					this.detail.wantInfo[index].is_follow = false;
					this.$showToast('已取消');
				});
			} else {
				userFollow(id).then(res => {
					this.$showToast('跟随成功');
					this.detail.wantInfo[index].is_follow = true;
				});
			}
		},
		wishAttention() {
			wishAttention(this.mixinsParam.wid).then(res => {
				this.$showToast('关注成功');
			});
		}
	}
}
