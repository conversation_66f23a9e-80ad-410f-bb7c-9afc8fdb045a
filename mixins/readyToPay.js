import {
	postCartAdd
} from '@/api/store';
import {
	checkLogin,
	autoAuth
} from '@/utils/common.js';
	import { ossImgParams } from '@/utils/oss.js';
export default {
	data() {
		return {
			attr: {
				cartAttr: false,
				productAttr: [],
				productSelect: {}
			},
			isOpen: false, //是否打开属性组件
			productValue: [],
			attrTxt: '请选择',
			attrValue: '',
			cart_num: 1, //购买数量
			storeInfo: {}
		};
	},
	methods: {
		// 购买相关,使用之前
		//默认选中属性；
		DefaultSelect: function() {
			let productAttr = this.attr.productAttr,
				value = [];
			// for (var key in this.productValue) {
			// 	if (this.productValue[key].stock > 0) {
			// 		value = this.attr.productAttr.length ? key.split(',') : [];
			// 		break;
			// 	}
			// }
            console.log('productAttr--',productAttr)
			for (let i = 0; i < productAttr.length; i++) {
				this.$set(productAttr[i], 'index', productAttr[i].attr_values[0]);
                value.push(productAttr[i].index)
			}
			//sort();排序函数:数字-英文-汉字；
			let productSelect = this.productValue[value.sort().join(',')];
			// const ossParams = { w: 67,h:90,m:'fill', q: 90 } ossImgParams(ossParams)
			let cart_num = 1;
			if (this.attrProductSelectType && this.attrProductSelectType === 'vip') {
				cart_num = this.attrProductSelectCart_numVip
			} else {
				cart_num = this.attrProductSelectCart_numNormal || 1
			}
			if (productSelect && productAttr.length) {
				if (this.attrProductSelectType && this.attrProductSelectType === 'vip') {
					this.$set(this.attr.productSelect, 'store_name', this.storeInfo.title);
					this.$set(this.attr.productSelect, 'quota', productSelect.quota || 0);
					this.$set(this.attr.productSelect, 'quota_show', productSelect.quota_show || 0);
				} else {
					this.$set(this.attr.productSelect, 'store_name', this.storeInfo.store_name);
				}
				this.$set(this.attr.productSelect, 'image', productSelect.image );
				this.$set(this.attr.productSelect, 'price', productSelect.price);
				this.$set(this.attr.productSelect, 'stock', productSelect.stock);
				this.$set(this.attr.productSelect, 'unique', productSelect.unique);
				this.$set(this.attr.productSelect, 'cart_num', cart_num);
				this.$set(this, 'attrValue', value.sort().join(','));
				this.$set(this, 'attrTxt', '已选择');
				// console.log('1',this.attr)

			} else if (!productSelect && productAttr.length) {
				if (this.attrProductSelectType && this.attrProductSelectType === 'vip') {
					this.$set(this.attr.productSelect, 'store_name', this.storeInfo.title);
					this.$set(this.attr.productSelect, 'quota', productSelect.quota || 0);
					this.$set(this.attr.productSelect, 'quota_show', productSelect.quota_show || 0);
				} else {
					this.$set(this.attr.productSelect, 'store_name', this.storeInfo.store_name);
				}
				this.$set(this.attr.productSelect, 'image', this.storeInfo.image );
				this.$set(this.attr.productSelect, 'price', this.storeInfo.price);
				this.$set(this.attr.productSelect, 'stock', 0);
				this.$set(this.attr.productSelect, 'unique', '');
				this.$set(this.attr.productSelect, 'cart_num', cart_num);
				this.$set(this, 'attrValue', '');
				this.$set(this, 'attrTxt', '请选择');
				// console.log('2')
			} else if (!productSelect && !productAttr.length) {
				if (this.attrProductSelectType && this.attrProductSelectType === 'vip') {
					this.$set(this.attr.productSelect, 'store_name', this.storeInfo.title);
					this.$set(this.attr.productSelect, 'quota', this.storeInfo.quota || 0);
					this.$set(this.attr.productSelect, 'quota_show', this.storeInfo.quota_show || 0);
				} else {
					this.$set(this.attr.productSelect, 'store_name', this.storeInfo.store_name);
				}
				this.$set(this.attr.productSelect, 'image', this.storeInfo.image );
				this.$set(this.attr.productSelect, 'price', this.storeInfo.price);
				this.$set(this.attr.productSelect, 'stock', this.storeInfo.stock);
				this.$set(this.attr.productSelect, 'unique', this.storeInfo.unique || '');
				this.$set(this.attr.productSelect, 'cart_num', cart_num);
				this.$set(this, 'attrValue', '');
				this.$set(this, 'attrTxt', '请选择');
				// console.log('3', this.attr.productSelect)
			}

		},
		//将父级向子集多次传送的函数合二为一；
		changeFun: function(opt) {
			if (typeof opt !== 'object') opt = {};
			let action = opt.action || '';
			let value = opt.value === undefined ? '' : opt.value;
			this[action] && this[action](value);
		},
		changeattr: function(msg) {
			this.attr.cartAttr = msg;
			this.isOpen = false;
			if (this.attrProductSelectType === 'vip') {
				console.log('xxxxxx'); //关闭时所有恢复正常
				this.normalBuy(true)
			}
		},
		//购物车；
		ChangeCartNum: function(changeValue, type) {
			this.attrProductSelectType = type ? type : this.attrProductSelectType; //仅在测评相关页面生效
			let attrProductSelectType = this.attrProductSelectType;
			if (attrProductSelectType === 'normal') {
				this.$set(this.attr.productSelect, 'cart_num', this.attrProductSelectCart_numNormal);
			} else if (attrProductSelectType === 'vip') {
				this.$set(this.attr.productSelect, 'cart_num', this.attrProductSelectCart_numVip);
			}
			//changeValue:是否 加|减
			//获取当前变动属性
			let productSelect = this.productValue[this.attrValue];
			// //如果没有属性,赋值给商品默认库存
			if (productSelect === undefined && !this.attr.productAttr.length) productSelect = this.attr.productSelect;
			// //无属性值即库存为0；不存在加减；
			if (productSelect === undefined) return;
			let stock = productSelect.stock || 0,
				quota = productSelect.quota || 0;
			let num = this.attr.productSelect;
			if (changeValue) {
				num.cart_num++;
				if (attrProductSelectType === 'normal') {

					if (num.cart_num > stock) {
						this.$set(this.attr.productSelect, 'cart_num', stock ? stock : 1);
						this.$set(this, 'cart_num', stock ? stock : 1);
					}

					this.attrProductSelectCart_numNormal = num.cart_num
				} else if (attrProductSelectType === 'vip') {
					console.log(productSelect)
					let quota = productSelect.quota;
					if (num.cart_num > quota) {
						this.$set(this.attr.productSelect, "cart_num", quota);
						this.attrProductSelectCart_numVip = num.quota
					} else {
						this.attrProductSelectCart_numVip = num.cart_num
					}
				}
			} else {
				num.cart_num--;
				if (num.cart_num < 1) {
					this.$set(this.attr.productSelect, 'cart_num', 1);
					this.$set(this, 'cart_num', 1);
				}

				if (attrProductSelectType === 'normal') {
					this.attrProductSelectCart_numNormal = num.cart_num
				} else if (attrProductSelectType === 'vip') {
					this.attrProductSelectCart_numVip = num.cart_num
				}
			}
		},
		//选择属性；
		ChangeAttr: function(res) {
			let productSelect = this.productValue[res];
			// console.log('productSelect', this.productValue)
			// console.log('productSelect', res)
			// console.log('productSelect', productSelect)
			if (productSelect) {
				if (this.attrProductSelectType && this.attrProductSelectType === 'vip') {
					this.$set(this.attr.productSelect, 'quota', productSelect.quota || 0);
					this.$set(this.attr.productSelect, 'quota_show', productSelect.quota_show || 0);
				}
				this.$set(this.attr.productSelect, 'image', productSelect.image + ossImgParams({ w: 55,h:75,m:'fill', q: 80 }));
				this.$set(this.attr.productSelect, 'price', productSelect.price);
				this.$set(this.attr.productSelect, 'stock', productSelect.stock);
				this.$set(this.attr.productSelect, 'unique', productSelect.unique);
				this.$set(this.attr.productSelect, 'cart_num', 1);
				this.$set(this, 'attrValue', res);
				this.$set(this, 'attrTxt', '已选择');
			} else {
				this.$set(this.attr.productSelect, 'image', this.storeInfo.image + ossImgParams({ w: 75,h:75,m:'fill', q: 80 }) );
				this.$set(this.attr.productSelect, 'price', this.storeInfo.price);
				if (this.attrProductSelectType && this.attrProductSelectType === 'vip') {
					this.$set(this.attr.productSelect, 'quota', 0);
					this.$set(this.attr.productSelect, 'quota_show', 0);
				}
				this.$set(this.attr.productSelect, 'stock', 0);
				this.$set(this.attr.productSelect, 'unique', '');
				this.$set(this.attr.productSelect, 'cart_num', 0);
				this.$set(this, 'attrValue', '');
				this.$set(this, 'attrTxt', '请选择');
			}
		},
		//立即购买
		tapBuy: function() {
			//  1=直接购买
			if (checkLogin()) {
				this.goCat(1);
			} else {
				autoAuth()
			}
		},
		// 加入购物车；
		goCat: function(news) {
			let that = this,
				productSelect = that.productValue[this.attrValue];
			//打开属性
			if (that.attrValue) {
				//默认选中了属性，但是没有打开过属性弹窗还是自动打开让用户查看默认选中的属性
				that.attr.cartAttr = !that.isOpen ? true : false;
			} else {
				if (that.isOpen) that.attr.cartAttr = true;
				else that.attr.cartAttr = !that.attr.cartAttr;
			}
			//只有关闭属性弹窗时进行加入购物车
			if (that.attr.cartAttr === true && that.isOpen === false) return (that.isOpen = true);
			if (!this.attr.productSelect.cart_num || parseInt(this.attr.productSelect.cart_num) <= 0) return that.$showToast(
				'请输入购买数量');
			//如果有属性,没有选择,提示用户选择
			if (that.attr.productAttr.length && productSelect === undefined && that.isOpen === true) return that.$showToast(
				'产品库存不足，请选择其它');
			let q = {
				productId: this.mixinsParam.productId,
				cartNum: that.attr.productSelect.cart_num,
				new: news,
				uniqueId: that.attr.productSelect !== undefined ? that.attr.productSelect.unique : ''
			};
			if (this.mixinsParam.secKillId) {
				q.secKillId = this.mixinsParam.secKillId;
			}
			if (this.mixinsParam.wishId) {
				q.wishId = parseInt(this.mixinsParam.wishId)
			}
			postCartAdd(q)
				.then(function(res) {
					that.isOpen = false;
					that.attr.cartAttr = false;
					if (news) {
						that.$navigator('/pages/order/OrderSubmission?cartId=' + res.data.cartId + '&isNew=' + (that.mixinsParam.wishId ?
							1 : 0));
					}else {
							that.$showToast('添加购物车成功');
							that.getCartCount(true);
							}
				})
				.catch(res => {
					that.isOpen = false;
					return that.$showToast(res.msg || res);
				});
		},
		// 购买相关结束
	}
};
