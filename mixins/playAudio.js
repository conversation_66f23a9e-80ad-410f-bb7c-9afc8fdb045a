export default {
    data() {
        return {
            autoplay: false,
            obeyMuteSwitch:false,
            progress: 0,
            innerAudioContext: null,
            duration: '00:00',
            currentTime: '00:00'
        };
    },
    methods: {
        initAudioInfo() {
            this.autoplay = false;
            this.obeyMuteSwitch = false;
            this.progress = 0;
            this.duration = '00:00';
            this.currentTime = '00:00';
            if (!this.innerAudioContext) {
                this.initAudioContext()
            }
        },
        destoryAudioCtx() {
            if (this.innerAudioContext) {
                this.innerAudioContext.pause();
                this.innerAudioContext = null;
                if (this.innerAudioContext == null) {
                    this.initAudioInfo();
                }
            }
        },
        initAudioContext() {
            let _this = this;
            let ctx = uni.createInnerAudioContext();
            ctx.obeyMuteSwitch = false;
            console.log('音频控制对象', ctx)
            // #ifndef MP-TOUTIAO
            ctx.autoplay = false;
            // #endif
            // #ifdef MP-WEIXIN
            uni.setInnerAudioOption({  
                obeyMuteSwitch: false  
            })
            // #endif
            _this.innerAudioContext = ctx;
            // console.log('获取音频对象',this.innerAudioContext)
            ctx.onCanplay(() => {
                const timeId = setTimeout(() => {
                    console.log('可播放', ctx.duration)

                    _this.duration = _this.format(ctx.duration);
                    clearTimeout(timeId)
                }, 500)
            });
            ctx.onPlay(() => {
                _this.autoplay = true;
            });
            //音频暂停事件
            ctx.onPause(() => {
                _this.autoplay = false;
            });
            //音频停止事件
            ctx.onStop(() => {});
            //音频自然播放结束事件
            ctx.onEnded(() => {
                _this.autoplay = false;
                _this.progress = 0;
                _this.currentTime = '00:00'
            });
            ctx.onTimeUpdate(() => {
                const {
                    duration,
                    currentTime
                } = ctx;
                if (_this.duration === '0:00') {
                    _this.duration = _this.format(ctx.duration);
                }

                _this.currentTime = this.format(currentTime);
                _this.progress = parseInt((currentTime / duration) * 100);
            });
            ctx.onError(res => {
                _this.autoplay = false;
                // _this.$showToast('播放出错')
                console.log('err---', res)
            });
        },
        format(interval) {
            interval = interval | 0
            const minute = interval / 60 | 0;
            let second = interval % 60;
            if (second < 10) {
                second = '0' + second;
            }
            return `${minute}:${second}`
        },
        initPlay(path) {
            console.log('path--',path)
            let that = this;
            if (path) {
                that.innerAudioContext.src = path;
            }
        },
        playAudio(path) {
            console.log('autoplay=====',this.autoplay)
            console.log('src=====',this.innerAudioContext.src)
            if (path) {
                this.innerAudioContext.src = path;
                // 小程序录制本地文件播放后无法暂停， 网络文件不受限制，
                // https://kbm.arthorize.com/t01c5khqt7vau69bd29jwrr29vs7cyk4.wav?auth_key=1698913634-d81b4966fe04e6c71902a8de4957ddc8-0-c62151b45790f5b2fa6b4b8fa279a660
            }
            if (!this.autoplay) {
                this.innerAudioContext.play();
                this.autoplay = true;
            } else {
                this.innerAudioContext.pause();
                this.autoplay = false;
            }
        },
        playPause() {
            this.innerAudioContext.pause();
            this.autoplay = false;
        },
        //改变播放进度
        sliderChange(e) {
            const audioCtx = this.innerAudioContext;
            console.log(audioCtx)
            audioCtx && audioCtx.seek(parseInt((audioCtx.duration * this.progress) / 100));
        },
        playVideo() {
            if (this.innerAudioContext) {
                this.innerAudioContext.pause()
            }
        },
    },
    mounted() {
        this.initAudioContext();
    }
};
