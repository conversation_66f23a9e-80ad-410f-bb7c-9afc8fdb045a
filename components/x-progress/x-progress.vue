<template>
	<view class="xProgress" :id="id" @touchstart="touchstart" @touchmove="touchmove" @touchend="touchend">
		<view class="tips absolute" v-show="tipsShow" :style="{left:(percentData-20)+'%'}">{{(percentData / 100 *  5).toFixed(1)}}</view>
		<template v-if="diy">
			<view class="bg_diy" :style="{height:strokeWidth+'rpx'}">
				<view class="bg " :style="{height:strokeWidth+'rpx',background:'url('+bg+')',backgroundSize:backgroundSize}"></view>
				<view class="bg "  :style="{width:percentData+'%',height:strokeWidth+'rpx',background:'url('+active+')',backgroundSize:backgroundSize}"></view>
			</view>
		</template>
		<template v-else>
			<progress :percent="percentData" :stroke-width="strokeWidth" :border-radius="borderRadius" :activeColor="active" :backgroundColor="bg" />
		</template>
	</view>
</template>

<script>
export default {
	props: {
		isMove: {
			//允许滑动
			type: Boolean,
			default: true
		},
		// 已选择的进度条的颜色 diy时图片路径
		active: {
			type: String,
			default: '#5A3925'
		},
		// 未选择进度条颜色  diy时图片路径
		bg: {
			type: String,
			default: '#EFB039'
		},
		strokeWidth: { //默认px,diy时单位为rpx
			type: Number,
			default: 6
		},
		// 小程序生效
		borderRadius: {
			type: [Number, String],
			default: 0
		},
		percent: {
			type: Number,
			default: 0
		},
		diy: {
			//允许滑动
			type: Boolean,
			default: false
		},
		tips: {
			//当前分数提示
			type: Boolean,
			default: false
		},
		backgroundSize:{
			type: String,
			default: '80rpx 80rpx'
		}
	},
	components: {},
	data() {
		return {
			percentData: this.percent>100?100:this.percent,
			beginX: 0, //进度条初始坐标
			endX: 0, //结束坐标
			id: 'xProgress' + parseInt(new Date().getTime()),
			isInt: false,
			tipsShow:false
		};
	},
	watch: {
		percent(a, b) {
			this.percentData = a>100?100:a;
			this.initQuery();
		}
	},
	methods: {
		touchstart(e) {},
		touchmove(e) {
			if (!this.isMove) return;
			const { clientX } = e.changedTouches[0];
			let calc = clientX - this.beginX,
				stopX;
			if (calc >= this.endX) {
				// 超过当前进度条最大值,显示最大值;
				stopX = this.endX;
			} else if (clientX <= this.beginX) {
				// 小于当前进度条最小值,显示0;
				stopX = 0;
			} else {
				// 在范围内
				stopX = calc;
			}
			let percent = Number((stopX / this.endX)*100);//
			
			if(this.tips){
				this.tipsShow =  true;
			}
			
			if(percent<20){
				// console.log('xxxxxxx',res)
				percent = 20;
				this.$showToast('最低分为1分');
			}

			this.percentData = percent.toFixed(2) || 0;
			this.$emit('move', this.percentData);
		},
		touchend(e) {
			this.tipsShow = false;
			this.$emit('end', this.percentData);
		},
		initQuery() {
			const query = uni.createSelectorQuery().in(this);
			query
				.select('#' + this.id)
				.boundingClientRect(res => {
					this.beginX = res.left;
					this.endX = res.right - res.left;
				})
				.exec();
		}
	},
	mounted() {
		this.$nextTick(res => {
			this.initQuery();
		});
	},
	onLoad(option) {}
};
</script>

<style scoped lang="scss">
.xProgress {
	position: relative;
	width: 100%;
	.tips{
		top: -70rpx;	
		width: 64rpx;
		height: 50rpx;
		background: #50506f;
		border-radius: 16rpx;
		font-size: 28rpx;
		font-family: PingFang SC, PingFang SC-Bold;
		font-weight: 700;
		text-align: left;
		color: #ffffff;	
		padding: 4rpx 0;
		text-align: center;
		&::after{
			content: '';
			position: absolute;
			bottom: -14rpx;
			left: calc(50% - 7rpx);
			width: 0rpx;
			height: 0rpx;
			background: #50506f;
			border-top: 14rpx solid  #50506f;
			border-right: 7rpx solid  #fff;
			border-left: 7rpx solid  #fff;
			// border: ;
		}
	}
	.bg_diy {
		position: relative;
		width: 100%;
		.bg {
			position: absolute;
			width: 100%;
			top:0;
			background-repeat: repeat-x !important;
			overflow: hidden;
		}
	}
}
</style>
