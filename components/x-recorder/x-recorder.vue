<template>
    <view>
        <view class="x-record" v-if="status > 0">
            <view v-if="status < 4" class="record">
                <view class="record_l ">
                    <view class="btn" @click="cancelRecord">取消</view>
                    <view class="time">{{ formatTime }}</view>
                </view>
                <view class="record_r ">
                    <view class="icon" @click="recordChange">
                        <u-icon :name="status === 1 || status === 3 ? 'pause-circle-fill' : 'play-circle-fill'"
                            size="40"></u-icon>
                    </view>
                    <view class="btn " @click="stopRecord">完成</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    // #ifdef H5
    import Recorder from './recorder.js';
    // #endif
    var recorderManager = '';
    let setInter = null;
    export default {
        name: 'xRecorder',
        props: {
            duration: {
                type: Number,
                default: 60000 // ，单位 ms 最大值 600000（10 分钟）,默认值 60000（1 分钟）
            },
            numberOfChannels: {
                type: Number,
                default: 1 //录音通道数，有效值 1/2
            },
            sampleRate: {
                type: Number,
                default: 8000 //采样率，有效值 8000/16000/44100
            },
            encodeBitRate: {
                type: Number,
                default: 16000 //编码码率，采样率和码率有一定要求  https://uniapp.dcloud.io/api/media/record-manager?id=getrecordermanager
            },
            format: {
                type: String,
                default: 'mp3' //音频格式，有效值 aac/mp3/wav/PCM
            },
            frameSize: {
                type: String,
                default: '' //指定帧大小，单位 KB  暂仅支持 mp3 格式。
            },
            sampleBits: {
                type: Number,
                default: 16 //仅h5支持 有效值 8/16
            }
        },
        data() {
            return {
                voice: {},
                voiceTime: 0,
                formatTime: '00:00',
                status: 0 //1开始，2暂停，3继续，0结束
            };
        },
        watch: {
            voiceTime(a) {
                let time = parseInt(a / 60);
                if (time > 0) {
                    a = a - time * 60;
                }
                let str = time < 10 ? `0${time}` : time;
                this.formatTime = str + ':' + (a < 10 ? `0${a}` : a);
            }
        },
        mounted() {
            // #ifdef MP
            recorderManager = uni.getRecorderManager();
            this.initRecorderManager();
            // #endif
            // #ifdef H5
            const {
                format,
                sampleRate,
                numberOfChannels,
                sampleBits
            } = this;
            recorderManager = new Recorder({
                bitRate: sampleBits,
                sampleRate,
                numChannels: numberOfChannels
            });
            // #endif
        },
        methods: {
            initRecorderManager() {
                let _this = this;
                recorderManager.onStart(function(res) {
                    _this.status = 1;
                });
                recorderManager.onPause(function(res) {
                    _this.status = 2;
                });
                recorderManager.onFrameRecorded(function(res) {});
                recorderManager.onStop(function(res) {
                    _this.status = 0;
                    let obj = {
                        path: res.tempFilePath,
                        ...res
                    };
                    console.log('recorder stopres', res);
                    console.log('recorder stop', obj);
                    _this.voice = obj;
                    _this.$emit('stop', obj);
                    _this.$emit('select', obj, 'recorder', [obj]);
                });
                recorderManager.onError(function(res) {});
            },
            recordChange() {
                const {
                    status
                } = this;
                if (status === 2) {
                    this.status = 3;
                    this.resumeRecord();
                } else {
                    this.status = 2;
                    this.pauseRecord();
                }
            },
            startRecord() {
                let _this = this;
                console.log('开始录音');
                if (setInter) {
                    clearInterval(setInter);
                    this.voiceTime = 0;
                }
                // #ifdef MP
                const {
                    duration,
                    sampleRate,
                    numberOfChannels,
                    encodeBitRate,
                    format,
                    frameSize
                } = this;
                uni.authorize({
                    scope: 'scope.record',
                    success() {
                        _this.status = 1;
                        setInter = setInterval(() => {
                            _this.voiceInterval();
                        }, 1000);
                        console.log('duration---', duration)
                        console.log('sampleRate---', sampleRate)
                        console.log('numberOfChannels---', numberOfChannels)
                        console.log('encodeBitRate---', encodeBitRate)
                        console.log('format---', format)
                        console.log('frameSize---', frameSize)
                        recorderManager.start({
                            duration,
                            sampleRate,
                            numberOfChannels,
                            encodeBitRate,
                            // #ifndef MP-TOUTIAO
                            format,
                            // #endif
                            frameSize
                        });
                        _this.$emit('start');
                    },
                    fail() {
                        _this.status = 0;
                        uni.showModal({
                            title: '提示',
                            content: '请允许使用您的麦克风',
                            confirmText: '去允许',
                            success: function(res) {
                                if (res.confirm) {
                                    uni.openSetting({
                                        success(res) {
                                            console.log(res.authSetting);
                                        }
                                    });
                                } else if (res.cancel) {}
                            }
                        });
                    }
                });

                // #endif
                // #ifdef H5
                recorderManager.start();
                this.$emit('start');
                // #endif
            },
            pauseRecord() {
                console.log('暂停录音');
                this.status = 2;
                setInter && clearInterval(setInter);
                recorderManager.pause();
                this.$emit('pause');
            },
            resumeRecord() {
                console.log('继续录音');
                this.status = 3;
                setInter = setInterval(() => {
                    this.voiceInterval();
                }, 1000);
                recorderManager.resume();
                this.$emit('resume');
            },
            stopRecord() {
                console.log('录音结束');
                this.status = 0;
                setInter && clearInterval(setInter);
                recorderManager.stop();
                // #ifdef H5
                console.log(recorderManager.getPCMBlob());
                // #endif
            },
            // 取消事件
            cancelRecord() {
                this.status = 0;
                this.$emit('cancel');
                this.$emit('select', null, 'recorder', null);
            },
            voiceInterval() {
                const {
                    duration,
                    voiceTime
                } = this;
                if (voiceTime * 1000 < duration) {
                    this.voiceTime = voiceTime + 1;
                } else {
                    clearInterval(setInter);
                }
            }
        }
    };
</script>

<style scoped lang="scss">
    .x-record {
        background: #f2f5f8;
        border: 6rpx solid #ffffff;
        border-radius: 30rpx;
        box-shadow: 0px 4rpx 20rpx 0px rgba(0, 0, 0, 0.06);
        padding: 38rpx 26rpx;

        .record {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .btn {
                width: 100rpx;
                text-align: center;
                font-size: 30rpx;
                color: #666;
                height: 40rpx;
                line-height: 36rpx;
            }

            .record_l,
            .record_r {
                display: flex;
                align-items: center;
            }

            .record_r {
                .btn {
                    font-weight: bold;
                }
            }

            .time {
                // width: 200rpx;
                height: 40rpx;
                line-height: 40rpx;
            }

            .icon {
                line-height: normal;
                // width: 200rpx;
            }
        }

        .play {
            display: flex;

            .play_r {
                flex: 1;
            }
        }
    }
</style>
