<template>
	<view class="promotionGood" v-if="benefit.length > 0">
		<view @click="goDetail(item)" v-for="(item, index) in benefit" :key="index" class="item acea-row row-between-wrapper">
			<view class="pictrue">
				<image :src="item.image" alt="img" class="image"></image>
				<span class="pictrue_log pictrue_log_class" v-if="item.activity && item.activity.type === '1'">秒杀</span>
				<span class="pictrue_log pictrue_log_class" v-if="item.activity && item.activity.type === '2'">砍价</span>
				<span class="pictrue_log pictrue_log_class" v-if="item.activity && item.activity.type === '3'">拼团</span>
			</view>
			<view class="text">
				<view class="name line1">{{ item.store_name }}</view>
				<view class="sp-money acea-row">
					<view class="moneyCon">
						促销价: ￥
						<span class="num">{{ item.price }}</span>
					</view>
				</view>
				<view class="acea-row row-between-wrapper">
					<view class="money">日常价：￥{{ item.ot_price }}</view>
					<view>仅剩：{{ item.stock }}{{ item.unit_name }}</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
export default {
	name: 'PromotionGood',
	props: {
		benefit: {
			type: Array,
			default: () => []
		}
	},
	data: function() {
		return {};
	},
	methods: {
		// 商品详情跳转
		goDetail(item) {
			if (item.activity && item.activity.type === '1') {
				this.$navigator(`/pages/activity/SeckillDetails?id=${item.activity.id}&time=${item.activity.time}&status=1`);
			} else if (item.activity && item.activity.type === '2') {
				this.$navigator(`/pages/activity/DargainDetails?id=${item.activity.id}`);
			} else if (item.activity && item.activity.type === '3') {
				this.$navigator(`/pages/activity/GroupDetails?id=${item.activity.id}`);
			} else {
				this.$navigator(`/pages/shop/GoodsCon?id=${item.id}`);
			}
		}
	}
};
</script>
<style scoped lang="scss">
.promotionGood {
	padding: 0 30rpx;
	background-color: #fff;
}

.promotionGood .item {
	border-bottom: 1px solid #eee;
	height: 250rpx;
}

.promotionGood .item .pictrue {
	width: 188rpx;
	height: 188rpx;
	position: relative;
}

.promotionGood .item .pictrue .image {
	width: 100%;
	height: 100%;
	border-radius: 8rpx;
}

.promotionGood .item .text {
	font-size: 24rpx;
	color: #999;
	width: 472rpx;
	text-align: left;
}

.promotionGood .item .text .name {
	font-size: 30rpx;
	color: #333;
}

.promotionGood .item .text .sp-money {
	margin: 34rpx 0 20rpx 0;
}

.promotionGood .item .text .sp-money .moneyCon {
	padding: 0 18rpx;
	background-color: red;
	height: 46rpx;
	line-height: 46rpx;
	background-image: linear-gradient(to right, #ff6248 0%, #ff3e1e 100%);
	font-size: 20rpx;
	color: #fff;
	background-image: -webkit-linear-gradient(to right, #ff6248 0%, #ff3e1e 100%);
	background-image: -moz-linear-gradient(to right, #ff6248 0%, #ff3e1e 100%);
	border-radius: 24rpx 3rpx 24rpx 3rpx;
}

.promotionGood .item .text .sp-money .moneyCon .num {
	font-size: 24rpx;
}

.promotionGood .item .text .money {
	text-decoration: line-through;
}
</style>
