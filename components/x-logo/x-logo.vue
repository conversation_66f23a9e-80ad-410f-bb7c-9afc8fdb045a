<template>
	<view>
		<view class="logo flex flex_align_center" v-if="logo"><image src="@/static/images/yuanshi/logo.png" mode="widthFix"></image></view>
		<view class="search" v-if="search" @click="goPages('/pages/yuanshi/search/search')">
			<!-- <view class="title font_size20">活动搜索</view> -->
			<xSearch :disabled="disabled" :fixed="false" :history="history" :btn="btn" :clear="clear" :type="type" @search="gosearch" @change="change" @focus="focus"></xSearch>
		</view>
	</view>
</template>

<script>
import xSearch from '@/components/x-search/x-search.vue';
export default {
	props: {
		disabled: {
			type: Boolean,
			default: false
		},
		type: {
			type: String,
			default: 'x' // menu navbar中的搜索
		},
		logo: {
			type: Boolean,
			default: true
		},
		clear: {
			type: <PERSON>olean,
			default: false
		},
		history: {
			type: <PERSON><PERSON><PERSON>,
			default: false
		},
		search: {
			type: Boolean,
			default: true
		},
		btn: {
			type: <PERSON>olean,
			default: true
		},
	},
	components: {
		xSearch
	},
	data() {
		return {};
	},
	methods: {
		goPages(path, type) {
			if(!this.disabled) return
			this.$navigator(path, type);
		},
		gosearch(val) {
			this.$emit('search', val);
		},
		change(val) {
			this.$emit('change', val);
		},
		focus(){
			this.$emit('focus');
		},
	},
	mounted() {},
	onLoad(option) {}
};
</script>

<style scoped lang="scss">
.logo {
	padding: 52rpx 0 40rpx 40rpx;
	image {
		width: 430rpx;
		height: 142rpx;
	}
}
.search {
	padding: 0rpx 40rpx;
	.title {
		color: #999999;
		padding:0 20rpx 20rpx 20rpx;
	}
}
</style>
