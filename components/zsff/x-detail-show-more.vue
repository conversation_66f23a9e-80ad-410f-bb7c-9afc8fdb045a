<template>
    <view class="">
        <view class="x-detail-show-more flex flex_align_center flex_between">
            <view class="info">{{sliceStr}}</view>
            <view class="x-right-more" v-if="sliceStr" @click="openpop">[展开详情]</view>
        </view>
        <u-popup v-model="show" mode="bottom" width="750rpx" :height="popheight + 'rpx'" border-radius="14"
            :closeable="true"> <!-- :height="strnum < 1200?'620rpx':'100%' " -->
            <!-- <view class="xpopup" >
                <view style=" position: fixed;top: 0;left: 0; width: 100%; height: 100rpx; overflow: hidden;background-color: #fff; z-index: 2;" 
                @touchstart="onTouch" 
                @touchmove="onTouch" 
                @touchend="onTouch"
                ></view>
                <u-parse :html="html" :tag-style="parseStyle" @linkpress="$linkpress">
                </u-parse>
            </view> -->
            
            <scroll-view :scroll-top="scrollTop" scroll-y="true" :style="[{height:popheight + 'rpx'}]" class="xpopup" @scrolltoupper="upper" @scrolltolower="lower"
            @scroll="scroll">
            	<u-parse :html="html" :tag-style="parseStyle" @linkpress="linkpress">
            	</u-parse>
            </scroll-view>
            
        </u-popup>
    </view>
</template>
<script>
    export default {
        props: {
            html: {
                type: String,
                default: ''
            }
        },
        data() {
            return {
                show: false,
                sliceStr: '',
                strnum: 0,

                panStyle: '',
                windowHeight:0,
                popheight:620,
                
                scrollTop: 0,
                old: {
                	scrollTop: 0
                }
            };
        },
        watch: {
            
   
            html(a, b) {
                const reg =
                    /[\u4e00-\u9fa5|\u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5]/g;
                let str = a.match(reg).join(""),
                    len = 20;
                let sliceStr = str.slice(0, len);
                this.sliceStr = sliceStr.length < len ? sliceStr : (sliceStr + '...');
            }

            // 修复开头数字显示
            // html(a, b) {
            //     this.strnum = a.length;
            //     let str = a.replace(/<\/?.+?>/g, "").replace(/ /g, "");
            //     let sliceStr = str.slice(0, 20);
            //     this.sliceStr = sliceStr.length < 20 ? sliceStr : (sliceStr + '...');
            // }
        },
        created() {
            let that = this;
            uni.getSystemInfo({
                success: function (res) {
                    that.windowHeight = res.windowHeight*2
                }
            });
        },
        components: {},

        methods: {
            linkpress(e){
                console.log('弹窗链接',e)
                // #ifdef MP
                if(!isNaN(e.href)){
                    this.show = false;
                    let settime = e.href;
                    this.$emit('settime', settime);
                }
                // #endif
                // #ifdef H5
                console.log('弹窗链接',e)
                return;
                // #endif
            },
            upper(e) {
                // console.log(e)
            },
            lower(e) {
                // console.log(e)
            },
            scroll(e) {
                this.old.scrollTop = e.detail.scrollTop*2
                if(this.old.scrollTop > 0){
                    this.popheight = this.popheight + (this.old.scrollTop/2);
                    if( this.popheight >= (this.windowHeight-100)){
                        this.popheight = this.windowHeight-100;
                    }
                }
                // console.log('this.popheight',this.popheight)
            },
                    
            // var a = 70;
            //     var b = 60,c = 70, d = 80, e = 90;
            //     var res = (a >= e ? '111' : a >= d ? '2222' : a >= c ? '3333' : a >= b ? "4444": '5555')
            //     console.log(res);// 3333
            openpop(){
                this.show = true;
                this.popheight = 620;
                // this.$refs.popup.open('bottom')
            },
            // onTouch(ev) {
            //     const {
            //         clientY
            //     } = ev.changedTouches[0] || ev
            //     this.startY = clientY;
            //     this.popheight = (this.windowHeight - this.startY)*2;
            //     if(this.popheight < 100 || this.popheight < 0){
            //         this.popheight = 0;
            //         this.show = false;
            //     }
            //     if(this.popheight > this.windowHeight*2){
            //         this.popheight = this.windowHeight*2-20;
            //     }
            //     // console.log('监听弹窗高度',this.popheight)
            // },
        }
    }
</script>
<style lang='scss' scoped>
    .x-detail-show-more {
        position: relative;
        margin-top: 30rpx;
        padding-bottom: 24rpx;
        border-bottom: 2rpx solid #d8d8d8;

        .info {
            font-size: 24rpx;
            font-weight: 400;
            text-align: left;
            color: #666666;
        }

        .x-right-more {
            font-weight: bold;
        }
    }

    .xpopup {
        padding: 100rpx 40rpx 0rpx 40rpx;
        height: 620rpx;
        box-sizing: border-box ;
        width: 100%;
    }
 
    .scroll-view_H {
    	white-space: nowrap;
    	width: 100%;
    }
    
    .scroll-view-item {
    	height: 300rpx;
    	line-height: 300rpx;
    	text-align: center;
    	font-size: 36rpx;
        background-color: pink;
    }
    
    .scroll-view-item_H {
    	display: inline-block;
    	width: 100%;
    	height: 300rpx;
    	line-height: 300rpx;
    	text-align: center;
    	font-size: 36rpx;
    }
</style>
