<template>
    <view class="flex flex_align_center" style="margin-top: 16rpx;">
        <button class="flex_line_height btn btn_l" @click="zanClick">
            <view class="flex flex_align_center">
                <image src="@/static/images/yuanshi/love1.png" mode="widthFix" v-if="isZan">
                    <image src="@/static/images/yuanshi/love1_1.png" mode="widthFix" v-else>
                        <text>{{collectcount || 0}}</text>
            </view>
        </button>
        <!-- #ifdef MP -->
        <button class="flex_line_height btn" open-type="share">
            <view class="flex flex_align_center">
                <image src="@/static/images/community/share.png" mode="widthFix"></image>
                <text>分享</text>
            </view>
        </button>
        <!-- #endif -->
    </view>
</template>
<script>
    export default {
        components: {},
        // props: {
        // 	isZan: {
        // 		type: [<PERSON><PERSON><PERSON>, Number],
        // 		default: true
        // 	}
        // },
        props: ["isZan","collectcount"],
        data() {
            return {

            };
        },
        methods: {
            zanClick() {
                console.log('zan')
                this.$emit('zanclick')
            }
        },
        created() {
        },
        onLoad(option) {

        },
    }
</script>
<style lang='scss' scoped>
    .btn {
        width: 134rpx;
        height: 46rpx;
        border: 2rpx solid #d2d2d2;
        border-radius: 24rpx;
        
        /* #ifdef MP-TOUTIAO */
        padding-right: 18rpx;
        /* #endif */

        &.btn_l {
            margin-right: 15rpx;
        }

        image {
            width: 26rpx;
            height: 28rpx;
            margin-right: 14rpx;

        }

        text {
            font-size: 24rpx;
            font-weight: 400;
            color: #666666;
        }
        
    }
</style>
