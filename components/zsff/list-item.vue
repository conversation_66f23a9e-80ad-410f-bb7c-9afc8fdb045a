<template>
    <view class="list_item">
        <view class="item " v-for="(item,index) in arr" :key="item.id">
            <view class="flex flex_between gift_top" v-if="rbType===4">
                <view class="status">{{item.is_draw?'赠送成功':'未赠送'}}</view>
                <view class="ungo" v-if="item.is_draw">领取人：{{item.gift_user.nickname}}</view>
                <button class="go" v-else open-type="share" @click="shareClick(item)">去赠送</button>
            </view>
            <view class="items" @click="listItemClick(item,index)">
                <view class="image relative">
                    <image class="image min-height" :src="item.image" mode="widthFix"></image>
                </view>
                <view class="item_r flex flex_column flex_between">
                    <view class="">
                        <view class="title" style="-webkit-box-orient: vertical;" :class="{titleB:rtitleB}">
                            {{item.title}}</view>
                        <view class="tag-subject" v-if="rbType != 4">
                            <view class="tag-subject-left" v-if="item.subject_image">
                                <image class="tag-subject-left-img" :src="item.subject_image" mode=""></image>
                            </view>
                            <view class="tag-subject-right">
                                <view class="rc_span" :class="!item.label.length?'active':''" v-if="rspan&&item.subject_name">
                                    <view :class="rspan"><text class="text">{{item.subject_name}}</text></view>
                                </view>
                                <view class="tagitem-box">
                                    <block v-for="(items, indexs) in item.label" :key="indexs">
                                        <view class="tagitem" @click.stop="goSearch(items)">
                                            <image class="tagitem-img" src="../../static/images/zsff/tag.png" mode=""></image>
                                            <text class="tagitem-txt">
                                                {{items}}
                                            </text>
                                        </view>
                                    </block>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view class="rb_type">
                        <!-- 1 我的订阅课程列表（显示观看、评论、收藏数） -->
                        <template v-if="rbType===1">
                            <view class="bottom-p">
                                <view class="num-txt1">
                                    <view class="txt1 margin0">
                                        <image class="textimg help" src="../../static/images/zsff/help_count.png" mode=""></image>
                                        <text class="text">{{item.likes || 0}}</text>
                                    </view>
                                    <view class="txt1">
                                        <image class="textimg comment" src="../../static/images/zsff/comment_count.png" mode=""></image>
                                        <text class="text">{{item.comments || 0}}</text>
                                    </view>
                                    <view class="txt1" v-if="!types">
                                        <image class="textimg look1" v-if="item.type == 1" src="../../static/images/zsff/look_count1.png" mode=""></image>
                                        <image class="textimg look2" v-if="item.type == 2" src="../../static/images/zsff/look_count2.png" mode=""></image>
                                        <image class="textimg look3" v-if="item.type == 3" src="../../static/images/zsff/look_count3.png" mode=""></image>
                                        <text class="text">{{item.browse_count || 0}}</text>
                                    </view>
                                    <view class="txt1" v-else>
                                        <image class="textimg look1" v-if="item.types == 1" src="../../static/images/zsff/look_count1.png" mode=""></image>
                                        <image class="textimg look2" v-if="item.types == 2" src="../../static/images/zsff/look_count2.png" mode=""></image>
                                        <image class="textimg look3" v-if="item.types == 3" src="../../static/images/zsff/look_count3.png" mode=""></image>
                                        <text class="text">{{item.browse_count || 0}}</text>
                                    </view>
                                </view>
                            </view>
                        </template>
                        <!-- 2 首页与课程首页列表（显示价格、直播状态、节数、观看、评论、收藏数）-->
                        <template v-else-if="rbType===2">
                            <view class="bottom-p">
                                <template v-if="item.pay_type">
                                    <view class="price" v-if="!(openIosPayment && platform == 'ios')">
                                        ￥{{item.money || 0}}
                                    </view>
                                    <view class="rbspan" v-if="item.is_live">
                                        <text class="text" v-if="item.live_status == 0">未开始</text>
                                        <text class="text" v-if="item.live_status == 1">直播中</text>
                                        <text class="text" v-if="item.live_status == 2">回放</text>
                                    </view>
                                    <view class="rbspan" v-else>
                                        <text class="text">共{{item.is_multi_period?item.total_count:item.count}}节课</text>
                                    </view>
                                </template>
                                <view class="num-txt1">
                                    <view class="txt1 margin0">
                                        <image class="textimg help" src="../../static/images/zsff/help_count.png" mode=""></image>
                                        <text class="text">{{item.likes || 0}}</text>
                                    </view>
                                    <view class="txt1">
                                        <image class="textimg comment" src="../../static/images/zsff/comment_count.png" mode=""></image>
                                        <text class="text">{{item.comments || 0}}</text>
                                    </view>
                                    <view class="txt1">
                                        <image class="textimg look1" v-if="item.type == 1" src="../../static/images/zsff/look_count1.png" mode=""></image>
                                        <image class="textimg look2" v-if="item.type == 2" src="../../static/images/zsff/look_count2.png" mode=""></image>
                                        <image class="textimg look3" v-if="item.type == 3" src="../../static/images/zsff/look_count3.png" mode=""></image>
                                        <text class="text">{{item.browse_count || 0}}</text>
                                    </view>
                                </view>
                            </view>
                        </template>
                        <!-- 3 未知（价格、节数）-->
                        <template v-else-if="rbType===3">
                            <view class="flex flex_align_center flex_between">
                                <view class="price">￥{{item.money || 0}}</view>
                                <view class="rbspan"><text class="text">共{{item.count || 1}}节课</text></view>
                            </view>
                        </template>
                        <!-- 4 我赠送的课程列表（价格） -->
                        <template v-else >
                            <view class="bottom-p">
                                <view class="price">￥{{item.money || 0}}</view>
                            </view>
                        </template>
                    </view>
                </view>
            </view>
        </view>
        <view class="nodata" v-if="nodata">
            <u-loadmore :status="loadmoreStatus" :loadText="loadText" color="#999999" />
        </view>

    </view>
</template>

<script>
    import {
        OPEN_IOS_PAYMENT
    } from '@/config.js';
    export default {
        name: "listItem",
        props: {
            arr: {
                type: Array,
                default () {
                    return []
                }
            },
            lspan: {
                type: Boolean,
                default: false
            },
            rspan: { //类名
                type: String,
                default: ''
            },
            rtitleB: {
                type: Boolean,
                default: false
            },
            rbType: {
                type: [String, Number],
                default: 1
            },
            types: {
                type: Boolean,
                default: false
            },
            nodata: {
                type: Boolean,
                default: true
            },
            loadmoreStatus: {
                type: String,
                default: 'loadmore'
            },
            pagemore: {
                type: Boolean,
                default: true
            },
        },
        created() {
            this.platform = uni.getSystemInfoSync().platform;
            console.log('this.platform', this.platform)
        },
        data() {
            return {
                openIosPayment:OPEN_IOS_PAYMENT,
                platform:'',
                loadText: {
                    loadmore: '加载更多',
                    loading: '努力加载中',
                    nomore: '已加载全部'
                }
            }
        },
        
        methods: {
            shareClick(item) {
                this.$emit('shareClick', item)
            },
            listItemClick(item, index) {
                this.$emit('listItemClick', item, index)
            },
            goPages() {
                this.$navigator('/pages/tabBar/index/index', 'switchTab');
            },
            goSearch(items){
                // this.$navigator('/pages/tabBar/course/index?name=' + items,'reLaunch');
            }
        }
    };
</script>
</script>

<style scoped lang="scss">
    .list_item {
        font-family: PingFang SC, PingFang SC-Regular;

        .image {
            width: 100%;
            height: 100%;
        }

        .gift_top {
            padding-bottom: 20rpx;
            font-size: 24rpx;
            border-bottom: 1rpx dashed #ccc;
            margin-bottom: 20rpx;

            .ungo {}

            .go {
                font-size: 24rpx;
                background: #feb720;
                color: #fff;
                padding: 10rpx 20rpx;
            }
        }

        .item {
            margin-bottom: 30rpx;
            
            .items {
                width: 100%;
                background: #ffffff;
                border-radius: 30rpx;
            }
            .min-height {
                min-height: 180rpx;
            }

            .image {
                width: 100%;
                border-radius: 32rpx 32rpx 0px 0px;
                overflow: hidden;

                .span {
                    bottom: 22rpx;
                    left: 12rpx;
                    width: 64rpx;
                    height: 28rpx;
                    line-height: 28rpx;
                    background: #6bb4fe;
                    border-radius: 8rpx;
                    text-align: center;

                    .text {
                        font-weight: 400;
                        color: #ffffff;
                        @include font_size(18);
                    }
                }
            }

            .item_r {
                flex: 1;
                padding: 32rpx 36rpx 38rpx;
                box-sizing: border-box;
                
                .tag-subject {
                    width: 100%;
                    overflow: hidden;
                    padding-top: 20rpx;
                    // margin-bottom: 36rpx;
                    
                    .tag-subject-left {
                        float: left;
                        width: 84rpx;
                        height: 84rpx;
                        margin-right: 20rpx;
                        
                        .tag-subject-left-img {
                            width: 100%;
                            height: 100%;
                        }
                    }
                    .tag-subject-right {
                        float: left;
                        /* #ifdef MP */
                        width: 495rpx;
                        /* #endif */
                        /* #ifdef H5 */
                        width: 494rpx;
                        /* #endif */
                        .rc_span {
                            width: 100%;
                            font-size: 20rpx;
                            font-family: PingFang SC, PingFang SC-Regular;
                            font-weight: 400;
                            text-align: left;
                            color: #6cb5ff;
                            margin-bottom: 16rpx;
                            
                            &.active {
                                margin-top: 24rpx;
                            }
                            .rspan {
                                
                                .text {
                                    // @include font_size(18, 1);
                                    // font-weight: 400;
                                    // text-align: left;
                                    // color: #999999;
                                }
                            }
                        }
                        
                        .tagitem-box {
                            
                            .tagitem {
                                float: left;
                                font-size: 20rpx;
                                font-family: PingFang SC, PingFang SC-Regular;
                                font-weight: 400;
                                text-align: left;
                                color: #b5771b;
                                height: 40rpx;
                                line-height: 38rpx;
                                padding: 0 10rpx 0 8rpx;
                                background: #f7f1e7;
                                border-radius: 4rpx;
                                box-sizing: border-box;
                                margin-right: 8rpx;
                                margin-bottom: 8rpx;
                                
                                .tagitem-img {
                                    width: 16rpx;
                                    height: 20rpx;
                                    margin-top: 10rpx;
                                    margin-right: 8rpx;
                                }
                            }
                        }
                    }
                    
                }
                .title {
                    width: 100%;
                    font-size: 24rpx;
                    font-family: PingFang SC, PingFang SC-Regular;
                    font-weight: 400;
                    text-align: left;
                    color: #666666;
                    
                    
                    @include show_line(2);

                    &.titleB {
                        color: #333
                    }
                }

                

                .rb_type {
                    .image {
                        width: 52rpx;
                        height: 36rpx;
                    }

                    
                    
                    .bottom-p {
                        width: 100%;
                        overflow: hidden;
                        margin-top: 36rpx;
                        
                        .price {
                            float: left;
                            font-size: 48rpx;
                            font-family: PingFang SC, PingFang SC-Semibold;
                            font-weight: 600;
                            text-align: left;
                            color: #ec625d;
                            margin-right: 44rpx;
                        }
                        .rbspan {
                            float: left;
                            font-size: 20rpx;
                            font-family: PingFang SC, PingFang SC-Regular;
                            font-weight: 400;
                            text-align: left;
                            color: #999999;
                            padding-top: 28rpx;
                        
                            .text {
                                
                            }
                        }
                        .num-txt1 {
                            float: right;
                            padding-top: 26rpx;
                            
                            .txt1 {
                                float: left;
                                margin-left: 30rpx;
                                &.margin0 {
                                    margin-left: 0;
                                }
                                .text {
                                    font-weight: 400;
                                    font-size: 24rpx;
                                    text-align: left;
                                    color: #50506f;

                                }
                                .textimg {
                                    width: 20rpx;
                                    height: 20rpx;
                                    margin-right: 15rpx;
                                    &.comment{
                                        height: 18rpx;
                                    }
                                    &.look1{
                                        width: 22rpx;
                                        height: 18rpx;
                                    }
                                    &.look2{
                                        height: 18rpx;
                                    }
                                    &.look3{
                                        width: 22rpx;
                                        height: 18rpx;
                                    }
                                    
                                }
                            }
                        }
                        
                    }
                }
            }
        }

        .nodata {
            font-size: 24rpx;
            font-weight: 400;
            text-align: center;
            color: #999999;
            line-height: 40px;
        }
    }
</style>
