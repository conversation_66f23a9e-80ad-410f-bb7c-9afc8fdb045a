<template>
    <view class=" ">
        <swiper class="t_swiper" v-if="detail.video.length || detail.image.length">
            <swiper-item class="swiper-item video" v-for="(item1, index1) in detail.video"
                v-if="detail.video&&detail.video.length" :key="item1">
                <video :id="`playVideo${index1}`" style="width:750rpx;height:422rpx;" :show-screen-lock-button="true"
                    :show-casting-button="true" :show-center-play-btn="false" :autoplay="autoplay" :controls="isControl"
                    :src="item1" :poster="poster" @loadedmetadata="loadedmetadata" custom-cache="false"
                    :enable-play-in-background="iswindow?true:false"
                    :picture-in-picture-mode="iswindow?wxsetPicture:wxclePicture" @fullscreenchange="fullscreenchange"
                    @controlstoggle="controlstoggle" @pause="onPause" @play="onPlay" @timeupdate="timeupdate" @ended="getended"
                    webkit-playsinline playsinline x5-playsinline >

                    <cover-view class="video-heizith-menu" @tap.stop="showSpeedOptions"
                        v-show="isShowControl">{{playbackRateItem == '1.0'?'倍数':playbackRateItem+'x'}}</cover-view>
                    
                    <cover-view v-if="showSpeedModal" class="modal" @tap.stop="hideSpeedOptions">
                        <cover-view class="modal-content" @click.stop>
                            <cover-view class="modal-content-txt">
                                倍数
                            </cover-view>
                            <cover-view class="speed-option" v-for="(items,indexs) in playbackRateList" :key="indexs"
                                @tap.stop="changeSpeed" :data-rate="items"
                                :class="{ active: items == playbackRateItem }">
                                {{ items }}X
                            </cover-view>
                        </cover-view>
                    </cover-view>
                </video>
                
                <view class="cover flex flex_align_center flex_around" v-show="!isControl">
                    <image src="@/static/images/zsff/play.png" @click="playVideo(item1,index1)">
                </view>
            </swiper-item>
            <swiper-item class="swiper-item flex flex_align_center" v-for="(item1, index1) in detail.image"
                v-if="detail.image && detail.image.length" :key="index1">
                <!-- <image :src="item1" class="bg"></image> -->
                <image v-if="item1" :src="item1" mode="aspectFit"></image>
            </swiper-item>
        </swiper>

    </view>
</template>
<script>
    export default {
        components: {},
        props: {
            setvideotime: {
                type: Number,
                default: 0
            },
            poster: {
                type: String,
                default: ''
            },
            // 是否暂停
            ispause: {
                type: Boolean,
                default: false
            },
            // 是否重置倍速显示
            resetSpeed: {
                type: Boolean,
                default: false
            },
            // 是否开启画中画
            iswindow: {
                type: Boolean,
                default: false
            },
            // 是否购买
            isBuy: {
                type: Boolean,
                default: true
            },
            detail: {
                type: Object,
                default () {
                    return {
                        status: false,
                        video: [],
                        image: [],
                    }
                }
            }
        },
        watch: {
            ispause(a, b) {
                let that = this;
                if(a){
                    // console.log('切换暂停上个视频')
                    that.videoCtx = uni.createVideoContext(`playVideo0`, that);
                    that.videoCtx.pause();
                }
            },
            resetSpeed(a, b) {
                if(a){
                    // console.log('切换倍速重置')
                    this.playbackRateItem = '1.0';
                    this.hideSpeedOptions();
                }
            },
            isBuy(a, b) {
                this.autoplay = a;
            },
            poster(a, b) {
                // console.log('素材详情1', a)
                // console.log('素材详情2', b)
            },
            setvideotime(a, b) {
                let that = this;
                that.videoCtx = uni.createVideoContext(`playVideo0`, that);
                // that.videoCtx.pause();
                let tid = setTimeout(() => {
                    // console.log('视频跳转时间', a)
                    that.videoCtx.seek(a);
                }, 500)
            }
        },
        created() {
            console.log('素材详情---', this.detail)
        },
        data() {
            return {
                videoCtx: null,
                isControl: false,
                isLoadFish: false,
                showLoadng: false,
                autoplay: this.isBuy,
                wxclePicture: [],
                wxsetPicture: [
                    'push',
                    'pop'
                ],
                // 是否显示视频控件
                isShowControl: false,
                // 倍速播放
                playbackRateItem: '1.0',
                // 倍速设置选项列表
                playbackRateList: ['0.5', '0.8', '1.0', '1.25', '1.5', '2.0'],

                showSpeedModal: false,  
                currentTime: 0,
                throttleTimeout: null,
                fullScreen: false,// 全屏状态
            };
        },
        methods: {
            // 倍速
            showSpeedOptions() {
                this.showSpeedModal = true;
            },
            hideSpeedOptions() {
                this.showSpeedModal = false;
            },
            changeSpeed(e) {
                this.$emit("isresetSpeed", true)
                this.playbackRateItem = e.currentTarget.dataset.rate;
                // this.resetSpeed = false;
                let rate = Number(this.playbackRateItem);
                this.videoCtx.playbackRate(rate)
                this.hideSpeedOptions();
            },

            // 进度
            onPause(e) {
                // console.log('暂停', e)
                this.$emit("onPause", true)
            },
            getended(e){
                this.$emit("getended", true)
            },
            onPlay(e) {
                // console.log('播放', e)
                this.$emit("onPlay", true)
            },
            timeupdate(event) {
                // this.currentTime = parseFloat(event.target.currentTime.toFixed(1));
                // // console.log('实时进度',this.currentTime)
                // this.$emit("currentTime", this.currentTime)

                // 如果已经有一个setTimeout执行中，则不再执行
                if (this.throttleTimeout) return;
                // 设置下一次可以执行的时间
                this.throttleTimeout = setTimeout(() => {
                    // 执行实际的事件处理逻辑
                    this.currentTime = parseFloat(event.target.currentTime.toFixed(1));
                    // console.log('实时进度',this.currentTime)
                    this.$emit("currentTime", this.currentTime)
                    // 清除定时器
                    this.throttleTimeout = null;
                }, 900); // 这里的500是节流时间阈值，单位是毫秒

            },


            // 是否显示控件
            controlstoggle(e) {
                if (!e.detail.show) {
                    this.isShowControl = false
                    return
                }
                this.isShowControl = true
            },
            // 设置倍速播放
            // playbackRate(index) {
            //     // this.videoContext = uni.createVideoContext(`playVideo${index}`, this);

            //     if (this.playbackRateIndex == this.playbackRateList.length - 1) {
            //         this.playbackRateIndex = 0
            //         this.videoCtx.playbackRate(Number(this.playbackRateList[this.playbackRateIndex]))
            //         return
            //     }

            //     this.playbackRateIndex++
            //     this.videoCtx.playbackRate(Number(this.playbackRateList[this.playbackRateIndex]))
            // },


            fullscreenchange(e) {
                console.log('全屏设置--', e.detail)

                // 安卓真机每次返回horizontal 使用待考察
                // console.log('全屏设置--',e.detail.direction)
                // if (e.detail.direction == 'vertical') {
                //     // 退出全屏
                //     this.$emit("direction", "vertical")
                // } else if (e.detail.direction == 'horizontal') {
                //     // 进入全屏
                //     this.$emit("direction", "horizontal")
                // }
                //当视频进入和退出全屏时触发，event.detail = {fullScreen, direction}，direction取为 vertical 或 horizontal
                this.fullScreen = e.detail.fullScreen;

            },
            loadedmetadata(e) {
                // 加载完成时
                this.isLoadFish = true
                this.$hideLoading();
                if (!this.isBuy) {
                    return
                }
                if (this.isControl) {
                    let ref = uni.createVideoContext(e.currentTarget.id, this);
                    this.videoCtx = ref;
                    let tid = setTimeout(() => {
                        ref.play();
                        clearTimeout(tid)
                    }, 500)
                } else {
                    this.playVideo({}, 0)
                }
            },
            playVideo(item, index) {
                if (!this.isBuy) {
                    return this.$showToast('未购买')
                }
                this.isControl = true;
                if (!this.isLoadFish) {
                    return this.$loadingToast('视频加载中', {
                        mask: true
                    })
                }
                this.videoCtx = uni.createVideoContext(`playVideo${index}`, this);
                let tid = setTimeout(() => {
                    this.videoCtx.play();
                    clearTimeout(tid)
                }, 500)
                this.$emit("send", this.videoCtx)
            },
        },
        onLoad(option) {

        },
    }
</script>
<style lang='scss' scoped>
    .t_swiper {
        width: 750rpx;
        height: 750rpx;
        height: 422rpx;

        image {
            width: 100%;
            height: 100%;
        }

        .swiper-item {
            position: relative;
            text-align: center;
            background-color: #000;

            &.video {


                .flotageY {
                    position: absolute;
                    right: 30rpx;
                    bottom: 16%;
                    .flotageY-txt {
                        font-size: 18rpx;
                        right: 10rpx;
                        top: 50%;
                        width: 50rpx;
                        height: 40rpx;
                        text-align: center;
                        line-height: 36rpx;
                        border: 1rpx solid #fff;
                        border-radius: 8rpx;
                        transform: scale(0.8);
                        color: #fff;

                    }
                }
            }

            .cover {
                position: absolute;
                width: 100%;
                height: 100%;
                z-index: 9999;
                top: 0;

                image {
                    width: 160rpx;
                    height: 160rpx;
                }
            }

            .bg {
                position: absolute;
                filter: blur(10px);
                opacity: 0.8;
            }

        }
    }

    .video-heizith-menu {
        position: absolute;
        right: 30rpx;
        bottom: 20%;
        z-index: 998;
        font-size: 20rpx;
        width: 52rpx;
        height: 40rpx;
        text-align: center;
        line-height: 38rpx;
        border: 1rpx solid #fff;
        border-radius: 8rpx;
        color: #fff;
        box-shadow: 0px 0px 50rpx rgba(0, 0, 0, 0.2);
    }

    .modal {
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
    }
    .modal-content {
        position: absolute;
        left: 46rpx;
        bottom: 20%;
        z-index: 9999;
        
    }
    .modal-content-txt {
        text-align: left;
        padding-bottom: 20rpx;
        color: #fff;
        font-size: 18rpx;
        z-index: 9999;
    }
        
    .speed-option {
        float: left;
        width: 72rpx;
        height: 72rpx;
        text-align: center;
        line-height: 68rpx;
        font-size: 22rpx;
        color: #fff;
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 4rpx;
        margin-right: 10rpx;
        z-index: 9999;
        
        
    }
    .speed-option.active {
        /* border-bottom: 2rpx solid #fd750b; */
        color: #fd750b;
    }
    
    /* .speed-option.optionActive::after {
        content: "";
        display: block;
        position: absolute;
        width: 50rpx;
        height: 6rpx;
        background-color: #EB0F1D;
        left: 50%;
        transform: translatex(-50%);
        bottom: 10%;
        z-index: 9999;
    } */
    
</style>