<template>
    <view class="recom">
        <view class="tab flex flex_between flex_align_end">
            <view class="title">
                <view>{{title}}</view>
                <view class="line"></view>
            </view>
        </view>
        <view class="scroll">
            <scroll-view scroll-x style="white-space:nowrap;" :scroll-with-animation="true">
                <view class="wrap" style="fontSize:30rpx">
                    <view class="item" v-for="(item, index) in arr" :key="index" :id="`t${index}`">
                        <view @click="goPages(item)" class="boximg" :class="item.list_type === 'product'?'productimg':''" >
                            <view class="img" v-if="item.list_type === 'course' ">
                                <image :src="item.image" alt="img" mode="aspectFill"></image>
                            </view>
                            <view class="productimg" v-if="item.list_type === 'product' ">
                                <image :src="item.image" alt="img" mode="aspectFill"></image>
                            </view>
                            <view v-if="item.list_type === 'product' " class="title line1">{{item.name}}</view>
                            <view v-if="item.list_type === 'course' " class="title line1">{{item.title}}</view>
                            <view class="info">
                                <text>音乐审美养成 </text>
                            </view>
                            <view class="wrap_b ">
                                <template v-if="item.list_type === 'course'">
                                    <view class="flex flex_align_center">
                                        <image class="btn" src="@/static/images/zsff/btn.png" mode="heightFix"></image>
                                        <view class="txt1"><text class="font_size20">{{item.play_count}}观看</text></view>
                                    </view>
                                </template>
                                <template v-if="item.list_type === 'product' ">
                                    <view class="flex flex_align_center flex_between">
                                        <view class="price">
                                            ￥{{item.price}}
                                        </view>
                                    </view>
                                </template>
                            </view>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </view>
    </view>
</template>

<script>
    import {
        mapGetters
    } from 'vuex';
    export default {
        props: {
            arr: {
                type: Array,
                default () {
                    return [];
                }
            },
            title:{
                type: String,
                default: ''
            },
        },
        computed: mapGetters(['userInfo']),
        data() {
            return {
                info: [],
                current: 0,
                scrollLeft: 0,
                leftArr: [],
                init: false
            };
        },
        watch: {
            arr(a, b) {
                // this.calcBanner(a)
            }
        },
        methods: {
            calcBanner(arr1) {
                if (!arr1.length) return;
                let arr = JSON.parse(JSON.stringify(arr1));
                let ossResize = ossImgParams({
                    w: 710,
                    h: 260,
                    q: 80
                });
                arr.forEach((item, index) => {
                    item.image = item.image + ossResize;
                });
                this.info = arr;
            },
            goPages(item) {
                console.log('develop-item',item)
                this.$linkpress(item.url)
            },
            uniSelectorQueryInfo(selector, _this) {
                return new Promise((resolve, reject) => {
                    const query = uni.createSelectorQuery().in(_this);
                    query
                        .select(selector)
                        .boundingClientRect(res => {
                            // 获取节点坐标
                            resolve(res);
                        })
                        .exec();
                });
            },
            swichTab(idx, item) {
                if (this.current === idx) {
                    return;
                }
                this.current = idx;
                this.scrollLeft = this.leftArr[idx] - this.leftArr[0];
            },
            async initScrollInfo() {
                let leftArr = [];
                for (let i = 0; i < this.arr.length; i++) {
                    const {
                        left
                    } = await this.uniSelectorQueryInfo(`#t${i}`, this);
                    leftArr.push(left);
                }
                this.leftArr = leftArr;
                if (this.current > 0) {
                    this.scrollLeft = this.leftArr[this.current] - this.leftArr[0];
                }
            }
        },
        beforeUpdate() {
            const _this = this;
            if (this.init) {
                return;
            }
            this.$nextTick(function() {
                _this.current = _this.active;
                _this.initScrollInfo();
                _this.init = true;
            });
        },
        mounted() {
            // this.calcBanner(this.arr);
        },
        onLoad(option) {}
    };
</script>

<style scoped lang="scss">
    .recom {
        white-space: nowrap;
        overflow: hidden;
        margin: 32rpx 0 60rpx 20rpx;

        image {
            width: 100%;
            height: 100%;
        }

        .tab {
            margin-bottom: 30rpx;

            .title {
                font-size: 32rpx;
                font-weight: bold;
                color: #333333;
                padding-left: 32rpx;

                .line {
                    width: 132rpx;
                    height: 8rpx;
                    background: linear-gradient(90deg, #ff5a73 0%, #ffa969 100%);
                    border-radius: 2rpx;
                }
            }

            .more {
                color: #999999;
            }
        }
    }

    .scroll {
        background: #ffffff;
        border-radius: 16rpx 0 0 16rpx;
        box-shadow: 0px 0px 12rpx 0px rgba(0, 0, 0, 0.05);
        padding: 20rpx 0 20rpx 20rpx;

        .wrap {
            display: flex;

            .item {
                width: 342rpx;
                margin-right: 20rpx;
                
                .boximg {
                    line-height:normal;
                    width: 310rpx;
                    
                    &.productimg {
                        width: 217rpx;
                    }
                    
                    .productimg {
                        width: 217rpx;
                        height: 176rpx;
                                            
                        image {
                            border-radius: 20rpx;
                        }
                    }
                    .img {
                        width: 310rpx;
                        height: 176rpx;
                    
                        image {
                            border-radius: 20rpx;
                        }
                    }
                    
                    .title {
                        margin-top: 20rpx;
                        font-size: 28rpx;
                        font-weight: 700;
                        color: #333333;
                    }
                    
                    .info {
                        font-size: 24rpx;
                        line-height: 34rpx;
                        font-weight: 400;
                        color: #666666;
                    }
                    
                    .wrap_b {
                        margin-top: 14rpx;
                    
                        .btn {
                            width: 52rpx;
                            height: 36rpx;
                        }
                    
                        .txt1 {
                            font-size: 20rpx;
                            font-weight: 400;
                            color: #999999;
                            margin-right: 18rpx;
                        }
                    
                        .price {
                            font-size: 24rpx;
                            font-weight: 500;
                            color: #ff5656;
                        }
                    
                        .span {
                            height: 36rpx;
                            border-radius: 14rpx;
                            line-height: 33rpx;
                            text-align: center;
                            font-size: 22rpx;
                            color: #ffffff;
                            background: #ff5656;
                    
                            text {
                                @include font_size(18);
                            }
                    
                        }
                    
                        .num {
                            font-weight: 400;
                            color: #666666;
                            @include font_size(18);
                        }
                    }
                }
                
                
            }
        }
    }
</style>
