<template>
	<view class="protocol flex flex_align_center">
		<view class="image">
			<image src="@/static/images/yuanshi/check.png" mode="widthFix" v-if="value"></image>
			<image src="@/static/images/yuanshi/uncheck.png" mode="widthFix" v-else></image>
		</view>

		<view class="txt"><text class="font_size20">勾选表示用户确认发布的信息不属于违反法律法规和国家有关规定禁止的信息。否则平台对此信息有权下线删除处理。</text></view>
	</view>
</template>
<!-- @click="check = !check" -->
<script>
export default {
	props: {
		value: {
			type: Boolean,
			default: false
		}
	},
	components: {},
	data() {
		return {};
	},
	methods: {
		goPages(path, type) {
			this.$navigator(path, type);
		}
	},
	mounted() {},
	onLoad(option) {}
};
</script>

<style scoped lang="scss">
.protocol {
	padding-left: 22rpx;
	width: 100%;
	overflow: hidden;
	image {
		width: 100%;
		height: 100%;
	}
	.image {
		width: 44rpx;
		height: 44rpx;
	}
	.txt {
		width: calc(100% - 44rpx);
		margin-left: 26rpx;
		.font_size20 {
			width: 118%;
			transform-origin: left;
			color: #999999;
		}
	}
}
</style>
