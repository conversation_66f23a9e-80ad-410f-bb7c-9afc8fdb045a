<template>
	<view class="protocol_vip " @click="goPages('/pages/yuanshi/vip/protocol')"><view class="font_size20"><text>购买即同意</text>《着调儿卡会员服务协议》</view></view>
</template>

<script>
export default {
	data() {
		return {};
	},
	methods: {
		goPages(path, type) {
			this.$navigator(path, type);
		}
	},
	mounted() {},
	onLoad(option) {}
};
</script>

<style scoped lang="scss">
.protocol_vip {
	padding-top: 40rpx;
	text-align: center;
	// color: #666666;
	color: #999999;
	text{
		color: #999999;
	}
}
</style>
