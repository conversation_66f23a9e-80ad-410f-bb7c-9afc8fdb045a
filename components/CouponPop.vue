<template>
	<view>
		<view class="coupon-list-window" :class="coupon.coupon === true ? 'on' : ''">
			<view class="title">
				优惠券
				<span class="iconfont icon-guanbi" @click="close"></span>
			</view>
			<view class="coupon-list" v-if="coupon.list.length > 0">
				<view class="item acea-row row-center-wrapper" v-for="(item, index) in coupon.list" :key="index" @click="getCouponUser(index, item.id)">
					<view class="money" :class="!item.is_use ? '' : 'moneyGray'">
						<view>
							￥
							<span class="num">{{ item.coupon_price }}</span>
						</view>
						<view class="pic-num">满{{ item.use_min_price }}元可用</view>
					</view>
					<view class="text">
						<view class="condition line1">
							<span class="line-title" :class="!item.is_use ? 'bg-color-check' : 'bg-color-huic'" v-if="item.type === 0">通用劵</span>
							<span class="line-title" :class="!item.is_use ? 'bg-color-check' : 'bg-color-huic'" v-else-if="item.type === 1">品类券</span>
							<span class="line-title" :class="!item.is_use ? 'bg-color-check' : 'bg-color-huic'" v-else>商品券</span>
							<span>{{ item.title }}</span>
						</view>
						<view class="data acea-row row-between-wrapper">
							<view>{{ item.start_time ? item.start_time + '-' : '' }}{{ item.end_time }}</view>
							<view class="bnt acea-row row-center-wrapper" :class="!item.is_use ? 'bg-color-red' : 'gray'">{{ !item.is_use ? '立即领取' : '已领取' }}</view>
						</view>
					</view>
				</view>
			</view>
			<!--无优惠券-->
			<view class="pictrue" v-else><image :src="imagePath + '/wximage/noCoupon.png'" class="image" /></view>
		</view>
		<view class="mask" @touchmove.prevent :hidden="coupon.coupon === false" @click="close"></view>
	</view>
</template>
<script>
import { getCouponReceive } from '@/api/user';
	import{VUE_APP_URL} from '@/config.js'
export default {
	name: 'CouponPop',
	props: {
		coupon: {
			type: Object,
			default: () => {}
		}
	},
	data: function() {
		return {
			imagePath:VUE_APP_URL
		};
	},
	mounted: function() {},
	methods: {
		close: function() {
			this.$emit('changeFun', { action: 'changecoupon', value: false }); //$emit():注册事件；
		},
		getCouponUser: function(index, id) {
			let that = this,
				list = that.coupon.list;
			if (list[index].is_use === true) return;
			getCouponReceive(id)
				.then(function() {
					that.$showToast( '已领取')
					that.$set(list[index], 'is_use', true);
					that.$emit('changefun', { action: 'currentcoupon', value: index });
					that.$emit('changeFun', { action: 'changecoupon', value: false });
				})
				.catch(function(res) {
					that.$showToast(res.msg || res);
				});
		}
	}
};
</script>
<style scoped>
	.coupon-list-window {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: #f5f5f5;
		border-radius: 16rpx 16rpx 0 0;
		z-index: 999;
		transition: all .3s cubic-bezier(.25, .5, .5, .9);
		-webkit-transition: all .3s cubic-bezier(.25, .5, .5, .9);
		-moz-transition: all .3s cubic-bezier(.25, .5, .5, .9);
		-o-transition: all .3s cubic-bezier(.25, .5, .5, .9);
		transform: translate3d(0, 100%, 0);
		-webkit-transform: translate3d(0, 100%, 0);
		-ms-transform: translate3d(0, 100%, 0);
		-moz-transform: translate3d(0, 100%, 0);
		-o-transform: translate3d(0, 100%, 0);
	}
	
	.coupon-list-window.on {
		transform: translate3d(0, 0, 0);
		-webkit-transform: translate3d(0, 0, 0);
		-ms-transform: translate3d(0, 0, 0);
		-moz-transform: translate3d(0, 0, 0);
		-o-transform: translate3d(0, 0, 0);
	}
	
	.coupon-list-window .title {
		height: 124rpx;
		width: 100%;
		text-align: center;
		line-height: 124rpx;
		font-size: 32rpx;
		font-weight: bold;
		position: relative;
		color: #333;
	}
	
	.coupon-list-window .title .iconfont {
		position: absolute;
		right: 30rpx;
		top: 50%;
		transform: translateY(-50%);
		font-size: 35rpx;
		color: #8a8a8a;
		font-weight: normal;
	}
	
	.coupon-list-window .coupon-list {
		margin: 0 0 50rpx 0;
		height: 550rpx;
		overflow: auto;
	}
	
	.coupon-list-window .pictrue {
		width: 413rpx;
		height: 336rpx;
		margin: 0 auto 50rpx auto;
	}
	
	.coupon-list-window .pictrue .image {
		width: 100%;
		height: 100%;
	}
.condition .line-title {
	width: 0.9rem;
	padding: 0 0.1rem;
	box-sizing: border-box;
	background: rgba(255, 247, 247, 1);
	border: 1px solid rgba(232, 51, 35, 1);
	opacity: 1;
	border-radius: 0.22rem;
	font-size: 0.2rem;
	color: #e83323;
	margin-right: 0.12rem;
}
.coupon-list .pic-num {
	color: #ffffff;
	font-size: 0.24rem;
}
</style>
