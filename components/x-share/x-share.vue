<template>
    <view>
        <canvas canvas-id="myCanvas" style="position:fixed;width: 750px;height: 1298px;top:0;left: -800px;"></canvas>
        <uni-popup ref="popup" type="bottom" zIndex="999" background-color="#fff">
            <view class="generate-posters acea-row row-middle">
                <!--  #ifdef H5 -->
                <view class="item" v-if="weixinStatus" @click="wxPtShare(true)">
                    <view class="iconfont icon-weixin3"></view>
                    <view class="">发送给朋友</view>
                </view>
                <!--  #endif -->
                <!-- #ifdef MP -->
                <button open-type="share" class="item" hover-class="none">
                    <view class="iconfont icon-weixin3"></view>
                    <view class="">发送给朋友</view>
                </button>
                <!-- #endif -->
                <view class="item" @click="canvasPoster">
                    <view class="iconfont icon-haibao"></view>
                    <view class="">生成海报</view>
                </view>
            </view>
        </uni-popup>
        <uni-popup ref="posterPopup" :zIndex="999">
            <template v-if="isWXPT">
                <view style="width: 750rpx;text-align:center">
                    <image src="@/static/images/share-info.png" mode="widthFix" style="width: 100%;"
                        @click="wxPtShare(false)"></image>
                </view>
            </template>
            <template v-else>
                <image :src="postImage" mode="widthFix" style="width: 600rpx;"></image>
                <view class="download" v-if="postImage" @click="savePosterPath">保存至本地</view>
            </template>
        </uni-popup>
        <!-- #ifdef MP -->
        <xWxmlToCanvas ref="xWxmlToCanvas" :hide="true" :width="350" :height="580" :xStyle="style" :xWxml="wxml">
        </xWxmlToCanvas>
        <!-- #endif -->

    </view>
</template>

<script>
    import xWxmlToCanvas from 'x-wxml-to-canvas/x-wxml-to-canvas'
    // import uniPopupShare from '@/components/uni-popup/uni-popup-share';
    import {
        getProductCode
    } from '@/api/store';
    import {
        imageBase64
    } from '@/api/public';
    import {
        checkLogin,
        autoAuth,
        PosterCanvas,
        saveImageToPhotosAlbum
    } from '@/utils/common.js';
    import {
        uniGetImageInfo
    } from '@/utils/uni_api.js';
    // #ifdef H5
    import {
        isWeixin
    } from '@/utils/validate.js';
    import {
        openShareAll
    } from '@/utils/wechat/share.js';
    const _isWeixin = isWeixin();
    // #endif
    // #ifdef MP
    const _isWeixin = true;
    // #endif
    const style = {
        container: {
            width: 350,
            height: 580,
            backgroundColor: '#fff'
        },
        img: {
            width: 350,
            height: 350
        },
        wrap: {
            textAlign: 'center',
            paddingTop: 10,
            backgroundColor: '#fff'
        },
        wtitle: {
            height: 30,
            fontSize: 16,
        },
        wprice: {
            color: 'red',
            fontSize: 18,
            height: 30
        },
        linewrap: {
            position: 'relative',
            backgroundColor: '#fff',
            height: 20
        },
        lineleft: {
            position: 'absolute',
            width: 20,
            height: 20,
            left: -10,
            borderRadius: 10,
            backgroundColor: '#585858'
        },
        line: {
            position: 'absolute',
            width: 330,
            left: 10,
            top: 10,
            borderWidth: 0.1
        },
        lineright: {
            position: 'absolute',
            width: 20,
            height: 20,
            right: -10,
            borderRadius: 10,
            backgroundColor: '#585858'
        },
        wbxxx: {
            flexDirection: 'row',
            alignItems: 'center',
            paddingTop: 10,
            paddingLeft: 20,
            paddingBottom: 30,
            backgroundColor: '#fff'

        },
        imgcode: {
            width: 100,
            height: 100,
            paddingRight: 20
        },
        wdesc: {
            width: 220,
            fontSize: 14,
            height: 20,
            textAlign: 'center',
            color: '#999'
        }
    };
    export default {
        components: {
            xWxmlToCanvas
        },
        props: {
            //此处一定要用value
            value: {
                type: Boolean,
                default: false
            },
            share: {
                type: Object,
                default () {
                    return {};
                }
            }
        },
        watch: {
            value: function(a, b) {
                // console.log('ddddd', a);
                this.open();
            },
            // #ifdef H5
            share: function(a, b) {
                this.initShare();
            }
            // #endif
        },
        data() {
            return {
                weixinStatus: _isWeixin,
                isWXPT: false, //是否是微信公众平台
                postImage: '',
                style,
                wxml: ''
            };
        },
        methods: {
            setWxml() {
                const {
                    code,
                    desc,
                    image,
                    price,
                    title
                } = this.share;
                const mode = 'aspectFill',
                    imgSrc = '',
                    imgSrc1 = '',
                    _this = this;
                this.wxml = `
			<view class="container">
				<view><image class="img" src="${image}" mode="${mode}" isMode="true"></image></view>
				<view class="wrap">
					<view class=""><text class="wtitle">${title}</text></view>
					<view class=""><text class="wprice">￥${price}</text></view>
				</view>
				<view class="linewrap">
					<view class="lineleft"></view>
					<view class="lineright"></view>
					<view class="line"></view>
				</view>
				<view class="wbxxx">
					<view><image src="${code}" mode="${mode}" isMode="true" class="imgcode"></image></view>
					<view><text class="wdesc">长按识别二维码 立即购买</text></view>
				</view>
			</view>
	`

                setTimeout(() => {
                    _this.$refs.xWxmlToCanvas.getCanvasImage().then(res => {
                        _this.postImage = res;
                    });
                }, 500)
            },
            close() {
                this.$refs.popup.close();
            },
            open() {
                this.$refs.popup.open();
            },
            wxPtShare(type) {
                this.isWXPT = type;
                if (type) {
                    this.close();
                    this.$refs.posterPopup.open();
                } else {
                    this.$refs.posterPopup.close();
                }
            },
            savePosterPath() {
                let _this = this;
                saveImageToPhotosAlbum(_this.postImage, this.share.title, function(res) {
                    _this.$refs.posterPopup.close();
                });
            },
            async canvasPoster() {
                // 后台返回地址为base64
                console.log('share', this.share)
                // #ifdef MP
                if (checkLogin()) {
                    this.$refs.posterPopup.open()
                    this.setWxml()
                } else {
                    autoAuth()
                }
                return;
                // #endif
                let productImage = this.share.image,
                    bgImage,
                    reg = /(http|https):\/\/([\w.]+\/?)\S*/,
                    _this = this;

                _this.$refs.posterPopup.open();
                _this.$loadingToast('海报生成中');

                // // getProductCode(this.share.id)
                // // 	.then(async res1 => {
                let code = this.share.code;
                // // #ifdef MP-WEIXIN
                // // 获取微信小程序二维码 h5中二维码已随商品详情返回
                // let res = await getProductCode(this.share.id);
                // code = res.data.code;
                // // #endif
                // // #ifdef H5
                // if (!code) {
                // 	let res = await getProductCode(this.share.id);
                // 	code = res.data.code;
                // }
                // // #endif
                if (!code) {
                    // #ifdef MP
                    _this.$showToast('小程序二维码获取失败');
                    // #endif
                    // #ifdef H5
                    _this.$showToast('商品二维码获取失败');
                    // #endif
                    _this.close();
                    _this.$refs.posterPopup.close();

                    return;
                }

                if (reg.test(productImage)) {
                    const res = await imageBase64(productImage);
                    productImage = res.data.image;
                }
                if (reg.test(code)) {
                    const resCode = await imageBase64(false, code);
                    code = resCode.data.code;
                }
                // #ifdef H5
                bgImage = require('@/static/images/posterbackgd.png');
                // #endif
                // #ifdef MP
                bgImage = '/static/images/posterbackgd.png'; //小程序中不识别@，require引入不识别
                // #endif
                let arr = [bgImage, productImage, code];
                console.log('arr', arr);
                PosterCanvas(arr, _this.share.title, _this.share.price, _this, function(res) {
                    _this.$hideToast();
                    _this.close();
                    _this.postImage = res;
                });
            },
            // #ifdef H5
            initShare() {
                // if (_isWeixin) {
                let share = this.share;
                console.log('bbbb', share);
                var configAppMessage = {
                    desc: share.desc,
                    title: share.title,
                    link: share.href,
                    imgUrl: share.image
                };
                openShareAll(configAppMessage);
                // }
            }
            // #endif
        },
        mounted() {
            // this.open()
            // #ifdef H5
            this.initShare();
            // #endif
        }
        // #ifdef MP
        // onShareAppMessage() {} 仅在页面生命周期中生效 组件不生效
        // #endif
    };
</script>
<style scoped lang="scss">
    .generate-posters {
        width: 100%;
        height: 170rpx;
        background-color: #fff;
    }

    .generate-posters .item {
        flex: 50%;
        -webkit-flex: 50%;
        -ms-flex: 50%;
        text-align: center;
        font-size: 28rpx;
    }

    .generate-posters .item .iconfont {
        font-size: 80rpx;
        color: #5eae72;
    }

    .generate-posters .item .iconfont.icon-haibao {
        color: #5391f1;
    }

    .download {
        width: 100%;
        text-align: center;
        font-size: 32rpx;
        margin-top: 10rpx;
        color: #fff;
    }
</style>
