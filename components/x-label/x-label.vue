<template>
	<view class="xlabel">
	<!-- 	<view class="title" v-if="title && labelArr.length">
			<text class="font_size20">{{ titleTxt }}</text>
		</view> -->
		<view class="wrap flex relative" v-if="labelArr.length && item.label_group.length" v-for="(item, index) in labelArr" :key="index">
			<view class="name">{{ item.cate_name }}</view>
			<view class="wrap_list relative" :style="showIdx === index ? 'padding-right:40rpx':''">
				<view class="list relative">
					<view class="flex flex_align_center " :class="showIdx === index ? 'list_wrap' : 'list_nowrap'">
						<view
							class="item"
							v-for="(item1, index1) in item.label_group.slice(0, showIdx === index ? item.label_group.length : 3)"
							:key="index1"
							:class="{ active: item1.is_selected }"
							@click="labelClick(item, item1, index, index1)"
						>
							<view class="des" :class="{ bg_diy: diy }">
								<text>{{ item1.name }}</text>
							</view>
							<view class="mark" :class="{ bg_diy: diy }" v-if="item.is_scoring === 1">
								<text v-if="item1.score || item1.scoring">{{ score ? item1.score || '0.0' : Number(item1.scoring).toFixed(1)  || '0.0' }}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="more absolute" @click="showMore(index, item, false)" v-if="showIdx === index"><text class="iconfont icon-xiangshang"></text></view>
			</view>
			<view class="more absolute" @click="showMore(index, item, true)" v-if="!(showIdx === index)"><text class="iconfont icon-xiangxia"></text></view>
		</view>
		<view class="diy wrap flex" v-if="diy">
			<view class="name">自建标签</view>
			<view class=" wrap_list ">
				<view class="list flex flex_align_center flex_wrap">
					<view class="item " v-for="(item, index) in diyLabelArr" :key="index">
						<text>{{ item.name }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		arr: {
			type: Array,
			default() {
				return [];
			}
		},
		diyArr: {
			type: Array,
			default() {
				return [];
			}
		},
		title: {
			type: Boolean,
			default: true
		},
		titleTxt: {
			type: String,
			default: '更多测评维度'
		},
		diy: {
			type: Boolean,
			default: false
		},
		score: {
			type: Boolean,
			default: false
		}
	},
	watch: {
		arr(a, b) {
			//小程序中组建中涉及数据变化，需要watch监听，否则不生效
			this.labelArr = a;
		}
	},
	data() {
		return {
			labelArr: this.arr,
			diyLabelArr: this.diyArr,
			showIdx: 'xyz',
			label: {
				title: 'ddd',
				num: 0.0
			}
		};
	},
	methods: {
		goPages(path, type) {
			this.$navigator(path, type);
		},
		showMore(index, item, type) {
			if (item.label_group.length < 3) return this.$showToast('无更多');
			this.showIdx = type ? index : 'xyz';
		}
	},
	mounted() {
		// console.log('ddd',this.arr)
		// this.$refs.popup.open();
	}
};
</script>

<style scoped lang="scss">
.xlabel {
	.title {
		margin: 0rpx 0 24rpx 0;
		height: 40rpx;
		line-height: 40rpx;
		font-weight: 500;
		border-radius: 16rpx 16rpx 0px 0px;
		color: #989898;
		.font_size20 {
			transform-origin: left;
		}
	}
	.wrap {
		background: #fff;
		&:not(.diy) {
			// margin-bottom: 20rpx;
		}
		.name {
			width: 114rpx;
			height: 60rpx;
			line-height: 60rpx;
			overflow: hidden;
			white-space: nowrap;
			text-align: center;
			font-weight: 400;
			font-size: 28rpx;
			color: #333333;
		}
		.more {
			width:80rpx;
			height: 60rpx;
			line-height: 60rpx;
			font-weight: 400;
			text-align: center;
			background: #fff;
			// box-shadow: 0px 0px 20rpx rgba(107, 127, 153, 0.2);
			right: 0;
			text {
				position: absolute;
				display: inline-block;
				text-align: center;
				width: 68rpx;
				background: #ffffff;
				box-shadow: 0px 0px 20rpx rgba(107, 127, 153, 0.2);
				border-radius: 24rpx 0px 0px 24rpx;
				right: 0;
				
			}
			.iconfont {
				font-size: 24rpx;
			}
		}
		.wrap_list {
			margin: -20rpx 0rpx 0 0;
			padding: 20rpx 88rpx 20rpx 20rpx;
			width: calc(100% - 114rpx);
			overflow-x: hidden;
			.list {
				> view.flex {
					transition: max-height 0.4s;
					background: #fff;
					&.list_wrap {
						flex-wrap: wrap;
						max-height: 400rpx;
						.item {
							margin: 0 10rpx 18rpx 0;
						}
					}
					&.list_nowrap {
						flex-wrap: nowrap;
						max-height: 60rpx;
						width: 200%;
						.item {
							margin: 0 10rpx 0 0;
						}
					}
				}

				.item {
					line-height: 60rpx;
					padding: 0 12rpx;
					border-radius: 24rpx;
					background: #fff;
					box-shadow: 0px 0px 20rpx rgba(107, 127, 153, 0.2);
					white-space: nowrap;
					color: #666666;
					text {
						display: inline-block;
						font-size: 24rpx;
					}

					.des {
						display: inline-block;
						color: #666666;
					}
					.mark {
						color: #3e3e3e;
						font-weight: 500;
						min-width: 40rpx;
						text-align: center;
						display: inline-block;
						// background: #62b7ff;
						padding-left: 4rpx;
						&.bg_diy {
							// background: #b7b7b7;
						}
					}
					&.active {
						.des,
						.mark {
							color: red;
						}
					}
					&:last-child{
						margin-right: 88rpx !important;
					}
				}
				// .more {
				// 	top: auto;
				// 	right: -88rpx; //内边距右
				// 	bottom: 20rpx;
				// 	height: 60rpx;
				// }
			}
.more {
					top: auto;
					right: 0; //内边距右
					bottom: 40rpx; //内边距20 + 阴影 20
					height: 60rpx;
				}
		}
	}
	.diy {
		.wrap_list {
			width: calc(100% - 114rpx);
			.item {
				margin: 0 18rpx 18rpx 0;
				padding: 12rpx;
				border-radius: 24rpx;
				background: #fff;
				box-shadow: 0px 0px 20rpx rgba(107, 127, 153, 0.2);
				&.add {
					color: #ff5656;
				}
			}
		}
	}
}
</style>
