<template>
	<view class="orderGoods">
		<view class="total">共{{ cartInfo.length }}件商品</view>
		<template v-if="isNew">
			<view class="newGoodErap">
				<view class="item relative" v-for="(item, index) in cartInfo" :key="item.id">
					<view class=" bg"><image :src="item.productInfo.image" mode="heightFix"></image></view>
					<view class="wrap absolute flex flex_column flex_between ">
						<view class="wrap_t flex flex_between">
							<view class="title">
								<view>{{ item.productInfo.store_name }}</view>
								<view class="time"></view>
							</view>
							<view class="num">x {{ item.cart_num }}</view>
						</view>
						<view class="wrap_b flex flex_between flex_align_center" @click.stop>
							<view class="b_l flex flex_align_center">
								<!-- <view class="span">{{ item.yw_index.toFixed(1) }}</view> -->
								<view class="image">
									<!-- <view class="bg"></view> -->
									<!-- <view class="bg" :style="{ width: (item.yw_index / 5) * 100 + '%' }"></view> -->
								</view>
							</view>
							<view class="b_r">￥{{ item.productInfo.attrInfo ? item.productInfo.attrInfo.price : item.productInfo.price }}</view>
						</view>
					</view>
				</view>
				<view class="price_total">共计<text> ￥{{price}}</text></view>
			</view>
		</template>
		<template v-else>
			<view class="goodWrapper">
				<view class="item acea-row row-between-wrapper" v-for="cart in cartInfo" :key="cart.id">
					<view class="pictrue" @click="goPages(`/pages/shop/GoodsCon?id=${cart.product_id}`)"><image :src="cart.productInfo.image" alt="img" class="image" mode="heightFix"/></view>
					<view class="text">
						<view class="acea-row row-between-wrapper">
							<view class="name line1">{{ cart.productInfo.store_name }}</view>
							<view class="num">x {{ cart.cart_num }}</view>
						</view>
						<view class="attr line1" v-if="cart.productInfo.attrInfo">{{ cart.productInfo.attrInfo.suk }}</view>
						<view class="money font-color-red">￥{{ cart.productInfo.attrInfo ? cart.productInfo.attrInfo.price : cart.productInfo.price }}</view>
						<view class="evaluate" v-if="evaluate === 3" @click="goPages('/pages/shop/GoodsEvaluate?unique=' + cart.unique)">评价</view>
					</view>
				</view>
			</view>
		</template>
	</view>
</template>
<script>
export default {
	name: 'OrderGoods',
	props: {
		evaluate: Number,
		cartInfo: {
			type: Array,
			default: () => []
		},
		isNew: {
			//是否是最新版本的
			type: Boolean,
			default: false
		},
		price: {
			//是否是最新版本的
			type: String,
			default: ''
		}
	},
	watch:{
	},
	data: function() {
		return {};
	},
	mounted: function() {},
	methods: {
		goPages(path) {
			if (this.isNew) {
				console.log('最新版本，禁止跳转');
				return;
			}
			this.$navigator(path);
		}
	}
};
</script>
<style scoped lang="scss">
.orderGoods {
	// background-color: #fff;
	margin-top: 12rpx;
}

.orderGoods .total {
	width: 100%;
	height: 86rpx;
	padding: 0 30rpx;
	// border-bottom: 1rpx solid #eee;
	font-size: 32rpx;
	color: #282828;
	line-height: 86rpx;

	font-weight: bold;
}
.newGoodErap {
	padding: 30rpx;
	image {
		width: 100%;
		height: 100%;
	}
	.item {
		padding: 0;
		height: 240rpx;
		margin-bottom: 20rpx;
		.bg {
			height: 100%;
			image {
				border-radius: 30rpx;
				&:after {
					content: '';
					width: 100%;
					height: 100%;
					position: absolute;
					left: 0;
					top: 0;
					border-radius: 30rpx;
					background: linear-gradient(270deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.46) 100%);
					// filter: blur(2px);
				}
			}
		}
		.wrap {
			padding: 20rpx 36rpx;
			top: 0;
			height: 100%;
			color: #fff;
			.wrap_t {
				.title {
					font-size: 32rpx;

					font-weight: bold;
					.time {
						padding-top: 10rpx;
						font-weight: 400;
						font-size: 24rpx;
					}
				}
				.num {
					font-size: 32rpx;

					font-weight: bold;
				}
			}
			.wrap_b {
				.b_l {
					.span {
						width: 60rpx;
						height: 36rpx;
						border-radius: 26rpx;
						line-height: 36rpx;
						text-align: center;
						font-size: 22rpx;
						color: #ffffff;
						background: #ff5656;
					}
					.image {
						position: relative;
						width: 28rpx;
						height: 28rpx;
						margin: 0 16rpx 0 8rpx;
						.bg {
							position: absolute;
							width: 100%;
							top: 0;
							background-repeat: repeat-x !important;
							overflow: hidden;
							&:first-child {
								background: url('@/static/images/yuanshi/star1.png');
								background-size: 28rpx 28rpx;
							}
							&:last-child {
								background: url('@/static/images/yuanshi/star.png');
								background-size: 28rpx 28rpx;
							}
						}
					}
				}
				.b_r {
					font-weight: 500;
					font-size: 40rpx;
				}
			}
		}
	}
	.price_total{
		
color: #666666;
font-weight: bold;
font-size: 32rpx;
		text-align: right;
		text{
		
color: #FF5656;	
font-weight: 500;
font-size: 34rpx;
		}
	}
}
.goodWrapper .item {
	margin-left: 30rpx;
	padding-right: 30rpx;
	border-bottom: 2rpx solid #f0f0f0;
	height: 180rpx;
}

.goodWrapper .item .pictrue {
	width: 130rpx;
	height: 130rpx;
}

.goodWrapper .item .pictrue .image {
	width: 100%;
	height: 100%;
	border-radius: 6rpx;
}

.goodWrapper .item .text {
	width: 537rpx;
	position: relative;
}

.goodWrapper .item .text .name {
	font-size: 28rpx;
	color: #282828;
	width: 453rpx;
}

.goodWrapper .item .text .num {
	font-size: 26rpx;
	color: #868686;
}

.goodWrapper .item .text .attr {
	font-size: 20rpx;
	color: #868686;
	margin-top: 7rpx;
}

.goodWrapper .item .text .money {
	font-size: 26rpx;
	margin-top: 17rpx;
}

.goodWrapper .item .text .evaluate {
	position: absolute;
	width: 113rpx;
	height: 46rpx;
	border: 1px solid #e93323;
	color: #e93323;
	border-radius: 4rpx;
	text-align: center;
	line-height: 46rpx;
	right: 0;
	bottom: -10rpx;
}

.goodWrapper .item .text .evaluate.userEvaluated {
	font-size: 26rpx;
	color: #aaa;
	background-color: #f7f7f7;
	border-color: #f7f7f7;
}
</style>
