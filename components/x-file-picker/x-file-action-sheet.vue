<template>
	<!-- 底部弹出面板 -->
	<view class="">
		<view @click="showActionSheet" class="btn">
			<slot>
				<view class="is-add">
					<view class="icon-add"></view>
					<view class="icon-add rotate"></view>
				</view>
			</slot>
		</view>
		<view class="x_file_picker">
			<x-file-picker
				v-model="fileValue"
				:disabled="disabled"
				:disablePreview="disablePreview"
				:delIcon="delIcon"
				:auto-upload="autoUpload"
				:limit="limit"
				:mode="mode"
				:fileExtname="fileExtname"
				:title="title"
				:list-styles="listStyles"
				:imageStyles="imageStyles"
				:readonly="readonly"
				:returnType="returnType"
				:disabledCloud="disabledCloud"
				:disabledCloudConfig="disabledCloudConfig"
				:sourceType="sourceType"
				:fileMediatype="fileMediatype"
				:opts="opts"
				ref="xFilePicker"
				@select="select"
				@progress="progress"
				@success="success"
				@fail="fail"
				@delete="deleteFile"
                @send="getSonValue"
			>
				<slot name="file"></slot>
			</x-file-picker>
		</view>
	</view>
</template>

<script>
import xFilePicker from './x-file-picker.vue';
export default {
	components: {
		xFilePicker
	},
	props: {
		// 上传文件相关参数
		disabled: {
			type: Boolean,
			default: false
		},
		disablePreview: {
			type: Boolean,
			default: false
		},
		delIcon: {
			type: Boolean,
			default: true
		},
		// 自动上传
		autoUpload: {
			type: Boolean,
			default: true
		},
		// 最大选择个数 ，h5只能限制单选或是多选
		limit: {
			type: [Number, String],
			default: 9
		},
		// 列表样式 grid | list | list-card
		mode: {
			type: String,
			default: 'grid'
		},
		// 文件类型筛选
		fileExtname: {
			type: [Array, String],
			default() {
				return [];
			}
		},
		title: {
			type: String,
			default: ''
		},
		listStyles: {
			type: Object,
			default() {
				return {
					// 是否显示边框
					border: true,
					// 是否显示分隔线
					dividline: true,
					// 线条样式
					borderStyle: {}
				};
			}
		},
		imageStyles: {
			type: Object,
			default() {
				return {
					width: 'auto',
					height: 'auto'
				};
			}
		},
		readonly: {
			type: Boolean,
			default: false
		},
		returnType: {
			type: String,
			default: 'array'
		},
		// 禁用上传至云服务器
		disabledCloud: {
			type: Boolean,
			default: false
		},
		// 禁用上传至云服务器需要的配置
		disabledCloudConfig: {
			type: Object,
			default() {
				return {
					url: '',
					header: {
						// #ifdef MP
						'Content-Type': 'multipart/form-data',
						// #endif
						['Authorization']: ''
					}
				};
			}
		},
		sourceType: {
			type: [Array, String],
			default() {
				return ['album', 'camera']; //仅在type为image或video时可用
			}
		},
		// 选择文件类型  image/video/audio/all
		fileMediatype: {
			type: String,
			default: 'image'
		},
		allowWX: {
			type: Boolean,
			default: true
		},
		opts: {
			type: Object,
			default () {
				return {
					compressed: true,
					limitSize: 0, //仅video生效
                    maxDuration: 15, //仅拍摄视频生效
                    limitDuration: 15 //仅video生效
				}
			}
		}
	},
	components: {},
	data() {
		return {
			fileValue: [],
			selectFiles:{
				image:[],
				video:[],
				audio:[],
				all:[]
			}
		};
	},
	computed: {
		showActions() {
			const type = this.fileMediatype,
				allowWX = this.allowWX;
			if (type === 'audio') {
				let arr = ['录音'];
				// #ifdef MP-WEIXIN
				if (allowWX) {
					arr.push('从好友列表获取');
				}
				// #endif
				return arr;
			} else {
				let arr = ['拍摄', '从手机相册选取'];
				// #ifdef MP-WEIXIN
				if (allowWX) {
					arr.push('从好友列表获取');
				}
				// #endif
				return arr;
			}
		}
	},
	methods: {
		select(e, type, files) {
			this.selectFiles[type]=files;
			this.$emit('select', e, type, files);
		},
		// 获取上传进度
		progress(e,type) {
			this.$emit('progress', e,type);
		},
		// 上传成功
		success(e,type) {
			this.$emit('success', e,type);
		},
		// 上传失败
		fail(e,type) {
			this.$emit('fail', e,type);
		},
		deleteFile(e,type) {
			this.$emit('delete', e,type);
		},
        getSonValue(res){
            this.$emit('getSonValue', res);
        },

		showActionSheet() {
			const type = this.fileMediatype,
				allowWX = this.allowWX,
				refFile = this.$refs.xFilePicker,
				_this = this;
				if(this.selectFiles[type].length>=Number(this.limit)){
					return uni.showToast({
						title: `最多允许选择 ${this.limit} 个当前类型文件`,
						icon: 'none'
					}); 
				}
			uni.showActionSheet({
				itemList: this.showActions,
				success: function(res) {
					const idx = res.tapIndex;
					let sourceType = [],
						chooseWX = false;
					if (type === 'audio') {
						if (idx === 0) {
							_this.$emit('recorder');
						} else {
							refFile.choose();
						}
					} else {
						if (idx === 0) {
							chooseWX = false;
							sourceType = ['camera'];
						} else if (idx === 1) {
							chooseWX = false;
							sourceType = ['album'];
						} else {
							chooseWX = true;
						}
						refFile.choose(sourceType, chooseWX);
					}
					_this.$emit('show', res.tapIndex, _this.fileMediatype);
					console.log('选中了第' + (res.tapIndex + 1) + '个按钮');
				},
				fail: function(res) {
					console.log(res.errMsg);
				}
			});
		},
		upload(recorder) {
			this.$refs.xFilePicker.upload(recorder);
		},
		delFile(index) {
			this.$refs.xFilePicker.delFile(index);
		}
	},
	mounted() {},
	onLoad(option) {}
};
</script>

<style scoped lang="scss">
.x_file_picker {
	display: none;
}
.btn {
	position: relative;
}
.is-add {
	width: 70px;
	height: 70px;
	border: 1px solid #f1f1f1;
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	align-items: center;
	justify-content: center;
}

.icon-add {
	width: 50px;
	height: 5px;
	background-color: #f1f1f1;
	border-radius: 2px;
}

.rotate {
	position: absolute;
	transform: rotate(90deg);
}
</style>
