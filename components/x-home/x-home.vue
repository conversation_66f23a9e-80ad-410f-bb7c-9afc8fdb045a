<template>
	<view v-if="homeShow" class="home" style="position:fixed;" :style="{ top: top + 'px', bottom: bottom }" id="right-nav" @touchmove.stop.prevent="touchmove">
		<view class="homeCon bg-color-red" :class="homeOpen ? 'on' : ''" v-if="homeOpen">
			<view @click="goPages('/pages/tabBar/index/index', 'switchTab')" class="iconfont icon-shouye-xianxing"></view>
			<!-- <view @click="goPages('/pages/shopCart/shopCart')" class="iconfont icon-caigou-xianxing"></view> -->
			<view @click="goPages('/pages/tabBar/user/user', 'switchTab')" class="iconfont icon-yonghu1"></view>
		</view>
		<view @click="open" class="pictrueBox">
			<view class="pictrue"><image :src="homeOpen ? '/static/images/close.gif' : '/static/images/open.gif'" class="image" /></view>
		</view>
	</view>
</template>
<script>
import { VUE_APP_URL } from '@/config.js';
export default {
	props: {
		show: {
			//小程序中使用该字段 H5中路由中配置即可
			type: Boolean,
			default: true
		}
	},
	watch: {
		'$store.getters.home': function(newVal, oldVal) {}
	},
	data() {
		return {
			imagePath: VUE_APP_URL,
			homeOpen: false,
			homeShow: true,
			top: '',
			bottom: '180rpx'
		};
	},
	methods: {
		goPages(path, type) {
			this.$navigator(path, type);
		},
		touchmove(e) {
			if (e.touches[0].clientY < 545 && e.touches[0].clientY > 66) {
				this.top = e.touches[0].clientY;
			}
		},
		open: function() {
			this.homeOpen = !this.homeOpen;
		}
	},
	mounted() {
		// #ifdef MP
		this.homeShow = this.show;
		// #endif
		// #ifndef MP
		this.homeShow = this.$store.state.home;
		// #endif
	}
};
</script>

<style lang="scss" scoped>
.home {
	position: fixed;
	color: white;
	text-align: center;
	z-index: 333;
	right: 15rpx;
	display: flex;
}

.home .homeCon {
	border-radius: 50rpx;
	opacity: 0;
	height: 0;
	color: #e93323;
	width: 0;
}

.home .homeCon.on {
	opacity: 1;
	animation: bounceInRight 0.5s cubic-bezier(0.215, 0.610, 0.355, 1.000);
	width: 300rpx;
	height: 86rpx;
	margin-bottom: 20rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	background: #f44939 !important;
}

.home .homeCon .iconfont {
	font-size: 48rpx;
	color: #fff;
	display: inline-block;
	margin: 0 auto;
}

.home .pictrue {
	width: 86rpx;
	height: 86rpx;
	border-radius: 50%;
	margin: 0 auto;
}

.home .pictrue .image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	transform: rotate(90deg);
	background-color: #e93323;
}

.pictrueBox {
	width: 130rpx;
	height: 120rpx;
}
</style>
