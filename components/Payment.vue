<template>
    <view>
        <view class="payment" :class="value === true ? 'on' : ''">
            <view class="title acea-row row-center-wrapper">
                支付方式
                <span class="iconfont icon-guanbi" @click="close"></span>
            </view>
            <view class="item acea-row row-between-wrapper" v-if="types.indexOf('bytedance') !== -1"
                @click="checked('bytedance')">
                <view class="left acea-row row-between-wrapper">
                    <image style="width: 46rpx; height: 46rpx; margin-right: 10rpx;"
                        src="@/static/images/yuanshi/douyin.png" mode=""></image>
                    <view class="text">
                        <view class="name">抖音支付</view>
                        <view class="info">使用抖音快捷支付</view>
                    </view>
                </view>
                <view class="image" v-if="paytype==='bytedance'">
                    <image src="@/static/images/yuanshi/check.png" mode="widthFix"></image>
                </view>
                <!-- <view class="iconfont icon-xiangyou"></view> -->
            </view>


            <view class="item acea-row row-between-wrapper" v-if="types.indexOf('weixin') !== -1"
                @click="checked('weixin')">
                <view class="left acea-row row-between-wrapper">
                    <view class="iconfont icon-weixin2"></view>
                    <view class="text">
                        <view class="name">微信支付</view>
                        <view class="info">使用微信快捷支付</view>
                    </view>
                </view>
                <view class="image" v-if="paytype==='weixin'">
                    <image src="@/static/images/yuanshi/check.png" mode="widthFix"></image>
                </view>
                <!-- <view class="iconfont icon-xiangyou"></view> -->
            </view>
            <view class="item acea-row row-between-wrapper" v-if="types.indexOf('alipay') !== -1"
                @click="checked('alipay')">
                <view class="left acea-row row-between-wrapper">
                    <view class="iconfont icon-zhifubao"></view>
                    <view class="text">
                        <view class="name">支付宝支付</view>
                        <view class="info">使用线上支付宝支付</view>
                    </view>
                </view>
                <view class="image" v-if="paytype==='alipay'">
                    <image src="@/static/images/yuanshi/check.png" mode="widthFix"></image>
                </view>
                <!-- <view class="iconfont icon-xiangyou"></view> -->
            </view>
            <view class="item acea-row row-between-wrapper" v-if="types.indexOf('yue') !== -1" @click="checked('yue')">
                <view class="left acea-row row-between-wrapper">
                    <view class="iconfont icon-yuezhifu"></view>
                    <view class="text">
                        <view class="name">余额支付</view>
                        <view class="info">
                            当前可用余额：
                            <span class="money">{{ now_money || 0}}</span>
                        </view>
                    </view>
                </view>
                <view class="image" v-if="paytype==='yue'">
                    <image src="@/static/images/yuanshi/check.png" mode="widthFix"></image>
                </view>
                <!-- <view class="iconfont icon-xiangyou"></view> -->
            </view>
            <view class="item acea-row row-between-wrapper" v-if="types.indexOf('offline') !== -1"
                @click="checked('offline')">
                <view class="left acea-row row-between-wrapper">
                    <view class="iconfont icon-yuezhifu1"></view>
                    <view class="text">
                        <view class="name">线下支付</view>
                        <view class="info">选择线下付款方式</view>
                    </view>
                </view>
                <view class="image" v-if="paytype==='offline'">
                    <image src="@/static/images/yuanshi/check.png" mode="widthFix"></image>
                </view>
                <!-- <view class="iconfont icon-xiangyou"></view> -->
            </view>
            <template v-if="other">
                <view class="other">
                    <view class="money">支付：<text>￥{{paymoney}}</text></view>
                    <view class="btn" @click="nowPay">立即支付</view>
                </view>
            </template>
        </view>
        <view class="mask" v-show="value" @click="close"></view>
    </view>
</template>
<script>
    import {
        getuserInfo
    } from '@/utils/common.js';
    import {
    	yIndex
    } from '@/api/yuanshi/public';
    export default {
        name: 'Payment',
        props: {
            value: {
                type: Boolean,
                default: false
            },
            balance: {
                type: [Number, String],
                default: 0
            },
            other: {
                type: Boolean,
                default: false
            },
            paymoney: {
                type: [Number, String],
                default: 0
            },
            types: {
                type: Array,
                default: () => ['bytedance', 'weixin', 'alipay', 'yue', 'offline']
            }
        },
        created() {
            this.platform = uni.getSystemInfoSync().platform;
            console.log('this.platform', this.platform)
        },
        data: function() {
            return {
                now_money: '0.00',
                // #ifndef MP-TOUTIAO
                paytype: 'weixin',
                // #endif
                // #ifdef MP-TOUTIAO
                paytype: 'bytedance',
                platform: '',
                site_wechat:''
                // #endif
            };
        },
        mounted: function() {
            getuserInfo().then(res => {
                this.now_money = res.data.now_money || this.balance;
            })
            yIndex().then(res => {
            	this.site_wechat = res.data.site_wechat;
                console.log('联系客服---：',this.site_wechat)
            });
        },
        methods: {
            checked: function(type) {
                this.paytype = type;
                if (!this.other) {
                    this.$emit('checked', type);
                    this.close();
                }
            },
            close: function() {
                this.$emit('input', false);
            },
            nowPay() {
                // #ifdef MP-TOUTIAO
                // let that = this;
                // if (this.platform === 'ios') {
                //     tt.showModal({
                //         title: "支付提示",
                //         cancelColor: '#787b7b',
                //         confirmColor: '#e93323',
                //         cancelText: '确定',
                //         confirmText: '复制微信',
                //         content: '很抱歉，由于相关规范，暂不支持IOS支付。如需帮助请联系客服或微信搜索好友：' + that.site_wechat ,
                //         success(res) {
                //             if (res.confirm) {
                //                 tt.setClipboardData({
                //                   data: that.site_wechat,
                //                   success(res) {
                //                       return that.$showToast('复制成功')
                //                   },
                //                   fail(res) {
                //                       return that.$showToast('复制失败')
                //                   },
                //                 });
                //             } else if (res.cancel) {
                //                 console.log("cancel, cold");
                //             } else {
                //                 // what happend?
                //             }
                //         },
                //         fail(res) {
                //             console.log(`showModal调用失败`);
                //         },
                //     });
                //     return;
                // }
                // #endif
                this.$emit('nowPay', this.paytype);
                this.close()
            }
        }
    };
</script>
<style scoped lang="scss">
    .payment {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        border-radius: 70rpx 70rpx 0 0;
        box-shadow: 0px 0px 30rpx 0px rgba(107, 127, 153, 0.30);
        background-color: #fff;
        padding-bottom: 60rpx;
        z-index: 6666;
        transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);

        transform: translate3d(0, 100%, 0);
    }

    .payment.on {
        transform: translate3d(0, 0, 0);
    }

    .payment .title {
        position: relative;
        text-align: center;
        height: 123rpx;
        font-size: 32rpx;
        color: #282828;
        font-weight: 700;
        color: #333333;
    }

    .payment .title .iconfont {
        position: absolute;
        right: 30rpx;
        top: 50%;
        transform: translateY(-50%);
        font-size: 43rpx;
        color: #8a8a8a;
        font-weight: normal;
    }

    .payment .item {
        border-bottom: 1rpx solid #eee;
        height: 130rpx;
        margin-left: 30rpx;
        padding-right: 30rpx;
    }

    .payment .item .left {
        width: 610rpx;
    }

    .payment .item .left .text {
        width: 540rpx;
    }

    .payment .item .left .text .name {
        font-size: 32rpx;
        color: #282828;
    }

    .payment .item .left .text .info {
        font-size: 24rpx;
        color: #999;
    }

    .payment .item .left .text .info .money {
        color: #ff9900;
    }

    .payment .item .left .iconfont {
        font-size: 45rpx;
        color: #09bb07;
    }

    .payment .item .left .iconfont.icon-zhifubao {
        color: #00aaea;
    }

    .payment .item .left .iconfont.icon-yuezhifu {
        color: #ff9900;
    }

    .payment .item .left .iconfont.icon-yuezhifu1 {
        color: #eb6623;
    }

    .payment .item .iconfont {
        font-size: 30rpx;
        color: #999;
    }

    .payment {
        .item {
            .image {
                width: 44rpx;
                height: 44rpx;

                image {
                    width: 100%;
                }
            }
        }

        .other {
            .money {
                margin: 30rpx 0;
                font-size: 32rpx;
                font-family: PingFang SC, PingFang SC-Bold;
                font-weight: 700;
                text-align: left;
                color: #333333;
                text-align: center;

                text {
                    color: #FF5656
                }
            }

            .btn {
                width: 670rpx;
                text-align: center;
                margin: 0 auto;
                height: 100rpx;
                line-height: 100rpx;
                background: #ff5656;
                border-radius: 50rpx;
                font-size: 28rpx;
                font-family: PingFang SC, PingFang SC-Bold;
                font-weight: 700;
                text-align: center;
                color: #ffffff;
            }
        }
    }
</style>
