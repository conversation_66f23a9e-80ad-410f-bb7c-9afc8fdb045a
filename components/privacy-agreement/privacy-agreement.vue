<template>
	<view>
		<view class="address-window" :class="[showPrivacy === true ? 'on' : '', largeSize === true ? 'setbig' : '']">
            <view class="title">
                <image class="title-logo" src="../../static/images/logo3.png" mode=""></image>
                <view class="title-txt">
                    {{privacyContractName}}提示
                </view>
            </view>
            <view class="notes">
                <view class="notes-top">
                    使用前请仔细阅读
                </view>
                <view class="notes-bot">
                    <text class="notes-bot-text"
                        @click.stop="openPrivacyProtection">{{ privacyContractName }}</text>
                        当您点击同意后，即表示您已理解并同意该条款内容，并可正常使用服务。如您拒绝，将无法使用该服务。
                </view>
            </view>
            
            <view class="task-btn">
                <view class="left" @click="handleRefuse">
                    拒绝
                </view>
                <button id="agree-btn" class="left right"
                    open-type="agreePrivacyAuthorization" @agreeprivacyauthorization="handleAgree">
                    同意
                </button>
            </view>
		</view>
		<view class="mask" @touchmove.prevent :hidden="showPrivacy === false" @click="handleRefuse"></view>
	</view>
</template>
<script>
    import {
        mapGetters
    } from 'vuex';
export default {
	name: 'StudentWindow',
	props: {
		
	},
    computed: mapGetters(['agreementPopupShow']),
	data: function() {
		return {
            headimg:'../../static/l-unhead.png',
            resolvePrivacyAuthorization: null,
            showPrivacy: false,
            largeSize:false, // 弹窗高度适配自定义导航栏
            privacyContractName: '', // 小程序协议名称
            getstatus:true, // true 未授权状态 需弹窗；false 无需授权
		};
	},
    watch: {
        // 组件监听是否登录判断弹窗，字节小程序除抖音外（头条。西瓜等不支持组件内监听，需单个写入每个页面）
        agreementPopupShow: function(newVal, oldVal) {
            let _this = this;
            if (newVal) {
                this.$nextTick(() => {
                    _this.open();
                });
            } else {
                _this.close()
            }
        }
    },
	mounted() {
	    // this.init();
	},
    created() {
        this.init();
    },
	methods: {
        open() {
            this.showPrivacy = true;
            this.$store.commit('SHOW_AGREEMENT_POPUP_SHOW');
        },
        close() {
            this.showPrivacy = false;
            this.$store.commit('HIDE_AGREEMENT_POPUP_SHOW');
        },
        handleAgree() {
            // 需要用户同意隐私授权时
            // 弹出开发者自定义的隐私授权弹窗
            // 用户同意协议
            this.resolvePrivacyAuthorization({
                buttonId: "agree-btn",
                event: "agree",
            });
            this.close();
            this.$emit("confirm", false);
        },
        handleRefuse() {
            this.resolvePrivacyAuthorization({
                event: "disagree",
            });
            this.close();
            this.$emit("cancel", this.getstatus);
        },
        openPrivacyProtection() {
            uni.openPrivacyContract({
                success: (res) => {
                    console.log("openPrivacyContract success");
                },
                fail: (res) => {
                    console.error("openPrivacyContract fail", res);
                },
            });
        },
        init() {
            // 查看是否需要阅读微信隐私协议
            try {
                // 获取加载的页面对象
                const pages = getCurrentPages();
                // 获取当前页面的对象
                const currentPage = pages[pages.length - 1];
                if(currentPage.route == 'pages/tabBar/index/index' || currentPage.route == 'pages/tabBar/user/user'){
                    this.largeSize = true;
                }else {
                    this.largeSize = false;
                }
                
                uni.getPrivacySetting({
                    success: (res) => {
                        console.log('查看是否需要阅读微信隐私协议',res)
                        // 如果是false，则说明之前授权过无需弹出隐私协议
                        if(res.needAuthorization == true){
                            uni.onNeedPrivacyAuthorization((resolve) => {
                                // console.log('弹出隐私指引授权',resolve)
                                this.open();
                                this.privacyContractName = res.privacyContractName;
                                this.resolvePrivacyAuthorization = resolve;
                            });
                            wx.requirePrivacyAuthorize({
                                  success: () => {
                                    // 用户同意授权
                                    // 继续小程序逻辑
                                  },
                                  fail: () => {}, // 用户拒绝授权
                                  complete: () => {}
                                })
                        }else {
                            // 授权提示根据需求删除
                            // uni.showToast({
                            //     title:'用户隐私协议已授权',
                            //     icon:'none',
                            //     duration:2500
                            // })
                            return;
                        }
                        
                    },
                    fail: () => {},
                    complete: () => {},
                });
            } catch (e) {
                //TODO handle the exception
            }
        },
	}
};
</script>
<style lang="scss" scoped>
    view,
    scroll-view,
    swiper,
    swiper-item,
    cover-view,
    cover-image,
    icon,
    text,
    rich-text,
    progress,
    button,
    checkbox,
    form,
    input,
    label,
    radio,
    slider,
    switch,
    textarea,
    navigator,
    audio,
    camera,
    image,
    video {
        box-sizing: border-box;
    }
    button {
        padding: 0upx;
        margin: 0upx;
        border: none;
        border-radius: 0upx;
        box-sizing: border-box;
        background-color: transparent;
    }
    
    button.button-hover {
        transform: translate(3rpx, 3rpx);
        background-color: transparent;
    }
    
    button::after {
        border: none
    }
    
    .mask {
    	position: fixed;
    	top: 0;
    	left: 0;
    	right: 0;
    	bottom: 0;
    	background-color: #000;
    	opacity: 0.5;
    	z-index: 9998;
    }
    
	.address-window {
		background-color: #fff;
		position: fixed;
        bottom: 0;
		left: 0;
		width: 100%;
		z-index: 9999;
        padding: 32rpx 40rpx 100rpx;
		transform: translate3d(0, 100%, 0);
		-webkit-transform: translate3d(0, 100%, 0);
		-ms-transform: translate3d(0, 100%, 0);
		-moz-transform: translate3d(0, 100%, 0);
		-o-transform: translate3d(0, 100%, 0);
		transition: all .3s cubic-bezier(.25, .5, .5, .9);
		-webkit-transition: all .3s cubic-bezier(.25, .5, .5, .9);
		-moz-transition: all .3s cubic-bezier(.25, .5, .5, .9);
		-o-transition: all .3s cubic-bezier(.25, .5, .5, .9);
        border-radius: 24rpx 24rpx 0 0;
        box-shadow: 0px 0px 30rpx 30rpx rgba(107,127,153,0.30);
        box-sizing: border-box;
        &.setbig {
            bottom: calc(constant(safe-area-inset-bottom) + 120rpx);
            bottom: calc(env(safe-area-inset-bottom) + 120rpx);
        }
        &.on {
            transform: translate3d(0, 0, 0);
            -webkit-transform: translate3d(0, 0, 0);
            -ms-transform: translate3d(0, 0, 0);
            -moz-transform: translate3d(0, 0, 0);
            -o-transform: translate3d(0, 0, 0);
        }
        .title {
            width: 100%;
                padding: 36rpx 10rpx 40rpx;
                box-sizing: border-box;
                overflow: auto;
                
            .title-logo {
                float: left;
                width: 40rpx;
                height: 40rpx;
                border-radius: 50%;
                margin-right: 20rpx;
            }
            .title-txt {
                float: left;
                font-size: 28rpx;
                font-family: PingFang SC, PingFang SC-Semibold;
                font-weight: 600;
                text-align: left;
                color: #000;
            }
        }
        .notes {
            width: 100%;
            
            .notes-top {
                width: 100%;
                font-size: 32rpx;
                font-family: PingFang SC, PingFang SC-Semibold;
                font-weight: 600;
                text-align: left;
                color: #000;
            }
            .notes-bot {
                width: 100%;
                font-size: 26rpx;
                font-family: PingFang SC, PingFang SC-Semibold;
                font-weight: 400;
                text-align: left;
                color: #666;
                margin-top: 50rpx;
                .notes-bot-text {
                    color: #1876ff;
                }
            }
        }
        .userbox {
            width: 100%;
            border-top: 2rpx solid #ededed;
            margin-top: 30rpx;
            .mydata-arrow-right {
                border-color: #fff;
                padding: 0rpx !important;
                margin: 0rpx !important;
                border: none !important;
                border-radius: 0rpx !important;
                box-sizing: border-box !important;
                background-color: transparent !important;
                
            }
            .userbox-item {
                width: 100%;
                height: 112rpx;
                border-bottom: 2rpx solid #ededed;
                overflow: auto;
                
                .userbox-item-img {
                    float: left;
                    width: 70rpx;
                    height: 70rpx;
                    border-radius: 8rpx;
                    margin-top: 20rpx;
                    margin-right: 20rpx;
                }
                .userbox-item-txt {
                    float: left;
                    font-size: 26rpx;
                    font-family: PingFang SC, PingFang SC-Semibold;
                    font-weight: 400;
                    line-height: 112rpx;
                    text-align: left;
                    color: #666;
                }
                .userbox-item-nicheng {
                    float: left;
                    font-size: 26rpx;
                    font-family: PingFang SC, PingFang SC-Semibold;
                    font-weight: 400;
                    line-height: 112rpx;
                    text-align: left;
                    color: #000;
                }
                .userbox-item-input {
                    float: left;
                    font-size: 26rpx;
                    font-family: PingFang SC, PingFang SC-Semibold;
                    font-weight: 400;
                    line-height: 112rpx;
                    text-align: left;
                    color: #000;
                    margin-top: 34rpx;
                        margin-left: 36rpx;
                }
            }
        }
        .task-btn {
            width: 80%;
            padding: 0 30rpx;
            box-sizing: border-box;
            margin: 84rpx auto 0;
            .left {
                float: left;
                width: 210rpx;
                height: 72rpx;
                line-height: 72rpx;
                text-align: center;
                background: #F2F2F2;
                border-radius: 8rpx;
                font-size: 28rpx;
                font-family: PingFang SC, PingFang SC-Regular;
                font-weight: 400;
                text-align: center;
                color: #5DBC6F;
                &.right {
                    float: right;
                    background: #5DBC6F;
                    color: #ffffff;
                }
            }
        }
        
	}

</style>