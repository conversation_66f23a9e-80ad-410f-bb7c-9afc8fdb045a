<template>
    <view class="play flex flex_align_center">
        <view class="status" @click="xPlayAudio">
            <image src="@/static/images/community/playing.png" v-if="autoplay" />
            <image src="@/static/images/community/play.png" v-else />
        </view>
        <view class="play_r">
            <view class="">{{title || '音频文件'}}</view>
            <view class="flex flex_align_center" style="width: 100%;margin-top: 8rpx;">
                <text class="time">{{currentTime}}</text>
                <view class="progress" @click="sliderChange">
                    <u-slider v-model="progress" block-width="6" @end="sliderChange" step="1" :use-slot="true">
                        <view class="">
                            <view class="badge-button">
                                <image src="@/static/images/community/scale.png"></image>
                            </view>
                        </view>
                    </u-slider>
                </view>
                <text class="time">{{duration}}</text>
            </view>

        </view>
    </view>
</template>

<script>
    import backgroundAudio from '@/mixins/backgroundAudio.js';
    export default {
        mixins: [
            backgroundAudio
        ],
        props: {
            setaudiotime: {
                type: Number,
                default: 0
            },
            music: {
                type: String,
                default: ''
            },
            title: {
                type: String,
                default: ''
            },
            img: {
                type: String,
                default: ''
            },
        },
        watch: {
            setaudiotime: {
                handler(a) {
                    let that = this;
                    // console.log('初始化音频跳转时间A', a);
                    if (a > 0) {
                        if (!that.innerAudioContext) {
                            // console.log('初始未完成+++');
                            that.initAudioContext();
                        }
                        if (that.innerAudioContext) {
                            // 直接用 startTime 替代 seek
                            that.innerAudioContext.startTime = Number(a);

                            // 如果已经在播放，需重设src startTime才生效，遂采用seek
                            if (that.innerAudioContext.src) {
                                that.innerAudioContext.seek(a);
                            }

                        }
                    }
                },
                immediate: true
            }
        },
        data() {
            return {
                // progress 已在 backgroundAudio mixin 中定义，这里不需要重复定义
            }
        },
        methods: {
            xPlayAudio() {
                if (!this.music) {
                    return this.$showToast('播放地址不存在')
                }
                // 确保音频上下文已初始化
                if (!this.innerAudioContext) {
                    this.initAudioContext();
                }
                // 如果是新的音频或者还没有设置src，先初始化
                if (!this.innerAudioContext.src || this.innerAudioContext.src !== this.music) {
                    console.log('this.music', this.music)
                    this.initPlay(this.music, this.title, this.img)
                }
                this.playAudio(this.music, this.title, this.img)
            },
            sliderChange() {
                const audioCtx = this.innerAudioContext;
                if (audioCtx && audioCtx.duration) {
                    audioCtx.seek(parseInt((audioCtx.duration * this.progress) / 100));
                }
            }
        },
        mounted() {
            // 只有在有音频地址时才初始化
            if (this.music) {
                this.initPlay(this.music, this.title, this.img)
            }
            // console.log('x-play-mounted-setaudiotime:', this.setaudiotime)
        },
        // destroyed() {
        // 	console.log('退出组件暂停')
        // 	this.playPause();
        // 	this.innerAudioContext = null
        // }
    }
</script>

<style scoped lang="scss">
    image {
        width: 100%;
        height: 100%;
    }

    .play {
        background: #f2f5f8;
        border: 6rpx solid #ffffff;
        border-radius: 30rpx;
        box-shadow: 0px 4rpx 20rpx 0px rgba(0, 0, 0, 0.06);
        padding: 38rpx 26rpx;

        .status {
            width: 96rpx;
            height: 96rpx;
        }

        .play_r {
            flex: 1;
            padding: 10rpx 0 10rpx 20rpx;
            font-size: 32rpx;
            font-weight: 700;
            color: #666666;

            .time {
                /* #ifdef MP-TOUTIAO */
                min-width: 64rpx;
                /* #endif */

                font-size: 24rpx;
                font-weight: 400;
                text-align: left;
                color: #666666;
            }

            .progress {
                width: 100%;
                padding: 0 10rpx;

                .badge-button {
                    width: 32rpx;
                    height: 32rpx;
                }
            }
        }
    }
</style>