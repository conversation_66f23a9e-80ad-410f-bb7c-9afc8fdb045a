<template>
	<view class="list">
		<view class="item relative">
			<view>
				<view class="item_t flex flex_align_center">
					<view class="t_l "><image src="" mode=""></image></view>
					<view class="t_r ">
						<view class="name">不愿去文化馆</view>
						<view class="flex des">
							<view>会社化指数</view>
							<view></view>
							<view>739</view>
						</view>
						<view class="flex flex_align_center discuss">
							<view class="image">
								<image src="@/static/images/balance.png" mode=""></image>
								<image src="@/static/images/balance.png" mode=""></image>
								<image src="@/static/images/balance.png" mode=""></image>
							</view>
							<text>正在讨论</text>
							<view class="price">会员价 ￥50</view>
						</view>
					</view>
				</view>
				<view class="item_b flex">
					<view class="iconfont"></view>
					<view>一句话特点评语，一句话特点评语</view>
				</view>
			</view>

			<view class="right_top absolute"></view>
			<view class="right_bottom absolute">可拼团</view>
		</view>
		<view class="item relative">
			<view>
				<view class="item_t flex flex_align_center">
					<view class="t_l "><image src="" mode=""></image></view>
					<view class="t_r ">
						<view class="name">不愿去文化馆</view>
						<view class="flex des">
							<view>会社化指数</view>
							<view></view>
							<view>739</view>
						</view>
						<view class="flex flex_align_center discuss">
							<view class="image">
								<image src="@/static/images/balance.png" mode=""></image>
								<image src="@/static/images/balance.png" mode=""></image>
								<image src="@/static/images/balance.png" mode=""></image>
							</view>
							<text>正在讨论</text>
							<view class="price">会员价 ￥50</view>
						</view>
					</view>
				</view>
				<view class="item_b flex">
					<view class="iconfont"></view>
					<view>一句话特点评语，一句话特点评语</view>
				</view>
			</view>
		
			<view class="right_top absolute"></view>
			<view class="right_bottom trail absolute">可拼团</view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		value: {
			type: '',
			default: ''
		}
	},
	components: {},
	data() {
		return {};
	},
	methods: {
		goPages(path, type) {
			this.$navigator(path, type);
		}
	},
	mounted() {},
	onLoad(option) {}
};
</script>

<style scoped lang="scss">
.list {
	image {
		width: 100%;
		height: 100%;
	}
	.item {
		padding: 30rpx;
		color: #707070;
		border: 2rpx solid #707070;
		border-radius: 8rpx;
		margin-bottom: 8rpx;
		.item_t {
			.t_l {
				width: 136rpx;
				height: 136rpx;
			}
			.t_r {
				margin-left: 52rpx;
				.name {
					font-size: 36rpx;
				}
				.des {
					font-size: 24rpx;
					margin: 12rpx 0;
				}
				.discuss {
					.image {
						width: 130rpx;
						image {
							width: 40rpx;
							height: 40rpx;
							&:not(:first-child) {
								margin-left: -20rpx;
							}
						}
					}
					text {
						display: inline-block;
						width: 130rpx;
						font-size: 24rpx;
						transform: scale(0.8);
						transform-origin: left;
					}
					.price {
						font-size: 24rpx;

						color: #ff7d57;
					}
				}
			}
		}
		.item_b {
			margin-top: 26rpx;
			border-top: 2rpx solid #707070;
			padding-top: 16rpx;
			.iconfont {
				width: 33rpx;
				height: 33rpx;
			}
		}
		.right_top {
			top: -2rpx;
			right: -2rpx;
			width: 73rpx;
			height: 48rpx;
			background: #efb039;
			box-shadow: 0px 2rpx 8rpx rgba(0, 0, 0, 0.16);
			border-radius: 0px 8rpx 0px 16rpx;
		}
		.right_bottom {
			bottom: -2rpx;
			right: -2rpx;
			width: 122rpx;
			height: 40rpx;
			text-align: center;
			line-height: 40rpx;
			font-size: 24rpx;
			background: #efb039;
			font-weight: 500;
			box-shadow: 0px 2rpx 8rpx rgba(0, 0, 0, 0.16);
			border-radius: 16rpx 0px 8rpx 0px;
			&.expire {
				background: #b7b7b7;

				color: #5a3925;
			}
			&.trail {
				background: #5a3925;

				color: #efb039;
			}
		}
	}
}
</style>
