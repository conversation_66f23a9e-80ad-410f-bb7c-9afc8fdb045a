<template>
	<view class="list flex  flex_wrap ">
		<view class="item relative" v-for="(item, index) in arr" :key="index" @click="goPages('/pages/expert/consult/consult')">
			<view>
				<view class="item_t"><image :src="item.pic" mode="aspectFill"></image></view>
				<view class="item_c relative ">
					<view class="name">美术馆</view>
					<view class="des text_ellipsis">能量充值能量充值能量充值能量充值能量充值</view>
					<view class="absolute  finish">心愿达成</view>
				</view>
				<view class="item_b  flex flex_between">
					<view class="des">777想去</view>
					<view class="btn">想去+1</view>
				</view>
			</view>
			<view class="absolute label flex">
				<view class="tag " :class="'t' + (index + 1)">已入住</view>
				<view class="enlist">活动招募中</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		arr: {
			type: Array,
			default() {
				return [
					{
						pic: require('@/static/images/plus.png'),
						des: '陆雪'
					},
					{
						pic: require('@/static/images/plus.png'),
						des: '陆雪'
					},
					{
						pic: require('@/static/images/plus.png'),
						des: '陆雪'
					},{
						pic: require('@/static/images/plus.png'),
						des: '陆雪'
					},
					{
						pic: require('@/static/images/plus.png'),
						des: '陆雪'
					},
					{
						pic: require('@/static/images/plus.png'),
						des: '陆雪'
					}
				];
			}
		},
		width: {
			type: String,
			default: '200rpx'
		}
	},
	components: {},
	data() {
		return {};
	},
	methods: {
		goPages(path, type) {
			this.$navigator(path, type);
		}
	},
	mounted() {},
	onLoad(option) {}
};
</script>

<style scoped lang="scss">
.list {
	image {
		width: 100%;
		height: 100%;
	}
	.item {
		// width: 332rpx;
		width: calc((100%  - 16rpx) / 2);
		border: 2rpx solid #707070;
		border-radius: 8rpx;
		margin-bottom: 8rpx;
		&:nth-child(2n-1) {
			margin-right: 16rpx;
		}
		.item_t {
			width: 332rpx;
			height: 262rpx;
			image {
			}
		}
		.item_c {
			padding: 26rpx;
			.name {
				font-size: 28rpx;

				font-weight: 400;
				color: #101010;
				&.price {
					color: #ff7d57;
				}
			}
			.des {
				margin-top: 8rpx;
				font-size: 24rpx;
				color: #b7b7b7;
				min-height: 32rpx;
			}
			.finish {
				bottom: 0;
				height: 46rpx;
				line-height: 46rpx;
				background: #5a3925;
				color: #efb039;
				text-align: center;
				left: 0;
			}
		}
		.item_b {
			background: #f4f4f6;
			padding: 24rpx;
			color: #555555;
			.btn {
				width: 122rpx;
				height: 42rpx;
				background: #efb039;
				border: 2rpx solid #707070;
				border-radius: 22rpx;
				text-align: center;
				line-height: 42rpx;

				font-size: 24rpx;

				color: #ffffff;
			}
		}
		.label {
			top: 0;
			> view {
				height: 54rpx;
				text-align: center;
				line-height: 54rpx;

				color: #efb039;
			}
			.tag {
				width: 118rpx;

				border-right: 2rpx solid #707070;
				border-bottom: 2rpx solid #707070;
				border-top-left-radius: 8rpx;
				&.t1 {
					background: #5a3925;

					color: #efb039;
				}
				&.t2 {
					background: #efb039;

					color: #5a3925;
				}
				&.t3 {
					background: #b5937f;

					color: #f4f4f6;
				}
			}
			.enlist {
				width: calc(100% - 118rpx);
				border-bottom: 2rpx solid #707070;
				border-top-right-radius: 8rpx;
				background: #f4f4f6;

				color: #5a3925;
			}
		}
	}
}
</style>
