<template>
	<view>
		<view class="coupon-list-window" :class="value === true ? 'on' : ''">
			<view class="title">
				优惠券
				<span class="iconfont icon-guanbi" @click="close"></span>
			</view>
			<view v-if="couponList.length > 0">
				<view class="coupon-list">
					<view class="item acea-row row-center-wrapper" v-for="coupon in couponList" :key="coupon.id" @click="click(coupon)">
						<view class="money">
							<view>
								￥
								<span class="num">{{ coupon.coupon_price }}</span>
							</view>
							<view class="pic-num">满{{ coupon.use_min_price }}元可用</view>
						</view>
						<view class="text">
							<view class="condition line1">
								<span class="line-title" v-if="coupon.type === 0">通用劵</span>
								<span class="line-title" v-else-if="coupon.type === 1">品类券</span>
								<span class="line-title" v-else>商品券</span>
								<span>{{ coupon.title }}</span>
							</view>
							<view class="data acea-row row-between-wrapper">
								<view>{{ coupon.start_time ? coupon.start_time + '-' : '' }}{{ coupon.end_time }}</view>
								<view class="iconfont icon-xuanzhong1 font-color-red" v-if="checked === coupon.id"></view>
								<view class="iconfont icon-weixuanzhong" v-else></view>
							</view>
						</view>
					</view>
				</view>
				<view class="couponNo bg-color-red" @click="couponNo">不使用优惠券</view>
			</view>
			<view v-if="!couponList.length && loaded">
				<view class="pictrue"><image :src="imagePath + '/wximage/noCoupon.png'" class="image" /></view>
			</view>
		</view>
		<view class="mask" @touchmove.prevent :hidden="value === false" @click="close"></view>
	</view>
</template>
<style scoped>
.coupon-list-window {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	background-color: #f5f5f5;
	border-radius: 16rpx 16rpx 0 0;
	z-index: 999;
	transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
	-webkit-transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
	-moz-transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
	-o-transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
	transform: translate3d(0, 100%, 0);
	-webkit-transform: translate3d(0, 100%, 0);
	-ms-transform: translate3d(0, 100%, 0);
	-moz-transform: translate3d(0, 100%, 0);
	-o-transform: translate3d(0, 100%, 0);
}
.coupon-list-window.on {
	transform: translate3d(0, 0, 0);
	-webkit-transform: translate3d(0, 0, 0);
	-ms-transform: translate3d(0, 0, 0);
	-moz-transform: translate3d(0, 0, 0);
	-o-transform: translate3d(0, 0, 0);
}
.coupon-list-window .title {
	height: 124rpx;
	width: 100%;
	text-align: center;
	line-height: 124rpx;
	font-size: 32rpx;
	font-weight: bold;
	position: relative;
	color: #333;
}
.coupon-list-window .title .iconfont {
	position: absolute;
	right: 30rpx;
	top: 50%;
	transform: translateY(-50%);
	font-size: 35rpx;
	color: #8a8a8a;
	font-weight: normal;
}
.coupon-list-window .coupon-list {
	margin: 0 0 50rpx 0;
	height: 550rpx;
	overflow: auto;
}
.coupon-list-window .pictrue {
	width: 413rpx;
	height: 336rpx;
	margin: 0 auto 50rpx auto;
}
.coupon-list-window .pictrue .image {
	width: 100%;
	height: 100%;
}
.coupon-list-window .iconfont {
	font-size: 0.4rem;
}
.couponNo {
	/* font-size: 0.3rem;
	font-weight: bold;
	color: #fff;
	width: 6.9rem;
	height: 0.86rem;
	border-radius: 0.43rem;
	text-align: center;
	line-height: 0.86rem;
	margin: 0.6rem auto; */
    font-size: 30rpx;
    font-weight: bold;
    color: #fff;
    width: 690rpx;
    height: 86rpx;
    border-radius: 43rpx;
    text-align: center;
    line-height: 86rpx;
    margin: 60rpx auto;
}
.condition .line-title {
	width: 0.9rem;
	padding: 0 0.1rem;
	box-sizing: border-box;
	background: rgba(255, 247, 247, 1);
	border: 1px solid rgba(232, 51, 35, 1);
	opacity: 1;
	border-radius: 0.22rem;
	font-size: 0.2rem;
	color: #e83323;
	margin-right: 0.12rem;
}
.coupon-list .pic-num {
	color: #ffffff;
	font-size: 0.24rem;
}
</style>
<script>
import { getOrderCoupon } from '@/api/order';
	import{VUE_APP_URL} from '@/config.js'
export default {
	name: 'CouponListWindow',
	props: {
		value: Boolean,
		checked: Number,
		price: {
			type: [Number, String],
			default: undefined
		},
		cartid: {
			type: String,
			default: ''
		}
	},
	data: function() {
		return {
			imagePath:VUE_APP_URL,
			couponList: [],
			loaded: false,
			cartids: this.cartid
		};
	},
	watch: {
		cartid(n) {
			if (n === undefined || n == null) return;
			this.cartids = n;
			this.getCoupon();
		},
		price(n) {
			if (n === undefined || n == null) return;
			this.getCoupon();
		}
	},
	mounted: function() {},
	methods: {
		close: function() {
			this.$emit('input', false);
			this.$emit('close');
		},
		getCoupon() {
			let data = {
				cartId: this.cartids
			};
			getOrderCoupon(this.price, data)
				.then(res => {
					this.couponList = res.data;
					this.loaded = true;
                    console.log('获取优惠券',this.couponList)
				})
				.catch(err => {
					this.$dialog.error(err.msg || err);
				});
		},
		click(coupon) {
			this.$emit('checked', coupon);
			this.$emit('input', false);
		},
		couponNo: function() {
			this.$emit('checked', null);
			this.$emit('input', false);
		}
	}
};
</script>
