<template>
    <view>
        <!-- #ifdef MP -->
        <uni-popup ref="popup" type="center" :maskClick="false">
            <view class="flex flex_align_center authorize">
                <view class="text_center relative">
                    <view class="top">
                        <view class="title">授权登录</view>
                        <view class="tip">请授权头像等信息，以便为您提供更好的服务</view>
                    </view>
                    <view class="bottom flex">
                        <button class="btn" @click="close">取消</button>
                        <!-- #ifdef MP-WEIXIN -->
                        <button v-if="canUseGetUserProfile" class="btn" type="primary"
                            @click="getUserProfile">授权</button>
                        <button v-else class="btn" type="primary" open-type="getUserInfo"
                            @getuserinfo="getUserInfo">授权</button>
                        <!-- #endif -->
                        <!-- #ifdef MP-TOUTIAO -->
                        <button class="btn" type="primary" @click="getUserInfo_tt">授权</button>
                        <!-- #endif -->
                    </view>
                </view>
            </view>
        </uni-popup>
        <!-- #ifdef MP-WEIXIN -->
        <x-agreement ref="agreement" @cancel="cancel" @confirm="confirm"></x-agreement>
        <!-- #endif -->
        <!-- #endif -->
    </view>
</template>
<script>
    import {
        toLogin,
        autoAuth
    } from '@/utils/common.js';
    import {
        mapGetters
    } from 'vuex';
    export default {
        components: {},
        data() {
            return {
                canUseGetUserProfile: false, //判断api是否存在
                wxres: ''
            };
        },
        computed: mapGetters(['isLogin', 'authPopupShow', 'userInfo']),
        props: {
            isAuto: {
                type: Boolean,
                default: true
            }
        },
        watch: {
            // 组件监听是否登录判断弹窗，字节小程序除抖音外（头条。西瓜等不支持组件内监听，需单个写入每个页面）
            authPopupShow: function(newVal, oldVal) {
                let _this = this;
                if (newVal) {
                    this.$nextTick(() => {
                        _this.open();
                    });
                } else {
                    _this.close()
                }
            },
            isLogin: function(newVal, oldVal) {
                if (newVal) {
                    this.close();
                    this.$emit('login', this.userInfo);
                }
            }
        },
        methods: {
            confirm(e){
                console.log('同意隐私指引',e)
            },
            cancel(e){
                console.log('拒绝隐私指引',e)
            },
            // #ifdef MP-TOUTIAO
            getUserInfo_tt() {
                let _this = this;
                let info = tt.getSystemInfoSync();
                if ((info.appName.toUpperCase() === 'DOUYIN')||(info.appName.toUpperCase() === 'DEVTOOLS')) {
                	_this.isAuto && uni.showLoading({
                	    title: '正在登录中'
                	});
                }
                let code;
                uni.login({
                    success(res) {
                        code = res.code
                    },
                    fail(err) {
                        console.log('errerr',err)
                        uni.hideLoading();
                    }
                });
                if (tt.getUserProfile) {
                    tt.getUserProfile({
                        success(res) {
                            _this.close();
                            res.code = code;
                            res.version = false;
                            res.app_name = info.appName;
                            // console.log('res：', res)
                            _this.getLoginInfo(res);
                        },
                        fail(res) {
                            uni.hideLoading();
                            _this.open();
                        },
                    });
                } else {
                    tt.getUserInfo({
                        withCredentials: true,
                        success(res) {
                            _this.close();
                            res.code = code;
                            res.version = false;
                            res.app_name = info.appName;
                            // console.log('res：', res)
                            _this.getLoginInfo(res);
                        },
                        fail(res) {
                            tt.openSetting({
                                success: (res) => {
                                    uni.showToast({
                                        title:'请授权用户信息',
                                        icon:'none',
                                        duration:2500
                                    })
                                },
                                fail: (err) => {
                                    console.log("openSetting fail");
                                },
                                complete: (res) => {
                                    console.log("openSetting complete");
                                },
                            });
                            uni.hideLoading();
                            _this.open();
                        },
                    });
                }
            },
            // #endif

            getUserInfo() {
                let _this = this;
                this.isAuto && uni.showLoading({
                    title: '正在登录中'
                });

                uni.login({
                    success(res) {
                        uni.getUserInfo({
                            // #ifdef MP-WEIXIN
                            lang: 'zh_CN', //头条不支持该字段
                            // #endif
                            success(userInfo) {
                                // console.log('userInfo---', userInfo)
                                _this.close();
                                userInfo.code = res.code;
                                userInfo.version = false;
                                _this.getLoginInfo(userInfo);
                            },
                            fail(err) {
                                // 用户未曾授权
                                uni.hideLoading();
                                _this.open();
                            }
                        });
                    },
                    fail(res) {
                        uni.hideLoading();
                    }
                });
            },
            getLoginCode() {
                let that = this;
                return new Promise((resolve, reject) => {
                    uni.checkSession({
                        success: (res) => {
                            resolve(that.wxres);
                        },
                        fail: (err) => {
                            console.log('checkSessionerr--', err)
                            uni.login({
                                success(res1) {
                                    // console.log('auth---', res);
                                    resolve(res1);
                                },
                                fail(err1) {
                                    reject(err1);
                                    uni.hideLoading();
                                }
                            });
                        },
                    })
                });
            },
            getUserProfile() {
                let _this = this;
                uni.getUserProfile({
                    desc: '用于完善会员信息', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
                    success: async userInfo => {
                        let res = await _this.getLoginCode();
                        console.log('获取校验code', res)
                        _this.close();
                        userInfo.code = res.code;
                        userInfo.version = true;
                        _this.getLoginInfo(userInfo);
                    },
                    fail(err) {
                        console.log('auth', err);
                        // 用户未曾授权
                        uni.hideLoading();
                        _this.open();
                    }
                });
                // uni.getPrivacySetting({
                //     success: (res) => {
                //         console.log('查看是否阅读微信隐私协议',res)
                //         // 如果是false，则说明之前授权过无需弹出隐私协议
                //         if (res.needAuthorization === false) {
                //             // 原登录流程
                //             uni.getUserProfile({
                //                 desc: '用于完善会员信息', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
                //                 success: async userInfo => {
                //                     let res = await _this.getLoginCode();
                //                     console.log('获取校验code', res)
                //                     _this.close();
                //                     userInfo.code = res.code;
                //                     userInfo.version = true;
                //                     _this.getLoginInfo(userInfo);
                //                 },
                //                 fail(err) {
                //                     console.log('auth', err);
                //                     // 用户未曾授权
                //                     uni.hideLoading();
                //                     _this.open();
                //                 }
                //             });
                //             return;
                //         }
                //         console.log('弹出协议')
                //         _this.$refs.agreement.init();
                //         // uni.onNeedPrivacyAuthorization((resolve) => {
                //         //     console.log('弹出协议',resolve)
                //         // });
                //     },
                //     fail: () => {},
                //     complete: () => {},
                // });
            },
            onlyGetUserInfo() {
                return new Promise((resolve, reject) => {
                    uni.getUserInfo({
                        // #ifdef MP-TOUTIAO
                        withCredentials: true,
                        // #endif
                        success(userInfo) {
                            resolve(userInfo);
                        },
                        fail(err) {}
                    });
                });
            },
            //检测登录状态
            checkAuthStatus() {
                let _this = this;
                if (this.isLogin) {
                    console.log('已登录');
                } else {
                    // #ifdef H5
                    if (_this.isAuto) {
                        autoAuth();
                    }
                    // #endif
                    // #ifdef MP
                    _this.isAuto && _this.open();
                    // #endif
                }
            },
            getLoginInfo(userInfo) {
                toLogin(userInfo, function(res) {
                    uni.hideLoading();
                });
            },
            close() {
                // #ifdef MP
                this.$refs.popup.close();
                uni.hideLoading();
                // #endif
                this.$store.commit('HIDE_AUTH_POPUP_SHOW');
            },
            open() {
                this.$hideToast();
                // #ifdef MP
                if (this.$store.state.isFromTimeline) {
                    this.$showToast('请前往小程序体验完整功能')
                    this.close()
                    return
                }
                this.$refs.popup.open();
                // #endif
            }
        },

        mounted() {
            if (uni.getUserProfile) {
                this.canUseGetUserProfile = true;
                let that = this;
                uni.login({
                    success(res) {
                        // console.log('auth---', res);
                        that.wxres = res
                    },
                    fail(err1) {
                        uni.hideLoading();
                    }
                });
            }
            // console.log('this.canUseGetUserProfile', this.canUseGetUserProfile)
            this.checkAuthStatus();
        }
    };
</script>

<style lang="scss" scoped>
    .authorize {
        width: 600rpx;
        // border-radius: 15rpx;
        background-color: #fff;

        .text_center {
            width: 100%;

            .top {
                padding: 20rpx 40rpx;

                .title {
                    padding: 20rpx 0;
                    font-size: 32rpx;
                    font-weight: bold;
                }

                .tip {
                    padding: 30rpx 0;
                    font-size: 30rpx;
                    height: 150rpx;
                }
            }

            .bottom {
                bottom: 0;
                width: 100%;

                // border-bottom-left-radius: 15rpx;
                // border-bottom-right-radius: 15rpx;
                .btn {
                    width: 50%;
                    border-radius: 0;
                    height: 80rpx;
                    line-height: 80rpx;

                    &:after {
                        border: none;
                    }

                    /* #ifdef MP-TOUTIAO */
                    &:last-child {
                        background-color: rgba(248, 89, 89, 1);
                    }

                    /* #endif */
                }
            }
        }
    }
</style>
