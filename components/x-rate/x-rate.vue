<template>
	<view class="rate" :style="{ width: width * 5 + 'rpx' }">
		<view style="" class="absolute">
			<view class="flex">
				<view v-for="i in 5" :key="i" @click="isClick ? rateClick(false, i) : null">
					<template v-if="bg">
						<image :src="bg" mode="widthFix" :style="{ width: width + 'rpx' }"></image>
					</template>
					<template v-else>
						<i class="iconfont icon-shoucang"></i>
					</template>
				</view>
			</view>
		</view>
		<view class="absolute" :style="{ width: ratePercent }">
			<view class="flex">
				<view v-for="i in 5" :key="i" @click="isClick ? rateClick(true, i) : null">
					<template v-if="active">
						<image :src="active" mode="widthFix" :style="{ width: width + 'rpx' }"></image>
					</template>
					<template v-else>
						<i class="iconfont icon-shoucang1"></i>
					</template>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		rate: {
			type: Number,
			default: 2
		},
		isClick: {
			type: Boolean,
			default: true
		},
		bg: {
			type: String,
			default: ''
		},
		active: {
			type: String,
			default: ''
		},
		width: {
			type: Number,
			default: 32
		}
	},
	data() {
		return {
			ratePercent: (this.rate / 5) * 100 + '%'
		};
	},
	methods: {
		rateClick(type, index) {
			let percent = (index / 5) * 100 + '%';
			this.ratePercent = percent;
			this.$emit('rate', index);
			if (type) {
			} else {
			}
		}
	}
};
</script>

<style scoped lang="scss">
.rate {
	position: relative;
	// width: 160rpx;
	height: 32rpx;
	.absolute {
		position: absolute;
		width: 100%;
		height: 32rpx;
		overflow: hidden;
	}
	.flex {
		display: flex;
	}
	image {
		// width: 35rpx;
	}
}
</style>
