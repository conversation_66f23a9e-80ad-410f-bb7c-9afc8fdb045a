<template>
	<view class="slider-banner product-bg">
		<view class="swiper relative">
			<swiper style="height:1000rpx" @change="dotsChange" :circular="true">
				<swiper-item v-if="videolines">
					<video id="videoIds"  :controls="true" :loop="true" style="width:750rpx;height: 1000rpx;"
						:src="videolines"  @pause="endVideo" @loadedmetadata="loadedmetadata" custom-cache="false">
					</video>
					<!-- <view class="dots_block" style="z-index:-1">1 / {{ swiper.length + 1}}</view> -->
				</swiper-item>
				<swiper-item v-for="(item, index) in swiper" :key="index" class="relative">
					<view class="slide-image" :style="{
							background: 'url(' + item + ') no-repeat center center',
							'background-size': '100% 100%',
							height: '100%'
						}">
					</view>
					<!-- <view class="dots_block" v-if="videolines">{{ index + 2 }} / {{ swiper.length + 1 }}</view> -->
					<view class="dots_block" >{{ index + 1 }} / {{ swiper.length }}</view>
				</swiper-item>
			</swiper>
		</view>
	</view>
</template>
<script>
	import xSwiper from '@/components/x-swiper/x-swiper.vue';
	let vm = null;
	import {
		handleImg
	} from '@/utils/oss.js';
	export default {
		name: 'ProductConSwiper',
		components: {
			xSwiper
		},
		props: {
			imgUrls: {
				type: Array,
				default: () => []
			},
			videoline: {
				type: String,
				default: () => ''
			}
		},
		watch: {
			async imgUrls(a, b) {
				let info = a;
				if(a.length&&b.length&&b.toString() === a.toString()) return;
                for(let i= 0; i < info.length; i++){
                     let res = await handleImg(info[i], {
                            w: 750,
                            h: 1000,
                            q: 80,
                            type: 'resize',
                            m: 'fill'
                        });
                        if (res) {
                            this.swiper.push(res);
                        } else {
                            this.swiper.push('');
                        }
                }
				// a.forEach(async (item, index) => {
				// 	let res = await handleImg(item, {
				// 		w: 750,
				// 		h: 1000,
				// 		q: 80,
				// 		type: 'resize',
				// 		m: 'fill'
				// 	});
				// 	if (res) {
				// 		this.swiper.push(res);
				// 		info.push(res);
				// 	} else {
				// 		this.swiper.push('');
				// 		info.push('');
				// 	}
				// });
			},
			videoline: {
				handler(newName) {
					this.videolines = newName;
				},
				deep: true
			}
		},
		data() {
			let that = this;
			return {
				videolines: this.videoline,
				isOnPlay: false,
				swiper: [],
				current: 0
			};
		},
		mounted() {},
		created() {},
		methods: {
			loadedmetadata() {
				console.log('加载完成')
				this.videoPlay()
			},
			dotsChange(e) {
				this.current = e.detail.current;
				this.$emit('change', this.current);
			},
			endVideo() {
				let ref = uni.createVideoContext(`videoIds`, this);
				ref.pause();
			},
			videoPlay() {
				let ref = uni.createVideoContext(`videoIds`, this);
				let tid = setTimeout(() => {
					ref.play();
					clearTimeout(tid)
				}, 500)
			}
		}
	};
</script>

<style scoped>
	.product-bg {
		/* height: 750rpx; */
	}


	.product-bg .slide-image {
		width: 100%;
		height: 100%;
	}


	.product-bg .pages {
		position: absolute;
		background-color: #fff;
		height: 34rpx;
		padding: 0 10rpx;
		border-radius: 3rpx;
		right: 30rpx;
		bottom: 30rpx;
		line-height: 34rpx;
		font-size: 24rpx;
		color: #050505;
		z-index: 9;
	}

	.videoBox {
		position: fixed;
		z-index: 999;
		top: 0;
		width: 100%;
		height: 100%;
	}

	.videoBox video {
		width: 100%;
		height: 100%;
	}

	.video-source {
		width: 100%;
		height: 100%;
	}

	.video_play {
		position: absolute;
		width: 100rpx;
		height: 100rpx;
		left: 45%;
		top: 45%;
		z-index: 11;
	}


	.dots {
		width: 100%;
		bottom: 10rpx;
	}

	.dots_block {
		position: absolute;
		bottom: 0;
		right: 0;
		background: #fff;
		padding: 5rpx 10rpx;
		font-size: 24rpx;
	}
</style>
