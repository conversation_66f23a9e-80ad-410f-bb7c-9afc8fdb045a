<template>
    <view style="padding-bottom: 108rpx;">
        <view class="list" v-for="(item,index) in list" :key="index"
            @click="isLink&&$navigator(`/pages/ycommunity/newActivity/detail?id=${item.id}&aid=${item.activity_id}&pageType=${pageType}&detailType=${detailType}`)">
            <view>
                <view class="common_avatar flex flex_align_center flex_between">
                    <view class="t_l flex flex_align_center">
                        <view class="images">
                            <image :src="item.user_role_icon" mode="aspectFill" class="bg" v-if="item.user_role_icon">
                            </image>
                            <image :src="item.avatar" mode="aspectFill" class="avatar" @click.stop="gohome('/pages/yuanshi/user/home?id=' + item.uid,isgohome)"></image>
                        </view>
                        <view class="name">{{ item.nickname }}</view>
                    </view>
                    <view class="time flex flex_align_center" v-if="pageType == 'list'">
                        <view></view>
                        <view class="replay" @click="isLink&&$navigator(`/pages/ycommunity/newActivity/detail?id=${item.id}&aid=${item.activity_id}&pageType=${pageType}&detailType=${detailType}&enterOpenStatus=1`)">回复</view>
                    </view>
                    <view class="time flex flex_align_center" v-if="pageType == 'detail'">
                        <view></view>
                        <view class="replay" @click.stop="showInput(1, item,index, null)">回复</view>
                    </view>
                </view>
                <view class="flex">
                    <view class="list_l"></view>
                    <view class="list_r">
                        <view class="desc" :class="item.contentAll?'lineclamp5':''" >{{ item.comment }}</view>
                        <view v-if="item.isMore">						
                            <view class="rightText" v-if="item.contentAll" @click.stop="changeAllFun(item, index)">全部</view>
                            <view class="rightText" v-else  @click.stop="changeAllFun(item, index)">收起</view>
                        </view>
                        <view class="del_txt">
                            <text @click.stop="delMyEvaluate(item, index)" v-if="pageType == 'list' && uid===item.uid">删除</text>
                        </view>
                        <view class="mess_handle flex flex_align_center flex_between">
                            <view class="time">
                                <text class="font_size20">{{item.add_time}}</text>
                            </view>
                            <view class="flex flex_align_center">
                                <view class=" permission" @click.stop="permissionShow(index,0,0,item.id)"
                                    v-if="!userPermission&&isHasPermission || userPermission&&item.uid===uid">...</view>

                                <view class="refining_span" v-if="item.is_refining">精</view>
                                <view class="vote-txt" v-if="detailType == 2" @click.stop="likeStars(item,index,null,null,1)">投票</view>
                                <view class="item flex flex_align_center"
                                    @click.stop="likeStars(item,index,null,null,1)">
                                    <image src="@/static/images/yuanshi/like_.png" mode="widthFix" v-if="item.is_like">
                                    </image>
                                    <image src="@/static/images/yuanshi/like.png" mode="widthFix" v-else></image>
                                    <text>{{ item.like_count>-1?item.like_count:0 }}</text>
                                </view>
                                <view class="item flex flex_align_center">
                                    <image src="@/static/images/yuanshi/comment.png" mode="widthFix"></image>
                                    <text>{{ item.comments || 0 }}</text>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view v-if="pageType == 'list'" class="show-permission flex flex_align_center flex_end"
                :class="{active:showPermissionActive===`${index}-0-0-${item.id}`}">
                <view @click.stop="permissionClick(item,idx,index,0,0,1,isdetail)" v-for="(per,idx) in  permissionArr"
                    :key="idx">
                    {{per.name}}
                </view>
            </view>
            <view v-if="pageType == 'detail'" class="show-permission flex flex_align_center flex_end"
                :class="{active:showPermissionActive===`${index}-0-0-${item.id}`}">
                <view @click.stop="permissionClick(item,idx,index,0,0,1,isdetail)" v-for="(per,idx) in  permissionArr"
                    :key="idx" v-if="!per.floorDisabled ">
                    {{per.name}}
                </view>
            </view>
            <!-- 视频、图片、音频的展示 -->
            <view class="flex wrap"
                v-if="item.video&&item.video.length || item.audio&&item.audio.length || item.image&&item.image.length">
                <view class="list_l"></view>
                <view class="mess_image flex">
                    <view class="nav rote180" @click="navClick('left')"><text class="iconfont icon-jiantou "></text>
                    </view>
                    <view class="wrap1 ">
                        <scroll-view scroll-x style="white-space:nowrap;" :scroll-with-animation="true"
                            :scroll-left="scrollLeft" @scroll="scrollX">
                            <view class="flex flex_align_center">
                                <view class="item video" v-for="(item1, index1) in item.video"
                                    v-if="item.video&&item.video.length" :key="item1.name">
                                    <video :id="`playVideo${index}-${index1}`" style="width:136rpx;height: 136rpx;"
                                        :show-center-play-btn="false" :controls="isFullscreen"
                                        @fullscreenchange="fullscreenchange($event,item1,index,index1)"
                                        :src="isvideostatus?item1.name:''" custom-cache="false"></video>
                                    <view class="cover flex flex_align_center flex_around">
                                        <image src="@/static/images/community/play.png"
                                            @click.stop="playVideo(item1,index,index1)">
                                    </view>
                                </view>
                                <view class="item audio" v-for="(item1, index1) in item.audio"
                                    v-if="item.audio&&item.audio.length" :key="item1.name">
                                    <view class="flex flex_align_center">
                                        <image src="@/static/images/community/play.png" v-if="item1.play===0"
                                            @click.stop="xPlayAudio(item1,index,index1,true)">
                                            <image src="@/static/images/community/pause.png" v-else
                                                @click.stop="xPlayAudio(item1,index,index1,false)">
                                    </view>
                                </view>
                                <view class="item" v-for="(item1, index1) in item.image"
                                    :id="index1 === 0 ? 'calcItem' : ''" :key="index1"
                                    v-if="item.image&&item.image.length">
                                    <image :src="item1.name" mode="aspectFill"
                                        @click.stop="previewImage(item.image, item1.name)">
                                    </image>
                                </view>
                            </view>
                        </scroll-view>
                    </view>
                    <view class="nav" @click="navClick('right')"><text class="iconfont icon-jiantou"></text>
                    </view>
                </view>
            </view>
            <!-- 楼层回复 -->
            <view class="flex wrap" v-if="(item.children_reply&&item.children_reply.length > 0) && pageType == 'detail'">
                <view class="list_l"></view>
                <view class="list_r" v-if="item.children_reply&&item.children_reply[0].nickname">
                    <view class="item" v-for="(item1, index1) in item.children_reply" :key="index1">
                        <view class="common_avatar flex flex_align_center flex_between">
                            <view class="t_l flex flex_align_center">
                                <view class="images">
                                    <image :src="item1.user_role_icon" mode="aspectFill" class="bg"
                                        v-if="item1.user_role_icon"></image>
                                    <image :src="item1.avatar" mode="aspectFill" class="avatar" @click.stop="gohome('/pages/yuanshi/user/home?id=' + item1.uid,isgohome)"></image>
                                    <view class="dots" v-if="isshowdot && item1.is_reading == 0"></view>
                                </view>
                                <view class="name" v-if="isshow">{{ item1.nickname }} to {{ item1.to_reply_nickname? item1.to_reply_nickname: item1.children_reply[0].to_reply_nickname}}</view> 
                                <view class="name" v-else>
                                    {{ item1.nickname }}
                                </view>
                            </view>
                            <view class="time flex flex_align_center">
                                <view>{{ item1.created_at }}</view>
                                <view class="replay" @click.stop="showInput(2, item1,index, index1)">回复</view>
                            </view>
                        </view>
                        <view class="flex">
                            <view class="list_l"></view>
                            <view class="desc">{{ item1.comment }}</view>

                        </view>
                        <view class="mess_handle flex flex_align_center flex_between">
                            <view class="time">
                                <text class="font_size20">{{item1.add_time}}</text>
                            </view>
                            <view class="flex flex_align_center">
                                <view class=" permission" @click.stop="permissionShow(index,index1,0,item1.id)"
                                    v-if="!userPermission&&isHasPermission || userPermission&&item.uid===uid">...
                                </view>
                                <view class="item flex flex_align_center"
                                    @click.stop="likeStars(item1,index,index1,null,2)">
                                    <image src="@/static/images/yuanshi/like_.png" mode="widthFix" v-if="item1.is_like">
                                    </image>
                                    <image src="@/static/images/yuanshi/like.png" mode="widthFix" v-else></image>
                                    <text>{{ item1.like_count>-1?item1.like_count:0 }}</text>
                                </view>
                                <view class="item flex flex_align_center" v-if="isFloor">
                                    <image src="@/static/images/yuanshi/comment.png" mode="widthFix"></image>
                                    <text>{{ item1.comments || 0 }}</text>
                                </view>
                            </view>
                        </view>
                        <view class="show-permission flex flex_align_center flex_end"
                            :class="{active:showPermissionActive===`${index}-${index1}-0-${item1.id}`}">
                            <view @click.stop="permissionClick(item1,idx1,index,index1,0,2,isdetail)"
                                v-for="(per1,idx1) in  permissionArr" :key="idx1" v-if="!per1.floorDisabled">
                                {{per1.name}}
                            </view>
                        </view>
                        <template v-if="item1.children_reply&&item1.children_reply.length">
                            <template v-if="item1.children_reply&&item1.children_reply[0].uid">
                                <view class="item2" v-for="(item2, index2) in item1.children_reply" :key="index2"
                                    v-if="index2 < item1.floor_count">
                                    <view class="item">
                                        <view class="common_avatar flex flex_align_center flex_between">
                                            <view class="t_l flex flex_align_center">
                                                <view class="images">
                                                    <image :src="item2.user_role_icon" mode="aspectFill" class="bg"
                                                        v-if="item2.user_role_icon"></image>
                                                    <image :src="item2.avatar" mode="aspectFill" class="avatar" @click.stop="gohome('/pages/yuanshi/user/home?id=' + item2.uid,isgohome)"></image>
                                                    <view class="dots" v-if="isshowdot && item2.is_reading == 0"></view>
                                                </view>
                                                <view class="name">{{ item2.nickname }} to {{ item2.to_reply_nickname }} 
                                                </view>
                                            </view>
                                            <view class="time flex flex_align_center">
                                                <view>{{ item2.created_at }}</view>
                                                <view class="replay"
                                                    @click.stop="showInput(3, item2,index, index1,index2)">
                                                    回复
                                                </view>
                                            </view>
                                        </view>
                                        <view class="flex">
                                            <view class="list_l"></view>
                                            <view class="desc">{{ item2.comment }}</view>
                                        </view>

                                        <view class="mess_handle flex flex_align_center flex_between">
                                            <view class="time">
                                                <text class="font_size20">{{item2.add_time}}</text>
                                            </view>
                                            <view class="flex flex_align_center">
                                                <view class=" permission"
                                                    @click.stop="permissionShow(index,index1,index2,item2.id)"
                                                    v-if="!userPermission&&isHasPermission || userPermission&&item.uid===uid">
                                                    ...
                                                </view>
                                                <view class="item flex flex_align_center"
                                                    @click.stop="likeStars(item2,index,index1,index2,3)">
                                                    <image src="@/static/images/yuanshi/like_.png" mode="widthFix"
                                                        v-if="item2.is_like">
                                                    </image>
                                                    <image src="@/static/images/yuanshi/like.png" mode="widthFix"
                                                        v-else></image>
                                                    <text>{{ item2.like_count>-1?item2.like_count:0 }}</text>
                                                </view>
                                                <!-- <view class="item flex flex_align_center">
													<image src="@/static/images/yuanshi/comment.png" mode="widthFix">
													</image>
													<text>{{ item2.comments || 0 }}</text>
												</view> -->
                                            </view>
                                        </view>
                                    </view>
                                    <view class="show-permission flex flex_align_center flex_end"
                                        :class="{active:showPermissionActive===`${index}-${index1}-${index2}-${item2.id}`}">
                                        <view @click.stop="permissionClick(item2,idx2,index,index1,index2,3,isdetail)"
                                            v-for="(per2,idx2) in  permissionArr" :key="idx2"
                                            v-if="!per2.floorDisabled">
                                            {{per2.name}}
                                        </view>
                                    </view>
                                </view>
                                <view class="show_more text_center" v-if="item1.children_reply.length">
                                    <view v-if="item1.children_reply.length - item1.floor_count > 0"
                                        @click.stop="loadMore(1, index, index1, '楼层回复')">
                                        展开{{ item1.children_reply.length - item1.floor_count }}条回复
                                    </view>
                                </view>
                            </template>
                        </template>
                    </view>
                </view>
            </view>
        </view>
        <!-- 	<xChat :adjustPosition="false" :placeholder="placeholder" :inputShow="inputShow" @inputBlur="inputBlur" @send="submitComment"
			desc="knowledge"></xChat> -->
    </view>
</template>

<script>
    import {
        authNavigator,
        navigator,
    } from '@/utils/common.js';
    import playAudioControl from '@/mixins/playAudio.js';
    import {
        uniSelectorQueryInfo,
    } from '@/utils/uni_api.js';

    import {
        activityOnlineMessageLike,
        activityOnlineMessageUnlike,
        activityOnlineCommentUnlike,
        activityOnlineCommentLike,
        setupActivityRefining,
        delActivityMyMessage,
        deleteActivityMessage
    } from '@/api/community'
    import xChat from '@/components/x-chat/x-chat';
    import {
        handleUserMute,
        // handleSetupRefining,
        // handleDelUserMute
    } from '@/api/zsff.js'
    
    
    export default {
        // props:["pageType","videoData","arr","isPermission","userPermission","isLink","isvideo","isFloor","permissionArr"],
        props: {
            detailType: {
                type: Number,
                default: 0
            },
            pageType: {
                type: String,
                default: 'list'
            },
            arr: {
                type: Array,
                default: []
            },
            isPermission: { //是否打开权限
                type: Boolean,
                default: true
            },
            userPermission: { //是否打开用户权限
                type: Boolean,
                default: false
            },
            isLink: {
                type: Boolean,
                default: true
            },
            isvideo: {
                type: Boolean,
                default: true
            },
            isFloor: { //false  评论详情页
                type: Boolean,
                default: true
            },
            isdetail: { // 是否详情使用
                type: Boolean,
                default: false
            },
            isshow: { // 二级回复显示to回复用户，目前在评论详情页面使用
                type: Boolean,
                default: false
            },
            isgohome: { // 点击头像去主页，目前在评论详情、课程评论区相关页面使用
                type: Boolean,
                default: false
            },
            isshowdot: { //二级回复红点显示，目前在我参与的评论页面组件中使用
                type: Boolean,
                default: false
            },
            permissionArr: {
                type: Array,
                default () {
                    return [{
                        name: '禁言',
                        type: 1
                    }, {
                        name: '精华',
                        type: 2,
                        floorDisabled: true //楼层禁止使用该操作
                    }, {
                        name: '删除',
                        type: 3
                    }]
                }
            }
        },
        mixins: [playAudioControl],
        components: {
            xChat
        },
        computed: {
            isHasPermission() {
                return this.isPermission && this.$store.state.userInfo.is_official_role > 0
            }
        },
        watch: {
            arr(a, b) {
                this.list = a;
                // console.log('评论数据',this.list)
                this.list.forEach(item=>{
                    if(item.comment.length > 90){
                        item.isMore = true
                        item.contentAll = true
                    }else{
                        item.isMore = false
                        item.contentAll = false
                    }
                })
            }
        },
        created() {
            this.isvideostatus = this.isvideo;
        },
        data() {
            return {
                isvideostatus: true, // 
                showPermissionActive: '1',
                isOfficialRole: this.$store.state.userInfo.is_official_role > 0, // 官方角色
                isMute: this.$store.state.userInfo.is_mute, // 当前用户是否禁言
                uid: this.$store.state.userInfo.uid,
                placeholder: '请输入内容',
                list: this.arr,
                inputShow: false,
                chatInfo: {},
                // 音视频显示区域
                scrollDetail: {},
                scrollLeft: 0,
                innerAudioContext: null,
                videoCtx: null,
                isFullscreen: false,
                audioPlayIdx: [],
            }
        },
        methods: {
            delMyEvaluate(item, index) {
                let _this = this;
                this.$showModal('提示', '是否要删除', {
                    success: function(res) {
                        console.log(res);
                        if (res.confirm) {
                            let param = {
                                id:item.id
                            };
                            delActivityMyMessage(param)
                                .then(res => {
                                    _this.$showToast('删除成功');
                                    _this.list.splice(index, 1);
                                })
                                .catch(err => {
                                    _this.$showToast('删除失败');
                                });
                        }
                    },
                    fail: function() {}
                });
            },
            gohome(path, type) {
                if(type){
                    this.$authNavigator(path)
                }
            },
            changeAllFun(item, index){
                let list = JSON.parse(JSON.stringify(this.list));	
                list.forEach((elem,ind)=>{
                    if(index === ind){
                        elem.contentAll = !elem.contentAll
                    } 
                })
                this.list = list 
            },
            permissionShow(index, index1 = 0, index2 = 0, id) {
                const idxs = `${index}-${index1}-${index2}-${id}`;
                this.showPermissionActive = idxs === this.showPermissionActive ? '1' : idxs
            },
            inputBlur() {
                // this.inputShow = false;
            },
            permissionClick(item, idx, index1, index2, index3, type, isdet) {
                // 1禁言 2 精华 3删除
                console.log(item, idx, index1, index2, index3, type);
                
                const pType = this.permissionArr[idx].type,
                    _this = this;
                console.log('pType', pType)
                console.log('userPermission', this.userPermission)
                if (this.userPermission) {
                    this.$emit('permissionClick', item, idx, index1, index2, index3, type)
                } else {
                    const {
                        uid
                    } = this.$store.state.userInfo;
                    if (pType === 1 && this.isOfficialRole && uid === item.uid) {
                        return this.$showToast('官方人员不允许该操作')
                    }
                    uni.showModal({
                        title:pType == 1?'禁言':pType == 2?'精华':'删除',
                        content: '确认进行该操作吗',
                        success: function(res) {
                            if (res.confirm) {
                                console.log('用户点击确定');
                                if (pType === 1) {
                                    handleUserMute(item.uid).then(res => {
                                        _this.$showToast('禁言成功')
                                    })
                                } else if (pType === 2) {
                                    setupActivityRefining(item.id).then(res => {
                                        _this.$showToast('设置成功')
                                    })
                                } else if (pType === 3) {
                                    // 管理员删除评论待考察
                                    if(!isdet){
                                        let param = {
                                            message_id: type === 1 ? item.id : _this.list[index1].id,
                                            comment_id: type === 1 ? 0: item.id
                                        }
                                        // console.log('删除参数list',param)
                                        deleteActivityMessage(param).then(res => {
                                            _this.updateListBySplice(index1, index2, index3, type)
                                        })
                                    }
                                    if(isdet){
                                        let param = {
                                            message_id: item.message_id,
                                            comment_id: item.id
                                        }
                                        // console.log('删除参数detail',param)
                                        deleteActivityMessage(param).then(res => {
                                            _this.updateListBySplice(index1, index2, index3, type)
                                        })
                                    }
                                }
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        }
                    })
                }
            },
            updateListBySplice(index1, index2, index3, type) {
                console.log('type---', type)

                if (type === 1) {
                    this.list.splice(index1, 1)
                } else if (type === 2) {
                    this.list[index1].children_reply.splice(index2, 1)
                } else if (type === 3) {
                    this.list[index1].children_reply[index2].children_reply.splice(index3, 1)
                }
                this.$showToast('删除成功')
            },
            showInput(type, item, idx1, idx2, idx3) {
                // console.log('item..', item)
                if (this.isMute === 1) {
                    return this.$showToast('已被禁言');
                }
                const {
                    uid
                } = this.$store.state.userInfo;
                if (type > 1 && uid === item.uid) {
                    return this.$showToast('不能自己回复自己');
                }
                this.inputShow = true;
                this.placeholder = '对此评论说点什么吧！@' + item.nickname;
                this.chatInfo = {
                    type,
                    item,
                    idx1,
                    idx2,
                    idx3
                }
                this.$emit('showInput', this.chatInfo, this.placeholder)
            },
            // 更新评论区数据
            updateMessage(type, idx1, idx2, info = {}) {
                let children_reply = [],
                    children_reply1 = [],
                    children_reply2 = [];
                if (type === 1) {
                    children_reply1 = this.list[idx1].children_reply || [];
                    children_reply = children_reply1;
                } else if (type === 2 || type === 3) {
                    children_reply1 = this.list[idx1].children_reply || [];
                    children_reply2 = children_reply1[idx2].children_reply || [];
                    children_reply = children_reply2;
                } else {
                    children_reply = this.list || [];
                }
                if (type !== 1) {
                    info.to_reply_nickname = info.children_reply.length ? info.children_reply[0].to_reply_nickname : info.nickname;
                }
                children_reply.unshift(info);
            },
            // 发表评论
            submitComment(val) {
                this.$emit('submitComment', {
                    ...this.chatInfo,
                    comment: val
                })
            },
            likeStars(item, index, index1, index2, type) {
                let req = null,
                    obj = {
                        // activity_id: item.activity_id,
                        // message_id: item.id
                    };
                if (type === 1 && this.isFloor) {
                    obj.activity_id = item.activity_id;
                    obj.message_id = item.id;
                    req = item.is_like ? activityOnlineMessageUnlike : activityOnlineMessageLike;
                } else {
                    // obj.message_id = this.list[index].id;
                    obj.comment_id = item.id;
                    obj.activity_id = item.activity_id;
                    req = item.is_like ? activityOnlineCommentUnlike : activityOnlineCommentLike;
                }
                req(obj).then(res => {
                    this.updateLikeStars(item, index, index1, index2, type)
                })
                // this.$emit('likeStars', item, index)
            },
            updateLikeStars(item, index, index1, index2, type) {
                const {
                    is_like
                } = item;
                if (type === 1) {
                    this.list[index].is_like = !is_like;
                    if (is_like) {
                        this.list[index].like_count--;
                    } else {
                        this.list[index].like_count++;
                    }
                } else if (type === 2) {
                    let children_reply = this.list[index].children_reply || [];
                    children_reply[index1].is_like = !is_like;
                    if (is_like) {
                        children_reply[index1].like_count--;
                    } else {
                        children_reply[index1].like_count++;
                    }
                    this.$set(this.list[index].children_reply[index1], 'like_count', children_reply[index1].like_count);
                } else if (type === 3) {

                    let children_reply1 = this.list[index].children_reply || [];
                    let children_reply2 = children_reply1[index1].children_reply || [];
                    children_reply2[index2].is_like = !is_like;
                    if (is_like) {
                        children_reply2[index2].like_count--;
                    } else {
                        children_reply2[index2].like_count++;
                    }
                    this.$set(this.list[index].children_reply1[index1].children_reply2[index2], 'like_count',
                        children_reply2[index2].like_count);
                }
            },
            previewImage(urls, current) {
                // console.log(urls, current)
                uni.previewImage({
                    urls: urls.map(item => item.name),
                    current: current //地址需为https
                });
            },
            fullscreenchange(e, item, index, index1) {
                if (e.detail.fullScreen) {
                    // 进入全屏
                } else {
                    // 退出全屏
                    this.isFullscreen = false
                    this.videoCtx.pause();
                    this.$emit("send", false)
                }
            },
            xPlayAudio(item, index, index1, status) {
                console.log('status', status)
                this.$emit("sendAudio", status)
                if (!item.name) {
                    return this.$showToast('播放地址不存在')
                }
                if (this.innerAudioContext) {
                    const audio = this.list[index].audio[index1];
                    this.$set(audio, 'play', Number(!audio.play))
                    if (this.innerAudioContext.src === item.name) {
                        this.innerAudioContext.pause();
                    } else {
                        const idxs = this.audioPlayIdx;
                        if (idxs.length) {
                            this.$set(this.list[idxs[0]].audio[idxs[1]], 'play', 0)
                        }
                        this.initAudioInfo()
                    }
                    this.audioPlayIdx = [index, index1]
                    this.playAudio(item.name)
                }
            },
            // pause(){
            //     this.ispause = true;
            // },
            playVideo(item, index, index1) {

                this.isvideostatus = true;
                // this.isvideo = true;
                this.audioOrVideoPause(true)
                this.videoCtx = uni.createVideoContext(`playVideo${index}-${index1}`, this);
                console.log('videoCtx', this.videoCtx)
                this.videoCtx.requestFullScreen(); // 进入全屏
                this.$emit("send", true)
                let tid = setTimeout(() => {
                    this.isFullscreen = true;
                    this.videoCtx.play();
                    clearTimeout(tid)
                }, 500)

            },

            audioOrVideoPause(type) {
                if (type) {
                    this.innerAudioContext && this.innerAudioContext.pause();
                } else {

                }
            },
            navClick(type) {
                let initCalcItemScroll = this.initCalcItemScroll,
                    oldScrollLeft = this.scrollDetail.scrollLeft || 0;
                if (type === 'left') {
                    if (oldScrollLeft > 0) {
                        this.scrollLeft = oldScrollLeft > initCalcItemScroll ? oldScrollLeft - initCalcItemScroll : 0;
                    }
                } else {
                    this.scrollLeft = oldScrollLeft + initCalcItemScroll;
                }
            },
            scrollX(e) {
                this.scrollDetail = e.detail;
            },
            loadMore(type, idx1, idx2, des) {
                let floor_count = this.list[idx1].children_reply[idx2].floor_count + 2 || 4;
                this.$set(this.list[idx1].children_reply[idx2], 'floor_count', floor_count);
            },
            initSelectorQueryInfo() {
                let _this = this;
                uniSelectorQueryInfo('#calcItem', this)
                    .then(res => {
                        // console.log("得到布局位置信息", res);
                        _this.initCalcItemScroll = res.width;
                    })
                    .catch(err => {});
            }
        },
        mounted() {
            this.$nextTick(() => {
                this.initSelectorQueryInfo();
            })
        },
        onReady() {

        }
    }
</script>

<style lang="scss" scoped>
    $height: 40rpx; //输入区域 初始高度

    .permission {
        width: 40rpx;
        height: 40rpx;
        text-align: center;
        line-height: 40rpx;
        border-radius: 50%;
        background-color: #ccc;
        color: #ff5656;
        margin-right: 20rpx;
    }

    .show-permission {
        height: 0;
        overflow: hidden;
        background: #e9f1fb;
        border-radius: 24rpx;
        transition: height 0.5s;

        &.active {
            height: 84rpx;
        }

        >view {
            width: 152rpx;
            height: 60rpx;
            line-height: 60rpx;
            text-align: center;
            background: #ffffff;
            border-radius: 24rpx;

            font-size: 24rpx;
            font-weight: 400;
            color: #50506f;
            margin-right: 8rpx;
        }
    }

    .list {
        border-radius: 16rpx;
        padding: 26rpx 34rpx 26rpx 34rpx;
        margin-bottom: 4rpx;

        &:not(:last-child) {
            border-bottom: 2rpx solid #e2e6ec;
        }

        .common_avatar {
            .t_l {
                .images {
                    position: relative;
                    
                    .dots {
                        position: absolute;
                        width: 24rpx;
                        height: 24rpx;
                        background: #ff5656;
                        border: 2rpx solid #ffffff;
                        border-radius: 50%;
                        right: 0rpx;
                        top: 0rpx;
                    }
                    .avatar {
                        width: 60rpx;
                        height: 60rpx;
                        border-radius: 50%;
                        border: 4rpx solid #b5937f;
                    }

                    .bg {
                        position: absolute;
                        width: 60rpx;
                        height: 125rpx;
                        left: 4rpx;
                        top: 4rpx;
                    }


                }

                .name {
                    font-size: 28rpx;
                    font-weight: 600;
                    color: #101010;
                    margin-left: 16rpx;
                }
            }

            .time {
                font-size: 24rpx;
                font-weight: 400;
                color: #b7b7b7;
            }

            .replay {
                width: 108rpx;
                height: 50rpx;
                text-align: center;
                line-height: 46rpx;
                // background: #f7f7f9;
                border: 2rpx solid rgba(0, 0, 0, 0.08);
                border-radius: 32rpx;

                font-size: 24rpx;

                color: #555555;
                margin-left: 14rpx;
            }
        }

        .wrap {
            max-height: 500rpx;
            overflow: scroll;
            -webkit-overflow-scrolling: touch;
            // background: #f7f7f9;
        }

        .list_l {
            width: 80rpx;
        }
        .del_txt {
            width: 100%;
            text-align: right;
            color: #999999;
            font-size: 26rpx;
        }
        .desc {
            width: 538rpx;
            padding: 0 30rpx 16rpx 0rpx;
            
            
            &.lineclamp5 {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 5; 
                -webkit-box-orient: vertical;
                white-space: normal;
                height: 190rpx;
               
            }
        }
        .rightText {
            font-size: 26rpx;
            color: #ff5656;
        }

        .list_r {
            width: calc(100% - 80rpx);
            padding: 24rpx 0rpx;
            height: 100%;

            .item {
                border-bottom: 2rpx dashed #b7b7b7;
                padding-bottom: 24rpx;
                margin-bottom: 24rpx;

                .common_avatar {
                    .t_l {

                        .name {
                            font-size: 24rpx;
                        }
                    }

                    .time {
                        font-size: 24rpx;
                        font-weight: 400;
                        color: #b7b7b7;
                    }
                }

                .desc {
                    width: 370rpx;
                }

                .item2 {
                    padding-left: 80rpx;
                }
            }

            .mess_handle {
                font-size: 24rpx;

                color: #ff5656;

                .time {
                    color: #999999;
                }

                .item {
                    image {
                        width: 24rpx;
                        height: 24rpx;
                    }

                    text {
                        padding: 0 16rpx;
                        font-size: 24rpx;

                        color: #ff5656;
                    }

                    border: none;
                    padding: 0;
                    margin: 0;
                }
            }

            .show_more {
                font-size: 24rpx;
                transform: scale(0.83);

                color: #b7b7b7;
            }
        }
    }

    .w_input {
        flex: 1;
        min-height: $height;
        /*#ifdef MP*/
        padding: 20rpx 10rpx 20rpx 10rpx;
        /*#endif*/
        /*#ifndef MP */
        padding: 20rpx;
        /*#endif*/
        background-color: #fff;
        border-radius: 26rpx;
    }

    .mess_image {
        flex: 1;
        height: 136rpx;
        margin-bottom: 20rpx;
        margin-left: -20rpx;

        .nav {
            width: 20rpx;
            line-height: 136rpx;
            text-align: center;

            text {
                font-size: 24rpx;
                font-weight: bolder;
            }
        }

        .wrap1 {
            width: calc(100% - 40rpx);

            .item {
                // display: inline-block;
                width: 136rpx;
                height: 136rpx;

                &:not(:last-child) {
                    margin-right: 6rpx;
                }

                image {
                    width: 136rpx;
                    height: 136rpx;
                }

                &.video {
                    position: relative;

                    .cover {
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        z-index: 10;
                        top: 0;

                        image {
                            width: 120rpx;
                            height: 120rpx;
                        }
                    }
                }

                &.audio {
                    // .cover {
                    display: flex;
                    align-items: center;
                    justify-content: space-around;

                    image {
                        width: 120rpx;
                        height: 120rpx;
                    }

                    // }
                }
            }
        }
    }

    .fixed_chat {
        height: 112rpx;
        position: fixed;
        z-index: 9999;
        /* top: 0; */
        width: 100%;
        bottom: 0;
        left: 0;
        background: #f7f7f9;
    }

    .refining_span {
        border: 1rpx solid red;
        color: red;
        padding: 2rpx 30rpx;
        font-weight: bold;
        font-size: 30rpx;
        letter-spacing: 6rpx;
        transform: rotate(-30deg);
        opacity: 0.6;
        border-radius: 10rpx;
    }
    .vote-txt {
        color: #ff5656;
        font-size: 24rpx;
        font-weight: 400;
    }
</style>
