<template>
    <view>
        <view class="xcomment">
            <view class="list " v-for="(item, index) in commentList" :key="index"
                v-if="pageMore ? index<3 : index<commentList.length">
                <view>
                    <view class="common_avatar flex flex_align_center flex_between">
                        <view class="t_l flex flex_align_center">
                            <image :src="item.avatar" mode="aspectFill"></image>
                            <view class="name">{{ item.nickname }} : {{ item.to_reply_nickname }}</view>
                        </view>
                        <view class="time flex flex_align_center">
                            <view v-if="time">{{ item.created_at || '' }}</view>
                            <view class="replay" @click="showInput(2, item,index, null)">回复</view>
                        </view>
                    </view>
                    <view class="flex">
                        <view class="list_l"></view>
                        <view class="list_r">
                            <view class="desc">{{ item.comment }}</view>
                            <view class="mess_handle flex flex_align_center flex_between" v-if="handle">
                                <view class="time">
                                    <text class="font_size20">{{item.add_time}}</text>
                                </view>
                                <view class="flex flex_align_center">
                                    <view class="item flex flex_align_center" @click="likeStars(item,index)">
                                        <image src="@/static/images/yuanshi/like_.png" mode="widthFix"
                                            v-if="item.is_like"></image>
                                        <image src="@/static/images/yuanshi/like.png" mode="widthFix" v-else></image>
                                        <text>{{ item.like_count>-1?item.like_count:0 }}</text>
                                    </view>
                                    <view class="item flex flex_align_center">
                                        <image src="@/static/images/yuanshi/comment.png" mode="widthFix"></image>
                                        <text>{{ item.comments || 0 }}</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="flex wrap" v-if="item.children_reply&&item.children_reply.length > 0">
                    <view class="list_l"></view>
                    <view class="list_r" v-if="item.children_reply&&item.children_reply[0].nickname">
                        <view class="item" v-for="(item1, index1) in item.children_reply" :key="index1"
                            v-if="index1 < item.floor_count">
                            <view class="common_avatar flex flex_align_center flex_between">
                                <view class="t_l flex flex_align_center">
                                    <image :src="item1.avatar" mode="aspectFill"></image>
                                    <view class="name">{{ item1.nickname }} : {{ item1.to_reply_nickname }}</view>
                                </view>
                                <view class="time flex flex_align_center">
                                    <view v-if="time">{{ item1.created_at }}</view>
                                    <view class="replay" @click="showInput(2, item1,index, index1)">回复</view>
                                </view>
                            </view>
                            <view class="flex">
                                <view class="list_l"></view>
                                <view class="desc">{{ item1.comment }}</view>
                            </view>
                        </view>
                        <view class="show_more" v-if="item.children_reply.length">
                            <view v-if="item.children_reply.length - item.floor_count > 0"
                                @click="loadMore(1, index, null, '楼层回复')">
                                展开{{ item.children_reply.length - item.floor_count }}条回复
                            </view>
                            <!-- <view v-else>已全部加载</view> -->
                        </view>
                        <!-- <view class="show_more" style="margin-top: 0;" v-else>去添加第一条评论吧</view> -->
                    </view>
                </view>
            </view>
            <view class="flex flex_around">
                <template v-if="commentList.length">
                    <view class="show_more" @click="showMore('more')">{{ pageMore ? '展开更多评论' : '已全部加载' }}</view>
                </template>
                <template v-if="!commentList.length&&tips">
                    <view class="show_more">去添加第一条评论吧</view>
                </template>
            </view>
        </view>
        <view v-if="chat">
            <xChat :adjustPosition="false" :placeholder="placeholder" :uid="uid" :inputShow="inputShow"
                @inputBlur="inputBlur" @send="submitComment" desc="comment"></xChat>
        </view>
    </view>
</template>

<script>
    import xChat from '@/components/x-chat/x-chat';
    import {
        checkLogin,
        autoAuth
    } from '@/utils/common.js';
    import {
        yComment,
        yCommentAdd,
        commentLike,
        commentUnLike
    } from '@/api/yuanshi/public.js';
    import {
        activityCommentAdd
    } from '@/api/community';
    export default {
        props: {
            info: {
                type: Array,
                default () {
                    return [];
                }
            },
            ctype: {
                type: Number,
                default: null
            },
            pageType: {
                type: String,
                default: 'yuanshi'
            },
            rid: {
                type: [String, Number],
                default: ''
            },
            eid: {
                type: [String, Number],
                default: ''
            },
            handle: {
                type: Boolean,
                default: false
            },
            tips: { //相关提示
                type: Boolean,
                default: true
            },
            time: {
                type: Boolean,
                default: true
            },
            chat: {
                type: Boolean,
                default: true
            },
            req: {
                type: Function,
                default: null
            }
        },
        watch: {
            info(a, b) {
                this.commentList = a;
                this.pageMore = a.length === 10;
            }
        },
        components: {
            xChat
        },
        data() {
            return {
                pageMore: true,
                uid: -1,
                commentList: this.info || [],
                inputShow: false,
                placeholder: '说点什么吧',
                message_num: 0,
                page: {
                    //ctype=2生效 默认已加载第一页数据
                    page: 2,
                    limit: 2,
                    more: true
                }
            };
        },
        methods: {
            loadMore(type, idx, idx1, des) {
                console.log(type, idx, idx1, des);
                switch (type) {
                    case 1:
                        // if(this.commentList[idx])
                        let floor_count = this.commentList[idx].floor_count + 2 || 4;
                        this.$set(this.commentList[idx], 'floor_count', floor_count);
                        break;
                    case 2:
                        break;
                    case 3:
                        break;
                    default:
                        break;
                }
            },
            likeStars(item, index) {
                let obj = {
                    related_id: item.related_id,
                    comment_id: item.id,
                    type: item.type
                };
                if (item.is_like) {
                    commentUnLike(obj).then(res => {
                        this.commentList[index].like_count--;
                    })
                } else {
                    commentLike(obj).then(res => {
                        this.commentList[index].like_count++;
                    })
                }
                this.commentList[index].is_like = !item.is_like
            },
            showMore() {
                if (!this.page.more) return;

                if (this.pageType === 'community') {
                    return this.pageMore = false
                }
                yComment({
                    related_id: this.rid,
                    type: 2,
                    page: this.page.page,
                    limit: this.page.limit
                }).then(res => {
                    this.commentList = this.commentList.concat(res.data);
                    this.page.more = res.data.length === this.page.limit;
                    this.pageMore = res.data.length === this.page.limit;
                    this.page.page++;
                });
            },
            showInput(type, item, idx = null, idx1 = null) {
                const {
                    wish_id = null, related_id = null, id = null, nickname = null, is_my = null, source_id = null,
                        special_id = null, isCourseList = false
                } = item;
                const pid = special_id && source_id || isCourseList ? id : (type === 1 ? 0 : id);
                if (!checkLogin()) {
                    this.$showToast('请登录');

                    let setT = setTimeout(() => {
                        autoAuth();
                        clearTimeout(setT);
                    }, 800);

                    return;
                }
                if (is_my) {
                    return this.$showToast('不能自己回复自己');
                }
                this.inputShow = true;
                this.uid = pid;
                this.replay = {
                    ...item,
                    type,
                    pid,
                    idx,
                    idx1,
                };

                switch (type) {
                    case 1:
                        // 添加第一层评论
                        this.placeholder = this.ctype === 1 || this.pageType === 'community' ? '添加你的评论' :
                            '对此测评说点什么吧！@' + nickname;
                        break;
                    case 2:
                        // 第一层楼与第二层回复
                        this.placeholder = '对此评论说点什么吧！@' + nickname;
                        break;
                    case 3:
                        // 第三层
                        this.placeholder = '对此评论说点什么吧！@' + nickname;
                        break;
                    default:
                        break;
                }

                this.$emit('showInput', {
                    id: this.rid,
                    uid: pid,
                    related_id
                }, this.placeholder, {
                    idx,
                    idx1
                })
            },
            submitComment(val) {
                this.inputShow = true;
                const {
                    type,
                    wish_id,
                    pid,
                    id,
                    idx,
                    idx1,
                    related_id,
                    source_id,
                    special_id
                } = this.replay;

                const {
                    pageType
                } = this;
                let obj = {
                    comment: val,
                    pid,
                    // #ifndef MP-TOUTIAO
                    from:'routine',
                    // #endif
                    // #ifdef MP-TOUTIAO
                    from: 'bytedance',
                    // #endif
                },
                req = null;
                if (this.req) {
                    if (source_id) {
                        obj.source_id = source_id;
                    } else {
                        obj.source_id = 0;
                        obj.message_id = id;
                    }
                    obj.special_id = special_id;
                    req = this.req(obj);
                } else {
                    obj.related_id = this.rid;
                    if (pageType === 'community') {
                        req = activityCommentAdd(obj)
                    } else {
                        obj.type = this.ctype;
                        req = yCommentAdd(obj)
                    }
                }
                req.then(res => {
                        console.log('回复评论---',res)
                        res.data.created_at = '刚刚';
                        res.data.is_my = true;
                        switch (type) {
                            case 1:
                                // 添加第一层评论
                                // this.wishExchange()
                                this.commentList.unshift(res.data);
                                this.$showToast('添加成功');
                                break;
                            case 2:
                                // 第一层楼与 第二层楼回复
                                let children_reply = this.commentList[idx].children_reply || [];
                                res.data.to_reply_nickname = res.data.children_reply[0].to_reply_nickname;
                                children_reply.unshift(res.data);
                                this.commentList[idx].comments++;
                                this.$set(this.commentList[idx], 'children_reply', children_reply);
                                this.$set(this.commentList[idx], 'comments', this.commentList[idx].comments);
                                this.$showToast('回复成功');
                                break;
                            case 3:
                                // 三层回复
                                // let children_reply2 = this.commentList[idx].children_reply[idx1].children_reply || [];
                                // children_reply2.unshift(res);
                                // console.log(children_reply2);
                                // this.$set(this.commentList[idx].children_reply[idx1], 'children_reply', children_reply2);
                                break;
                            default:
                                break;
                        }
                        this.inputShow = false;
                        this.message_num++;
                        this.$emit('success', res);
                    })
                    .catch(err => {
                        console.log('eee', err);
                        this.$showToast(err || err.error || '发送失败');
                        this.inputShow = false;
                    });
            },
            inputBlur() {
                this.inputShow = false;
            }
        },
        mounted() {}
    };
</script>

<style scoped lang="scss">
    .xcomment {
        // min-height: 400rpx;
        // padding-bottom: 40rpx;

        .show_more {
            width: 100%;
            text-align: center;
            font-size: 24rpx;
            margin: 22rpx 0;
            font-family: PingFang SC;
            font-weight: 400;
            color: #555555;
        }

        .list {
            // background: #f7f7f9;
            // background: #ffffff;
            border-radius: 16rpx;
            padding: 26rpx 34rpx 26rpx 34rpx;
            margin-bottom: 4rpx;

            &:not(:last-child) {
                border-bottom: 2rpx solid #e2e6ec;
            }

            .common_avatar {
                .t_l {
                    image {
                        width: 60rpx;
                        height: 60rpx;
                        border-radius: 50%;
                        border: 4rpx solid #b5937f;
                    }

                    .name {
                        font-size: 28rpx;
                        font-weight: 600;
                        color: #101010;
                        margin-left: 16rpx;
                    }
                }

                .time {
                    font-size: 24rpx;
                    font-weight: 400;
                    color: #b7b7b7;
                }

                .replay {
                    width: 108rpx;
                    height: 50rpx;
                    text-align: center;
                    line-height: 46rpx;
                    // background: #f7f7f9;
                    border: 2rpx solid rgba(0, 0, 0, 0.08);
                    border-radius: 32rpx;

                    font-size: 24rpx;

                    color: #555555;
                    margin-left: 14rpx;
                }
            }

            .wrap {
                max-height: 500rpx;
                overflow: scroll;
                -webkit-overflow-scrolling: touch;
                // background: #f7f7f9;
            }

            .list_l {
                width: 80rpx;
            }

            .desc {
                width: 538rpx;
                padding: 0 30rpx 16rpx 16rpx;
            }

            .list_r {
                width: 100%;
                padding: 24rpx 0rpx;
                height: 100%;

                .item {
                    border-bottom: 2rpx dashed #b7b7b7;
                    padding-bottom: 24rpx;
                    margin-bottom: 24rpx;

                    .common_avatar {
                        .t_l {
                            image {
                                width: 58rpx;
                                height: 58rpx;
                            }

                            .name {
                                font-size: 24rpx;
                            }
                        }

                        .time {
                            font-size: 24rpx;
                            font-weight: 400;
                            color: #b7b7b7;
                        }
                    }

                    .desc {
                        width: 370rpx;
                    }
                }

                .mess_handle {
                    font-size: 24rpx;

                    color: #ff5656;

                    .time {
                        color: #999999;
                    }

                    .item {
                        image {
                            width: 24rpx;
                            height: 24rpx;
                        }

                        text {
                            padding: 0 16rpx;
                            font-size: 24rpx;

                            color: #ff5656;
                        }

                        border: none;
                        padding: 0;
                        margin: 0;
                    }
                }

                .show_more {
                    font-size: 24rpx;
                    transform: scale(0.83);

                    color: #b7b7b7;
                }
            }
        }
    }

    .fixed_chat {
        height: 112rpx;
        position: fixed;
        z-index: 9999;
        /* top: 0; */
        width: 100%;
        bottom: 0;
        left: 0;
        background: #f7f7f9;
    }
</style>
