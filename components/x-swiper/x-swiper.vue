<template>
	<view class="swiper relative">
		<swiper
			:indicator-dots="indicatorDots"
			:indicator-color="indicatorColor"
			:indicator-active-color="indicatorActiveColor"
			:display-multiple-items="displayMultipleItems > arr.length ? arr.length : displayMultipleItems"
			:previous-margin="previousMargin"
			:next-margin="nextMargin"
			:autoplay="autoplay"
			:vertical="vertical"
			:interval="interval"
			:duration="duration"
			:circular="circular"
			@change="dotsChange"
			:style="{ height: height }"
		>
			<swiper-item v-for="(item, index) in arr" :key="index" @click="swiperClick(item)" :class="xClass" :style="{ padding: padding }">
				<!-- 头条小程序不支持slot-scope 解构 -->
				<slot :item="item" :index="index"><image :src="srcName==='item' ?  item : item[srcName] " :mode="mode" :class="current !== index && scale ? 'scale' : ''" :style="{ borderRadius: radius }" lazy-load></image></slot>
			</swiper-item>
		</swiper>
		<view class="absolute dots" v-if="dots">
			<view class="acea-row row-around" v-if="dotsType == 'line'">
				<view class="acea-row dots_line">
					<view v-for="(item, index) in arr" :key="index" :style="{ background: current == index ? indicatorActiveColor : indicatorColor }"></view>
				</view>
			</view>
			<view class="acea-row row-right" v-if="dotsType == 'block'">
				<view class="dots_block">{{ current + 1 }} / {{ arr.length }}</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		indicatorDots: {
			type: Boolean,
			default: false
		},
		indicatorColor: {
			type: String,
			default: '#C7C7C7'
		},
		indicatorActiveColor: {
			type: String,
			default: '#ffffff'
		},
		displayMultipleItems: {
			type: Number,
			default: 1
		},
		previousMargin: {
			//前后显示图片的一部分，调整该属性
			type: String,
			default: '0rpx'
		},
		mode:{
			type: String,
			default: ''
		},
		nextMargin: {
			type: String,
			default: '0rpx'
		},
		autoplay: {
			type: Boolean,
			default: false
		},
		vertical: {
			type: Boolean,
			default: false
		},
		interval: {
			type: Number,
			default: 4000
		},
		duration: {
			type: Number,
			default: 500
		},
		circular: {
			type: Boolean,
			default: true
		},
		scale: {
			type: Boolean,
			default: false
		},
		padding: {
			type: String,
			default: '0rpx'
		},
		arr: {
			type: Array,
			default() {
				return [];
			}
		},
		height: {
			type: String,
			default: '100%'
		},
		dots: {
			type: Boolean,
			default: true
		},
		dotsType: {
			type: String,
			default: 'line'
		},
		xClass: {
			type: String,
			default: 'swiper_item'
		},
		slotProp: {
			type: String,
			default: 'item'
		},
		srcName: {
			type: String,
			default: 'image'
		},
		radius: {
			type: String,
			default: '26rpx'
		}
	},
	data() {
		return {
			current: 0
		};
	},
	methods: {
		dotsChange(e) {
			this.current = e.detail.current;
			this.$emit('change', this.current);
		},
		swiperClick(item) {
			this.$emit('swiperClick', item);
		}
	}
};
</script>

<style scoped="" lang="less">
.swiper {
	height: 100%;
	image {
		width: 100%;
		height: 100%;
	}
	.scale {
		transform-origin: center center;
	}
	swiper-item {
		box-sizing: border-box;
	}
}
.dots {
	width: 100%;
	bottom: 10rpx;
	.dots_line {
		> view {
			width: 16rpx;
			height: 4rpx;
			// background: rgba(49, 48, 59, 1);
			&.dot_active {
				// background:#fff;
			}
		}
	}
	.dots_block {
		background: #fff;
		padding: 5rpx 10rpx;
		margin-right: 10rpx;
		font-size: 24rpx;
	}
}
</style>
