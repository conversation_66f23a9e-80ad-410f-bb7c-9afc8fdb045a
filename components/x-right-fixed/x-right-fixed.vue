<template>
	<view class="xRightFixed" :style="{opacity:opacity}">
		<view class="wrap">
			<view class="tool">
				<image src="@/static/images/yuanshi/share.png" mode="widthFix"></image>
				<view class="txt" @click="rightHandle('share')">分享</view>
			</view>
			<view class="write flex flex_around flex_align_center"  @click="rightHandle('write')"><image src="@/static/images/yuanshi/write1.png" mode="widthFix"></image></view>
			<view class="tool" @click="rightHandle('top')">
				<image src="@/static/images/yuanshi/share.png" mode="widthFix"></image>
				<view class="txt">顶部</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		opacity: {
			type: [Number,String],
			default: 0
		}
	},
	components: {},
	data() {
		return {};
	},
	methods: {
		goPages(path, type) {
			this.$navigator(path, type);
		},
		rightHandle(type){
			this.$emit('rightFixedHandle',type)
		}
		
	},
	mounted() {},
	onLoad(option) {}
};
</script>

<style scoped lang="scss">
.xRightFixed {
	position: fixed;
	right: 0;
	width: 78rpx;
	background: #b5937f;
	border: 1rpx solid rgba(0, 0, 0, 0.078);
	opacity: 1;
	border-radius: 40rpx 0 0 40rpx;

	.wrap {
		padding: 38rpx 4rpx 34rpx 8rpx;
	}
	.txt {
		font-size: 24rpx;
		transform: scale(0.834);
		color: #5a3925;
	}
	.tool{
		// width: 48rpx;
		text-align: center;
		image {
			width: 30rpx;
		}
	}
	.write {
		width: 66rpx;
		height: 66rpx;
		background: #5a3925;
		border-radius: 50%;
		margin:22rpx auto;
		image {
			width: 33rpx;
			height: 33rpx;
		}
	}
}
</style>
