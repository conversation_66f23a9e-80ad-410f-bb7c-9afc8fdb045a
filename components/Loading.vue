<template>
	<view>
		<view class="Loads acea-row row-center-wrapper" v-if="loading && !loaded" style="margin-top: 20rpx;">
			<view v-if="loading">
				<view class="iconfont icon-jiazai loading acea-row row-center-wrapper"></view>
				正在加载中
			</view>
			<view v-else>上拉加载更多</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'Loading',
	props: {
		loaded: {
			type: Boolean,
			default: false
		},
		loading: {
			type: Boolean,
			default: false
		}
	}
};
</script>
