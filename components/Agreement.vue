<template>
	<view>
		<view class="address-window" :class="value === true ? 'on' : ''">
			<view class="title">
				<!-- “组队过夏天”活动协议书 -->
				<span class="iconfont icon-guanbi" @click="closeAddress"></span>
			</view>
            <view class="title-txt">
                <u-parse :html="agreementReadtxt" :tag-style="parseStyle" @linkpress="$linkpress">
                </u-parse>
            </view>
			
			<view class="address-window-but">
			    <view class="address-window-but-left" @click="closeAddress">
			        不同意
			    </view>
                <view class="address-window-but-right" @click="isCloseAddress">
                    同意
                </view>
			</view>
		</view>
        <view class="mask" @touchmove.prevent :hidden="value === false" @click="closeAddress"></view>
	</view>
</template>
<script>
	import{VUE_APP_URL} from '@/config.js'
export default {
	name: 'AddressWindow',
	props: {
		value: <PERSON><PERSON>an,
		checked: Number,
        agreementReadtxt:String
	},
	data: function() {
		return {
			imagePath:VUE_APP_URL,
			current: 0,
			cartId: 0,
			pinkId: 0,
			couponId: 0
		};
	},
	mounted: function() {},
	methods: {
		closeAddress() {
			this.$emit('input', false);
		},
        isCloseAddress(){
            this.$emit('input', false);
            this.$emit('isread', true);
        }
	}
};
</script>
<style scoped>
	.address-window {
		background-color: #fff;
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		z-index: 6666;
		transform: translate3d(0, 100%, 0);
		-webkit-transform: translate3d(0, 100%, 0);
		-ms-transform: translate3d(0, 100%, 0);
		-moz-transform: translate3d(0, 100%, 0);
		-o-transform: translate3d(0, 100%, 0);
		transition: all .3s cubic-bezier(.25, .5, .5, .9);
		-webkit-transition: all .3s cubic-bezier(.25, .5, .5, .9);
		-moz-transition: all .3s cubic-bezier(.25, .5, .5, .9);
		-o-transition: all .3s cubic-bezier(.25, .5, .5, .9);
	}
	
	.address-window.on {
		transform: translate3d(0, 0, 0);
		-webkit-transform: translate3d(0, 0, 0);
		-ms-transform: translate3d(0, 0, 0);
		-moz-transform: translate3d(0, 0, 0);
		-o-transform: translate3d(0, 0, 0);
	}
	
	.address-window .title {
		font-size: 32rpx;
		font-weight: bold;
		text-align: center;
		height: 123rpx;
		line-height: 123rpx;
		position: relative;
		color: #333;
	}
    .address-window .title-txt {
        width: 100%;
        height: 500rpx;
    	font-size: 24rpx;
    	text-align: center;
    	position: relative;
    	color: #333;
        overflow: auto;
        padding: 0 30rpx;
        box-sizing: border-box;
    }
    
    .address-window .address-window-but {
        width: 100%;
        height: auto;
        overflow: hidden;
        padding: 0 30rpx;
        box-sizing: border-box;
        margin: 40rpx 0;
    }
    
    .address-window .address-window-but .address-window-but-left{
        float: left;
       width: 240rpx;
       text-align: center;
       height: 70rpx;
       line-height: 70rpx;
       color: #000;
       border: 2rpx solid #999;
       border-radius: 10rpx;
    }
    .address-window .address-window-but .address-window-but-right{
        float: right;
       width: 240rpx;
       text-align: center;
       height: 70rpx;
       line-height: 70rpx;
       color: #fff;
       border: 2rpx solid #0081ff;
       background-color: #0081ff;
       border-radius: 10rpx;
    }
    
	.address-window .title .iconfont {
		position: absolute;
		right: 30rpx;
		color: #8a8a8a;
		font-size: 35rpx;
	}

</style>