<template>
    <view class="nav-scroll">
        <scroll-view :scroll-x="true" style="font-weight: bold;margin: 15px 0;" class="scroll-view" :scroll-left="currentItemLeft" scroll-with-animation="true">
            <view class="nav-scroll-item" v-for="(item,index) in navItems" :key="index"
                :style="{'color':`${item.selected?color:'#3399FF'}`,}"
                @click="changTab(index,true,item)">
                <view class="nav-scroll-items">
                    <template v-if="item.selected">
                        <image v-if="!ispause" class="nav-scroll-items-img" src="@/static/images/course.gif" mode=""></image>
                        <image v-else class="nav-scroll-items-img" src="@/static/images/course.png" mode=""></image>
                    </template>
                    {{index>8?index+1:'0'+(index+1)}} <text class="nav-scroll-items-txt">|</text> {{item.title}}
                </view>
            </view>
        </scroll-view>
        <uni-popup ref="work_popup" type="center" :maskClick="false">
            <view class="cancelPopup work">
                <image class="cancel-icon" src="../../static/images/cancel-icon.png" mode="" @click="closeWorkPopup">
                </image>
                <view class="cancel-title work">您需要提交作业才可以解锁彩蛋哦~</view>
                <view class="cancel-title work">如已提交作业请等待审核通过</view>
                <view class="cancel-button work">
                    <view class="left-work-button" @click="goCommentSubmit()">去交作业</view>
                    <view class="right-work-button" @click="closeWorkPopup">取消</view>
                </view>
            </view>
        </uni-popup>

    </view>
</template>

<script>
    import {
        getSpecialDetail,
        getSpecialCourseCatalogList, // 目录（升级版）
        specialUnlockSurprise
    } from '@/api/yknowledge.js'
    import storage from '@/utils/storage.js';
    import {
        toLogin,
        debounce,
        checkLogin,
        autoAuth,
        authNavigator,
        openWeChatCustomerService,
        zxauthNavigator
    } from '@/utils/common.js'

    export default {
        props: {
            ispause: {
                type: Boolean,
                default: false
            },
            sid: {
                type: Number,
                default: 0
            },
            cid: {
                type: Number,
                default: 0
            },
            currentIndex: {
                type: Number,
                default: 0
            },
            height: {
                type: Number,
                default: 70
            },
            color: {
                type: String,
                default: '#05A8E9'
            },
            items: {
                type: Array,
                default: []
            },
            scroll: {
                type: Boolean,
                default: true
            }
        },
        data() {
            return {
                loaded: false,
                navItems: [],
                docItems: [],
                scrollLeft: 0,
                screenWidth: 0,
                navItemsIndex: 0,
                currentItemLeft: 0,
                currentLineLeft: 0,
                currentLineWidth: uni.upx2px(36),
                platform: uni.getSystemInfoSync().platform,
                tmplIds: ['nlD298UK462gFa5oZjL9oX-Ajk2WDWZjy_oLx4Ic5Ls'],
                // #ifdef MP-WEIXIN
                from: 'routine',
                // #endif
                // #ifdef MP-TOUTIAO
                from: 'bytedance',
                // #endif
                // #ifdef H5
                from: 'weixinh5',
                // #endif
                detail: {},
                count_lh:0,
                lastTime_lh:0
            }
        },
        watch: {
            items: {
                handler(newVal, oldVal) {
                    this.navItems = newVal;
                    this.getSpecialDetail()
                },
                deep: true
            },
        },
        mounted: async function() {
            this.navItems = this.items;
            console.log('this.navItems',this.navItems)
            this.navItemsIndex = this.navItems.findIndex((item) => { //这边是按默认选中的导航，也可以再传递一个默认的currentIndex来当默认值
                return item.selected === true;
            })
            if (this.navItemsIndex === -1) {
                this.navItemsIndex = 0;
            }

            this.screenWidth = uni.getSystemInfoSync().screenWidth;
            this.docItems = await this.getDocItems(); //使用异步可以控制changTab函数的直接调用
            this.changTab(this.navItemsIndex, false, {});

        },
        onReady() {
            this.getSpecialDetail()
        },
        methods: {
            goPages(path, type, pathType) {
                if (type) {
                    this.$authNavigator(path);
                } else {
                    this.$navigator(path, pathType);
                }
            },
            goCommentSubmit(status, type) {
                let that = this;
                if (this.detail.pay_type === 1 && !this.detail.is_pay) {
                    return this.$showToast('仅学员可提交作业');
                } else {
                    if (this.isMute === 1) {
                        return this.$showToast('已被禁言');
                    }
                    // #ifdef MP-WEIXIN
                    uni.getSetting({
                        withSubscriptions: true,
                        success(res) {
                            console.log('获取设置res++', res.subscriptionsSetting.mainSwitch)
                            if (res.subscriptionsSetting.mainSwitch) {
                                uni.requestSubscribeMessage({
                                    tmplIds: that.tmplIds,
                                    success(res) {},
                                    fail(err) {},
                                    complete: function(res) {
                                        console.log('获取设置res--', res[
                                            'nlD298UK462gFa5oZjL9oX-Ajk2WDWZjy_oLx4Ic5Ls'
                                        ])
                                        if (res[
                                                'nlD298UK462gFa5oZjL9oX-Ajk2WDWZjy_oLx4Ic5Ls'] ==
                                            'accept') {
                                            // reject
                                            that.closeWorkPopup()
                                            that.goPages(
                                                '/pages/yknowledge/comment/submitWork?id=' +
                                                this.cid + '&sid=' + this.sid, true)
                                        } else {
                                            uni.showModal({
                                                title: '您已经拒绝课程解锁成功通知',
                                                content: '请先打开消息通知设置',
                                                confirmText: '去允许',
                                                success: function(res1) {
                                                    if (res1.cancel) {
                                                        uni.showToast({
                                                            title: '已取消',
                                                            icon: 'none',
                                                            duration: 1000
                                                        })
                                                        return
                                                    } else if (res1.confirm) {
                                                        uni.openSetting({
                                                            success: function(
                                                                res2) {
                                                                console.log(
                                                                    'res2',
                                                                    res2
                                                                )
                                                            },
                                                            fail: function(
                                                                err) {
                                                                console.log(
                                                                    'xxx',
                                                                    err)
                                                            }
                                                        })
                                                    }
                                                },
                                            })
                                        }
                                    },
                                })
                            } else { // 用户没有点击“总是保持以上，不再询问”则每次都会调起订阅消息
                                uni.openSetting({ // 打开设置页
                                    success(res) {
                                        // console.log(res.authSetting) 
                                    }
                                });
                            }
                        },
                        fail(err) {
                            console.log('获取设置err', err)
                        },
                    })
                    // #endif
                    // #ifndef MP-WEIXIN
                    this.closeWorkPopup()
                    this.goPages('/pages/yknowledge/comment/submitWork?id=' + this.cid + '&sid=' + this.sid, true)
                    // #endif
                }
            },

            // 彩蛋弹窗开关
            openWorkPopup() {
                this.$refs.work_popup.open();
            },
            closeWorkPopup() {
                this.$refs.work_popup.close();
            },

            getSpecialDetail() {
                let param = {
                    id: this.sid,
                    gift_uid: '',
                    gift_order_id: '',
                    from: this.from,
                    platform: this.platform
                }
                getSpecialDetail(param).then(res => {
                    this.detail = res.data;
                    // 是否彩蛋课
                    if (this.detail.is_easter_eggs) {
                        for (let i = 0; i < this.navItems.length; i++) {
                            this.navItems[i].is_eggs_class = false
                        }
                        // 是否解锁彩蛋
                        if (this.detail.get_easter_eggs_status) {
                            this.navItems[this.navItems.length - 1].is_eggs_class = true
                        }
                    }

                }).catch(err => {

                })
            },
            getDocItems() {
                return new Promise((ret) => {
                    this.$nextTick(() => {
                        const query = uni.createSelectorQuery().in(this); //当前组件作用域内
                        setTimeout(() => {
                            const view = query.selectAll('.nav-scroll-item');
                            view.boundingClientRect(data => {
                                this.docItems = data; //得到每个菜单的doc属性值
                                ret(this.docItems);
                            }).exec();
                            this.loaded = true; //需要导航菜单完成后显示
                        }, 100);
                    })
                })
            },

            changTab(index, callback = true, items) {
                if (callback && this.navItemsIndex === index) { //防止重复点击
                    return false;
                }
                const currentTime = new Date().getTime();
                if (currentTime - this.lastTime_lh > 60000) {
                    this.count_lh = 0;
                }
                this.count_lh++;
                if (this.count_lh > 6) {
                    this.$showToast('太快了，请稍后再试~ \n 只要功夫深，铁杵磨成针！ ')
                    return;
                }
                this.lastTime_lh = currentTime;
                
                if (items.is_eggs_class) {
                    if (this.detail.get_easter_eggs_condition) {
                        // 需提交作业才可领彩蛋
                        if (!this.detail.user_work_submit_status) {
                            this.openWorkPopup()
                        }
                        if (this.detail.user_work_submit_status == 1) {
                            if (!this.detail.get_easter_eggs_status) {
                                specialUnlockSurprise({
                                    special_id: this.sid,
                                    source_id: 0,
                                    category: 'easter_egg'
                                }).then(res => {
                                    this.detail.get_easter_eggs_status = 1;
                                    this.$showToast('彩蛋已解锁')
                                    setTimeout(() => {
                                        
                                        const currentDocItem = this.docItems[index];
                                        const itemLeft = currentDocItem.left ||
                                        0; //计算当前选中的导航项加载时候初始的left
                                        const itemWidth = currentDocItem.width || 0; //当前选中菜单的宽度

                                        this.currentItemLeft = itemLeft - this.screenWidth /
                                        2; //选中菜单居中显示（减去屏幕的一半，剩余部分就是横向滚动条位置）
                                        this.currentItemLeft += itemWidth / 2; //还要再加上本身宽度的一半的误差

                                        this.currentLineLeft = itemLeft + itemWidth /
                                        2; //选中菜单的下划线位置 = 菜单所在的位置 + 菜单自身的宽度/2
                                        this.currentLineLeft -= this.currentLineWidth /
                                        2; //还要再减去下划线本身宽度一半的误差

                                        this.navItemsIndex = index;
                                        this.navItems.forEach((nItem, nIndex) => {
                                            if (index === nIndex) {
                                                let videoCourse = {
                                                    videoCourseId: this.sid,
                                                    courseLastId: this.navItems[nIndex].id
                                                }
                                                storage.set('videoCourse' + this.sid,
                                                    videoCourse)

                                            } 
                                        })
                                        if (callback) {
                                            this.$emit('itempause', true);
                                            this.$emit('currentItem', this.navItems[index]);
                                        }
                                    }, 1000)
                                    console.log('解锁彩蛋')
                                })
                            } else {
                                this.$showToast('彩蛋已解锁')
                                setTimeout(() => {
                                    const currentDocItem = this.docItems[index];
                                    const itemLeft = currentDocItem.left || 0; //计算当前选中的导航项加载时候初始的left
                                    const itemWidth = currentDocItem.width || 0; //当前选中菜单的宽度

                                    this.currentItemLeft = itemLeft - this.screenWidth /
                                    2; //选中菜单居中显示（减去屏幕的一半，剩余部分就是横向滚动条位置）
                                    this.currentItemLeft += itemWidth / 2; //还要再加上本身宽度的一半的误差

                                    this.currentLineLeft = itemLeft + itemWidth /
                                    2; //选中菜单的下划线位置 = 菜单所在的位置 + 菜单自身的宽度/2
                                    this.currentLineLeft -= this.currentLineWidth / 2; //还要再减去下划线本身宽度一半的误差

                                    this.navItemsIndex = index;
                                    this.navItems.forEach((nItem, nIndex) => {
                                        if (index === nIndex) {
                                            let videoCourse = {
                                                videoCourseId: this.sid,
                                                courseLastId: this.navItems[nIndex].id
                                            }
                                            storage.set('videoCourse' + this.sid, videoCourse)

                                        } 
                                    })
                                    if (callback) {
                                        this.$emit('itempause', true);
                                        this.$emit('currentItem', this.navItems[index]);
                                    }
                                }, 1000)
                            }
                        }
                    } else {
                        // 不限制交作业 即可领彩蛋
                        if (!this.detail.get_easter_eggs_status) {
                            specialUnlockSurprise({
                                special_id: this.sid,
                                source_id: 0,
                                category: 'easter_egg'
                            }).then(res => {
                                this.detail.get_easter_eggs_status = 1;
                                this.$showToast('彩蛋已解锁')
                                setTimeout(() => {

                                    const currentDocItem = this.docItems[index];
                                    const itemLeft = currentDocItem.left || 0; //计算当前选中的导航项加载时候初始的left
                                    const itemWidth = currentDocItem.width || 0; //当前选中菜单的宽度

                                    this.currentItemLeft = itemLeft - this.screenWidth /
                                    2; //选中菜单居中显示（减去屏幕的一半，剩余部分就是横向滚动条位置）
                                    this.currentItemLeft += itemWidth / 2; //还要再加上本身宽度的一半的误差

                                    this.currentLineLeft = itemLeft + itemWidth /
                                    2; //选中菜单的下划线位置 = 菜单所在的位置 + 菜单自身的宽度/2
                                    this.currentLineLeft -= this.currentLineWidth /
                                    2; //还要再减去下划线本身宽度一半的误差

                                    this.navItemsIndex = index;
                                    this.navItems.forEach((nItem, nIndex) => {
                                        if (index === nIndex) {
                                            let videoCourse = {
                                                videoCourseId: this.sid,
                                                courseLastId: this.navItems[nIndex].id
                                            }
                                            storage.set('videoCourse' + this.sid, videoCourse)

                                        }
                                    })
                                    if (callback) {
                                        this.$emit('itempause', true);
                                        this.$emit('currentItem', this.navItems[index]);
                                    }
                                }, 1000)
                                console.log('解锁彩蛋')
                            })
                        } else {
                            this.$showToast('彩蛋已解锁')
                            setTimeout(() => {
                                const currentDocItem = this.docItems[index];
                                const itemLeft = currentDocItem.left || 0; //计算当前选中的导航项加载时候初始的left
                                const itemWidth = currentDocItem.width || 0; //当前选中菜单的宽度

                                this.currentItemLeft = itemLeft - this.screenWidth /
                                2; //选中菜单居中显示（减去屏幕的一半，剩余部分就是横向滚动条位置）
                                this.currentItemLeft += itemWidth / 2; //还要再加上本身宽度的一半的误差

                                this.currentLineLeft = itemLeft + itemWidth /
                                2; //选中菜单的下划线位置 = 菜单所在的位置 + 菜单自身的宽度/2
                                this.currentLineLeft -= this.currentLineWidth / 2; //还要再减去下划线本身宽度一半的误差

                                this.navItemsIndex = index;
                                this.navItems.forEach((nItem, nIndex) => {
                                    if (index === nIndex) {
                                        let videoCourse = {
                                            videoCourseId: this.sid,
                                            courseLastId: this.navItems[nIndex].id
                                        }
                                        storage.set('videoCourse' + this.sid, videoCourse)
                                    } 
                                })
                                if (callback) {
                                    this.$emit('itempause', true);
                                    this.$emit('currentItem', this.navItems[index]);
                                }
                            }, 1000)
                        }
                    }

                } else {
                    const currentDocItem = this.docItems[index];
                    const itemLeft = currentDocItem.left || 0; //计算当前选中的导航项加载时候初始的left
                    const itemWidth = currentDocItem.width || 0; //当前选中菜单的宽度

                    this.currentItemLeft = itemLeft - this.screenWidth / 2; //选中菜单居中显示（减去屏幕的一半，剩余部分就是横向滚动条位置）
                    this.currentItemLeft += itemWidth / 2; //还要再加上本身宽度的一半的误差

                    this.currentLineLeft = itemLeft + itemWidth / 2; //选中菜单的下划线位置 = 菜单所在的位置 + 菜单自身的宽度/2
                    this.currentLineLeft -= this.currentLineWidth / 2; //还要再减去下划线本身宽度一半的误差

                    this.navItemsIndex = index;
                    this.navItems.forEach((nItem, nIndex) => {
                        if (index === nIndex) {
                            let videoCourse = {
                                videoCourseId: this.sid,
                                courseLastId: this.navItems[nIndex].id
                            }
                            storage.set('videoCourse' + this.sid, videoCourse)
                        } 
                    })
                    if (callback) {
                        this.$emit('itempause', true);
                        this.$emit('currentItem', this.navItems[index]);
                    }
                }
            }
        }
    }
</script>

<style lang="scss">
    .nav-scroll {
        display: inline-block;
        width: 100%;
        padding: 0 40rpx;
        white-space: nowrap;
        position: relative;
        margin: 40rpx auto 12rpx;
        
        .scroll-view {
            // width: 100%;
            // height: 96rpx;
            // overflow: auto;
        }

        &::after {
            display: none;
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 2rpx;
            background-color: #ddd;
            transform: scaleY(.5);
            content: '';
        }

        &-item {
            width: 246rpx;
            padding: 8rpx 28rpx;
            display: inline-block;
            font-size: 22rpx;
            height: 96rpx;
            line-height: 42rpx;
            border-radius: 16rpx;
            margin-right: 16rpx;
            white-space: normal;
            overflow: auto;
            background-color: #e9f1fb;


            .nav-scroll-items {
                width: 180rpx;
                height: 72rpx;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
                
                .nav-scroll-items-img {
                    display: inline-block;
                    width: 20rpx;
                    height: 20rpx;
                    margin-right: 12rpx;
                }
                .nav-scroll-items-txt {
                    margin: 0 14rpx;
                }

            }
        }

        &-line {
            position: absolute;
            height: 4rpx;
            border-radius: 2rpx;
            transition: left .3s ease-in-out;
            z-index: 10;
        }

        ::-webkit-scrollbar {
            display: none;
        }
    }

    .cancelPopup {
        position: relative;
        width: 622rpx;
        min-height: 366rpx;
        max-height: 732rpx;
        border: 4rpx solid #50506f;
        border-radius: 30rpx;
        background-color: #fff;
        box-sizing: border-box;
        padding: 92rpx 80rpx 0;
        overflow: auto;

        &.work {
            padding: 92rpx 46rpx 0;
        }
    }

    .cancel-icon {
        position: absolute;
        right: 12rpx;
        top: 12rpx;
        width: 56rpx;
        height: 56rpx;
    }

    .cancel-title {
        width: 100%;
        font-size: 28rpx;
        font-family: PingFang SC, PingFang SC-Bold;
        font-weight: 700;
        text-align: center;
        color: #50506f;
        line-height: 38rpx;
        min-height: 114rpx;
        max-height: 360rpx;
        word-break: break-all;
        overflow-x: scroll;

        &.work {
            line-height: 38rpx;
            min-height: 0;
            text-align: left;
        }
    }

    .cancel-button {
        width: 248rpx;
        height: 64rpx;
        line-height: 64rpx;
        text-align: center;
        background: #50506f;
        border-radius: 32rpx;
        font-size: 24rpx;
        font-family: PingFang SC, PingFang SC-Regular;
        font-weight: 400;
        text-align: center;
        color: #ffffff;
        margin: 38rpx auto;

        &.work {
            width: 100%;
            background: none;

            .left-work-button {
                float: left;
                width: 240rpx;
                height: 64rpx;
                line-height: 64rpx;
                text-align: center;
                background-color: #fc5656;
                font-size: 26rpx;
                color: #fff;
                border-radius: 12rpx;
            }

            .right-work-button {
                float: right;
                width: 240rpx;
                height: 64rpx;
                line-height: 64rpx;
                text-align: center;
                background-color: #fc5656;
                font-size: 26rpx;
                color: #fff;
                border-radius: 12rpx;
            }
        }
    }
</style>