<template>
		<view class="xtextarea">
			<textarea
				:value="value"
				:placeholder-style="placeholderStyle"
				:placeholder="placeholder"
				@input="input"
				:maxlength="maxlength"
				:style="{  height: height, fontSize: fontSize }"
			/>
			<view class="limit " :style="{ placeholderStyle, fontSize: fontSize }" v-if="limit">{{ count }}/{{ maxlength }}</view>
		</view>
</template>

<script>
export default {
	props: {
		value: {
			type: String, 
			default: ''
		},
		placeholder: {
			//顶部placeholder
			type: String,
			default: '请输入...'
		},
		maxlength: {
			//顶部字数限制
			type: Number,
			default: 300
		},
		limit: {
			//是否显示统计字数
			type: Boolean,
			default: true
		},
		isSaveMess: {
			//是否保留用户信息
			type: Boolean,
			default: false
		},
        isEditType: {
        	//是否编辑
        	type: Boolean,
        	default: false
        },
		saveMessStr: {
			//缓存字段
			type: String,
			default: 'textarea'
		},

		height: {
			type: String,
			default: '300rpx'
		},
		placeholderStyle: {
			type: String,
			default: 'color:#b7b7b7'
		},
		fontSize: {
			type: String,
			default: '24rpx'
		}
	},
	behaviors: ['uni://form-field'], //使用form时可以直接捕获到name属性
	data() {
		return {
			count: 0,
			textareaModal: this.value
		};
	},
	watch:{
		value(a,b){
			this.count = a.length
		}
	},
	methods: {
		input(e) {
			console.log(e)
			let val = e.detail.value;
			let len = val.length,
				maxlength = Number(this.maxlength);
			if (this.limit) {
				if (len === maxlength) {
					this.count = maxlength;
					uni.showToast({
						icon: 'none',
						title: '超出字数限制'
					});
				}
			}
			// this.textareaModal = val;
			this.count = len;
			if (this.isSaveMess) {
				uni.setStorageSync(this.saveMessStr, val);
			}
			this.$emit('input', val);
		},
		initStroge() {
			let getStroge = uni.getStorageSync(this.saveMessStr);
			if (getStroge !== '' && !this.isEditType) {
				// 仿e.detail.value
				this.input({detail:{ 
					value:getStroge
				}});
			}
		}
	},
	mounted() {
		this.count = this.value.length
	},
	onReady() {
		if (this.isSaveMess) {
			this.initStroge();
		}
	}
};
</script>

<style scoped lang="scss">
.xtextarea {
	height: 100%;
	textarea {
		width: 100%;
		height: 100%;
	}
	.limit{
		text-align: right;
		color: #B7B7B7;
	}
}
</style>
