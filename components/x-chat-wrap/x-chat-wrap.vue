<template>
	<view class="wrap">
		<view v-for="(item, index) in arr" :key="item.id" @click="showMessTime(item.add_time,index)">
			<view class="text_center timeTxt" v-if="clickIdx===index">
				<text class="font_size20">{{showTime}}</text>
			</view>
			<template v-if="all">
				<view v-show="item.idx < more">
					<view class="item acea-row row-top" v-if="item.uid === value">
						<view class="pictrue"><image :src="item.avatar" /></view>
						<view class="text">
							<view class="name">{{ item.nickname }} </view>
							<view class="acea-row">
								<view class="conter acea-row row-middle" v-if="item.msn_type === 4"><image src="@/static/images/signal2.gif" class="signal" style="margin-right:27rpx;" /></view>
								<view class="conter acea-row row-middle" v-if="item.msn_type === 3"><image :src="item.msn" mode="widthFix" style="width: 200rpx;" alt="聊天图片" /></view>
								<view class="conter acea-row row-middle" v-if="item.msn_type === 2">
									<i class="em" style="background-image: url('https://shop.arthorize.com/static/images/emoji.png');" :class="item.msn"></i>
								</view>
								<view class="conter acea-row row-middle" v-if="item.msn_type === 1">{{ item.msn }}</view>
							</view>
						</view>
					</view>
					<view class="item acea-row row-top row-right" v-else>
						<view class="text textR">
							<view class="name">{{ item.nickname }}</view>
							<view class="acea-row ">
								<view class="conter acea-row row-middle" v-if="item.msn_type === 4">
									<image src="@/static/images/signal2.gif" mode="widthFix" class="signal" style="margin-right:27rpx;" />
								</view>
								<view class="conter acea-row row-middle" v-if="item.msn_type === 3" style="padding:0">
									<image :src="item.msn" mode="widthFix" style="width: 200rpx;" alt="聊天图片" />
								</view>
								<view class="conter acea-row row-middle" v-if="item.msn_type === 2">
									<i class="em" style="background-image: url('https://shop.arthorize.com/static/images/emoji.png');" :class="item.msn"></i>
								</view>
								<view class="conter acea-row row-middle" v-if="item.msn_type === 1">{{ item.msn }}</view>
							</view>
						</view>
						<view class="pictrue"><image :src="item.avatar" mode="widthFix" /></view>
					</view>
				</view>
			</template>
			<template v-if="right">
				<view class="item acea-row row-top  row-right"  style="flex-wrap: nowrap;">
					<view class="pictrue sendErr" @click="againSendMsg(item,index)">发送失败</view>
					<view class="text textR">
						<view class="name">{{ userInfo.nickname }}</view>
						<view class="acea-row">
							<view class="conter acea-row row-middle" v-if="item.type === 4"><image src="@/static/images/signal2.gif" class="signal" style="margin-right:27rpx;" /></view>
							<view class="conter acea-row row-middle" v-if="item.type === 3"><image :src="item.msn" mode="widthFix" style="width: 200rpx;" alt="聊天图片" /></view>
							<view class="conter acea-row row-middle" v-if="item.type === 2">
								<i class="em" style="background-image: url('https://shop.arthorize.com/static/images/emoji.png');" :class="item.msn"></i>
							</view>
							<view class="conter acea-row row-middle" v-if="item.type === 1">{{ item.msn }}</view>
						</view>
					</view>
					<view class="pictrue"><image :src="userInfo.avatar" /></view>
				</view>
			</template>
		</view>
	</view>
</template>

<script>
	import { initTime } from '@/utils/common.js';
export default {
	props: {
		value: {
			type: Number,

			default: -1
		},
		arr: {
			type: Array,
			default: () => {
				return [];
			}
		},
		all: {
			type: Boolean,
			default: true
		},
		left: {
			type: Boolean,
			default: true
		},
		right: {
			type: Boolean,
			default: false
		},
		show: {
			type: Number,
			default: 10
		}
	},
	watch: {
		show(a, b) {
			this.more = a;
		}
	},
	data() {
		return {
			userInfo: this.$store.state.userInfo,
			clickIdx:-1,
			showTime:'',
			more: this.show
		};
	},
	methods: {
		againSendMsg(item, idx) {
			this.$emit('againSendMsg',item, idx);
		},
		showMessTime(time,idx){
			this.showTime = initTime(time*1000);
			this.clickIdx = idx;
		}
	},
	mounted() {
	}
};
</script>

<style scoped lang="scss">
.wrap {
	image {
		width: 100%;
		height: 100%;
	}
.timeTxt{
	color: #999;
}
	.item {
		margin-top: 37rpx;
		.pictrue {
			width: 80rpx;
			height: 80rpx;
			margin-top: 10rpx;
			image {
				border-radius: 50%;
			}
		}
		.sendErr {
			width: 150rpx;
			text-align: right;
			// line-height: 80rpx;
			font-size: 24rpx;
			padding: 30rpx 20rpx 0 0;
			color: red;
		}
		.text {
			margin-left: 20rpx;
			.name {
				font-size: 24rpx;
				transform: scale(0.8);
				transform-origin: left;
				color: #999;
				.return {
					color: #509efb;
					margin-left: 17rpx;
				}
			}
			.conter {
				background-color: #fff;
				border-radius: 8rpx;
				padding: 12rpx;
				font-size: 30rpx;
				color: #333;
				position: relative;
				max-width: 496rpx;
				margin-top: 2rpx;
				image {
					min-height: 200rpx;
					display: block;
				}
				.signal {
					width: 48rpx;
					height: 48rpx;
					&.signalR {
						transform: rotate(180deg);
						-ms-transform: rotate(180deg);
						-webkit-transform: rotate(180deg);
					}
				}
				&:before {
					position: absolute;
					content: '';
					width: 0;
					height: 0;
					border-bottom: 9rpx solid transparent;
					border-right: 14rpx solid #fff;
					border-top: 9rpx solid transparent;
					left: -14rpx;
					top: 25rpx;
				}
			}
			&.textR {
				text-align: right;
				margin: 0 20rpx 0 0;
				.name {
					transform-origin: right;
					.return {
						margin: 0 17rpx 0 0;
					}
				}
				.conter {
					&:before {
						left: unset;
						right: -14rpx;
						transform: rotateY(180deg);
					}
				}
			}
		}
	}
}
</style>
