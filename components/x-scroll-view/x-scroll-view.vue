<template>
	<view class="scroll">
		<scroll-view scroll-x style="white-space:nowrap;" :scroll-with-animation="true" :scroll-left="scrollLeft">
			<view class="wrap" :style="{ fontSize: fontSize }">
				<view
					class="item"
					v-for="(item, index) in arr"
					:key="index"
					@click="swichTab(index, item)"
					:class="index == current ? activeClass : ''"
					:style="{ padding: padding }"
					:id="`t${index}`"
				>
					<view class="title" :style="{ height: height, lineHeight: height, color: index == current ? activeColor : color, fontWeight: index == current ? bold : 'normal' }">
						<!-- 头条小程序不支持slot-scope 解构 -->
							<slot :item="item">{{ item[title] }}</slot>
					</view>
					<view :class="lineClass" :style="{ width: lineWidth, backgroundColor: index == current ? lineColor : '' }"></view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>
<script>
export default {
	props: {
		arr: {
			type: Array,
			default() {
				return [];
			}
		},
		active: {
			type: Number,
			default: 0
		},
		title: {
			//显示title的字段
			type: String,
			default: 'title'
		},
		color: {
			type: String,
			default: '#000'
		},
		activeColor: {
			type: String,
			default: 'red'
		},
		fontSize: {
			type: String,
			default: '30rpx'
		},
		activeClass: {
			type: String,
			default: 'on'
		},
		lineClass: {
			type: String,
			default: 'line'
		},
		lineWidth: {
			type: String,
			default: '30rpx'
		},
		lineColor: {
			type: String,
			default: 'red'
		},
		padding: {
			type: String,
			default: '0 20rpx'
		},
		bold: {
			type: String,
			default: 'normal'
		},
		height: {
			type: String,
			default: '70rpx'
		}
	},
	watch:{
		active(a,b){
			this.current = a;
		}
	},
	data() {
		return {
			current: 0,
			scrollLeft: 0,
			leftArr: [],
			init: false
		};
	},
	methods: {
		uniSelectorQueryInfo(selector, _this) {
			return new Promise((resolve, reject) => {
				const query = uni.createSelectorQuery().in(_this);
				query
					.select(selector)
					.boundingClientRect(res => {
						// 获取节点坐标
						resolve(res);
					})
					.exec();
			});
		},
		swichTab(idx, item) {
			if (this.current === idx) {
				return;
			}
			this.current = idx;
			this.scrollLeft = this.leftArr[idx] - this.leftArr[0];
			this.$emit('change', idx, item);
		},
		async initScrollInfo() {
			let leftArr = [];
			for (let i = 0; i < this.arr.length; i++) {
				const { left } = await this.uniSelectorQueryInfo(`#t${i}`, this);
				leftArr.push(left);
			}
			this.leftArr = leftArr;
			if (this.current > 0) {
				this.scrollLeft = this.leftArr[this.current] - this.leftArr[0];
			}
		}
	},
	beforeUpdate() {
		const _this = this;
		if (this.init) {
			return;
		}
		this.$nextTick(function() {
			_this.current = _this.active;
			_this.initScrollInfo();
			_this.init = true;
		});
	}
};
</script>
<style lang="scss" scoped>
	::-webkit-scrollbar {
	  width: 0;
	  height: 0;
	  color: transparent;
	}
.flex_around {
	justify-content: space-around;
}
.scroll {
	.wrap {
		display: flex;
		.item {
			.title {
			}
			.line {
				transition: all 0.15s linear;
				height: 4rpx;
				margin: 0 auto;
			}
			&.on {
				.line {
					background-color: red;
				}
			}
		}
	}
}
</style>
