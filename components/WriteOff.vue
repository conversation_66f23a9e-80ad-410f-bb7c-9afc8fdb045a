<template>
	<view v-show="iShidden === false">
		<view class="WriteOff">
			<view class="pictrue"><image :src="orderInfo.image" /></view>
			<view class="num acea-row row-center-wrapper">
				{{ orderInfo.order_id }}
				<view class="views" @click="toDetail(orderInfo)" v-if="source!=='community'">
					查看
					<span class="iconfont icon-jiantou views-jian"></span>
				</view>
			</view>
			<view class="tip">确定要核销此订单吗？</view>
			<view class="sure" @click="confirm">确定核销</view>
			<view class="sure cancel" @click="cancel">取消</view>
		</view>
		<view class="mask" @touchmove.prevent></view>
	</view>
</template>

<script>
export default {
	name: 'WriteOff',
	props: {
		source:{
			type:String,
			default:''
		},
		iShidden: {
			type: Boolean,
			default: true
		},
		orderInfo: {
			type: Object
		}
	},
	data: function() {
		return {};
	},
	mounted: function() {},
	methods: {
		toDetail: function(item) {
			this.$navigator('/pages/orderAdmin/AdminOrder?order_id='+ item.order_id + '&goname=looks')
		},
		cancel: function() {
			this.$emit('cancel', true);
		},
		confirm: function() {
			this.$emit('confirm', true);
		}
	}
};
</script>
<style scoped>
.views {
	font-size: 16rpx;
	background: #c68937;
	border-radius: 4px;
	color: #fff;
	padding: 5rpx 2rpx 5rpx 8rpx;
	margin-left: 10rpx;
}
.views-jian {
	font-size: 10rpx;
}
.WriteOff {
	width: 560rpx;
	height: 800rpx;
	background-color: #fff;
	border-radius: 20rpx;
	position: fixed;
	top: 50%;
	left: 50%;
	margin-top: -400rpx;
	margin-left: -280rpx;
	z-index: 666;
	padding-top: 55rpx;
}
.WriteOff .pictrue {
	width: 340rpx;
	height: 340rpx;
	margin: 0 auto;
}
.WriteOff .pictrue image {
	width: 100%;
	height: 100%;
	display: block;
	border-radius: 10rpx;
}
.WriteOff .num {
	font-size: 30rpx;
	color: #666;
	margin: 28rpx 0 30rpx 0;
}
.WriteOff .num .see {
	font-size: 16rpx;
	color: #fff;
	border-radius: 4rpx;
	background-color: #c68937;
	padding-left: 5rpx;
	margin-left: 12rpx;
}
.WriteOff .num .see .iconfont {
	font-size: 15rpx;
}
.WriteOff .tip {
	font-size: 36rpx;
	color: #282828;
	text-align: center;
	border-top: 1px dashed #ccc;
	padding-top: 40rpx;
	position: relative;
}
.WriteOff .tip:after {
	content: '';
	position: absolute;
	width: 25rpx;
	height: 25rpx;
	border-radius: 50%;
	background-color: #7f7f7f;
	right: -12rpx;
	top: -12rpx;
}
.WriteOff .tip:before {
	content: '';
	position: absolute;
	width: 25rpx;
	height: 25rpx;
	border-radius: 50%;
	background-color: #7f7f7f;
	left: -12rpx;
	top: -12rpx;
}
.WriteOff .sure {
	font-size: 32rpx;
	color: #fff;
	text-align: center;
	line-height: 82rpx;
	height: 82rpx;
	width: 459rpx;
	border-radius: 41rpx;
	margin: 40rpx auto 0 auto;
	background-image: linear-gradient(to right, #f67a38 0%, #f11b09 100%);
	background-image: -webkit-linear-gradient(to right, #f67a38 0%, #f11b09 100%);
	background-image: -moz-linear-gradient(to right, #f67a38 0%, #f11b09 100%);
}
.WriteOff .sure.cancel {
	background-image: none;
	color: #999;
	margin-top: 10rpx;
}
</style>