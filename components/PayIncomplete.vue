<template>
    <view>
        <view class="payment" :class="value === true ? 'on' : ''">
            <view class="title acea-row row-center-wrapper">
                未支付订单结算
                <span class="iconfont icon-guanbi" @click="close"></span>
            </view>
            <view class="paymoney">
                支付：<text>¥{{paymoney}}</text>
            </view>
            <view class="paybut">
                <view class="paybutton" @click="notNowPay">
                    继续支付
                </view>
                <view class="paybutton right" @click="cancelOrder">
                    取消订单
                </view>
            </view>
        </view>
        <view class="mask" v-show="value" @click="close"></view>
    </view>
</template>
<script>
    import {
        
    } from '@/api/yknowledge.js'
    
    
    export default {
        name: 'PayIncomplete',
        props: {
            value: {
                type: Boolean,
                default: false
            },
            other: {
                type: Boolean,
                default: false
            },
            paymoney: {
                type: [Number, String],
                default: 0
            },
        },
        created() {
        },
        data: function() {
            return {
                now_money: '0.00',
                // #ifndef MP-TOUTIAO
                paytype: 'weixin',
                // #endif
                // #ifdef MP-TOUTIAO
                paytype: 'bytedance',
                // #endif
            };
        },
        mounted: function() {
            
        },
        methods: {
            close: function() {
                this.$emit('input', false);
            },
            notNowPay() {
                this.$emit('notNowPay', this.paytype);
                this.close()
            },
            cancelOrder(){
                this.$emit('cancelOrder', this.paytype);
                this.close()
            }
            
        }
    };
</script>
<style scoped lang="scss">
    .payment {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        border-radius: 70rpx 70rpx 0 0;
        box-shadow: 0px 0px 30rpx 0px rgba(107, 127, 153, 0.30);
        background-color: #fff;
        padding-bottom: 60rpx;
        z-index: 6666;
        transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);

        transform: translate3d(0, 100%, 0);
    }

    .payment.on {
        transform: translate3d(0, 0, 0);
    }

    .payment .title {
        position: relative;
        text-align: center;
        height: 123rpx;
        font-size: 32rpx;
        color: #282828;
        font-weight: 700;
        color: #333333;
    }
    
    .paymoney {
        width: 100%;
        text-align: center;
        height: 360rpx;
        line-height: 300rpx;
        font-size: 32rpx;
        font-family: PingFang SC, PingFang SC-Bold;
        font-weight: 700;
        color: #333333;
        text {
            color: #FF5656
        }
    }
    .paybut {
        width: 100%;
        overflow: auto;
        padding: 0 80rpx;
        box-sizing: border-box;
        margin-bottom: 60rpx;
    }
    .paybutton {
        float: left;
        width: 248rpx;
        height: 76rpx;
        line-height: 76rpx;
        text-align: center;
        background: #ff5656;
        color: #ffffff;
        border-radius: 50rpx;
        
    }
    .paybutton.right {
        float: right;
       background: #00aaea;
    }
    
    .payment .title .iconfont {
        position: absolute;
        right: 30rpx;
        top: 50%;
        transform: translateY(-50%);
        font-size: 43rpx;
        color: #8a8a8a;
        font-weight: normal;
    }

    .payment .item {
        border-bottom: 1rpx solid #eee;
        height: 130rpx;
        margin-left: 30rpx;
        padding-right: 30rpx;
    }

    .payment .item .left {
        width: 610rpx;
    }

    .payment .item .left .text {
        width: 540rpx;
    }

    .payment .item .left .text .name {
        font-size: 32rpx;
        color: #282828;
    }

    .payment .item .left .text .info {
        font-size: 24rpx;
        color: #999;
    }

    .payment .item .left .text .info .money {
        color: #ff9900;
    }

    .payment .item .left .iconfont {
        font-size: 45rpx;
        color: #09bb07;
    }

    .payment .item .left .iconfont.icon-zhifubao {
        color: #00aaea;
    }

    .payment .item .left .iconfont.icon-yuezhifu {
        color: #ff9900;
    }

    .payment .item .left .iconfont.icon-yuezhifu1 {
        color: #eb6623;
    }

    .payment .item .iconfont {
        font-size: 30rpx;
        color: #999;
    }

    .payment {
        .item {
            .image {
                width: 44rpx;
                height: 44rpx;

                image {
                    width: 100%;
                }
            }
        }

        .other {
            .money {
                margin: 30rpx 0;
                font-size: 32rpx;
                font-family: PingFang SC, PingFang SC-Bold;
                font-weight: 700;
                text-align: left;
                color: #333333;
                text-align: center;

                text {
                    color: #FF5656
                }
            }

            .btn {
                width: 670rpx;
                text-align: center;
                margin: 0 auto;
                height: 100rpx;
                line-height: 100rpx;
                background: #ff5656;
                border-radius: 50rpx;
                font-size: 28rpx;
                font-family: PingFang SC, PingFang SC-Bold;
                font-weight: 700;
                text-align: center;
                color: #ffffff;
            }
        }
    }
</style>
