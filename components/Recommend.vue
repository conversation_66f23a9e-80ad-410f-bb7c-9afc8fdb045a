<template>
	<view class="recommend" >
		<view class="title acea-row row-center-wrapper">
			<span class="iconfont icon-zhuangshixian"></span>
			<span class="name">为你推荐</span>
			<span class="iconfont icon-zhuangshixian lefticon"></span>
		</view>
		<view class="recommendList acea-row row-between-wrapper">
			<view @click="goDetail(item)" v-for="(item, index) in hostProduct" :key="index" class="item">
				<view class="pictrue">
					<image :src="item.image" alt="img" class="image"></image>
					<span class="pictrue_log_big pictrue_log_class" v-if="item.activity && item.activity.type === '1'">秒杀</span>
					<span class="pictrue_log_big pictrue_log_class" v-if="item.activity && item.activity.type === '2'">砍价</span>
					<span class="pictrue_log_big pictrue_log_class" v-if="item.activity && item.activity.type === '3'">拼团</span>
				</view>
				<view class="name line1">{{ item.store_name }}</view>
				<view class="money font-color-red">
					￥
					<span class="num">{{ item.price }}</span>
				</view>
			</view>
		</view>
		<Loading :loaded="loadend" :loading="loading"></Loading>
	</view>
</template>
<script>
import { getHostProducts } from '@/api/store';
import Loading from '@/components/Loading';
import { goShopDetail } from '@/utils/order.js';
export default {
	name: 'Recommend',
	props: {},
	components: {
		Loading
	},
	data: function() {
		return {
			hostProduct: [],
			page: 1,
			limit: 20,
			loadTitle: '',
			loading: false,
			loadend: false
		};
	},
	methods: {
		// 商品详情跳转
		goDetail(item) {
			goShopDetail(item);
		},
		hostProducts: function() {
			let that = this;
			if (that.loading) return; //阻止下次请求（false可以进行请求）；
			if (that.loadend) return; //阻止结束当前请求（false可以进行请求）；
			that.loading = true;
			getHostProducts(that.page, that.limit).then(res => {
				that.loading = false;
				//apply();js将一个数组插入另一个数组;
				that.hostProduct.push.apply(that.hostProduct, res.data);
				that.loadend = res.data.length < that.limit; //判断所有数据是否加载完成；
				that.page = that.page + 1;
			});
		}
	},
	  mounted() {
	    this.hostProducts();
	  },
	onPullDownRefresh() {
		// this.hostProducts();
		// this.$stopPullRefresh(1000)
	},
	onReachBottom() {
		this.hostProducts();
		console.log('到达底部');
	}
};
</script>
<style scoped lang="scss">
.recommend {
	background-color: #fff;
}

.recommend .title {
	height: 135rpx;
	font-size: 28rpx;
	color: #282828;
}

.recommend .title .name {
	margin: 0 28rpx;
}

.recommend .title .iconfont {
	font-size: 170rpx;
	color: #454545;
	height: 50rpx;
	line-height: 50rpx;
}

.recommend .title .iconfont.lefticon {
	transform: rotate(180deg);
	-webkit-transform: rotate(180deg);
	-ms-transform: rotate(180deg);
	-moz-transform: rotate(180deg);
	-o-transform: rotate(180deg);
}

.recommend .recommendList {
	padding: 0 30rpx;
}

.recommend .recommendList .item {
	width: 335rpx;
	margin-bottom: 30rpx;
}

.recommend .recommendList .item .pictrue {
	width: 100%;
	height: 335rpx;
	position: relative;
}

.recommend .recommendList .item .pictrue .image {
	width: 100%;
	height: 100%;
	border-radius: 10rpx;
}

.recommend .recommendList .item .name {
	font-size: 28rpx;
	color: #282828;
	margin-top: 20rpx;
}

.recommend .recommendList .item .money {
	font-size: 20rpx;
	margin-top: 3rpx;
}

.recommend .recommendList .item .money .num {
	font-size: 28rpx;
}
</style>
