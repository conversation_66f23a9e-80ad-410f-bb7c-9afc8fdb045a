<template>
    <view>
        <view class="payment" :class="[value === true ? 'on' : '',openstudent?'padb0':'']">
            <swiper class="swiper" :class="swiperCurrent == 2 && openstudent?'h90':''" :current="swiperCurrent" @change="swiperChange">
                <swiper-item catchtouchmove="false">
                    <view class="title acea-row row-center-wrapper">
                        完善购买信息（1/{{openstudent?'4':'3'}}）
                        <span class="iconfont icon-guanbi" @click="close"></span>
                    </view>
                    <view class="settitle">
                        设置头像
                    </view>
                    <!-- #ifdef MP-WEIXIN -->
                    <button class="setavatar" open-type="chooseAvatar" @chooseavatar="onChooseAvatar" type="default">
                        <image class="setavatar-img"
                            :src="AuthorizedAvatar && avatar?avatar:'../static/images/noPictrue.png'" mode=""></image>
                    </button>
                    <!-- #endif -->
                    <!-- #ifndef MP-WEIXIN -->
                    <xImageUpload class="setavatar" :num="1" @chooseImage="chooseImage" @avatarSize="avatarSize"
                        :close="false" width="200rpx" height="200rpx" borderRadius="4rpx">
                        <view class="setavatar " style="margin:0;border-radius:4rpx">
                            <image class="setavatar-img"
                                :src="AuthorizedAvatar && avatar?avatar:'../static/images/noPictrue.png'" />
                        </view>
                    </xImageUpload>
                    <!-- #endif -->
                    <view class="other">
                        <view class="btn" @click="nextstep1">下一步</view>
                    </view>
                </swiper-item>
                <swiper-item >
                    <view class="title acea-row row-center-wrapper">
                        完善购买信息（2/{{openstudent?'4':'3'}}）
                        <span class="iconfont icon-guanbi" @click="close"></span>
                    </view>
                    <view class="settitle">
                        设置昵称
                    </view>
                    <view class="setnickname">
                        <input class="input" type="nickname" v-model="nickname" placeholder="请输入您的微信昵称"
                            @blur="getNickname" @confirm="getNickname" placeholder-style="font-size:26rpx;color:#999">
                    </view>
                    <view class="settitle">
                        联系手机（微信号）
                    </view>
                    <view class="setnickname">
                        <view class="setnickname-title">
                            为更好的提供课程相关服务我们会通过手机或微信与您取得联系
                        </view>
                        <input class="input" type="text" v-model="weChatPhone" placeholder="请输入手机号或微信号"
                            placeholder-style="font-size:26rpx;color:#999">
                    </view>
                    <view class="other" style="margin-top: 26rpx;">
                        <view class="btn" @click="nextstep2">下一步</view>
                    </view>
                </swiper-item>
                <swiper-item v-if="openstudent && showpay">
                    <view class="title acea-row row-center-wrapper">
                        完善购买信息（3/{{openstudent?'4':'3'}}）
                        <span class="iconfont icon-guanbi" @click="close"></span>
                    </view>
                    
                    <scroll-view class="student-box" scroll-y="true" >
                        <view class="concat-item">
                            <view class="concat-item-left">请问您的身份是</view>
                            <view class="channel-box"> 
                            <!-- career_list -->
                                <block v-for="(item, index) in identity" :key="index">
                                    <view class="sex-radios-box call" @click="careeradios(item)"
                                        :class="item.id == '4'?'qita':''">
                                        <image class="sex-radios-box-img" src="@/static/images/yuanshi/check1.png" mode=""
                                            v-if="studentProfile.identity === item.name"></image>
                                        <image class="sex-radios-box-img" src="@/static/images/yuanshi/uncheck1.png" mode="" v-else>
                                        </image>
                                        <view class="sex-radios-box-title" :class="[studentProfile.identity === item.name?'active':'',item.id == '4'?'float0':'']">
                                            {{item.name}}
                                        </view>
                                    </view>
                                </block>
                            </view>
                        </view>
                        <view class="concat-item" v-if="identityType == 1">
                            <view class="concat-item-left">您的孩子处于哪个学习阶段？</view>
                            <view class="channel-box"> 
                                <block v-for="(item, index) in learningStage" :key="index">
                                    <view class="sex-radios-box w50" @click="learningStageradios(item)">
                                        <image class="sex-radios-box-img" src="@/static/images/yuanshi/check1.png" mode=""
                                            v-if="studentProfile.learningStage === item"></image>
                                        <image class="sex-radios-box-img" src="@/static/images/yuanshi/uncheck1.png" mode="" v-else>
                                        </image>
                                        <view class="sex-radios-box-title" :class="[studentProfile.learningStage === item?'active':'']">
                                            {{item}}
                                        </view>
                                    </view>
                                </block>
                            </view>
                        </view>
                        <view class="concat-item" v-if="identityType == 1">
                            <view class="concat-item-left">孩子学习的乐器是？（多选）</view>
                            <view class="channel-box border-b2" style="padding-bottom: 34rpx;">
                                <block v-for="(item, index) in learninstruments" :key="index">
                                    <view class="sex-radios-box w50" @click="checkboxChange3(index, item)"
                                        :class="item.name == '其他（填空）'?'qita':''">
                            
                                        <image class="sex-radios-box-img" src="@/static/images/yuanshi/check1.png" mode=""
                                            v-if="item.isCheck"></image>
                                        <image class="sex-radios-box-img" src="@/static/images/yuanshi/uncheck1.png" mode="" v-else>
                                        </image>
                                        <view class="sex-radios-box-title" :class="item.isCheck?'active':''">
                                            {{item.name}}
                                        </view>
                                        <input @click.stop class="sex-radios-box call input"
                                            placeholder-style="font-size: 12px;color:#d2d2d2" v-if="item.name == '其他（填空）'"
                                            v-model="item.content" type="text">
                                    </view>
                                </block>
                            </view>
                        </view>
                        <view class="concat-item" v-if="identityType == 1">
                            <view class="concat-item-left">孩子学习音乐的情况以及购买课程想要解决什么问题？（填写）</view>
                            <textarea class="introduce-textarea-box" placeholder="比如学习年限,有没有考级？参与比赛情况？音乐学习中的困难？"
                                placeholder-style="font-size: 16px;color:#d2d2d2" maxlength="200"
                                v-model="studentProfile.childSituation"></textarea>
                        </view>
                        <view class="concat-item" v-if="identityType == 2">
                            <view class="concat-item-left">您购买课程想要解决什么问题？（多选）</view>
                            <view class="channel-box border-b2" style="padding-bottom: 34rpx;">
                                <block v-for="(item, index) in problemSolving" :key="index">
                                    <view class="sex-radios-box w50" @click="checkboxChange4(index, item)"
                                        :class="item.name == '其他（填空）'?'qita':''">
                            
                                        <image class="sex-radios-box-img" src="@/static/images/yuanshi/check1.png" mode=""
                                            v-if="item.isCheck"></image>
                                        <image class="sex-radios-box-img" src="@/static/images/yuanshi/uncheck1.png" mode="" v-else>
                                        </image>
                                        <view class="sex-radios-box-title" :class="item.isCheck?'active':''">
                                            {{item.name}}
                                        </view>
                                        <input @click.stop class="sex-radios-box call input"
                                            placeholder-style="font-size: 12px;color:#d2d2d2" v-if="item.name == '其他（填空）'"
                                            v-model="item.content" type="text">
                                    </view>
                                </block>
                            </view>
                        </view>
                        <view class="concat-item" v-if="identityType == 3">
                            <view class="concat-item-left">您主要教授的内容是什么？</view>
                            <view class="channel-box"> 
                                <block v-for="(item, index) in teachingContent" :key="index">
                                    <view class="sex-radios-box w50" @click="teachingContentradios(item)">
                                        <image class="sex-radios-box-img" src="@/static/images/yuanshi/check1.png" mode=""
                                            v-if="studentProfile.teachingContent === item"></image>
                                        <image class="sex-radios-box-img" src="@/static/images/yuanshi/uncheck1.png" mode="" v-else>
                                        </image>
                                        <view class="sex-radios-box-title" :class="[studentProfile.teachingContent === item?'active':'']">
                                            {{item}}
                                        </view>
                                    </view>
                                </block>
                            </view>
                        </view>
                        <view class="concat-item" v-if="identityType == 3 || identityType == 4">
                            <view class="concat-item-left">您购买课程想要解决什么问题</view>
                            <textarea class="introduce-textarea-box" placeholder="请填写"
                                placeholder-style="font-size: 16px;color:#d2d2d2" maxlength="200"
                                v-model="studentProfile.problemSolving"></textarea>
                        </view>
                        <view class="concat-item">
                            <view class="concat-item-left">请问您在哪里生活工作</view>
                            <view class="channel-box border-b2">
                                <block v-for="(item, index) in countrytypeList" :key="index">
                                    <view class="sex-radios-box qita" @click="countryradios(item.id)">
                                        <image class="sex-radios-box-img" src="@/static/images/yuanshi/check1.png" mode=""
                                            v-if="countrytype === item.id"></image>
                                        <image class="sex-radios-box-img" src="@/static/images/yuanshi/uncheck1.png" mode="" v-else>
                                        </image>
                                        <view class="sex-radios-box-title" :class="countrytype == item.id?'active':''">
                                            {{item.name}}
                                        </view>
                                        
                                        
                                        <picker v-if="item.id == 1" mode="multiSelector" @change="bindRegionChange" @columnchange="bindMultiPickerColumnChange" :value="valueRegion" :range="multiArray">
                                            <view class="selex">
                                                <view class="selex-box">
                                                    <view class="selex-picker-box">
                                                        <view class="selex-picker-item">
                                                            <view class="title">{{ region[0] }}{{ region[1] }}{{ region[2] }}</view>
                                                            <image class="img" src="../static/images/arrow-bottom.png" mode=""></image>
                                                        </view>
                                                    </view>
                                                </view>
                                            </view>
                                        </picker>
                                        
                                        <input style="width: 450rpx;" class="sex-radios-box call input"
                                            placeholder-style="font-size: 12px;color:#d2d2d2" v-if="item.id == 2"
                                            v-model="studentProfile.countrycontent" type="text">
                                    </view>
                                </block>
                            </view>
                        </view>
                        
                        
                        
                        
                        <view class="concat-item">
                            <view class="concat-item-left">性别</view>
                            <view class="channel-box border-b2">
                                <block v-for="(item, index) in sexRadioList" :key="index">
                                    <view class="sex-radios-box" @click="sexradios(item.id)">
                                        <image class="sex-radios-box-img" src="@/static/images/yuanshi/check1.png" mode=""
                                            v-if="studentProfile.sex === item.id"></image>
                                        <image class="sex-radios-box-img" src="@/static/images/yuanshi/uncheck1.png" mode="" v-else>
                                        </image>
                                        <view class="sex-radios-box-title" :class="studentProfile.sex == item.id?'active':''">
                                            {{item.name}}
                                        </view>
                                    </view>
                                </block>
                            </view>
                        </view>
                        <view class="concat-item">
                            <view class="concat-item-left">请问您的年龄段</view>
                            <view class="channel-box"> 
                                <block v-for="(item, index) in yourAge" :key="index">
                                    <view class="sex-radios-box call" @click="yourAgeradios(item)">
                                    
                                        <image class="sex-radios-box-img" src="@/static/images/yuanshi/check1.png" mode=""
                                            v-if="studentProfile.yourAge === item"></image>
                                        <image class="sex-radios-box-img" src="@/static/images/yuanshi/uncheck1.png" mode="" v-else>
                                        </image>
                                        <view class="sex-radios-box-title" :class="studentProfile.yourAge === item?'active':''">
                                            {{item}}
                                        </view>
                                    </view>
                                </block>
                            </view>
                        </view>
                        
                        <view class="concat-item">
                            <view class="concat-item-left">您是从哪里了解到我们的课程的（多选）</view>
                            <view class="channel-box border-b2" style="padding-bottom: 34rpx;">
                                <block v-for="(item, index) in channels" :key="index">
                                    <view class="sex-radios-box call" @click="checkboxChange2(index, item)"
                                        :class="item.name == '其他（填空）'?'qita':''">
                            
                                        <image class="sex-radios-box-img" src="@/static/images/yuanshi/check1.png" mode=""
                                            v-if="item.isCheck"></image>
                                        <image class="sex-radios-box-img" src="@/static/images/yuanshi/uncheck1.png" mode="" v-else>
                                        </image>
                                        <view class="sex-radios-box-title" :class="item.isCheck?'active':''">
                                            {{item.name}}
                                        </view>
                                        <input @click.stop class="sex-radios-box call input"
                                            placeholder-style="font-size: 12px;color:#d2d2d2" v-if="item.name == '其他（填空）'"
                                            v-model="item.content" type="text">
                                    </view>
                                </block>
                            </view>
                        </view>
                    </scroll-view>
                    
                    <view class="other" style="margin-top: 18rpx;">
                        <view class="btn fixed" @click="nextstep3">下一步</view>
                    </view>
                </swiper-item>
                <swiper-item v-if="showpay && openshowpay">
                    <view class="title acea-row row-center-wrapper">
                        完善购买信息（{{openstudent?'4':'3'}}/{{openstudent?'4':'3'}}）
                        <span class="iconfont icon-guanbi" @click="close"></span>
                    </view>
                    <view class="settitle">
                        支付方式
                    </view>

                    <view style="width: 100%;min-height: 260rpx;">
                        <view class="item acea-row row-between-wrapper" v-if="types.indexOf('bytedance') !== -1"
                            @click="checked('bytedance')">
                            <view class="left acea-row row-between-wrapper">
                                <image style="width: 46rpx; height: 46rpx; margin-right: 10rpx;"
                                    src="@/static/images/yuanshi/douyin.png" mode=""></image>
                                <view class="text">
                                    <view class="name">抖音支付</view>
                                    <view class="info">使用抖音快捷支付</view>
                                </view>
                            </view>
                            <view class="image" v-if="paytype==='bytedance'">
                                <image src="@/static/images/yuanshi/check.png" mode="widthFix"></image>
                            </view>
                        </view>
                        <view class="item acea-row row-between-wrapper" v-if="types.indexOf('weixin') !== -1"
                            @click="checked('weixin')">
                            <view class="left acea-row row-between-wrapper">
                                <view class="iconfont icon-weixin2"></view>
                                <view class="text">
                                    <view class="name">微信支付</view>
                                    <view class="info">使用微信快捷支付</view>
                                </view>
                            </view>
                            <view class="image" v-if="paytype==='weixin'">
                                <image src="@/static/images/yuanshi/check.png" mode="widthFix"></image>
                            </view>
                        </view>
                        <view class="item acea-row row-between-wrapper" v-if="types.indexOf('alipay') !== -1"
                            @click="checked('alipay')">
                            <view class="left acea-row row-between-wrapper">
                                <view class="iconfont icon-zhifubao"></view>
                                <view class="text">
                                    <view class="name">支付宝支付</view>
                                    <view class="info">使用线上支付宝支付</view>
                                </view>
                            </view>
                            <view class="image" v-if="paytype==='alipay'">
                                <image src="@/static/images/yuanshi/check.png" mode="widthFix"></image>
                            </view>
                        </view>
                        <view class="item acea-row row-between-wrapper" v-if="types.indexOf('yue') !== -1"
                            @click="checked('yue')">
                            <view class="left acea-row row-between-wrapper">
                                <view class="iconfont icon-yuezhifu"></view>
                                <view class="text">
                                    <view class="name">余额支付</view>
                                    <view class="info">
                                        当前可用余额：
                                        <span class="money">{{ now_money || 0}}</span>
                                    </view>
                                </view>
                            </view>
                            <view class="image" v-if="paytype==='yue'">
                                <image src="@/static/images/yuanshi/check.png" mode="widthFix"></image>
                            </view>
                        </view>
                        <view class="item acea-row row-between-wrapper" v-if="types.indexOf('offline') !== -1"
                            @click="checked('offline')">
                            <view class="left acea-row row-between-wrapper">
                                <view class="iconfont icon-yuezhifu1"></view>
                                <view class="text">
                                    <view class="name">线下支付</view>
                                    <view class="info">选择线下付款方式</view>
                                </view>
                            </view>
                            <view class="image" v-if="paytype==='offline'">
                                <image src="@/static/images/yuanshi/check.png" mode="widthFix"></image>
                            </view>
                        </view>
                        <view class="opencoupon" v-if="opencoupon">
                            <view class="opencoupon-item" v-if="mvarea.length <= 0">
                                <view class="opencoupon-left">
                                    优惠券
                                </view>
                                <view class="opencoupon-right">无</view>
                            </view>
                            <picker v-else @change="mvareaChange($event)" :value="mvareaindex" range-key="coupon_title" :range="mvarea">
                                <view class="opencoupon-item">
                                    <view class="opencoupon-left">
                                        优惠券
                                    </view>
                                    <view class="opencoupon-right">{{ mvareaname ?mvareaname:'不使用优惠券'}}</view>
                                </view>
                            </picker>
                        </view>
                    </view>
                    <template v-if="other">
                        <view class="other">
                            <view class="money" v-if="opencoupon">
                                <view class="money-left" v-if="deductionsPrice">
                                    优惠金额：<text class="money-left-txt">-￥{{deductionsPrice}}</text>
                                </view>
                                <view class="money-center">
                                    支付：<text>￥{{paymoney}}</text>
                                </view>
                            </view>
                            <view class="money" v-else>
                                支付：<text>￥{{paymoney}}</text>
                            </view>
                            <view class="btn" :class="paymoney == 0?'gray':''" @click="nowPay">立即支付</view>
                        </view>
                    </template>
                </swiper-item>
            </swiper>

        </view>
        <view class="mask" v-show="value" @click="close"></view>
    </view>
</template>
<script>
    import { getCity } from '@/api/public';
    import {
        couponsUserAvailable,
        
    } from '@/api/activity';
    import xImageUpload from './x-image-upload/x-image-upload.vue';
    import {
        getuserInfo,
        toLogin,
        debounce,
        checkLogin,
        autoAuth,
        authNavigator,
        zxauthNavigator
    } from '@/utils/common.js';
    import {
        uploadImg
    } from '@/utils/upload.js';
    import {
        yIndex
    } from '@/api/yuanshi/public';
    import {
        getUser,
        postUserEdit
    }
    from '@/api/user.js';

    export default {
        name: 'Payment',
        components: {
            xImageUpload
        },
        props: {
            value: {
                type: Boolean,
                default: false
            },
            balance: {
                type: [Number, String],
                default: 0
            },
            other: {
                type: Boolean,
                default: false
            },
            paymoney: {
                type: [Number, String],
                default: 0
            },
            courseId: {
                type: [Number, String],
                default: 0
            },
            deductionsPrice: {
                type: [Number, String],
                default: 0
            },
            types: {
                type: Array,
                default: () => ['bytedance', 'weixin', 'alipay', 'yue', 'offline']
            },
            opencoupon: {
                type: Boolean,
                default: false // 是否启用优惠券
            },
            openstudent: {
                type: Boolean, // 是否打开学员档案填写
                default: false
            },
        },
        created() {
            this.platform = uni.getSystemInfoSync().platform;
            console.log('this.platform', this.platform);
            if(this.openstudent){
                this.getchannels()
                this.getlearninstruments()
                this.getproblemSolving()
                this.getCityList()
            }
        },
        data: function() {
            return {
                district: [],
                address: {},
                region: ['省', '市', '区'],
                multiArray: [],
                valueRegion: [0, 0, 0],
                multiIndex: [0, 0, 0],
                identityType:0,
                reasonIndex: 0,
                identity:[
                    {
                        name:'琴童家长',
                        id:1,
                    },
                    {
                        name:'音乐爱好者',
                        id:2,
                    },
                    {
                        name:'音乐教师',
                        id:3,
                    },
                    {
                        name:'教师之外的专业从业者（包括不限于音乐专业生，专业乐团成员等）',
                        id:4,
                    }
                ], // 身份
                teachingContent:[
                    '钢琴',
                    '吉他',
                    '演唱',
                    '其他'
                ], // 教授内容
                problemSolving:[
                    '学习演奏音乐',
                    '学会欣赏音乐',
                    '其他（填空）'
                ], // 解决问题
                learningStage:[
                    '幼儿园及以下',
                    '小学',
                    '初中',
                    '高中及以上'
                ], // 当前学习阶段
                learninstruments:[
                    '乐器-钢琴',
                    '乐器-小提琴',
                    '其他（填空）'
                ], // 孩子学习的乐器
                yourAge:[
                    '18以下',
                    '18~24',
                    '25~30',
                    '31~45',
                    '46~55',
                    '55以上',
                ], // 您的年龄段
                channels:[
                    '抖音',
                    '视频号',
                    '小红书',
                    '今日头条',
                    '微信小程序',
                    '其他（填空）'
                ],
                sexRadioList: [
                    {
                        name: '男',
                        id: 1
                    },
                    {
                        name: '女',
                        id: 2
                    }
                ],
                countrytypeList: [
                    {
                        name: '国内',
                        id: 1
                    },
                    {
                        name: '国外-填写国家',
                        id: 2
                    }
                ],
                countrytype:0,
                
                studentProfile: {
                    identity:'', // 您的身份
                    problemSolving:'', // 您想解决的问题
                    sex:0, // 您的性别
                    yourAge:'', // 您的年龄段
                    learningStage:'' , // 孩子所处学习阶段
                    countrycontent:'', // 生活工作地
                    childSituation:'', // 孩子学习音乐情况与想解决的问题
                    teachingContent:'',
                    channelsArr: [],
                    learninstrumentsArr:[],
                    problemSolvingArr:[], // 
                },
                now_money: '0.00',
                // #ifndef MP-TOUTIAO
                paytype: 'weixin',
                // #endif
                // #ifdef MP-TOUTIAO
                paytype: 'bytedance',
                platform: '',
                site_wechat: '',
                // #endif
                swiperCurrent: 0,
                AuthorizedAvatar: false, // 是否授权头像
                AuthorizedNickname: false, // 是否授权昵称
                avatar: '',
                avatar1: '',
                nickname: '',
                weChatPhone: '',
                userInfo: {},
                showpay: false,
                openshowpay:false,
                isEdit: false,
                avatarSizes: 0,
                mvareaname: '',
                mvareaindex: 0,
                coupon_money: 0,
                mvarea: [],
            };
        },
        mounted: function() {
            getuserInfo().then(res => {
                this.now_money = res.data.now_money || this.balance;
            });
            yIndex().then(res => {
                this.site_wechat = res.data.site_wechat;
                // console.log('联系客服---：', this.site_wechat)
            });
            couponsUserAvailable({
                    type: 3,       // 类型：1=商品 、3=课程
                    id: this.courseId    // 商品id｜课程专题记录id｜
                })
                .then(res => {
                    this.mvarea = res.data;
                    let obj = {
                        id: 0,
                        coupon_price: 0,
                        coupon_title: '不使用优惠券'
                    }
                    if(this.mvarea.length >= 1){
                        this.mvarea.unshift(obj)
                    }
                    // console.log('this.mvarea', this.mvarea)
                })
        },
        methods: {
            getCityList: function() {
            	let that = this;
            	getCity().then(res => {
            			that.district = res.data;
            			that.initCityArr();
            		})
            		.catch(err => {
            			that.$showToast(err.msg || err);
            		});
            },
            initCityArr() {
            	let that = this,
            		province = [],
            		city = [],
            		area = [];
            	if (that.district.length) {
            		let cityChildren = that.district[0].c || [];
            		let areaChildren = cityChildren.length ? cityChildren[0].c || [] : [];
            		that.district.forEach(function(item) {
            			province.push(item.n);
            		});
            		cityChildren.forEach(function(item) {
            			city.push(item.n);
            		});
            		areaChildren.forEach(function(item) {
            			area.push(item.n);
            		});
            		that.multiArray = [province, city, area];
            	}
            },
            bindMultiPickerColumnChange(e) {
            	let that = this,
            		column = e.detail.column,
            		value = e.detail.value,
            		currentCity = this.district[value] || { c: [] },
            		multiArray = that.multiArray,
            		multiIndex = that.multiIndex;
            	multiIndex[column] = value;
            	switch (column) {
            		case 0:
            			let areaList = currentCity.c[0] || { c: [] };
            			multiArray[1] = currentCity.c.map(item => {
            				return item.n;
            			});
            			multiArray[2] = areaList.c.map(item => {
            				return item.n;
            			});
            			this.multiIndex.splice(1, 1, 0)
            			this.multiIndex.splice(2, 1, 0)
            			break;
            		case 1:
            			let cityList = that.district[multiIndex[0]].c[multiIndex[1]].c || [];
            			multiArray[2] = cityList.map(item => {
            				return item.n;
            			});
            			this.multiIndex.splice(2, 1, 0)
            			break;
            		case 2:
            			break;
            	}
            	this.multiArray = multiArray;
            	this.multiIndex = multiIndex;
            },
            bindRegionChange(e) {
            	let multiIndex = this.multiIndex,
            		province = this.district[multiIndex[0]] || { c: [] },
            		city = province.c[multiIndex[1]] || { v: 0 },
            		multiArray = this.multiArray,
            		value = e.detail.value;
            	this.region = [multiArray[0][value[0]], multiArray[1][value[1]], multiArray[2][value[2]]];
            	this.cityId = city.v;
            	this.valueRegion = [0, 0, 0];
            
            	this.address = {
            		province: this.region[0],
            		city: this.region[1],
            		district: this.region[2],
            		city_id: this.cityId
            	};
            },
            getchannels(){
                let obj = {};
                for (let key in this.channels) {
                    obj[key] = this.channels[key];
                };
                this.channels = Object.keys(obj).map(val => ({
                    name: obj[val],
                    isCheck: false,
                    content: ''
                }));
            },
            getlearninstruments(){
                let obj = {};
                for (let key in this.learninstruments) {
                    obj[key] = this.learninstruments[key];
                };
                this.learninstruments = Object.keys(obj).map(val => ({
                    name: obj[val],
                    isCheck: false,
                    content: ''
                }));
            },
            getproblemSolving(){
                let obj = {};
                for (let key in this.problemSolving) {
                    obj[key] = this.problemSolving[key];
                };
                this.problemSolving = Object.keys(obj).map(val => ({
                    name: obj[val],
                    isCheck: false,
                    content: ''
                }));
            },
            
            checkboxChange2(e, info) {
                let arr = [...this.channels];
                let selarr = [...this.studentProfile.channelsArr];
                if (arr[e].isCheck == false) {
                    arr[e].isCheck = true;
                    selarr.push(info)
                } else {
                    arr[e].isCheck = false;
                    var index11 = selarr.indexOf(info)
                    selarr.splice(index11, 1)
            
                }
                this.channels = arr;
                this.studentProfile.channelsArr = selarr;
            },
            checkboxChange3(e, info) {
                let arr = [...this.learninstruments];
                let selarr = [...this.studentProfile.learninstrumentsArr];
                if (arr[e].isCheck == false) {
                    arr[e].isCheck = true;
                    selarr.push(info)
                } else {
                    arr[e].isCheck = false;
                    var index11 = selarr.indexOf(info)
                    selarr.splice(index11, 1)
            
                }
                this.learninstruments = arr;
                this.studentProfile.learninstrumentsArr = selarr;
            },
            checkboxChange4(e, info) {
                let arr = [...this.problemSolving];
                let selarr = [...this.studentProfile.problemSolvingArr];
                if (arr[e].isCheck == false) {
                    arr[e].isCheck = true;
                    selarr.push(info)
                } else {
                    arr[e].isCheck = false;
                    var index11 = selarr.indexOf(info)
                    selarr.splice(index11, 1)
            
                }
                this.problemSolving = arr;
                this.studentProfile.problemSolvingArr = selarr;
            },
            sexradios(index) {
                this.studentProfile.sex = index;
            },
            countryradios(id){
                this.countrytype = id;
            },
            careeradios(item) {
                this.studentProfile.identity = item.name;
                this.identityType = item.id
            },
            yourAgeradios(item) {
                this.studentProfile.yourAge = item;
            },
            learningStageradios(item){
                this.studentProfile.learningStage = item;
            },
            teachingContentradios(item){
                console.log('item',item)
                this.studentProfile.teachingContent = item;
            },
            mvareaChange(e) {
                let that = this
                let item = {};
                that.mvareaindex = e.target.value;
                item = that.mvarea[e.target.value]
                // console.log('item---',item)
                that.mvareaname = item.coupon_title;
                // that.coupon_money = item.coupon_price;
                this.$emit('couponMoney', item);
            },
            avatarSize(e) {
                this.avatarSizes = e;
                console.log('头像上传大小-字节', this.avatarSizes)
            },
            chooseImage(val) {
                this.isEdit = true;
                this.avatar = val;
                console.log('this.avatar', this.avatar)
            },
            getNickname(e) {
                if (e.detail.value.trim() == '微信用户' || e.detail.value.trim() == '') {
                    return this.$showToast('输入昵称不规范')
                } else {
                    this.nickname = e.detail.value
                    this.AuthorizedNickname = true;
                }
            },
            getUser: function() {
                let that = this;
                getUser().then(res => {
                    this.userInfo = res.data;
                    if (this.userInfo.avatar == '' || !this.userInfo.avatar || this.userInfo.avatar ==
                        'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132' ||
                        this.userInfo.avatar ==
                        'https://thirdwx.qlogo.cn/mmopen/vi_32/Z22RuflpLpHqnZy0icITdAuMrwXdaMyibm58t2dibBOH7a5sWW8lVew8OWfia3oJwKT56dHYSgkIWiaicnZ5kRmenibCw/132'
                    ) {
                        this.AuthorizedAvatar = false

                    } else {
                        this.AuthorizedAvatar = true
                        this.avatar = this.userInfo.avatar;
                    }
                    if (this.userInfo.nickname.trim() == '' || !this.userInfo.nickname || this.userInfo
                        .nickname == '微信用户') {
                        this.AuthorizedNickname = false
                    } else {
                        this.AuthorizedNickname = true
                        this.nickname = this.userInfo.nickname;
                    }
                });
            },
            nextstep1() {
                let that = this;
                if (!that.AuthorizedAvatar || !this.avatar) {
                    return that.$showToast('请完善头像信息')
                } else {
                    that.swiperCurrent = 1;
                }
            },
            async nextstep2() {
                let that = this;
                let avatarInfo = '';
                if (!that.AuthorizedNickname || this.nickname == '') {
                    return that.$showToast('请完善昵称信息')
                }
                if (this.nickname == '微信用户' || this.nickname.trim() == '') {
                    return that.$showToast('昵称不规范')
                }
                if (!that.weChatPhone || that.weChatPhone.trim() == '') {
                    return that.$showToast('请完善您的联系方式（微信或手机号）')
                }
                that.showpay = true;
                if(!that.openstudent){
                    that.openshowpay = true
                }
                // #ifdef MP-WEIXIN
                avatarInfo = this.avatar;
                // #endif
                // #ifndef MP-WEIXIN
                if (this.isEdit) {
                    let res = await uploadImg(this.avatar);
                    console.log('字节上传头像', res[0])
                    avatarInfo = res[0];
                    this.avatar1 = res[0];
                    this.isEdit = false;
                    this.avatar = avatarInfo;
                } else {
                    if (this.avatar1) {
                        avatarInfo = this.avatar1;
                    } else {
                        avatarInfo = this.avatar;
                    }
                }
                // #endif
                postUserEdit({
                    // nickname: '微信用户',
                    // avatar: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132',
                    nickname: this.nickname,
                    avatar: avatarInfo
                }).then(
                    res => {
                        // console.log('res---', that.weChatPhone)
                        // return this.$showToast('上传成功')
                        
                        that.swiperCurrent = 2;
                        
                        couponsUserAvailable({
                                type: 3,       // 类型：1=商品 、3=课程
                                id: this.courseId    // 商品id｜课程专题记录id｜
                            })
                            .then(res => {
                                this.mvarea = res.data;
                                let obj = {
                                    id: 0,
                                    coupon_price: 0,
                                    coupon_title: '不使用优惠券'
                                }
                                if(this.mvarea.length >= 1){
                                    this.mvarea.unshift(obj)
                                    var largest = 0;
                                    var largestItem = {};
                                    
                                    // &&  this.mvarea[i].coupon_price > this.paymoney
                                    for(let i =0; i < this.mvarea.length;i++){
                                        if(this.mvarea[i].coupon_price > largest ){
                                            console.log('this.mvarea---11', this.mvarea[i].coupon_price,this.paymoney)
                                            if(Number(this.mvarea[i].coupon_price) < Number(this.paymoney)){
                                                console.log('this.mvarea---22', this.mvarea[i].coupon_price)
                                                that.mvareaindex = i;
                                                largestItem = this.mvarea[i];
                                                that.mvareaname = largestItem.coupon_title;
                                                this.$emit('couponMoney', largestItem);
                                            }
                                        }
                                    }
                                }
                                console.log('this.mvarea---', this.mvarea)
                            })
                    },
                    error => {
                        console.log('error---', error)
                    }
                );

            },
            nextstep3(){
                this.swiperCurrent = 3;
                this.openshowpay = true
                console.log('this.swiperCurrent',this.swiperCurrent)
            },
            async onChooseAvatar(e) {
                if (checkLogin()) {
                    this.AuthorizedAvatar = true;
                    this.avatar = e.detail.avatarUrl;
                    // console.log('头像', this.avatar)
                    let res = await uploadImg(this.avatar);
                    this.avatar = res[0];
                } else {
                    autoAuth();
                }
            },
            swiperChange(e) {
                let that = this;
                this.swiperCurrent = e.detail.current
                if (this.swiperCurrent == 1) {
                    setTimeout(function() {
                        that.showpay = false;
                        that.openshowpay = false;
                    }, 420);
                }
                if (that.openstudent && that.swiperCurrent == 2) {
                    setTimeout(function() {
                        that.openshowpay = false;
                    }, 420);
                }
            },
            checked: function(type) {
                this.paytype = type;
                if (!this.other) {
                    this.$emit('checked', type);
                    this.close();
                }
            },
            close: function() {
                this.$emit('input', false);
            },
            nowPay() {
                // #ifdef MP
                // let that = this;
                // console.log('判断设备',that.platform)
                // if (that.platform === 'ios') {
                //     uni.showModal({
                //         title: "支付提示",
                //         cancelColor: '#787b7b',
                //         confirmColor: '#e93323',
                //         cancelText: '确定',
                //         confirmText: '复制微信',
                //         content: '很抱歉，由于相关规范，暂不支持IOS支付。如需帮助请联系客服或微信搜索好友：' + that.site_wechat ,
                //         success(res) {
                //             if (res.confirm) {
                //                 uni.setClipboardData({
                //                   data: that.site_wechat,
                //                   success(res) {
                //                       return that.$showToast('复制成功')
                //                   },
                //                   fail(res) {
                //                       return that.$showToast('复制失败')
                //                   },
                //                 });
                //             } else if (res.cancel) {
                //                 console.log("cancel, cold");
                //             } else {
                //                 // what happend?
                //             }
                //         },
                //         fail(res) {
                //             console.log(`showModal调用失败`);
                //         },
                //     });
                //     return;
                // }
                // #endif
                if (!this.weChatPhone || this.weChatPhone.trim() == '') {
                    return this.$showToast('请完善您的联系方式（微信或手机号）')
                } else {
                    if(this.paymoney > 0){
                        this.$emit('getmark', this.weChatPhone);
                        this.$emit('nowPay', this.paytype);
                        this.close()
                    }
                    if(this.paymoney == 0){
                        return this.$showToast('优惠券超出订单金额，请重新选择')
                    }
                }

            }
        }
    };
</script>
<style scoped lang="scss">
    .payment {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        border-radius: 70rpx 70rpx 0 0;
        box-shadow: 0px 0px 30rpx 0px rgba(107, 127, 153, 0.30);
        background-color: #fff;
        padding-bottom: 60rpx;
        z-index: 999;
        transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
        transform: translate3d(0, 100%, 0);
    }
    .payment.padb0 {
        padding-bottom: 0;
    }
    .payment.on {
        transform: translate3d(0, 0, 0);
    }

    .payment .title {
        position: relative;
        text-align: center;
        height: 123rpx;
        font-size: 32rpx;
        color: #282828;
        font-weight: 700;
        color: #333333;
    }

    .payment .title .iconfont {
        position: absolute;
        right: 30rpx;
        top: 50%;
        transform: translateY(-50%);
        font-size: 43rpx;
        color: #8a8a8a;
        font-weight: normal;
    }

    .payment .item {
        border-bottom: 1rpx solid #eee;
        height: 130rpx;
        margin-left: 30rpx;
        padding-right: 30rpx;
    }

    .payment .item .left {
        width: 610rpx;
    }

    .payment .item .left .text {
        width: 540rpx;
    }

    .payment .item .left .text .name {
        font-size: 32rpx;
        color: #282828;
    }

    .payment .item .left .text .info {
        font-size: 24rpx;
        color: #999;
    }

    .payment .item .left .text .info .money {
        color: #ff9900;
    }

    .payment .item .left .iconfont {
        font-size: 45rpx;
        color: #09bb07;
    }

    .payment .item .left .iconfont.icon-zhifubao {
        color: #00aaea;
    }

    .payment .item .left .iconfont.icon-yuezhifu {
        color: #ff9900;
    }

    .payment .item .left .iconfont.icon-yuezhifu1 {
        color: #eb6623;
    }

    .payment .item .iconfont {
        font-size: 30rpx;
        color: #999;
    }

    .setnickname {
        width: 100%;

        .setnickname-title {
            width: 100%;
            font-size: 24rpx;
            color: #999999;
            padding: 0 40rpx 20rpx 100rpx;
        }

        .input {
            width: 300rpx;
            height: 60rpx;
            line-height: 60rpx;
            border-radius: 4rpx;
            border: 2rpx solid #d3d8d8;
            margin-left: 100rpx;
            padding: 0 20rpx;
        }
    }

    .settitle {
        width: 100%;
        text-align: left;
        font-size: 32rpx;
        color: #282828;
        font-weight: 700;
        color: #333333;
        padding: 40rpx;
    }

    .setavatar {
        position: relative;
        display: block;
        width: 200rpx;
        height: 200rpx;
        margin: 70rpx auto 88rpx;
        border-radius: 4rpx;
    }

    .setavatar-img {
        width: 100%;
        height: 100%;
        border-radius: 4rpx;
    }

    .opencoupon {
        border-bottom: 1rpx solid #eee;
        height: 120rpx;
        line-height: 120rpx;
        margin-left: 30rpx;
        padding-right: 30rpx;
    }
    .opencoupon-item {
        width: 100%;
        overflow: auto;
    }
    .opencoupon-left {
        float: left;
        font-size: 32rpx;
        font-family: PingFang SC, PingFang SC-Bold;
        font-weight: 700;
        text-align: left;
        color: #333333;
    }

    .opencoupon-right {
        float: right;
        font-size: 24rpx;
        color: #000;
    }

    .payment {
        .swiper {
            width: 100%;
            min-height: 770rpx;
            transition: all .46s ease-in-out;
                
            &.h90 {
                min-height: 97vh;
                
            }
        }

        .item {
            .image {
                width: 44rpx;
                height: 44rpx;

                image {
                    width: 100%;
                }
            }
        }

        .other {
            .money {
                position: relative;
                margin: 30rpx;
                font-size: 32rpx;
                font-family: PingFang SC, PingFang SC-Bold;
                font-weight: 700;
                text-align: center;
                color: #333333;
                overflow: auto;
                min-height: 45rpx;

                text {
                    color: #FF5656
                }

                .money-left {
                    text-align: left;
                    float: left;

                    .money-left-txt {
                        font-size: 24rpx;
                        color: #000;
                    }
                }

                .money-center {
                    position: absolute;
                    left: 50%;
                    top: 0;
                    transform: translateX(-50%);
                    text-align: center;
                }
            }

            .btn {
                width: 670rpx;
                text-align: center;
                margin: 0 auto;
                height: 100rpx;
                line-height: 100rpx;
                background: #ff5656;
                border-radius: 50rpx;
                font-size: 28rpx;
                font-family: PingFang SC, PingFang SC-Bold;
                font-weight: 700;
                text-align: center;
                color: #ffffff;
                
                &.gray {
                    background: #999;
                }
                &.fixed {
                    position: fixed;
                    left: 50%;
                    transform: translateX(-50%);
                    bottom: 70rpx;
                }
            }
        }
    }
    .student-box {
        width: 100%;
        padding: 0 40rpx;
        height: 70vh;
        box-sizing: border-box;
        
        .concat-item {
            width: 100%;
            padding-top: 34rpx;
            overflow: auto;
            border-bottom: 2rpx solid #d2d2d2;
            padding-bottom: 24rpx;
            box-sizing: border-box;

            .headimg-box {
                width: 100%;
                padding-bottom: 34rpx;
                box-sizing: border-box;
                overflow: auto;

                .headimg-title {
                    float: left;
                    width: 100%;
                    margin-bottom: 14rpx;
                }

                .headimg-img {
                    float: left;
                    width: 240rpx;
                    height: 240rpx;
                    border-radius: 100rpx;

                    image {
                        width: 100%;
                        height: 100%;
                        border-radius: 100rpx;
                    }

                    .headimg-img-txt {
                        width: 100%;
                        height: 100%;
                        color: #d2d2d2;
                        font-size: 32rpx;
                        border: 2rpx solid;
                        border-radius: 100rpx;
                        padding-top: 90rpx;
                        box-sizing: border-box;

                    }
                }
            }

            .introduce-title {
                width: 100%;
                font-size: 24rpx;
                font-family: PingFang SC, PingFang SC-Regular;
                font-weight: 400;
                text-align: left;
                color: #666666;

                .introduce-title {
                    color: red;
                }
            }

            .introduce-textarea {
                position: relative;
                width: 100%;

                .introduce-textarea-box {
                    width: 100%;
                    height: 300rpx;
                    font-size: 32rpx;
                    font-family: SF Pro Text, SF Pro Text-Regular;
                    font-weight: 400;
                    text-align: left;
                    border-radius: 12rpx;
                    border: 2rpx solid #d3d6e1;
                    box-sizing: border-box;
                    padding: 20rpx;
                }

                .introduce-textarea-des {
                    position: absolute;
                    right: 20rpx;
                    bottom: 20rpx;
                }
            }

            .concat-item-left {
                width: 100%;
                font-size: 24rpx;
                font-family: PingFang SC, PingFang SC-Regular;
                font-weight: 400;
                text-align: left;
                color: #666666;

                .title {
                    color: #d2d2d2;
                    font-size: 22rpx;
                    margin-left: 8rpx;
                }
            }

            .introduce-textarea-box {
                width: 100%;
                height: 144rpx;
                box-sizing: border-box;
                padding: 20rpx 0;
            }

            .concat-item-right {
                width: 100%;
                font-size: 32rpx;
                height: 82rpx;
                line-height: 82rpx;
                font-family: SF Pro Text, SF Pro Text-Regular;
                font-weight: 400;
                text-align: left;
                color: #000;
                border-bottom: 2rpx solid #d2d2d2;

                &.active {
                    color: #d2d2d2;
                }
            }

            .channel-box {
                width: 100%;
                overflow: auto;

                &.relative {
                    position: relative;
                }

                .videourl-box {
                    width: 100%;
                    height: 76rpx;
                    line-height: 76rpx;
                    margin-top: 20rpx;

                    .videourl-box-left {
                        float: left;
                        width: 520rpx;
                        margin-top: 12rpx;
                    }

                    .videourl-box-right {
                        float: right;
                        width: 60rpx;
                        height: 40rpx;
                        margin-top: 12rpx;
                        background-color: #50516a;
                        border-radius: 24rpx;

                        .videourl-box-right-image {
                            display: block;
                            margin: 8rpx auto;
                            width: 25rpx;
                            height: 25rpx;
                        }
                    }
                }

                .channel-box-video {
                    position: relative;
                    width: 200rpx;
                    height: 200rpx;
                    border-radius: 12rpx;
                    margin-top: 28rpx;
                    background-color: #f4f4f4;
                    overflow: auto;
                    border: 2rpx dashed #d2d2d2;

                    .item {}

                    .upload-video {
                        width: 66rpx;
                        height: 64rpx;
                        margin: 68rpx auto;
                    }

                    video {
                        width: 200rpx;
                        height: 200rpx;
                        border-radius: 12rpx;
                    }

                    .icon-del-box {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        position: absolute;
                        top: 0;
                        right: 0;
                        height: 48rpx;
                        width: 48rpx;
                        border-radius: 50%;
                        background-color: #ff5656;
                        z-index: 2;
                    }
                }

                .channel-box-musical {
                    float: left;
                    width: 50%;
                    margin-top: 20rpx;
                    padding-right: 45rpx;
                    box-sizing: border-box;

                    &.qita {
                        width: 100%;
                    }

                    .channel-box-musical-left {
                        float: left;
                        margin-top: 6rpx;

                        .sex-radios-box-img {
                            float: left;
                            width: 44rpx;
                            height: 44rpx;
                            margin-right: 16rpx;
                        }

                    }

                    .channel-box-musical-right {

                        float: left;
                        width: 224rpx;

                        &.qita {
                            width: 560rpx;
                        }

                        .title {
                            font-size: 24rpx;
                            font-family: PingFang SC, PingFang SC-Regular;
                            font-weight: 400;
                            text-align: left;
                            color: #d2d2d2;

                            &.active {
                                color: #50506f;
                            }
                        }

                        .input {
                            float: right;
                            width: 150rpx;
                            border-bottom: 2rpx solid #d2d2d2;
                            padding: 0 4rpx;
                            box-sizing: border-box;

                            &.qita {
                                width: 490rpx;
                            }
                        }
                    }
                }

                .sex-radios-box {
                    float: left;
                    width: 172rpx;
                    margin-top: 20rpx;

                    &.call {
                        width: 33%;
                    }
                    &.w50 {
                        width: 50%;
                    }
                    &.qita {
                        width: 100%;
                    }

                    &.input {
                        width: 460rpx;
                        margin-top: -4rpx;
                        margin-left: 8rpx;
                        border-bottom: 2rpx solid #d2d2d2;
                        font-size: 24rpx;
                    }

                    &.input2 {
                        width: 432rpx;

                    }

                    .sex-radios-box-img {
                        float: left;
                        width: 44rpx;
                        height: 44rpx;
                        margin-right: 16rpx;
                    }

                    .sex-radios-box-title {
                        float: left;
                        font-size: 24rpx;
                        font-family: PingFang SC, PingFang SC-Regular;
                        font-weight: 400;
                        text-align: left;
                        color: #d2d2d2;
                        margin-top: 4rpx;

                        &.active {
                            color: #50506f;
                        }
                        &.float0 {
                            float: none;
                        }
                    }
                    
                    .selex {
                        float: left;
                        overflow: auto;
                        padding: 4rpx 20rpx;
        
                        .selex-box {
                            float: left;
                            min-width: 128rpx;
                            height: 36rpx;
                            line-height: 32rpx;
                            background: #ffffff;
                            border: 2rpx solid #f8e8ea;
                            border-radius: 4rpx;
                            padding: 0 16rpx;
        
        
                            .selex-picker-box {
                                width: 100%;
        
                                .selex-picker-item {
                                    width: 100%;
        
                                    .title {
                                        float: left;
                                        font-size: 20rpx;
                                        font-family: PingFang SC, PingFang SC-Regular;
                                        font-weight: 400;
                                        text-align: center;
                                        color: #666666;
                                        margin-right: 26rpx;
                                        padding: 0;
                                    }
        
                                    .img {
                                        float: right;
                                        width: 18rpx;
                                        height: 18rpx;
                                        margin-top: 6rpx;
                                    }
                                }
                            }
                        }
                    }
                }

                .channel-box-musical {
                    float: left;
                    width: 50%;
                    height: 56rpx;
                    line-height: 56rpx;
                    margin-top: 12rpx;

                    .left {
                        float: left;
                    }

                    .right {
                        float: left;
                        width: 160rpx;
                        padding-left: 20rpx;
                        box-sizing: border-box;
                        border-bottom: 2rpx solid #9999995e;
                        margin-left: 14rpx;
                    }
                }

                // &.border-b2 {
                //     border-bottom: 2rpx solid #d2d2d2;
                //     padding-bottom: 24rpx;

                // }

                .learning {
                    width: 100%;
                    overflow: auto;
                    margin-top: 20rpx;
                    margin-bottom: 16rpx;

                    .learning-content {
                        float: left;
                        width: 500rpx;
                        // border-bottom: 2rpx solid #d2d2d2;
                    }

                    .learning-radio {
                        float: left;
                    }

                    .learning-add {
                        float: right;
                        width: 150rpx;
                        height: 40rpx;
                        line-height: 40rpx;
                        text-align: center;
                        font-size: 24rpx;
                        font-family: PingFang SC, PingFang SC-Medium;
                        font-weight: 500;
                        text-align: center;
                        color: #d2d2d2;
                        background-color: #50516a;
                        border-radius: 24rpx;
                    }
                }

                .learning-box {
                    width: 100%;
                    overflow: auto;


                    .learning-box-item {
                        width: 100%;
                        height: 60rpx;
                        line-height: 60rpx;
                        overflow: auto;

                        .learning-box-item-left {
                            float: left;
                            width: 500rpx;
                        }

                        .learning-box-item-del {
                            float: right;
                            width: 50rpx;
                            height: 50rpx;
                        }
                    }
                }
            }
        }
    }
</style>
