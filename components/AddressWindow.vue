<template>
	<view>
		<view class="address-window" :class="value === true ? 'on' : ''">
			<view class="title">
				选择地址
				<span class="iconfont icon-guanbi" @click="closeAddress"></span>
			</view>
			<view class="list" v-if="addressList.length">
				<view
					class="item acea-row row-between-wrapper"
					:class="item.id === checked ? 'font-color-red' : ''"
					v-for="(item, index) in addressList"
					@click="tapAddress(index)"
					:key="index"
				>
					<span class="iconfont icon-ditu" :class="item.id === checked ? 'font-color-red' : ''"></span>
					<view class="addressTxt">
						<view class="name" :class="item.id === checked ? 'font-color-red' : ''">
							{{ item.real_name }}
							<span class="phone">{{ item.phone }}</span>
						</view>
						<view class="line1">{{ item.province }}{{ item.city }}{{ item.district }}{{ item.detail }}</view>
					</view>
					<span class="iconfont icon-complete" :class="item.id === checked ? 'font-color-red' : ''"></span>
				</view>
			</view>
			<view class="pictrue" v-if="addressList.length < 1"><image :src="imagePath + '/wximage/noAddress.png'" class="image" /></view>
			<view class="addressBnt bg-color-red" @click="goAddressPages">新加地址</view>
		</view>
		<view class="mask" @touchmove.prevent :hidden="value === false" @click="closeAddress"></view>
	</view>
</template>
<script>
import { getAddressList } from '@/api/user';
	import{VUE_APP_URL} from '@/config.js'
export default {
	name: 'AddressWindow',
	props: {
		value: Boolean,
		checked: Number
	},
	data: function() {
		return {
			imagePath:VUE_APP_URL,
			addressList: [],
			current: 0,
			cartId: 0,
			pinkId: 0,
			couponId: 0,
            // checked: 0
		};
	},
	mounted: function() {},
	methods: {
		getAddressList: function() {
			let that = this;
			getAddressList().then(res => {
				that.addressList = res.data;
			});
		},
		closeAddress() {
			this.$emit('input', false);
		},
		goAddressPages: function() {
			// this.$router.push({ path: '/user/add_address' });
			this.$navigator('/pages/user/address/AddAddress')
			this.$emit('redirect');
		},
		tapAddress: function(index) {
			this.$emit('checked', this.addressList[index]);
			this.$emit('input', false);
		}
	}
};
</script>
<style scoped>
	.address-window {
		background-color: #fff;
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		z-index: 6666;
		transform: translate3d(0, 100%, 0);
		-webkit-transform: translate3d(0, 100%, 0);
		-ms-transform: translate3d(0, 100%, 0);
		-moz-transform: translate3d(0, 100%, 0);
		-o-transform: translate3d(0, 100%, 0);
		transition: all .3s cubic-bezier(.25, .5, .5, .9);
		-webkit-transition: all .3s cubic-bezier(.25, .5, .5, .9);
		-moz-transition: all .3s cubic-bezier(.25, .5, .5, .9);
		-o-transition: all .3s cubic-bezier(.25, .5, .5, .9);
	}
	
	.address-window.on {
		transform: translate3d(0, 0, 0);
		-webkit-transform: translate3d(0, 0, 0);
		-ms-transform: translate3d(0, 0, 0);
		-moz-transform: translate3d(0, 0, 0);
		-o-transform: translate3d(0, 0, 0);
	}
	
	.address-window .title {
		font-size: 32rpx;
		font-weight: bold;
		text-align: center;
		height: 123rpx;
		line-height: 123rpx;
		position: relative;
		color: #333;
	}
	
	.address-window .title .iconfont {
		position: absolute;
		right: 30rpx;
		color: #8a8a8a;
		font-size: 35rpx;
	}
	
	.address-window .list {
		max-height: 600rpx;
		overflow-y: auto;
		overflow-x: hidden;
	}
	
	.address-window .list .item {
		margin-left: 30rpx;
		padding-right: 30rpx;
		border-bottom: 1px solid #eee;
		height: 129rpx;
		font-size: 25rpx;
		color: #333;
	}
	
	.address-window .list .item .iconfont {
		font-size: 37rpx;
		color: #2c2c2c;
	}
	
	.address-window .list .item .iconfont.icon-complete {
		font-size: 30rpx;
		color: #fff;
	}
    .font-color-red {
    	color: red !important;
    }
	
    
	.address-window .list .item .addressTxt {
		width: 560rpx;
	}
	
	.address-window .list .item .addressTxt .name {
		font-size: 28rpx;
		font-weight: bold;
		color: #282828;
		margin-bottom: 4rpx;
	}
	
	.address-window .list .item .addressTxt .name .phone {
		margin-left: 18rpx;
	}
	
	.address-window .addressBnt {
		font-size: 30rpx;
		font-weight: bold;
		color: #fff;
		width: 690rpx;
		height: 86rpx;
		border-radius: 43rpx;
		text-align: center;
		line-height: 86rpx;
		margin: 85rpx auto;
	}
	
	.address-window .pictrue {
		width: 413rpx;
		height: 336rpx;
		margin: 80rpx auto;
	}
	
	.address-window .pictrue .image {
		width: 100%;
		height: 100%;
	}

</style>