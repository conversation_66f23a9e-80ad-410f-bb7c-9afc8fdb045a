<template>
    <view>
        <view class="product-window " :class="attr.cartAttr === true ? 'on' : ''"
            :style="'padding-bottom:' + (isShowBtn ? '0' : '')">
            <view class="textpic flex">
                <view class="pictrue flex flex_around" @click="previewImage(attr.productSelect.image)">
                    <image :src="attr.productSelect.image" class="image" mode="heightFix" />
                </view>
                <view class="text">
                    <view class="line1">{{ attr.productSelect.store_name }}</view>
                    <view class="money font-color-red">
                        ￥
                        <span class="num">{{ attr.productSelect.price }}</span>
                        <span class="stock" v-if="isShow">库存: {{ attr.productSelect.stock }}</span>
                        <span class="stock" v-else>限量:
                            {{ attr.productSelect.quota_show ? attr.productSelect.quota_show : 0 }}</span>
                    </view>
                </view>
                <view class="iconfont" @click="closeAttr">
                    <image src="@/static/images/yuanshi/close1.png" mode="widthFix"></image>
                </view>
            </view>
            <view class="productWinList">
                <view class="item" v-for="(item, indexw) in productAttr" :key="indexw">
                    <!-- <view class="title">{{ item.attr_name }} </view> -->
                    <view class="listn acea-row row-middle">
                        <view class="itemn" :class="item.index  === itemn.attr ? 'on' : ''"
                            v-for="(itemn, indexn) in item.attr_value" v-if="itemn.attr!=='默认'"
                            @click="tapAttr(indexw, itemn.attr)" :key="indexn">
                            {{ itemn.attr }}
                        </view>
                    </view>
                </view>
            </view>
            <view class="cart flex flex_end">
                <view class="flex flex_align_center">
                    <view class="title">购买数量</view>
                    <view class="carnum flex flex_align_center">
                        <view class="item reduce" :class="attr.productSelect.cart_num <= 1 ? 'on' : ''"
                            @click="CartNumDes">-</view>
                        <view class="item num"><input type="number" v-model="attr.productSelect.cart_num"
                                class="ipt_num" :disabled="true" /></view>
                        <view v-if="iSplus" class="item plus"
                            :class="attr.productSelect.cart_num >= attr.productSelect.stock ? 'on' : ''"
                            @click="CartNumAdd">+</view>
                        <view v-else class="item plus" :class="
								attr.productSelect.cart_num >= attr.productSelect.product_stock ||
								attr.productSelect.cart_num >= attr.productSelect.quota_show ||
								attr.productSelect.cart_num >= attr.productSelect.num ? 'on' : '' " @click="CartNumAdd">+
                        </view>
                    </view>
                </view>
                <view style="height:72rpx ;"></view>
            </view>
            <view class="wrapper" v-if="isShowBtn">
                <view class="teamBnt bg-color-red" @click="openAlone"
                    v-if="attr.productSelect.quota > 0 && attr.productSelect.product_stock > 0">立即参团</view>
                <view class="teamBnt bg-color-hui" v-else>已售罄</view>
            </view>
        </view>
        <view class="mask" @touchmove.prevent :hidden="attr.cartAttr === false" @click="closeAttr"></view>
    </view>
</template>
<script>
    export default {
        name: 'ProductWindow',
        props: {
            attr: {
                type: Object,
                default: () => {}
            },
            iSplus: {
                type: Boolean,
                value: true
            },
            isQuota: {
                //是否限量
                type: Boolean,
                value: false
            }
        },
        data: function() {
            return {
                isShow: !this.isQuota,
                pageRoute: '',
                productAttr: [],
                isAttr: false,
            };
        },
        watch: {
            'attr.productAttr': function(a, b) {
                if (this.isAttr) return;
                this.productAttr = a;
            },
            isQuota(a, b) {
                this.isShow = !a;
            }
        },
        computed: {
            // isShow() {
            // 	// console.log(this.pageRoute)
            // 	// return this.pageRoute==='pages/shop/GoodsCon' || this.pageRoute==='pages/yuanshi/evaluate/detail';
            // 	// return this.pageRoute.indexOf('pages/shop/GoodsCon') === 0 || this.pageRoute.indexOf('pages/yuanshi/detail/detail') === 0;
            // },
            isShowBtn() {
                // return this.pageRoute==='pages/yuanshi/detail/detail';
                return '/pages/activity/GroupRule'.indexOf(this.pageRoute) != -1;
            }
        },
        mounted: function() {
            let pages = getCurrentPages();
            let page = pages[pages.length - 1];
            this.pageRoute = page.route;
        },

        methods: {
            previewImage(url) {
                let item = '';
                if (url.indexOf('?') != -1) {
                    item = url.substring(0, url.indexOf('?'));
                } else {
                    item = url;
                }
                let photoList = [];
                photoList[0] = item;
                uni.previewImage({
                    current: 0, // 当前显示图片的链接/索引值
                    urls: photoList, // 需要预览的图片链接列表，photoList要求必须是数组
                });
            },
            initAttr() {
                this.isAttr = false;
            },
            openAlone() {
                this.$emit('changeFun', {
                    action: 'goPay',
                    value: false
                });
            },
            closeAttr: function() {
                this.$emit('changeFun', {
                    action: 'changeattr',
                    value: false
                });
            },
            CartNumDes: function() {
                this.$emit('changeFun', {
                    action: 'ChangeCartNum',
                    value: false
                });
            },
            CartNumAdd: function() {
                this.$emit('changeFun', {
                    action: 'ChangeCartNum',
                    value: 1
                });
            },
            tapAttr: function(indexw, indexn) {
                // console.log('tapAttr', indexw, indexn)
                let that = this;
                this.isAttr = true;
                that.productAttr[indexw].index = indexn;
                that.$emit('productAttr', that.productAttr);
                that.$set(that.attr.productAttr[indexw], 'index', indexn);
                let value = that
                    .getCheckedValue()
                    .sort()
                    .join(',');
                that.$emit('changeFun', {
                    action: 'ChangeAttr',
                    value: value
                });
            },
            //获取被选中属性；
            getCheckedValue: function() {
                let productAttr = this.attr.productAttr;
                let value = [];
                for (let i = 0; i < productAttr.length; i++) {
                    for (let j = 0; j < productAttr[i].attr_value.length; j++) {
                        if (productAttr[i].index === productAttr[i].attr_value[j].attr) {
                            value.push(productAttr[i].attr_value[j].attr);
                        }
                    }
                }
                return value;
            }
        }
    };
</script>
<style scoped lang="scss">
    .product-window {
        position: fixed;
        bottom: 0;
        width: 100%;
        left: 0;
        background-color: #fff;
        z-index: 666;
        border-radius: 70rpx 70rpx 0 0;
        padding: 72rpx 42rpx 0 60rpx;
        box-shadow: 0px 0px 30rpx rgba(107, 127, 153, 0.3);
        /* padding-bottom: 140rpx; */
        padding-bottom: calc(constant(safe-area-inset-bottom) + 140rpx);
        /* 兼容 IOS<11.2 */
        padding-bottom: calc(env(safe-area-inset-bottom) + 140rpx);
        /* 兼容 IOS>11.2*/
        // height: calc(constant(safe-area-inset-bottom) + 140rpx) !important;
        transform: translate3d(0, 100%, 0);
        transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);

        .textpic {
            position: relative;
            margin-bottom: 40rpx;

            .pictrue {
                width: 135rpx;
                height: 180rpx;
                margin-right: 20rpx;

                .image {
                    width: 100%;
                    height: 100%;
                    // border-radius: 30rpx;
                }
            }

            .text {
                width: 388rpx;
                font-size: 32rpx;

                font-weight: bold;
                color: #333333;

                .money {
                    margin-top: 10rpx;

                    font-weight: 500;

                    .num {
                        font-size: 28rpx;
                    }

                    .stock {
                        font-size: 24rpx;
                        color: #666666;
                        margin-left: 30rpx;
                    }
                }
            }

            .iconfont {
                position: absolute;
                right: 0rpx;
                top: -5rpx;

                image {
                    width: 48rpx;
                    height: 48rpx;
                }
            }
        }

        .productWinList {
            max-height: 495rpx;
            overflow: auto;
            // margin-top: 52rpx;

            .item {
                .title {
                    font-size: 30rpx;
                    color: #999;
                    margin-bottom: 16rpx;
                }

                .listn {

                    // padding: 0 30rpx 0 16rpx;
                    .itemn {
                        border: 1px solid #bbb;
                        font-size: 26rpx;
                        color: #282828;
                        padding: 12rpx 30rpx;
                        margin: 0 16rpx 16rpx 0;

                        border-radius: 24rpx;

                        &.on {
                            color: #fc5656;
                            border: 2rpx solid #ff5656;
                        }
                    }
                }
            }
        }

        .cart {
            padding: 24rpx 0rpx;
            border-top: 2rpx solid #e2e6ec;

            .title {
                font-size: 24rpx;
                color: #999;

                color: #333333;
                font-weight: 400;
                margin-right: 40rpx;
            }

            .carnum {
                .item {
                    &:not(.num) {
                        border-radius: 50%;
                        border: 2rpx solid #a4a4a4;
                        width: 40rpx;
                        height: 40rpx;
                        line-height: 36rpx;
                        text-align: center;
                        color: #a4a4a4;
                        font-size: 32rpx;
                    }

                    &.reduce.on {
                        border-color: #e3e3e3;
                        color: #dedede;
                    }

                    &.num {
                        width: 78rpx;
                        color: #282828;
                        font-size: 28rpx;

                        .ipt_num {
                            width: 100%;
                            display: block;
                            text-align: center;
                        }
                    }

                    &.plus.on {
                        border-color: #e3e3e3;
                        color: #dedede;
                    }
                }
            }
        }
    }

    .product-window.on {
        transform: translate3d(0, 0, 0);
    }

    .product-window .productWinList .item~.item {
        margin-top: 36rpx;
    }
</style>
