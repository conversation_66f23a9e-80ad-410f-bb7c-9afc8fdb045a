<template>
	<view @touchmove.stop.prevent>
		<view class="x_chat" :style="{ paddingBottom: `${keyboardHeight}px` }" :class="{ inputHide: !inputShow }">
			<view class="chat_wrap">
				<template v-if="inputType === 'input'">
					<input hold-keyboard :disable-default-padding="true" class="w_input" :fixed="true" :focus="focus"
						:adjust-position="adjustPosition" :maxlength="maxlength" v-model.trim="content"
						v-bind:placeholder="placeholder" @focus="showTool(1)" @blur="inputBlur" @input="inputInput($event)"
						@confirm="inputConfirm" style="padding-left: 20rpx;" @bindfocus="handleFocus"/>
				</template>
				<template v-else-if="inputType === 'textarea'">
					<textarea hold-keyboard auto-height :disable-default-padding="true" class="w_input" :fixed="true"
						:focus="focus" :adjust-position="adjustPosition" :maxlength="maxlength" v-model.trim="content"
						v-bind:placeholder="placeholder" :show-confirm-bar="false" @focus="showTool(1)"
						@blur="inputBlur" @input="inputInput($event)" style="padding-left: 20rpx;" @bindfocus="handleFocus"></textarea>
				</template>
				<view class="btn_wrap">
					<view class="face" v-if="emoji">
						<image src="@/static/images/face.png" @click="showTool(2)" />
					</view>
					<view class="face" v-if="isTool">
						<image src="@/static/images/keyboard.png" @click="showTool(3)" />
					</view>
                    <!-- :class="btnClass" v-else -->
					<view class="btn_send" :class="content == ''?'':'active'" :style="{ color: color }" :hover-stay-time="150"
						@click="sendMsg(content, 1)">{{ sendText }}</view>
				</view>
			</view>
			<view class="panel">
				<view class="emoji" v-if="toolType === 2">
					<swiper class="swiper-wrapper" v-if="emojiGroup.length > 0">
						<swiper-item class="swiper-slide acea-row" v-for="(emojiList, index) in emojiGroup"
							:key="index">
							<i class="em"
								style="background-image: url('https://kf.arthorize.com/admin/UEditor/img/emoji.jpg');"
								:class="emoji" v-for="emoji in emojiList" :key="emoji" @click="sendMsg1(emoji, 2)"></i>
							<image src="@/static/images/del.png" class="emoji-outer" @click="delInput" />
						</swiper-item>
					</swiper>
				</view>
				<view class="tool" v-else-if="toolType === 3">
					<xImageUpload :num="1" :preview="false" @chooseImage="chooseImage">
						<view class="upload" style="margin:0">
							<image src="@/static/images/plus.png" />
						</view>
					</xImageUpload>
				</view>
			</view>
		</view>
        <!-- <image src="'https://shop.arthorize.com/static/images/emoji.png" mode="" style="display: none;" v-if="emoji"> -->
		</image>
	</view>
</template>

<script>
	import './dist/css/google.min.css';
	import emojiList from './emoji';
	import xImageUpload from '@/components/x-image-upload/x-image-upload';
	import {
		uploadImg
	} from '@/utils/upload.js';
	import storage from '@/utils/storage.js';
	import {
		CHAR_MESS
	} from '@/config.js';
	const chunk = function(arr, num) {
		num = num * 1 || 1;
		var ret = [];
		arr.forEach(function(item, i) {
			if (i % num === 0) {
				ret.push([]);
			}
			ret[ret.length - 1].push(item);
		});
		return ret;
	};
	export default {
		props: {
			sendText: {
				type: String,
				default: '发送'
			},
			color: {
				type: String,
				default: '#666666'
			},
			uid: {
				type: Number,
				default: -1
			},
			emoji: {
				type: Boolean, //显示表情
				default: false
			},
			tool: {
				type: Boolean, //显示工具栏
				default: false
			},
			btnClass: {
				type: String,
				default: ''
			},
			placeholder: {
				type: String,
				default: '请输入内容'
			},
			maxlength: {
				type: Number,
				default: 300
			},
			inputShow: {
				type: Boolean, //输入框的显示
				default: false
			},
            adjustPosition:{
                type: Boolean, //输入是是否顶起页面
                default: false
            },
			desc: {
				//聊天场景说明 防止缓存时数据覆盖
				type: String,
				default: 'chat'
			},
			inputType: {
				type: String,
				default: 'textarea'
			},
			isCash: {
				type: Boolean, //是否缓存
				default: true
			},
            isReplace: {
            	type: Boolean, //是否进行字符优化转换
            	default: false
            },
		},
		components: {
			xImageUpload
		},
		data() {
			return {
				emojiGroup: chunk(emojiList, 20),
				keyboardHeight: 0,
				// content: this.value,
                content: '',
				toolType: 0, // //1-键盘 2-表情 3工具集合
				isTool: this.tool, // 工具集合true 或 显示键盘fasle
				focus: false
			};
		},
		watch: {
			uid(a, b) {
				// this.init(a);
			},
			// #ifdef MP-WEIXIN
			inputShow(a, b) {
				if (a) {
					this.focus = true;
				} else {
					this.focus = false;
				}
			}
			// #endif
		},
		created() {
            let that = this;
            // #ifdef MP-WEIXIN
            //键盘高度监听
            uni.onKeyboardHeightChange(res => {
            	if(that.adjustPosition == false){
                    that.keyboardHeight = res.height;
                    if(that.inputShow == false){
                        that.keyboardHeight = 0;
                    }
                } else if(that.adjustPosition == true) {
                    that.keyboardHeight = 0;
                }
                uni.setStorageSync('keyboardHeight', that.keyboardHeight);
            	
            });
            // #endif
		},
		mounted() {
			if (this.isCash) {
				this.init(this.uid);
			}
		},
		methods: {
			init(uid) {
				let chat = storage.get(`${CHAR_MESS}${this.desc}${uid}`);

				console.log(`${CHAR_MESS}${this.desc}${uid}`)
				if (chat) {
					chat = JSON.parse(chat);
					if (chat.uid === uid && uid > -1) {
						this.content = chat.content;
					}
				}
			},
			clear() {
				storage.set(`${CHAR_MESS}${this.desc}${this.uid}`, null);
			},
			hideKeyboard() {
				this.toolType = 0;
				this.keyboardHeight = 0;
				let timer = setTimeout(() => {
					uni.hideKeyboard();
					clearTimeout(timer);
				}, 500);
			},
			inputInput(e) {
				let {
					value,
					cursor
				} = e.detail;
				value = value.trim();
				if (value.length) {
					if (value.length < this.maxlength) {} else {
						value = value.slice(0, this.maxlength);
						uni.showToast({
							title: '允许输入最大长度为' + this.maxlength,
							icon: 'none'
						});
					}
					// if (this.isTool) {
					// 	this.isTool = false;
					// }
                    if(this.isReplace){
                        let strCont = e.target.value.replace(/\n\s(\s)*/gi, '\n') // 将多个回车换行合并为 1个
                        strCont = strCont.replace(/^\s*/gi, '') // 清除首行的 空格与换行
                        
                        let strHtml = strCont.replace(/</gi, '&lt;')  // 将所有的 < 转义为 &lt; 防止html标签被转义
                        strHtml = strCont.replace(/\n(\n)*/gi, '<br>')  // 回车换行替换为 <br>
                        strHtml = strHtml.replace(/\s+/gi, '&nbsp;')  // 一个或过个空格 替换为 &nbsp;
                        
                        strCont = strHtml.replace(/&nbsp;/gi, ' ')  // 逆向处理
                        strCont = strCont.replace(/<br>/gi, '\n')   // 逆向处理
                        strCont = strCont.replace(/&lt;/gi, '<')
                        
                        this.content = strCont
                    }
                    
					let obj = {
						content: value,
						uid: this.uid
					};
					this.content = value;
					if (this.uid > -1 && this.isCash) {
						storage.set(`${CHAR_MESS}${this.desc}${this.uid}`, JSON.stringify(obj));
					}

				} else {
					// this.isTool = this.emoji ? true : false;
					this.content = '';
					return {
						value: ''
					}

				}
			},
			inputFocus() {
				console.log('this.focus', this.focus);
				this.focus = true;
				this.$emit('inputFocus');
			},
			inputBlur() {
				this.keyboardHeight = 0;
				this.focus = false;
				this.$emit('inputBlur');
			},
			inputConfirm(e) {
				this.sendMsg(this.content, 1)
			},
            handleFocus(e){
                console.log('监听抖音键盘',e)
            },
			showTool(index) {
				this.keyboardHeight = uni.getStorageSync('keyboardHeight') || 0;
				console.log('this.keyboardHeight', this.keyboardHeight);
				if (this.toolType == index) {
					return;
				}
				if (index !== 1) {
					this.hideKeyboard();
				}
				this.toolType = index;
			},
			delInput() {
				this.content = this.content.slice(0, this.content.length - 1);
			},
			sendMsg(val, type) {
				this.clear();
				if (!val) {
					return this.$showToast('输入内容为空');
				}
                if(type == 2){
                    this.content = `[${val}]`;
                }
				this.$emit('send', val, type);
				if (type === 1) {
					let timer = setTimeout(() => {
						this.content = '';
						clearTimeout(timer);
					}, 500);
				}
			},
            sendMsg1(val, type) {
            	this.clear();
            	if (!val) {
            		return this.$showToast('输入内容为空');
            	}
                if(type == 2){
                    this.content += `[${val}]`;
                }
            	// this.$emit('send', val, type);
            	if (type === 1) {
            		let timer = setTimeout(() => {
            			this.content = '';
            			clearTimeout(timer);
            		}, 500);
            	}
            },
			chooseImage(arr) {
				uploadImg(arr, false).then(res => {
					this.sendMsg(res[0], 3);
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	$height: 40rpx; //输入区域 初始高度

	.x_chat {
		width: 100%;
		position: fixed;
		left: 0;
		bottom: 0;
		display: flex;
		flex-direction: column;
		box-sizing: border-box;
		z-index: 99999;
		// height: calc(constant(safe-area-inset-bottom) + #{$height}) !important;
		// height: calc(env(safe-area-inset-bottom) + #{$height}) !important;
		/* 兼容 IOS<11.2 */
		padding-bottom: constant(safe-area-inset-bottom) !important;
		/* 兼容 IOS>11.2*/
		padding-bottom: constant(safe-area-inset-bottom) !important;

		image {
			width: 100%;
			height: 100%;
		}

		.chat_wrap {
			width: 100%;
			background-color: #f7f8fa;
			display: flex;
			align-items: flex-end;
			padding: 16rpx 24rpx;
			box-sizing: border-box;

			&::after {
				content: ' ';
				position: absolute;
				top: 0;
				right: 0;
				left: 0;
				// border-top: 1rpx solid #ccc;
				-webkit-transform: scaleY(0.5);
				transform: scaleY(0.5);
			}

			textarea,
			input {
				font-size: 24rpx;
			}

			.w_input {
				flex: 1;
				min-height: $height;
				/*#ifdef MP*/
				padding: 20rpx 10rpx 20rpx 10rpx;
				/*#endif*/
				/*#ifndef MP */
				padding: 20rpx;
				/*#endif*/
				background-color: #fff;
				border-radius: 26rpx;
			}

			.btn_wrap {
				display: flex;
				align-items: center;
				justify-content: flex-end;
				/*#ifdef MP*/
				height: calc(#{$height} + 40rpx);
				/*#endif*/
				/*#ifndef MP */
				height: calc(#{$height} + 40rpx);

				/*#endif*/
				.face {
					width: 50rpx;
					height: 50rpx;
					margin-left: 10rpx;
				}

				.btn_send {
					width: 148rpx;
					text-align: center;
					background: #f7f8fa;
					flex-shrink: 0;
					font-size: 24rpx;
					border-radius: 26rpx;
					border: 2rpx solid #d2d2d2;
					margin-left: 20rpx;
					/*#ifdef MP*/
					line-height: calc(#{$height} + 38rpx);
					/*#endif*/
					/*#ifndef MP */
					line-height: calc(#{$height} + 38rpx);
					/*#endif*/
                    &.active {
                        background-color: #6bb4ff;
                        color: #fff !important;
                        transition: all .3s ease;
                    }
				}
			}
		}

		.panel {

			.emoji,
			.tool {
				min-height: 300rpx;
				background: #fff;
			}

			.emoji {
				flex-wrap: wrap;
				-webkit-flex-wrap: wrap;
				background-color: #fff;
				padding-bottom: 50rpx;
				border-top: 1px solid #f5f5f5;

				.emoji-outer,
				.em {
					display: block;
					width: 50rpx;
					height: 50rpx;
					margin: 40rpx 0 0 50rpx;
				}
			}

			.tool {
				padding: 20rpx;

				.upload {
					width: 80rpx;
					height: 80rpx;
				}
			}
		}
	}

	.inputHide {
		height: 0rpx;
		transition: height 5s;
		overflow: hidden;
        // display: none;
	}
</style>
