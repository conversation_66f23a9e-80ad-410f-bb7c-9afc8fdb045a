<template>
	<view class="noCommodity" v-cloak v-if="arr.length === 0 && page > 1">
		<view class="noPictrue"><image :src="imagePath + imgSrc" class="image" /></view>
		<view v-if="isR">
			<Recommend></Recommend>
		</view>
	</view>
</template>

<script>
	import Recommend from '@/components/Recommend';
	import{VUE_APP_URL} from '@/config.js'
	export default {
		components: {
			Recommend
		},
		props:{
			page:{
				type:Number,
				default:0
			},
			imgSrc:{
				type:String,
				default:'/wximage/noSearch.png'
			},
			isR:{
				type:Boolean,
				default:true
			},
			arr:{
				type:Array,
				default:()=>{
					return []
				}
			}
		},
		data() {
			return {	
				imagePath:VUE_APP_URL
			};
		},
		mounted() {
		}
	}
</script>

<style scoped="" lang="scss">
.noCommodity{padding-top: 75rpx;}
.noCommodity .noPictrue{width:414rpx;height:336rpx;margin:0 auto 30rpx auto;}
.noCommodity .noPictrue .image{width:100%;height:100%;}
</style>
