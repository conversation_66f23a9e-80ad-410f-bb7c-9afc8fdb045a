<template>
	<view class="vip_card">
		<view class="wrap">
			<view class="title flex flex_between">
				<view class="name"> {{detail.name || ''}}</view>
				<view class="title_r">
					<template v-if="userInfo.vip">
						<view class="font_size20 say_feeling">{{ userInfo.last_time }}到期</view>
					</template>
					<template v-else>
						<view class="btn" @click="goPages('/pages/yuanshi/vip/equity')" v-if="equity"><view class="font_size20">查看权益</view></view>
					</template>
				</view>
			</view>
			<view class="wrap_m">先于财富自由实现精神自由！</view>
			<view class="wrap_b flex flex_align_center flex_between">
				<view class="input flex_line_height">
					<template v-if="userInfo.vip">
						<view class="vip">
							累计已省
							<text>￥{{userInfo.thrift_money}}</text>
						</view>
					</template>
					<template v-else>
						<text class="font_size20 ">开通即享全场平均5折优惠</text>
					</template>
				</view>
				<view class="image"><image src="@/static/images/yuanshi/vip.png" mode="widthFix"></image></view>
			</view>
		</view>
		<view class="show">
			<template v-if="userInfo.vip">
				<view class="vip" v-if="equity">
					<view class="link flex flex_around" @click="goPages('/pages/yuanshi/vip/equity')">
						<view class="flex flex_align_center">
							<view>查看我的权益 <text class="iconfont icon-jiantou"></text></view>
							
						</view>
					</view>			
				</view>
				<view v-else><xProtocolVip></xProtocolVip></view>
			</template>
			<template v-else>
				<button class="btn flex_line_height" @click="goPages('/pages/yuanshi/vip/BuyVip')">
					<view>
						<view class="btn_t">开通{{detail.name}}</view>
						<view class="btn_b">{{ detail.money }} 元/年  <text class="font_size20" style="">原价120元</text></view>
					</view>
				</button>
				<view class="protocol"><xProtocolVip></xProtocolVip></view>
			</template>
		</view>
	</view>
</template>

<script>
import xProtocolVip from '@/components/x-protocol/x-protocol-vip';
import { getVipInfo, getUserInfo } from '@/api/user';
import { mapGetters } from 'vuex';
export default {
	props: {
		equity:{
			type:Boolean,
			default:true
		}
	},
	components: { xProtocolVip },
	data() {
		return {detail: {} };
	},
	computed: mapGetters(['userInfo']),
	methods: {
		goPages(path, type) {
			this.$navigator(path, type);
		},
		getInfo: function() {
			let that = this;
			getVipInfo().then(
				res => {
					that.detail = res.data.list[0];
				},
				err => {
					that.$showToast(err.msg || err)
				}
			);
		}
	},
	mounted() {
		this.getInfo();
	},
	onLoad(option) {}
};
</script>

<style scoped lang="scss">
.vip_card {
	.wrap {
		// width: 670rpx;
		margin: 0rpx 40rpx;
		padding: 52rpx 0 0 0;
		overflow: hidden;
		background: rgba(255, 255, 255, 0.9);
		border: 2rpx solid #d2d2d2;
		box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
		opacity: 1;
		border-radius: 50rpx;
		.title {
			margin: 0 36rpx 20rpx 36rpx;
			padding-bottom: 16rpx;
			border-bottom: 2rpx solid #d2d2d2;
			.name {
				font-weight: bold;
				color: #999999;
				font-size: 32rpx;
			}
			.title_r {
				.font_size20 {
					color: #999999;
				}
				.say_feeling{
					padding-right: 20rpx;
					&:after{
						right: -30rpx;
						top: 0;
					}
				}
				.btn {
					width: 120rpx;
					height: 38rpx;
					text-align: center;
					line-height: 34rpx;
					border-radius: 14rpx;
					border: 2rpx solid #ff5656;
					font-weight: 400;
					color: #ff5656;
					.font_size20 {
						color: #ff5656;
					}
				}
			}
		}
		.wrap_m {
			padding: 0 36rpx;
			font-size: 40rpx;
			height: 162rpx;
			color: #333333;
			font-weight: bold;
		}
		.wrap_b {
			height: 128rpx;
			padding: 0 36rpx;
			background: #f2f5f8;
			.input {
				min-width: 284rpx;
				height: 52rpx;
				background: #ffffff;
				box-shadow: 0px 4rpx 20rpx rgba(0, 0, 0, 0.06);
				border-radius: 26rpx;

				color: #999999;

				.vip {
					color: #666666;
					font-size: 24rpx;
					text {
						font-weight: 500;
						color: #3e3e3e;
					}
				}
			}
			image {
				width: 134rpx;
			}
		}
	}
	.show {
		.vip {
			.link {
				margin-top: 40rpx;
				color: #666666;
				font-size: 24rpx;
				text-align: center;
				.iconfont {
					font-size: 24rpx;
					color: #666666;
				}
			}
		}
		button {
			width: 670rpx;
			height: 100rpx;
			margin: 68rpx auto 0 auto;
			background: #ff5656;
			color: #ffffff;
			border-radius: 40rpx;
			.btn_t {
				font-weight: bold;
				font-size: 32rpx;
			}
			.btn_b {
				font-size: 24rpx;
				text{
					text-decoration: line-through;
					transform-origin:right;
				}
			}
		}
	}
}
</style>
