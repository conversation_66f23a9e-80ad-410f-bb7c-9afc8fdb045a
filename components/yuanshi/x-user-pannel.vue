<template>
    <view class="userPannel">
        <view class="pannel flex flex_align_center">
            <view class="pannel_l relative">
                <image :src="info.avatar" mode="scaleToFill"></image>
                <image src="@/static/images/yuanshi/avatar.png" mode="widthFix" class="bg absolute"
                    v-if="info.level === 1"></image>
                <image src="@/static/images/yuanshi/avatar1.png" mode="widthFix" class="bg absolute" v-else></image>
            </view>
            <view class="pannel_r">
                <view class="nickname">
                    <view class="name">{{info.nickname}}</view>
                    <view class="level" v-if="info.isLevel">
                        用户等级：{{info.level ? (info.level === 1 ?'小嘢人':'着调儿人') : '小嘢人'}}</view>
                    <!-- <view class="level" v-if="info.growing_integral">成长等级：{{info.growing_integral}}</view> -->
                </view>
                <view class="btn flex">
                    <!-- #ifdef MP -->
                    <button class="btn_l flex flex_around flex_align_center" open-type="share">
                        <!-- #endif -->
                        <!-- #ifdef H5 -->
                        <button class="btn_l flex flex_around flex_align_center" @click="posters = !posters">
                            <!-- #endif -->
                            <view class="flex flex_align_center">
                                <image src="@/static/images/yuanshi/share1.png" mode="widthFix"></image>
                                <view class="tt_padding-right">分享</view>
                            </view>
                        </button>
                        <button class="btn_r flex flex_around flex_align_center tt_padding-right" v-if="tagType === 'other'"
                            @click.stop="goFollow(isFollow, info.uid)">{{ isFollow ? '已跟随' : ' 点击跟随' }}</button>
                    </button>
                </view>
            </view>
        </view>
        <view class="tag  flex" :class="tagType">
            <view class="item" @click="tagType === 'other' ?'':goPages('/pages/yuanshi/user/fans?type=trends')"><text
                    class="font_size20">跟随 {{ info.follows  || 0 }}</text></view>
            <view class="item" @click="tagType === 'other' ?'':goPages('/pages/yuanshi/user/fans?type=fans')">
                <text class="font_size20">粉丝 {{ info.fans || 0 }}</text>
            </view>
            <view class="item" @click="goChat('/pages/yuanshi/chat/ChatRoom?toUid=' + info.uid)"
                v-if="tagType === 'other'"><text class="font_size20">Say Hi</text></view>
        </view>
        <!-- #ifdef H5 -->
        <ShareInfo></ShareInfo>
        <!-- <xShare v-model="posters" :share="{}"></xShare> -->
        <!-- #endif -->
    </view>
</template>

<script>
    // #ifdef H5
    import ShareInfo from '@/components/ShareInfo';
    import xShare from '@/components/x-share/x-share.vue';
    // #endif
    import {
        userFollow,
        userUnFollow
    } from '@/api/yuanshi/user.js';
    import {
        mapGetters
    } from 'vuex';
    export default {
        props: {
            info: {
                type: Object,
                default () {
                    return {};
                }
            },
            tagType: {
                type: String,
                default: 'user'
            },
            activeIdx: {
                type: Number,
                default: -1
            }
        },
        computed: mapGetters(['userInfo']),
        watch: {
            info(a, b) {
                this.isFollow = a.is_follow
            }
        },

        components: {
            // #ifdef H5
            ShareInfo,
            xShare
            // #endif
        },
        data() {
            return {
                // #ifdef H5
                posters: false,
                // #endif
                isFollow: this.info.is_follow,
                tabIdx: this.activeIdx,
                tagIdx: -1
            };
        },
        methods: {
            pannelClick(idx, type) {
                if (this.tabIdx === idx) {
                    this.tabIdx = -1;
                    type = 'user';
                } else {
                    this.tabIdx = idx;
                }

                this.$emit('pannelClick', idx, type);
            },
            goFollow(type, id, idx) {
                if (type) {
                    userUnFollow(id).then(res => {
                        this.$showToast('已取消');
                    });
                } else {
                    userFollow(id).then(res => {
                        this.$showToast('跟随成功');

                    });
                }
                this.isFollow = !this.isFollow
            },
            goChat(path, type) {
                if (this.info.uid === this.userInfo.uid) {
                    return this.$showToast('不能自己与自己聊天')
                }
                this.goPages(path);
            },
            goPages(path, type) {
                this.$navigator(path, type);
            }
        },
        mounted() {},
        onLoad(option) {
            let href = '/pages/yuanshi/home?id=' + this.info.uid;
            if (this.tagType === 'other') {
                href = href + '&spid=' + this.$store.state.userInfo.uid;
            }
            this.shareInfo = {
                image: data.avatar,
                title: data.nickname,
                //  #ifdef H5
                desc: '分享',
                href: href
                // #endif
            };
        }
    };
</script>

<style scoped lang="scss">
    .userPannel {
        image {
            width: 100%;
            height: 100%;
        }

        .pannel {
            background: $uni-bg-color;
            border-radius: 0px 0px 16rpx 16rpx;
            padding: 56rpx 56rpx 98rpx 42rpx;

            .pannel_l {
                width: 240rpx;
                // height: 240rpx;
                margin-right: 48rpx;

                image {
                    width: 240rpx;
                    height: 240rpx;
                    border-radius: 100rpx;

                    &.bg {
                        position: absolute;
                        left: -48rpx;
                        top: -48rpx;
                        width: 336rpx;
                        height: 336rpx;
                        border-radius: 0rpx;
                    }
                }
            }

            .pannel_r {
                .nickname {
                    color: #333333;

                    .name {
                        min-height: 60rpx;
                        font-size: 44rpx;

                        font-weight: 800;
                    }

                    .level {
                        min-height: 34rpx;
                        margin-top: 4rpx;
                        font-size: 24rpx;
                    }
                }

                .btn {
                    margin-top: 30rpx;

                    button {
                        width: 172rpx;
                        height: 72rpx;
                        line-height: 72rpx;
                        background: #ffffff;
                        opacity: 1;
                        border-radius: 30rpx;
                        color: #666666;
                        font-size: 24rpx;

                        &.btn_l {
                            border: 2rpx solid #d2d2d2;
                            margin-right: 20rpx;

                            image {
                                width: 31rpx;
                                margin-right: 14rpx;
                            }
                        }

                        &.btn_r {
                            border: 2rpx solid #ff5656;
                            color: #ff5656;
                            
                            // #ifdef
                        }
                        
                    }
                }
            }
        }

        .tag {
            padding: 0 20rpx;

            .item {
                height: 50rpx;
                background: #efb039;
                text-align: center;
                // line-height: 40rpx;
                background: #ffffff;
                box-shadow: 0px 10rpx 20rpx -10rpx rgba(107, 127, 153, 0.1);
                border-radius: 0px 0px 24rpx 24rpx;
                color: #999999;
                font-weight: 400;
                flex: 1;
                display: flex;
                // align-items: center;
                justify-content: space-around;

                &:not(:last-child) {
                    margin-right: 20rpx;
                }
            }

            &.user {
                .item {

                    // width: 248rpx;
                    &:not(:last-child) {
                        margin-right: 30rpx;
                    }
                }
            }

            &.other {
                .item {
                    // width: 152rpx;

                    &.active {
                        background: #5a3925;
                        color: #efb039;
                    }
                }
            }
        }
    }

    // #ifdef MP-TOUTIAO
    .tt_padding-right {
        padding-right: 28rpx;
    }

    // #endif
</style>
