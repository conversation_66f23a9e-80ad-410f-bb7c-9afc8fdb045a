<template>
	<view class="swiper relative">
		<xSwiper :arr="swiper" height="1000rpx" srcName="item" radius="0rpx" :autoplay="true" :dots="true"
			mode="widthFix" @change="dotChange" @swiperClick="swiperClick"></xSwiper>
		<view class="absolute wrap">
			<view class="dot flex_line_height">{{ dotIdx + 1 }} / {{ arr.length }}</view>
			<view class="name">{{ infos.store_name }}</view>
			<view class="flex flex_between">
				<view class="time">
					<text v-if="type === 'wish'">能量积累中…</text>
					<text v-else> <text v-if="infos.evaluation_time"> 测评时间
							{{ infos.evaluation_time || '敬请期待' }}</text></text>
				</view>
				<view class="flex flex_align_center image" @click="btnAttention">
					<image src="@/static/images/yuanshi/love1.png" mode="widthFix" v-if="infos.is_follow"></image>
					<image src="@/static/images/yuanshi/love3.png" mode="widthFix" v-else></image>
					<view>{{ infos.follow_test_number }}</view>
				</view>
			</view>
		</view>
		<!-- <view class="acea-row flex_around">
			<view class="wrap acea-row row-middle">
				<view class="dots " v-for="(item, index) in arr" :key="index" :class="{ dotActive: dotIdx === index }"><image :src="item.pic" mode=""></image></view>
				<view class="more" @click="goPages('/pages/yuanshi/evaluate/atlas?id=1')">| 更多</view>
			</view>	
		</view> -->
	</view>
</template>

<script>
	import xSwiper from '@/components/x-swiper/x-swiper.vue';
	import {
		wishAttention,
		wishUnAttention
	} from '@/api/yuanshi/wish.js';
	import {
		evaluationAttention,
		evaluationUnAttention
	} from '@/api/yuanshi/evaluate.js';
	import {
		handleImg
	} from '@/utils/oss.js';
	export default {
		components: {
			xSwiper
		},
		props: {
			arr: {
				type: Array,
				default: () => {
					return [];
				}
			},
			info: {
				type: Object,
				default: () => {
					return {};
				}
			},
			type: {
				type: String,
				default: ''
			}
		},
		watch: {
			arr(a, b) {
				this.cropImage(a);
			},
			info(a, b) {
				this.infos = a;
			}
		},
		data() {
			return {
				infos: this.info,
				dotIdx: 0,
				swiper: []
			};
		},
		methods: {
			cropImage(arr) {
				if (arr.length) {
					this.swiper = [];
					let info = [];
					this.copyImage = arr;
					arr.forEach(async (item, index) => {
						let res = await handleImg(item, {
							w: 750,
							h: 1000,
							q: 80,
							type: 'resize',
							m: 'fill'
						});
						if (res) {
							this.swiper.push(res);
							info.push(res);
						} else {
							this.swiper.push('');
							info.push('');
						}
					});
				}
			},
			goPages(path, type) {
				this.$navigator(path, type);
			},
			dotChange(idx) {
				this.dotIdx = idx;
			},
			btnAttention() {
				const {
					id,
					is_follow,
					follow_test_number
				} = this.info;
				let request = '';
				if (is_follow) {
					if (this.type === 'wish') {
						request = wishUnAttention(id);
					} else {
						request = evaluationUnAttention(id);
					}
					request.then(res => {
						this.infos.is_follow = false;
						this.infos.follow_test_number = follow_test_number > 1 ? follow_test_number - 1 : 0;
						this.$showToast('已取消');
					});
				} else {
					if (this.type === 'wish') {
						request = wishAttention(id);
					} else {
						request = evaluationAttention(id);
					}
					request.then(res => {
						this.infos.is_follow = true;
						this.infos.follow_test_number = follow_test_number + 1;
						this.$showToast('关注成功');
					});
				}

				// this.$emit('btnAttention');
			},
			swiperClick(item) {
				console.log(item)
				item = item.split('?')[0]
				uni.previewImage({
					urls: this.copyImage,
					current: item //地址需为https
				});
			}
		},
		mounted() {
			// this.cropImage(this.arr);
			// this.arr.forEach((item,index)=>{

			// })
		}
	};
</script>

<style lang="scss">
	.swiper {
		position: relative;

		// background-image: url('https://shop.arthorize.com/wximage/bg.png');
		.wrap {
			color: #fff;
			padding: 50rpx 48rpx;
			bottom: 0rpx;
			background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.46) 100%);

			image {
				width: 100%;
				height: 100%;

				border-radius: 4rpx;
			}

			.dot {
				width: 96rpx;
				height: 44rpx;
				border: 1rpx solid #ffffff;
				border-radius: 32rpx;
				font-size: 24rpx;
			}

			.name {
				font-size: 56rpx;
				margin-top: 40rpx;
				margin-bottom: 12rpx;
			}

			.time {
				font-size: 24rpx;
			}

			.image {
				font-size: 28rpx;

				image {
					width: 42rpx;
					margin-right: 16rpx;
				}
			}
		}
	}
</style>
