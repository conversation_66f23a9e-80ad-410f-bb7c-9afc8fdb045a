<template>
<!-- 	<view class="wrap_vip">
		<view class="vip_nav flex flex_align_center flex_between">
			<view class="nav_l flex flex_align_center">
				<view class="vip"><image src="@/static/images/yuanshi/vip.png" mode="widthFix"></image></view>
				<text class="txt ">着调儿卡</text>
				<text class="txt2  font_size20">开通即享全场平均5折优惠</text>
			</view>
			<view class="nav_r " @click="goPages('/pages/yuanshi/vip/equity')">
				<button class="flex_line_height"><text class="font_size20">查看权益</text></button>
			</view>
		</view>
	</view> -->
</template>

<script>
export default {
	props: {
		value: {
			type: '',
			default: ''
		}
	},
	components: {},
	data() {
		return {};
	},
	methods: {
		goPages(path, type) {
			this.$navigator(path, type);
		}
	},
	mounted() {},
	onLoad(option) {}
};
</script>

<style scoped lang="scss">
.wrap_vip {
	.vip_nav {
		// height: 56rpx;
		margin: 0 28rpx;
		padding: 0rpx 20rpx ;
		font-size: 28rpx;
		.nav_l {
			.vip {
				image {
					width: 95rpx;
				}
			}
			.txt {
				font-weight: 600;
				font-size: 28rpx;
				color: #333333;
				margin-left: 12rpx;
			}
			.txt2 {
				color: #999999;
			}
		}
		.nav_r {
			button {
				width: 120rpx;
				height: 40rpx;
				border: 2rpx solid #ff5656;
				border-radius: 14rpx;
				color: #ff5656;
			}
		}
	}
}
</style>
