<template>
	<view>
		<xSwiper :arr="banner" :autoplay="true" :interval="3000" :dots="false" padding="20rpx 10rpx 60rpx 10rpx"
			@swiperClick="swiperClick" srcName="pic" :displayMultipleItems="1" height="360rpx" previousMargin="30rpx"
			nextMargin="30rpx"></xSwiper>
	</view>
</template>

<script>
	import xSwiper from '@/components/x-swiper/x-swiper.vue';
	import {
		regHref
	} from '@/utils/validate.js';
	import {
		ossImgParams
	} from '@/utils/oss.js';
	import {
		openChannelsActivity,
		openChannelsLive,
		getChannelsLiveInfo
	} from '@/utils/channelsLive.js'
	export default {
		props: {
			arr: {
				type: Array,
				default () {
					return [];
				}
			}
		},
		components: {
			xSwiper
		},
		watch: {
			arr(a, b) {
				this.calcBanner(a);
			}
		},
		data() {
			return {
				banner: []
			};
		},
		methods: {
			calcBanner(arr1) {
				if (!arr1.length) return;
				let arr = JSON.parse(JSON.stringify(arr1));

				let ossResize = ossImgParams({
					w: 710,
					h: 300,
					q: 80
				});
				arr.forEach((item, index) => {
					item.pic = item.pic + ossResize;
				});
				this.banner = arr;
			},
			goPages(path, type) {
				this.$navigator(path, type);
			},
			swiperClick(item) {
				let {
					feedId = '', type, finderUserName = '', nonceId = '', wap_url = '', url = '' , toutiao_url = ''
				} = item;
                
				if (url) {
					url = url.trim();
				}
				if (wap_url) {
					wap_url = wap_url.trim();
				}
                if (toutiao_url) {
                	toutiao_url = toutiao_url.trim();
                }
				if (type === 0) {
					// #ifndef MP-TOUTIAO
					// 内部跳转
					if (url.indexOf('/pages') > -1) {
					
					} else {
						return this.$showToast('内部地址配置错误');
					}
					// #endif
                    // #ifdef MP-TOUTIAO
                    if (toutiao_url.indexOf('/pages') > -1) {
                    
                    } else {
                    	return this.$showToast('内部地址配置错误');
                    }
                    
                    // #endif
                    
				} else if (type === 1) {
                    // #ifndef MP-TOUTIAO
                    // 公众号跳转
                    if (regHref(wap_url)) {
                        url = '/pages/webview/webview?url=' + wap_url;
                    } else {
                        return this.$showToast('公众号地址配置错误');
                    }
                    // #endif
                     // #ifdef MP-TOUTIAO
                     if (toutiao_url.indexOf('/pages') > -1) {
                     
                     } else {
                        return this.$showToast('内部地址配置错误');
                     }
                     
                     // #endif
				} else if (type === 2) {
					// 视频号
					openChannelsActivity(finderUserName, feedId, wap_url).then(res => {
						if (res.xtype === 'mp') {
							this.$navigator('/pages/webview/webview?type=mp&&url=' + wap_url);
						}
					}).catch(err => {
						console.log(err)
						const {errCode=0,errMsg} = err
						this.$showToast(errCode===5? '已取消':'视频号获取失败');
					})

					return
				} else if (type === 3) {
					// 视频号直播间
					// #ifdef MP
					getChannelsLiveInfo(finderUserName).then(res => {
						if (res.status === 1) {
							this.$showToast('直播还在准备中');
						} else if (res.status === 2) {
							openChannelsLive(finderUserName, res.feedId, res.nonceId).then(res1 => {

							})
						} else {
							this.$showToast('直播已结束');
						}
					}).catch(err => {
						console.log(err)
						this.$showToast('直播间获取失败');
					})
					// #endif
					// #ifdef H5
					if (wap_url.indexOf('weixin://') > -1) {
						this.$navigator('/pages/webview/webview?type=mp&&url=' + wap_url);
					} else {
						uni.showModal({
							title: '提示',
							content: '请前往微信搜索 着调 小程序',
							success: function(res) {
								if (res.confirm) {
									resolve('用户点击确定');
								} else if (res.cancel) {
									reject('用户点击取消');
								}
							}
						});
					}

					// #endif

					return
				} else {
					this.$showToast('其它情况');
				}
				// #ifdef MP-TOUTIAO
				this.goPages(toutiao_url);
				// #endif
                // #ifndef MP-TOUTIAO
                this.goPages(url);
                // #endif
				this.$emit('swiperClick', item);
			}
		},
		mounted() {
			this.calcBanner(this.arr)
		},
		onLoad(option) {}
	};
</script>

<style scoped lang="scss">
	::deep .swiper_item {
		image {
			box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
		}
	}
</style>
