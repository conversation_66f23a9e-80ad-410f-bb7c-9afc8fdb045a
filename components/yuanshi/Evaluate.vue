<template>
	<view class="evaluate">
<!-- 		<xScrollView :arr="info.fastList" height="420rpx" lineWidth="0" padding="0">
			<template v-slot="{ item }">
				<view class="swiper-slide" @click="goPages(``)">
					<view class="img-box"><image :src="item.pic" mode="aspectFill" alt="img"></image></view>
					<view class="info line1">
						<view class="title text_ellipsis">理候选据七己层者西家须响量较参难前你据实品线低</view>
						<view class="price">￥ 1000</view>
					</view>
				</view>
			</template>
		</xScrollView> -->
	</view>
</template>

<script>
import { getHomeData, getShare, follow } from '@/api/public';
import xScrollView from '@/components/x-scroll-view/x-scroll-view.vue';
export default {
	components: {
		xScrollView
	},
	data() {
		return {
			info: {
				fastList: [],
				bastBanner: [],
				firstList: [],
				bastList: []
			}
		};
	},
	mounted(option) {
		let that = this;
		getHomeData().then(res => {
			// that.$set(that, 'banner', res.data.banner);
			that.$set(that, 'info', res.data.info);
		});
	}
};
</script>

<style lang="scss" scoped>
.evaluate {
	// border: 1px solid red;
	// border-radius: 20rpx;
	// padding: 20rpx;
	// margin: 40rpx 0;
	// box-shadow: 10rpx 10rpx 8rpx #888888;
	// view {
	// 	min-width: 30%;
	// 	height: 300rpx;
	// 	border: 1px solid red;
	// }
	.text_ellipsis {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	.swiper-slide {
		width: 304rpx;
		margin: 32rpx 32rpx 0 0;
		background: #fff;
		.img-box {
			width: 100%;
			height: 240rpx;
			image {
				width: 100%;
				height: 100%;
				border-radius: 6rpx 6rpx 0 0;
			}
		}
		.info {
			margin-top: 20rpx;
			padding:0 8rpx 20rpx 8rpx;
			line-height: normal;
			.title {
				font-size: 28rpx;
				font-weight: 400;
				color: #101010;
				margin: 24rpx 0 20rpx 0;
			}

			.price {
				font-size: 24rpx;
				transform: scale(0.83);
				transform-origin: left;
				font-weight: 400;
				
color: #EFB039;
			}
		}
	}
}
</style>
