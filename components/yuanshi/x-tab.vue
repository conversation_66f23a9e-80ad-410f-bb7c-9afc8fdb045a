<template>
    <view class="yxtab flex flex_around">
        <view class="item " :class="{ active: tabIdx === index }" v-for="(item, index) in arr" :key="index"
            @click="tabClick(item, index)">
            <view class="" :style="{ padding: padding, textAlign: align }">
                <view class="num font_size20" v-if="item.num>-1">{{ item.num }}</view>
                <view class="label">{{ item.label }}
                    <view class="dots" v-if="item.label == '我的评论' && dotsNum"></view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    export default {
        props: {
            arr: {
                type: Array,
                default () {
                    return [{
                        label: '我的关注',
                        num: 10
                    }, {
                        label: '我的心愿',
                        num: 10
                    }, {
                        label: '我的跟随',
                        num: 10
                    }];
                }
            },
            padding: {
                type: String,
                default: '0 0rpx 0 32rpx'
            },
            align: {
                type: String,
                default: 'left'
            },
            active: {
                type: Number,
                default: 0
            },
            reCancel: {
                type: Boolean, //再次点击取消
                default: false
            },
            dotsNum: {
                type: Number,
                default: 0
            }
        },
        components: {},
        watch: {
            arr(a, b) {
                console.log('监听tablist', a)
            }
        },
        data() {
            return {
                tabIdx: this.active
            };
        },
        methods: {
            goPages(path, type) {
                this.$navigator(path, type);
            },
            init() {
                this.tabIdx = -1;
            },
            tabClick(item, index) {
                let reClick = false;
                if (this.tabIdx === index) {
                    reClick = true
                    if (this.reCancel) {
                        this.tabIdx = -1
                    } else {
                        return;
                    }
                } else {
                    this.tabIdx = index;
                }
                this.$emit('tabClick', item, reClick);
            }
        },
        mounted() {},
        onLoad(option) {}
    };
</script>

<style scoped lang="scss">
    .yxtab {
        background: #e9f1fb;
        margin-bottom: 50rpx;
        border-top: 2rpx solid #6BB4FF;
        border-bottom: 2rpx solid #6BB4FF;

        .item {
            flex: 1;
            color: #6BB4FF;
            padding: 24rpx 0 22rpx 0;

            >view {

                // padding: 0 74rpx 0 24rpx;
                .num {
                    transform-origin: left;
                }

                .label {
                    position: relative;
                    font-weight: bold;
                    font-size: 28rpx;
                    letter-spacing: 2rpx;

                    .dots {
                        position: absolute;
                        width: 24rpx;
                        height: 24rpx;
                        background: #ff5656;
                        border: 2rpx solid #ffffff;
                        border-radius: 50%;
                        right: 20rpx;
                        top: -12rpx;
                    }
                }
            }

            &.active {
                background: #f4f4f4;

                .label {
                    color: #333333;
                }
            }

            &:not(:last-child) {
                >view {
                    border-right: 2rpx solid #d8d8d8;
                }
            }
        }
    }
</style>
