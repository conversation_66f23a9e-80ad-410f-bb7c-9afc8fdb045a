<template>
	<view class="recom">
		<view class="tab flex flex_between flex_align_end">
			<view class="title">
				<view>{{ title }}</view>
				<view class="line" v-if="line"></view>
			</view>
			<!-- <view class="more" v-if="more"><text class="font_size20">查看更多</text></view> -->
		</view>
		<view class="scroll">
			<scroll-view scroll-x style="white-space:nowrap;" :scroll-with-animation="true">
				<view class="wrap" style="fontSize:30rpx">
<!-- 					<view class="item" >
						<view class="title">
							<view @click="goPages({product_id:28,type:0})" class="slot_wrap" style="line-height:normal">
								<view class="img"><image src="https://cshop.arthorize.com/attach/2021/12/c7358202112231427301277.jpg" alt="img" mode="aspectFill"></image></view>
								<view class="info line1">皮可童族+游戏主机</view>
								<view class="money">
									<text >价格 ￥2598.00</text>
								</view>
								<view class="wrap_b flex flex_align_center">
									<view class="span">
										<text>5.0</text>
									</view>
									<view class="image">
										<view class="bg" style="100%"></view>
									</view>
									<view class="num font_size20">
										1 人感兴趣
									</view>
								</view>
							</view>
						</view>
					</view> -->
					<view class="item" v-for="(item, index) in arr" :key="index" @click="swichTab(index, item)" :id="`t${index}`">
						<view class="title">
							<view @click="goPages(item)" class="slot_wrap" style="line-height:normal">
								<view class="img"><image :src="item.image" alt="img" mode="aspectFill"></image></view>
								<view class="info line1">{{ item.name }}</view>
								<view class="money">
									<text v-if="item.vip_price">价格 ￥{{ item.vip_price }}</text>
								</view>
								<view class="wrap_b flex flex_align_center">
									<view class="span" v-if="item.yw_index">
										<text>{{ item.yw_index }}</text>
									</view>
									<view class="image">
										<!-- <view class="bg"></view> -->
										<!-- <view class="bg" :style="{ width: ((item.yw_index>5?5:item.yw_index) / 5) * 100 + '%' }"></view> -->
										<view class="bg" style="100%"></view>
									</view>
									<view class="num font_size20">
										{{ item.type === 1 && (item.status === 3 || item.status === 2) ? item.want_to_test_number : item.follow_test_number }} 人感兴趣
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import { mapGetters } from 'vuex';
import { ossImgParams } from '@/utils/oss.js';
export default {
	props: {
		value: {
			type: '',
			default: ''
		},
		arr: {
			type: Array,
			default() {
				return [];
			}
		},
		title: {
			//显示title的字段
			type: String,
			default: '新品推荐'
		},
		more: {
			type: Boolean,
			default: true
		},
		line: {
			type: Boolean,
			default: true
		},
		ids: {
			type: Object,
			default() {
				return {};
			}
		}
	},
	computed: mapGetters(['userInfo']),
	data() {
		return {
			info: [],
			current: 0,
			scrollLeft: 0,
			leftArr: [],
			init: false
		};
	},
	watch: {
		arr(a, b) {
			// this.calcBanner(a)
		}
	},
	methods: {
		calcBanner(arr1) {
			if (!arr1.length) return;
			let arr = JSON.parse(JSON.stringify(arr1));
			let ossResize = ossImgParams({ w: 710, h: 260, q: 80 });
			arr.forEach((item, index) => {
				item.image = item.image + ossResize;
			});
			this.info = arr;
		},
		goPages(item) {
			if (item.id === parseInt(this.ids.wishId)) {
				return this.$showToast('已在当前页');
			}
			let page = '';
			if (item.type === 0) {
				page = `/pages/shop/GoodsCon?id=${item.product_id}`;
			} else {
				// page = '/pages/yuanshi/wish/detail?wid=' + item.id + '&pid=' + item.product_id;
				page = '/pages/yuanshi/evaluate/detail?wid=' + item.id + '&pid=' + item.product_id;
			}
			this.$navigator(page);
		},
		uniSelectorQueryInfo(selector, _this) {
			return new Promise((resolve, reject) => {
				const query = uni.createSelectorQuery().in(_this);
				query
					.select(selector)
					.boundingClientRect(res => {
						// 获取节点坐标
						resolve(res);
					})
					.exec();
			});
		},
		swichTab(idx, item) {
			if (this.current === idx) {
				return;
			}
			this.current = idx;
			this.scrollLeft = this.leftArr[idx] - this.leftArr[0];
		},
		async initScrollInfo() {
			let leftArr = [];
			for (let i = 0; i < this.arr.length; i++) {
				const { left } = await this.uniSelectorQueryInfo(`#t${i}`, this);
				leftArr.push(left);
			}
			this.leftArr = leftArr;
			if (this.current > 0) {
				this.scrollLeft = this.leftArr[this.current] - this.leftArr[0];
			}
		}
	},
	beforeUpdate() {
		const _this = this;
		if (this.init) {
			return;
		}
		this.$nextTick(function() {
			_this.current = _this.active;
			_this.initScrollInfo();
			_this.init = true;
		});
	},
	mounted() {
		this.calcBanner(this.arr);
	},
	onLoad(option) {}
};
</script>

<style scoped lang="scss">
.recom {
	white-space: nowrap;
	overflow: hidden;
	image {
		width: 100%;
		height: 100%;
	}
	.tab {
		margin-bottom: 30rpx;
		.title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333333;
			padding-left: 12rpx;
			.line {
				width: 132rpx;
				height: 8rpx;
				background: linear-gradient(90deg, #ff5a73 0%, #ffa969 100%);
				border-radius: 2rpx;
			}
		}
		.more {
			color: #999999;
		}
	}
}
.scroll {
	.wrap {
		display: flex;
		.item {
			padding: 0 20rpx 0 0;
			.title {
				height: 410rpx;
				line-height: 410rpx;
			}
			.line {
				transition: all 0.15s linear;
				height: 4rpx;
				margin: 0 auto;
			}
			&.on {
				.line {
					background-color: red;
				}
			}
		}
	}
}
.slot_wrap {
	display: inline-block;
	width: 310rpx;
	// margin-right: 20rpx;
	border-radius: 12rpx;
	image {
		width: 100%;
		height: 100%;
	}
	.img {
		width: 310rpx;
		height: 260rpx;
		image {
			border-radius: 30rpx;
		}
	}
	.info {
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
		padding: 20rpx 10rpx 0 0rpx;
	}
	.money {
		padding: 0 10rpx 16rpx 0rpx;
		font-size: 24rpx;

		color: #666666;
		// font-weight: bold;
	}
	.wrap_b {
		.span {
			width: 60rpx;
			height: 36rpx;
			border-radius: 14rpx;
			line-height: 33rpx;
			text-align: center;
			// font-size: 22rpx;
			color: #ffffff;
			background: #ff5656;
			text {
				@include font_size(22);
			}
		}
		.image {
			position: relative;
			width: 28rpx;
			margin: 0 8rpx;
			height: 28rpx;
			.bg {
				position: absolute;
				width: 100%;
				height: 100%;
				top: 0;
				background-repeat: repeat-x !important;
				overflow: hidden;
				&:first-child {
					background: url('@/static/images/yuanshi/star11.png');
					background-size: 28rpx 28rpx;
				}
				&:last-child {
					background: url('@/static/images/yuanshi/star.png');
					background-size: 28rpx 28rpx;
				}
			}
		}
		.num {
			transform-origin: left;
			color: #999999;
		}
	}
}
</style>
