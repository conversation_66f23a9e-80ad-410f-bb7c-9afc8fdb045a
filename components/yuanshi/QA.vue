<template>
	<view class="questions">
		<template v-if="type === 'radio'">
			<view>
				<radio-group @change="groupChange">
					<label class="label acea-row row-middle " v-for="(item, index) in arr" :key="item.value">
						<view><radio :value="`${item.value}`" :checked="item.checked" /></view>
						<view>{{ item.name }}</view>
					</label>
				</radio-group>
				<view v-if="isTextarea">
					<view class="textarea" v-show="arr[arr.length - 1].checked"><textarea :value="textareaVal" placeholder="" @input="inputChange"/></view>
				</view>
			</view>
		</template>
		<template v-if="type === 'checkbox'">
			<view>
				<checkbox-group @change="groupChange">
					<label class="label acea-row row-middle" v-for="(item, index) in arr" :key="item.value">
						<view><checkbox :value="`${item.value}`" :checked="item.checked" /></view>
						<view>{{ item.name }}</view>
					</label>
				</checkbox-group>
				<view v-if="isTextarea">
					<view class="textarea" v-show="arr[arr.length - 1].checked"><textarea :value="textareaVal" placeholder="" @input="inputChange"/></view>
				</view>
			</view>
		</template>
	</view>
</template>

<script>
export default {
	props: {
		type: {
			type: String,
			default: 'radio'
		},
		isTextarea: {
			type: Boolean,
			default: true
		},
		arr: {
			type: Array,
			default: () => {
				return [];
			}
		},
		mark:{ //同一组件在同一页面多次引用 防止最后取值重复
			type: String,
			default: 'radio1'
		}
	},
	components: {},
	data() {
		return {
			textareaVal:''
		};
	},
	methods: {
		goPages(path, type) {
			this.$navigator(path, type);
		},
		groupChange(e) {
			let { value } = e.detail,
				arr = this.arr,
				isArr = Array.isArray(value),
				checked = [];
			for (var i = 0, lenI = arr.length; i < lenI; ++i) {
				const item = arr[i];
				if (isArr) {
					if (value.includes(`${item.value}`)) {
						this.$set(item, 'checked', true);
					} else {
						this.$set(item, 'checked', false);
					}
				} else {
					if (value === `${item.value}`) {
						this.$set(item, 'checked', true);
					} else {
						this.$set(item, 'checked', false);
					}
				}

				if (item.checked) {
					checked.push(item);
				}
			}
			console.log('checked', checked);
			this.$emit('change', this.type,this.mark, checked);
			// console.log(this.current);
		},
		inputChange(e){
			let { value } = e.detaill;
			this.$emit('input', this.type,this.mark,value);
		}
	},
	mounted() {},
	onLoad(option) {}
};
</script>

<style scoped lang="scss">
.questions {
	.label{
		>view{
			margin:10rpx 20rpx 0 0;
		}
	}
	.textarea {
		border: 1px solid red;
		padding: 10rpx;
		margin-left: 44rpx;
		width: 500rpx;
		height: 200rpx;
		textarea {
			width: 100%;
			height: 100%;
		}
	}
}
</style>
