<template>
    <view class="yxlist">
        <block v-for="(item, index) in list" :key="index">
        <view class="scroll_view">
            <view class="item flex" :id="`t${index}`"
                @click="goPages(item)">
                <view class="item_l relative">
                    <image :src="item.image" alt="活动回顾" mode="aspectFill"></image>
                </view>
                <view class="item_r">
                    <view class="r_title">{{item.name}}</view>
                    <view class="r_time">
                        <text v-if="item.activity_time">{{item.activity_time}}</text>
                    </view>
                    <view class="r_desc">
                        <text v-if="item.info">{{item.info}}</text>
                    </view>
                    <view class="r_bottom flex flex_between">
                        <!-- #ifndef MP-TOUTIAO -->
                        <view class="price" v-show="item.price">￥{{item.price}}</view>
                        <!-- #endif -->
                        <view class="limit">
                            <text v-if="item.activity_type == 2">限制人数：{{item.limit_numbers}}</text>
                            <text v-if="item.activity_type == 1">关注人数：{{item.follow_test_number }}</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <!-- <view v-if="item.activity_type == 1" class="item relative" @click="goPages(item)">
            <view class=" bg">
                <image :src="item.image + ossResize" mode="aspectFill"></image>
            </view>
            <view class="wrap absolute flex flex_column flex_between ">
                <view class="wrap_t">
                    <view class="title flex flex_align_center flex_between">
                        <view class="name">{{ item.name }}</view>
                        <view class="status" :class="'status'+item.status" v-if="status">
                            <text v-if="item.status === 1">已达成</text>
                            <text v-if="item.status === 2">未审核</text>
                            <text v-else-if="item.status === 3">待测评</text>
                            <text v-else-if="item.status === 4">已测评</text>
                        </view>
                    </view>
                    <view class="price"
                        v-if="(item.status === 1 || item.status === 4) && (item.vip_price || item.price)">
                        <text v-if="item.vip_price">价格 ￥{{ item.vip_price }}</text>
                    </view>
                </view>
                <view class="wrap_b flex flex_between flex_align_center" @click.stop>
                    <view class="b_l flex flex_align_center">
                        <template v-if="item.yw_index">
                            <view class="span">
                                <text>{{ item.yw_index.toFixed(1) }}</text>
                            </view>
                            <view class="image">
                                <view class="bg"></view>
                            </view>
                        </template>
                        <template v-else>
                            <view class="span no"><text>0.0</text></view>
                            <view class="image">
                                <view class="bg"></view>
                                <view class="bg" style="width: 0;"></view>
                            </view>
                        </template>
                        <view class="num font_size20">
                            {{ item.type === 1 && (item.status === 3 || item.status === 2 || status) && !status1 ? item.want_to_test_number + ' 人想测' : item.follow_test_number + ' 人关注' }}
                        </view>
                    </view>
                    <view class="b_r flex flex_align_center">
                        <button
                            v-if="btn && item.type === 1 && (item.status === 3 || item.status === 2 || status) && !status1"
                            @click.stop="btnClick(item, index)" :disabled="item.is_want_to_test"
                            :style="{ color: item.is_want_to_test ? '#d2d2d2' : '' }">
                            <slot>{{ item.is_want_to_test ? '已想测' : '想测 +1' }}</slot>
                        </button>
                        <view class="image" @click.stop="btnAttention(item, index)"
                            v-if="item.type === 2 || (item.type === 1 && item.status === 1 && !status) || (item.type === 1 && item.status === 4 && !status) || status1">
                            <image src="@/static/images/yuanshi/love.png" mode="widthFix" v-if="item.is_follow"></image>
                            <image src="@/static/images/yuanshi/love_1.png" mode="widthFix" v-else></image>
                        </view>
                    </view>
                </view>
            </view>
        </view> -->
        </block>
        
        
        
        
    </view>
</template>

<script>
    import {
        wishWant,
        wishUnWant,
        wishAttention,
        wishUnAttention
    } from '@/api/yuanshi/wish.js';
    import {
        evaluationAttention,
        evaluationUnAttention
    } from '@/api/yuanshi/evaluate.js';
    import {
        mapGetters
    } from 'vuex';
    import {
        ossImgParams
    } from '@/utils/oss.js';
    import {
        debounce
    } from '@/utils/common.js';
    export default {
        props: {
            btn: {
                type: Boolean,
                default: false
            },
            vip: {
                type: Boolean,
                default: true
            },
            status: {
                //个人中心是否显示状态
                type: Boolean,
                default: false
            },
            status1: {
                //个人主页是否显示状态
                type: Boolean,
                default: false
            },
            attention: {
                //个人中心是否显示状态
                type: Boolean,
                default: false
            },
            other: {
                //其它操作
                type: Boolean,
                default: false
            },
            type: {
                type: String,
                default: 'evaluate'
            },
            arr: {
                type: Array,
                default () {
                    return [];
                }
            }
        },
        computed: mapGetters(['userInfo']),
        watch: {
            arr(a, b) {
                this.list = a;
            }
        },
        data() {
            return {
                list: this.arr,
                ossResize: ossImgParams({
                    w: 710,
                    h: 300,
                    m: 'fill',
                    q: 80
                })
            };
        },
        methods: {
            goPages(item) {
                if(item.activity_type == 1){
                    let page = '';
                    if (item.type === 1) {
                        page = `/pages/shop/GoodsCon?id=${item.product_id}`;
                    } else {
                        // page = '/pages/yuanshi/wish/detail?wid=' + item.id + '&pid=' + item.product_id;
                        page = '/pages/yuanshi/evaluate/detail?wid=' + item.id + '&pid=' + item.product_id;
                    }
                    this.$navigator(page);
                }
                if(item.activity_type == 2){
                    let page = '';
                    page = `/pages/ycommunity/shop/detail?id=${item.id}`;
                    this.$navigator(page);
                }
            },
            btnClick(item, index) {
                const {
                    is_want_to_test,
                    id,
                    type,
                    want_to_test_number
                } = item;
                if (type === 1) {
                    if (is_want_to_test) {
                        wishUnWant(id).then(res => {
                            this.list[index].is_want_to_test = false;
                            this.list[index].want_to_test_number = want_to_test_number > 1 ?
                                want_to_test_number - 1 : 0;
                            this.$showToast('已取消');
                        });
                    } else {
                        wishWant(id).then(res => {
                            this.list[index].is_want_to_test = true;
                            this.list[index].want_to_test_number = want_to_test_number + 1;
                            this.$showToast('想测+1');
                        });
                    }
                } else {
                    this.btnAttention(item, index);
                }
            },
            btnAttention(item, index) {
                const {
                    id,
                    is_follow,
                    status,
                    product_id,
                    type,
                    follow_test_number
                } = item;
                console.log('关注相关操作', type, status, item);
                let request = '';
                if (is_follow) {
                    if (type === 1) {
                        if (status === 3 || this.attention) {
                            request = wishUnAttention(id);
                        } else {
                            request = evaluationUnAttention(product_id);
                        }
                    } else if (type === 2) {
                        request = evaluationUnAttention(product_id);
                    }
                    request.then(res => {
                        this.list[index].is_follow = false;
                        this.list[index].follow_test_number = follow_test_number > 1 ? follow_test_number - 1 :
                            0;
                        this.$showToast('已取消');
                        console.log(this.list[index]);
                        if (this.other) {
                            this.list.splice(index, 1);
                        }

                        this.$emit('change', false);
                    });
                } else {
                    if (type === 1) {
                        if (status === 3 || this.attention) {
                            request = wishAttention(id);
                        } else {
                            request = evaluationAttention(product_id);
                        }
                    } else if (type === 2) {
                        request = evaluationAttention(product_id);
                    }
                    request.then(res => {
                        this.list[index].is_follow = true;
                        this.list[index].follow_test_number = follow_test_number + 1;
                        this.$showToast('关注成功');

                        console.log(this.list[index]);

                        this.$emit('change', true);
                    });
                }
            }
        },
        mounted() {},
        onLoad(option) {}
    };
</script>

<style scoped lang="scss">
    .yxlist {
        
        .scroll_view {
            // height: 600rpx;
        
            .item {
                margin-bottom: 40rpx;
        
                .item_l {
                    width: 310rpx;
                    height: 260rpx;
                    margin-right: 40rpx;
        
                    image {
                        border-radius: 30rpx;
                    }
                }
        
                .item_r {
                    flex: 1;
                    text-align: left;
        
                    .r_title {
                        @include show_line(1);
                        font-weight: 700;
                        font-size: 28rpx;
                        color: #333333;
                    }
        
                    .r_time {
                        font-size: 24rpx;
                        color: #666;
                    }
        
                    .r_desc {
                        @include font_size;
                        transform-origin: left;
                        color: #999999;
                        margin: 10rpx 0 10rpx 0;
                    }
        
                    .r_bottom {
                        .price {
                            font-size: 28rpx;
                            color: #333333;
                        }
        
                        .limit {
                            @include font_size;
                            @include show_line(1);
                            color: #999999;
                        }
                    }
                }
        
                .status {
                    font-size: 24rpx;
                    color: #ffffff;
                    bottom: 0;
                    right: 0;
                    width: 142rpx;
                    height: 62rpx;
                    text-align: center;
                    line-height: 62rpx;
                    opacity: 1;
                    background: linear-gradient(180deg, #ff6f6f, #ff5656);
                    border-radius: 30rpx 0px 30rpx 0px;
                    box-shadow: 0px 6rpx 12rpx 0px rgba(0, 0, 0, 0.16);
                }
            }
        }
        
        image {
            width: 100%;
            height: 100%;
        }

        .item {
            padding: 0;
            height: 240rpx;
            margin-bottom: 20rpx;

            .bg {
                height: 100%;

                image {
                    border-radius: 30rpx;

                    &:after {
                        content: '';
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        left: 0;
                        top: 0;
                        border-radius: 30rpx;
                        background: linear-gradient(270deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.46) 100%);
                        // filter: blur(2px);
                    }
                }
            }

            .wrap {
                padding: 24rpx 26rpx 24rpx 30rpx;
                top: 0;
                height: 100%;
                color: #fff;

                .wrap_t {
                    .title {
                        font-size: 32rpx;
                        font-weight: 400;

                        .name {
                            @include show_line;
                        }

                        .status {
                            position: absolute;
                            width: 142rpx;
                            height: 62rpx;
                            right: -2rpx;
                            top: -2rpx;
                            border-radius: 0 30rpx 0 30rpx;
                            line-height: 62rpx;
                            text-align: center;
                            color: #ffffff;
                            padding: 0 10rpx;
                            font-size: 24rpx;
                            font-weight: 400;
                            box-shadow: 0px 0px 10rpx rgba(0, 0, 0, 0.16);

                            &.status1 {
                                background: #ff5656;
                            }

                            &.status2 {
                                background: #C0C0C1;
                            }

                            &.status3 {
                                background: #ff5656;
                            }

                            &.status4 {
                                background: #ff5656;
                            }
                        }
                    }

                    .price {
                        font-size: 24rpx;
                        line-height: 34rpx;
                        font-weight: 400;
                    }
                }

                .wrap_b {
                    .b_l {
                        .span {
                            width: 60rpx;
                            height: 36rpx;
                            border-radius: 14rpx;
                            line-height: 33rpx;
                            text-align: center;
                            // font-size: 22rpx;
                            color: #ffffff;
                            background: #ff5656;

                            text {
                                @include font_size(22);
                            }

                            &.no {
                                background: #d2d2d2;
                            }
                        }

                        .image {
                            position: relative;
                            width: 28rpx;
                            height: 28rpx;
                            margin: 0 16rpx 0 8rpx;

                            .bg {
                                position: absolute;
                                width: 100%;
                                top: 0;
                                background-repeat: repeat-x !important;
                                overflow: hidden;

                                &:first-child {
                                    background: url('@/static/images/yuanshi/star1.png');
                                    background-size: 28rpx 28rpx;
                                }

                                &:last-child {
                                    background: url('@/static/images/yuanshi/star.png');
                                    background-size: 28rpx 28rpx;
                                }
                            }
                        }

                        .num {
                            transform-origin: left;

                            color: #d2d2d2;
                        }
                    }

                    .b_r {
                        button {
                            width: 120rpx;
                            height: 36rpx;
                            line-height: 36rpx;
                            font-size: 24rpx;
                            // margin-right: 20rpx;

                            color: #50506f;
                            background: #fff;
                        }

                        .image {
                            width: 36rpx;
                            height: 36rpx;

                            // padding: 0 7rpx;
                            // line-height: 36rpx;
                            // border-radius: 10rpx;
                            // background: #f1f1f3;
                            image {
                                // width: 22rpx;
                                width: 36rpx;
                                height: 36rpx;
                            }
                        }
                    }
                }
            }
        }
    }
    .margin-bot20 {
        margin-top: 40rpx;
    }
</style>
