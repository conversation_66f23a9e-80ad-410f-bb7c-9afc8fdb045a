<template>
	<view class="xsearch">
		<view :class="[type, { fixed: fixed }]">
			<view class="flex flex_align_center relative"
				:style="{ color: color, marginTop: type === 'menu' ? menuButtonTop + 'px' : '0rpx' }">
				<view class="input flex flex_align_center"
					:style="{ borderRadius: radius, height: height, backgroundColor: bgColor }">
					<image src="@/static/images/yuanshi/search.png" mode="widthFix"></image>
					<input type="text" v-model.trim="input" @focus="inputFocus" @blur="inputBlur" :focus="focus"
						:placeholder="placeholder" :confirm-type="confirmType" placeholder-style="font-size:24rpx;color:#ccc"
						:disabled="disabled" @confirm="search" @input="inputChange" />
					<image src="@/static/images/yuanshi/fail.png" mode="widthFix" @click="clearinput"
						style="width: 48rpx;margin-right: 0;" v-if="clear"></image>
				</view>
				<view v-if="btn && type === 'search'" class="btn" @click="search">搜索</view>
			</view>
			<view class="history relative" v-if="history">
				<view class="flex absolute">
					<view class="input relative" :class="{active:historyShow}">
						<template v-if="historyArr.length">
							<view class="list flex flex_wrap ">
								<view v-for="(item,index) in historyArr" :key="index" class="item" @click="historyClick(item)">
									{{item}}</view>
							</view>
						</template>
						<template v-else>
							<view class="noresult">暂无搜索历史</view>
						</template>
						<view class="r_b" v-show="historyArr.length" @click="clearHistory">清除历史记录</view>
					</view>
					<!-- <view v-if="btn && type === 'search'" class="btn"></view> -->
				</view>

			</view>
		</view>
	</view>
</template>

<script>
    import {
        IS_TOUTIAO_PAY
    } from '@/config.js';
	const SEARCH_NAME = 'xseachHistory'
	export default {
		props: {
			value: {
				type: String,
				default: ''
			},
			type: {
				type: String,
				default: 'menu' // menu navbar中的搜索
			},
			placeholder: {
				type: String,
				default: '搜索你喜欢的活动 / 商品'
			},
			color: {
				type: String,
				default: '#000'
			},
			clear: {
				type: Boolean,
				default: false
			},
			focus: {
				type: Boolean,
				default: true
			},
			disabled: {
				type: Boolean,
				default: false
			},
			isLink: {
				type: Boolean,
				default: false
			},
			history: {
				type: Boolean,
				default: false
			},
			fixed: {
				type: Boolean,
				default: true
			},
			bgColor: {
				type: String,
				default: '#fff'
			},
			btn: {
				type: Boolean,
				default: true
			},
			confirmType: {
				type: String,
				default: 'send'
			},
			radius: {
				type: String,
				default: '26rpx'
			},
			height: {
				type: String,
				default: '80rpx'
			}
		},
		components: {},
		data() {
			return {
				menuButtonTop: 0,
				input: this.value,
				historyShow: false,
				historyArr: []
			};
		},
		watch: {
			value(a, b) {
				console.log(a);
				this.input = a;
			}
		},
		methods: {
            clearinput(){
                this.input = '';
                this.$emit('change', this.input);
            },
			search(e) {
				const {
					type,
					detail
				} = e;
				// if(type==='click'){
				// console.log(this.input)
				// }else{
				// 	console.log(detail.value)
				// }
				
				if(this.isLink){
					// 带搜索结果跳转
					this.goPages('/pages/yuanshi/search/search?value='+this.input)
					return;
				}
				
				if (!this.input) {
					return this.$showToast('输入内容为空')
				}
				if (this.history) {
					const storage = this.$storage;
					let getStorage = this.getSearchHistory();
					let idx = getStorage.indexOf(this.input);
					if (idx > -1) {
						getStorage.splice(idx, 1)
					}
					getStorage.unshift(this.input)
					storage.set(SEARCH_NAME, getStorage);
					this.setSearchHistory();
				}
				this.$emit('search', this.input);
			},
			historyClick(val){
				this.input = val;
				this.$emit('search',val);
			},
			inputChange() {
				this.$emit('change', this.input);
			},
			inputFocus() {
				this.setSearchHistory();
				this.historyShow = true;
				this.$emit('focus');
			},
			inputBlur() {
				this.historyShow = false;
			},
			goPages(path, type) {
				this.$navigator(path, type);
			},
			getSearchHistory() {
				return this.$storage.get(SEARCH_NAME) || [];
			},
			setSearchHistory() {
				this.historyArr = this.getSearchHistory()
			},
			clearHistory() {
				const storage = this.$storage;
				storage.set(SEARCH_NAME, []);
				this.historyArr = []
			}
		},
		mounted() {
			// #ifdef MP
			if (this.type === 'menu') {
				let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
				this.menuButtonTop = menuButtonInfo.top;
			}

			// #endif
		},
		onLoad(option) {
			this.setSearchHistory()
		}
	};
</script>

<style lang="scss" scoped>
	.xsearch {
		width: 100%;

		.input {
			width: 100%;
			flex: 1;
			box-sizing: border-box;
			// flex-wrap: nowrap;
			padding: 0 28rpx;
			font-size: 28rpx;
			color: #bbb;

			box-shadow: 0px 0px 40px rgba(107, 127, 153, 0.2);

			image {
				width: 35rpx;
				height: 35rpx;
				margin-right: 20rpx;
			}

			input {
				width: 100%;
				font-size: 24rpx;
			}
		}

		.fixed {
			position: fixed;
			left: 0;
			top: 0;
			width: 100%;
			z-index: 999;
		}

		.menu {
			box-sizing: border-box;
			padding: 0rpx 26rpx 10rpx 26rpx; //距离顶部的位移，在小程序端完全动态计算，css中不参与
			height: calc(var(--status-bar-height) + 98rpx);

			.input {
				/* #ifdef MP */
				// width: 500rpx;
				width: 61%;
				margin-left: 52rpx; //预留左侧返回箭头
				/* #endif */
				/* #ifdef H5 */
				// width: 680rpx;
				width: 91%;
				margin: 10rpx auto 0 auto;
				/* #endif */
			}
		}

		.search {
			box-sizing: border-box;
			// padding: 10rpx 26rpx;
		}

		.btn {
			margin-left: 20rpx;
			width: 120rpx;
			height: 80rpx;
			background: #ff5656;
			border-radius: 24rpx;
			line-height: 80rpx;
			font-size: 24rpx;
			font-family: PingFang SC, PingFang SC-Regular;
			font-weight: 400;
			text-align: center;
			color: #ffffff;
		}

		.history {
			.absolute {
				top: 0rpx;
				z-index: 999;
			}

			.input {
				height: 0;
				overflow: hidden;
				transition: height 0.3s linear;
				box-shadow: none;
				background: rgba($color: #fff, $alpha: 1);
				border-radius: 0px 0px 35px 35px;

				&.active {
					height: 400rpx;
				}

				.item {
					padding: 5rpx;
					margin: 10rpx;
					height: 40rpx;
					line-height: 60rpx;
				}
			}

			.r_b {
				position: absolute;
				right: 70rpx;
				bottom: 35rpx;
				font-size: 28rpx;
				color: #bbb;
			}

			.noresult {
				text-align: center;
				height: 400rpx;
				line-height: 400rpx;
			}
		}
	}
</style>
