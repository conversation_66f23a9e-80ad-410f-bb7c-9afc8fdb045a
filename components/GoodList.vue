<template>
  <view class="goodList">
    <view
      @click="goDetail(item)"
      v-for="(item, index) in goodList"
      :key="index"
      class="item acea-row row-between-wrapper"
    >
      <view class="pictrue">
				<image :src="item.image" mode="" alt="img" class="image"></image>
        <span
          class="pictrue_log pictrue_log_class"
          v-if="item.activity && item.activity.type === '1'"
          >秒杀</span
        >
        <span
          class="pictrue_log pictrue_log_class"
          v-if="item.activity && item.activity.type === '2'"
          >砍价</span
        >
        <span
          class="pictrue_log pictrue_log_class"
          v-if="item.activity && item.activity.type === '3'"
          >拼团</span
        >
      </view>
      <view class="underline">
        <view class="text">
          <view class="line1">{{ item.store_name }}</view>
          <view class="money font-color-red">
            ￥<span class="num">{{ item.price }}</span>
          </view>
          <view class="vip-money acea-row row-middle">
            <view class="vip" v-if="item.vip_price && item.vip_price > 0">
              ￥{{ item.vip_price || 0
              }}
							<image src="@/static/images/vip.png" mode="" alt="img" class="image"></image>
            </view>
            <span class="num">已售{{ item.sales }}{{ item.unit_name }}</span>
          </view>
        </view>
      </view>
      <view
        class="iconfont icon-gouwuche cart-color acea-row row-center-wrapper"
      ></view>
    </view>
  </view>
</template>
<script>
// import { goShopDetail } from "@/utils/order.js";
export default {
  name: "GoodList",
  props: {
    goodList: {
      type: Array,
      default: () => []
    },
    isSort: {
      type: Boolean,
      default: true
    }
  },
  data: function() {
    return {};
  },
  methods: {
    // 商品详情跳转
  goDetail(item) {
  	if (item.activity && item.activity.type === '1') {
  		this.$navigator(`/pages/activity/SeckillDetails?id=${item.activity.id}&time=${item.activity.time}&status=1`);
  	} else if (item.activity && item.activity.type === '2') {
  		this.$navigator(`/pages/activity/DargainDetails?id=${item.activity.id}`);
  	} else if (item.activity && item.activity.type === '3') {
  		this.$navigator(`/pages/activity/GroupDetails?id=${item.activity.id}`);
  	} else {
  		this.$navigator(`/pages/shop/GoodsCon?id=${item.id}`);
  	}
  }
  }
};
</script>
<style scoped lang="scss">
	.goodList .item {
		position: relative;
		padding-left: 30rpx;
		background-color: #fff;
	}
	
	.goodList .item .pictrue {
		width: 180rpx;
		height: 180rpx;
		position: relative;
	}
	
	.goodList .item .pictrue .image {
		width: 100%;
		height: 100%;
		border-radius: 6rpx;
	}
	
	.goodList .item .pictrue .numPic {
		position: absolute;
		left: 7rpx;
		top: 7rpx;
		width: 50rpx;
		height: 50rpx;
		border-radius: 50%;
	}
	
	.goodList .item .underline {
		padding: 30rpx 30rpx 30rpx 0;
		border-bottom: 1px solid #f5f5f5;
	}
	
	.goodList .item:nth-last-child(1) .underline {
		border-bottom: 0;
	}
	
	.goodList .item .text {
		font-size: 30rpx;
		color: #222;
		width: 488rpx;
		text-align: left;
	}
	
	.goodList .item .text .money {
		font-size: 26rpx;
		font-weight: bold;
		margin-top: 50rpx;
	}
	
	.goodList .item .text .money .num {
		font-size: 34rpx;
	}
	
	.goodList .item .text .vip-money {
		font-size: 24rpx;
		color: #282828;
		font-weight: bold;
		margin-top: 10rpx;
	}
	
	.goodList .item .text .vip-money .vip {
		margin-right: 22rpx;
	}
	
	.goodList .item .text .vip-money .image {
		width: 46rpx;
		height: 21rpx;
		margin-left: 5rpx;
	}
	
	.goodList .item .text .vip-money .num {
		font-size: 22rpx;
		color: #aaa;
		font-weight: normal;
		margin: -2rpx 0 0 0;
	}
	
	.goodList .item .iconfont {
		position: absolute;
		right: 30rpx;
		width: 50rpx;
		height: 50rpx;
		border-radius: 50%;
		font-size: 30rpx;
		bottom: 38rpx;
	}

</style>
