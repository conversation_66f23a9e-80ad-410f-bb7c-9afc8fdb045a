<template>
	<view class="chooseImage " style=" display: flex;flex-wrap: wrap;">
		<view style="position: relative;" v-for="(item, index) in imgList" :key="index" :style="{ width: width , height: height,margin:(index+1)%show===0?'0':margin }" v-if="preview">
			<image :src="imgList[index]" :style="{ width: width, height: height,borderRadius:borderRadius }" mode="aspectFill" @click="viewImg(imgList[index])"></image>
			<view v-if="close" class="icon_close "  @click="delImg(index)">
				<i class="iconfont " style="">&#xe635;</i>
			</view>
		</view>
		<view v-if="imgList.length < num || !preview"  :style="{ width:width}" @click="chooseImage" >
			<slot>
				<image :src="bgImage" mode="widthFix" :style="{ width: width ,height: height }"></image>
			</slot>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		show:{
			//每行显示数量
			type: Number,
			default: 4
		},
		width: {
			//图片的尺寸
			type: String,
			default: '140rpx'
		},
		height: {
			//图片的尺寸
			type: String,
			default: '140rpx'
		},
		num: {
			//上传图片数量
			type: Number,
			default: 9
		},
		margin:{
			type: String,
			default: '0 20rpx 20rpx 0'
		},
		isSave: {
			//是否记录用户的选择记录
			type: Boolean,
			default: false
		},
		saveStr: {
			//记录用户的缓存字段
			type: String,
			default: 'chooseImage'
		},
		bgImage:{
			type:String,
			default:'https://www.xyzgy.xyz/image/upload.png'
		},
		isBase64: {
			//是否转base64 受数据传输长度限制，不建议在组件中使用，如果一定要使用，在返回结果中自己转换
			type: Boolean,
			default: false
		},
		sourceType:{
			type:Array,
			default:()=>{
				return ['album', 'camera']
			}
		},
		sizeType:{
			type:Array,
			default:()=>{
				return  ['original', 'compressed']
			}
		},
		close:{
			type: Boolean,
			default: true
		},
		borderRadius:{
			type:String,
			default:'0rpx'
		},
		preview:{
			type: Boolean, //只使用上传功能
			default: true
		},
	},
	data() {
		return {
			imgList: [],
			base64: '',
            avatarSize:0
		};
	},
	methods: {
		chooseImage: async function() {
			let _this = this;
			await _this.getImage();
			this.$emit('chooseImage', _this.imgList);
            this.$emit('avatarSize', _this.avatarSize);
		},
		getImage() {
			let _this = this;
			let _count = _this.num - _this.imgList.length;
			return new Promise((resolve, reject) => {
				uni.chooseImage({
					count: _count, //默认9
					//#ifndef MP-TOUTIAO
					sizeType:_this.sizeType, //可以指定是原图还是压缩图，默认二者都有
					//#endif
					sourceType:_this.sourceType, //从相册选择
					success: function(res) {
                        _this.avatarSize = res.tempFiles[0].size;
						if (_this.isBase64) {
							//#ifdef MP-WEIXIN || MP-TOUTIAO
							uni.getFileSystemManager().readFile({
								filePath: res.tempFilePaths[0], //选择图片返回的相对路径
								encoding: 'base64', //编码格式
								success: function(ress) {
									//成功的回调
									console.log(ress);
									let base64 = 'data:image/jpeg;base64,' + ress.data;
									if (_this.imgList.length != 0) {
										_this.imgList = _this.imgList.concat(base64);
									} else {
										_this.imgList = [base64];
									}
								},
								fail: function(err) {
									console.log(err);
								}
							});
							//#endif
							//#ifndef MP-WEIXIN
							if (_this.imgList.length != 0) {
								_this.imgList = _this.imgList.concat(res.tempFilePaths);
							} else {
								_this.imgList = res.tempFilePaths;
							}
							//#endif
						} else {
							
							if (_this.imgList.length != 0) {
								_this.imgList = _this.imgList.concat(res.tempFilePaths);
							} else {
								_this.imgList = res.tempFilePaths;
							}
						}
						if (_this.isSave) {
							uni.setStorageSync(_this.saveStr, _this.imgList.join(','));
						}
						resolve(_this.imgList);
					}
				});
			});
		},
		delImg(idx) {
			this.imgList.splice(idx, 1);
			this.imgList = this.imgList;
			if (this.isSave) {
				uni.setStorageSync(this.saveStr, this.imgList.join(','));
			}
			this.$emit('delImg', this.imgList);
		},
		viewImg(path) {
			uni.previewImage({
				urls: this.imgList,
				current: path
			});
		}
	},
	mounted() {
		if (this.isSave) {
			let str = uni.getStorageSync(this.saveStr);
			if (str != '') {
				str = str.split(',');
				if (str.length > this.num) {
					str = str.slice(0, this.num);
				}
				this.imgList = str;
			}
		} else {
			uni.removeStorageSync(this.saveStr);
		}
	}
};
</script>

<style lang="less" scoped="">
@font-face {
	font-family: 'iconfont'; /* project id 1035847 */
	src: url('https://at.alicdn.com/t/font_1035847_ne3azjcnkk.eot');
	src: url('https://at.alicdn.com/t/font_1035847_ne3azjcnkk.eot?#iefix') format('embedded-opentype'), url('https://at.alicdn.com/t/font_1035847_ne3azjcnkk.woff2') format('woff2'),
		url('https://at.alicdn.com/t/font_1035847_ne3azjcnkk.woff') format('woff'), url('https://at.alicdn.com/t/font_1035847_ne3azjcnkk.ttf') format('truetype'),
		url('https://at.alicdn.com/t/font_1035847_ne3azjcnkk.svg#iconfont') format('svg');
}
.iconfont {
	font-family: 'iconfont' !important;
	font-size: 20px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: 0.2px;
	-moz-osx-font-smoothing: grayscale;
	cursor: pointer;
}
.chooseImage {
	> view {
		image{
			border:1px solid  rgba(0, 0, 0, 0.2);
		}
	}
	.icon_close {
		position: absolute;
		right: -20rpx;
		top: -20rpx;
		.iconfont {
			display:block;
			background: rgba(0, 0, 0, 0.3);
			color: #fff;
			border-radius: 50%;
			width:40rpx;
			height:40rpx;
			line-height:40rpx;
			font-size: 24rpx;
			text-align:center;
		}
	}
}
</style>
