<template>
	<view class="productSort">
			<view class="header acea-row row-center-wrapper" id="header">
				<view class="acea-row row-between-wrapper input">
					<span class="iconfont icon-sousuo"></span>
					<input type="text" placeholder="搜索商品信息" v-model="search" @confirm="submitForm"/>
				</view>
			</view>
		<view class="aside">
			<view class="item acea-row row-center-wrapper" :class="index === navActive ? 'on' : ''" v-for="(item, index) in category" :key="index" @click="asideTap(index)">
				<span>{{ item.cate_name }}</span>
			</view>
		</view>
		<view class="conter">
			<scroll-view scroll-y="true" :scroll-into-view="`b${navActive}`" :style="{ height: height }" @scroll="scroll" :scroll-with-animation="true">
				<view class="listw" v-for="(item, index) in category" :key="index" :id="`b${index}`">
					<view class="title acea-row row-center-wrapper" ref="title">
						<view class="line"></view>
						<view class="name">{{ item.cate_name }}</view>
						<view class="line"></view>
					</view>
					<view class="list acea-row" style="min-height: 200rpx;">
						<view
							class="item acea-row row-column row-middle"
							v-for="(child, index) in item.children"
							:key="index"
							@click="goPages(`/pages/shop/GoodsList?id=${child.id}&title=${child.cate_name}`)"
						>
							<view class="picture"><image :src="child.pic" /></view>
							<view class="name line1">{{ child.cate_name }}</view>
						</view>
					</view>
				</view>
				<view :style="{ height: height }" v-if="number<15"></view>
			</scroll-view>
		</view>
	</view>
</template>
<script>
import { debounce } from '@/utils/common.js';
import { getCategory } from '@/api/store';
import { uniSelectorQueryInfo } from '@/utils/uni_api.js';

export default {
	name: 'GoodsClass',
	components: {},
	props: {},
	data: function() {
		return {
			category: [],
			navActive: 0,
			search: '',
			lock: false,
			pid: '',
			number: '',
			height: '100%'
		};
	},
	watch: {
		// "$route.params.pid": function(n) {
		//   if (n) {
		//     this.activeCateId(n);
		//   }
		// }
	},
	mounted: function() {
	},
	onLoad(options) {
		const { pid = '' } = options;
		this.pid = pid;
		this.loadCategoryData();
	},
	methods: {
		goPages(path, type) {
			this.$navigator(path, type);
		},
		activeCateId(n) {
			let index = 0;
			n = parseInt(n);
			if (!n) return;
			this.category.forEach((cate, i) => {
				if (cate.id === n) index = i;
			});
			if (index !== this.navActive) {
				this.asideTap(index);
			}
		},
		loadCategoryData() {
			getCategory().then(res => {
				this.category = res.data;
				this.$nextTick(() => {
					this.initScrollInfo();
				});
			});
		},
		submitForm: function() {
			var val = this.search.trim();
			if (val) {
				this.goPages('/pages/shop/GoodsList?s='+val)
				setTimeout(() => (this.search = ''), 500);
			}
		},
		asideTap(index) {
			this.navActive = index;
		},

		async initScrollInfo() {
			let that = this,
				hightArr = [];
			let len = that.category.length,
				stytemInfo = that.$store.state.stytemInfo,
				topHeight = this.$store.state.tarBarHeight;
			// #ifdef MP
			topHeight = topHeight + that.$store.state.navigationBarHeight;
			// #endif
			this.number = that.category[len - 1].children.length;
			this.height = stytemInfo.windowHeight - stytemInfo.statusBarHeight - topHeight + 'px';
			for (var i = 0; i < len; i++) {
				//获取元素所在位置
				const { top } = await uniSelectorQueryInfo('#b' + i, this);
				hightArr.push(top + topHeight);
			}
			that.hightArr = hightArr;
		},

		scroll: function(e) {
			let scrollTop = e.detail.scrollTop,
				scrollArr = this.hightArr;
			for (var i = 0; i < scrollArr.length; i++) {
				if (scrollTop >= 0 && scrollTop < scrollArr[1] - scrollArr[0]) {
					this.navActive = 0;
					this.lastActive = 0;
				} else if (scrollTop >= scrollArr[i] - scrollArr[0] && scrollTop < scrollArr[i + 1] - scrollArr[0]) {
					this.navActive = 0;
				} else if (scrollTop >= scrollArr[scrollArr.length - 1] - scrollArr[0]) {
					this.navActive = scrollArr.length - 1;
				}
			}
		}
	}
};
</script>
<style lang="scss">
	page{
		background-color: $uni-bg-color;
	}
</style>
<style scoped>
.productSort .header {
	width: 100%;
	height: 96rpx;
	background-color: #fff;
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	z-index: 9;
	border-bottom: 1px solid #f5f5f5;
}

.productSort .header .input {
	width: 700rpx;
	height: 60rpx;
	background-color: #f5f5f5;
	border-radius: 50rpx;
	padding: 0 25rpx;
}

.productSort .header .input .iconfont {
	font-size: 35rpx;
	color: #555;
}

.productSort .header .input input {
	font-size: 26rpx;
	height: 100%;
	width: 597rpx;
}

.productSort .header .input input::placeholder {
	color: #999;
}

.productSort .aside {
	position: fixed;
	width: 180rpx;
	left: 0;
	top: 96rpx;
	bottom: 100rpx;
	background-color: #f7f7f7;
	overflow-y: auto;
	overflow-x: hidden;
	-webkit-overflow-scrolling: touch;
	overflow-scrolling: touch;
	height: 100%;
}

.productSort .aside .item {
	height: 80rpx;
	width: 100%;
	font-size: 26rpx;
	color: #424242;
}

.productSort .aside .item.on {
	background-color: #fff;
	border-left: 4rpx solid #fc4141;
	width: 100%;
	text-align: center;
	color: #fc4141;
	font-weight: bold;
}

.productSort .conter {
	margin-left: 180rpx;
	padding: 0 14rpx;
	padding-top: 96rpx;
}

.productSort .conter .listw {
	padding-top: 20rpx;
}

.productSort .conter .listw .title {
	height: 90rpx;
}

.productSort .conter .listw .title .line {
	width: 100rpx;
	height: 2rpx;
	background-color: #999;
}

.productSort .conter .listw .title .name {
	font-size: 28rpx;
	color: #333;
	margin: 0 30rpx;
	font-weight: bold;
}

.productSort .conter .list {
	flex-wrap: wrap;
	-webkit-flex-wrap: wrap;
	-ms-flex-wrap: wrap;
}

.productSort .conter .list .item {
	width: 177rpx;
	margin-top: 26rpx;
}

.productSort .conter .list .item .picture {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
}

.productSort .conter .list .item .picture image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.productSort .conter .list .item .name {
	font-size: 24rpx;
	color: #333;
	height: 56rpx;
	line-height: 56rpx;
	width: 120rpx;
	text-align: center;
}
</style>
