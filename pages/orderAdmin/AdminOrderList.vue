<template>
	<view class="pos-order-list" ref="container">
		<view class="nav acea-row row-around row-middle">
			<view class="item" :class="where.status == 0 ? 'on' : ''" @click="changeStatus(0)">待付款</view>
			<view class="item" :class="where.status == 1 ? 'on' : ''" @click="changeStatus(1)">待发货</view>
			<view class="item" :class="where.status == 2 ? 'on' : ''" @click="changeStatus(2)">待收货</view>
			<view class="item" :class="where.status == 3 ? 'on' : ''" @click="changeStatus(3)">待评价</view>
			<view class="item" :class="where.status == 4 ? 'on' : ''" @click="changeStatus(4)">已完成</view>
			<view class="item" :class="where.status == -3 ? 'on' : ''" @click="changeStatus(-3)">退款</view>
		</view>
		<view class="list">
			<view class="item" v-for="(item, index) in list" :key="index">
				<view class="order-num acea-row row-middle" @click="goPages(`/pages/orderAdmin/AdminOrder?order_id=${item.order_id}`)" >
					订单号：{{ item.order_id }}
					<span class="time">下单时间：{{ item.add_time }}</span>
				</view>
				<view class="pos-order-goods" v-for="(val, key) in item._info" :key="key">
					<view class="goods acea-row row-between-wrapper" @click="goPages(`/pages/orderAdmin/AdminOrder?order_id=${item.order_id}`)">
						<view class="picTxt acea-row row-between-wrapper">
							<view class="pictrue"><image :src="val.cart_info.productInfo.image" /></view>
							<view class="text acea-row row-between row-column">
								<view class="info line2">{{ val.cart_info.productInfo.store_name }}</view>
								<view class="attr" v-if="val.cart_info.productInfo.suk">{{ val.cart_info.productInfo.suk }}</view>
							</view>
						</view>
						<view class="money">
							<view class="x-money">￥{{ val.cart_info.productInfo.price }}</view>
							<view class="num">x{{ val.cart_info.cart_num }}</view>
							<view class="y-money">￥{{ val.cart_info.productInfo.ot_price }}</view>
						</view>
					</view>
				</view>
				<view class="public-total">
					共{{ item.total_num }}件商品，应支付
					<span class="money">￥{{ item.pay_price }}</span>
					( 邮费 ¥{{ item.total_postage }} )
				</view>
				<view class="operation acea-row row-between-wrapper">
					<view class="more"></view>
					<view class="acea-row row-middle">
						<view class="bnt" @click="modify(item, 0)" v-if="where.status == 0">一键改价</view>
						<view class="bnt" @click="modify(item, 1)">订单备注</view>
						<view class="bnt" @click="modify(item, 0)" v-if="where.status == -3 && item.refund_status === 1">立即退款</view>
						<view class="bnt cancel" v-if="item.pay_type === 'offline' && item.paid === 0" @click="offlinePay(item)">确认付款</view>
						<view class="bnt" v-if="where.status == 1" @click="goPages(`/pages/orderAdmin/GoodsDeliver?order_id=${item.order_id}`)" >去发货</view>
					</view>
				</view>
			</view>
		</view>
		<x-home></x-home>
		<Loading :loaded="loaded" :loading="loading"></Loading>
		<PriceChange :change="change" :orderInfo="orderInfo" @closechange="changeclose()" @savePrice="savePrice" :status="status"></PriceChange>
	</view>
</template>
<script>
import PriceChange from '@/components/PriceChange';
import Loading from '@/components/Loading';
import { getAdminOrderList, setAdminOrderPrice, setAdminOrderRemark, setOfflinePay, setOrderRefund } from '@/api/admin';
import { RegMoney } from '@/utils/validate';
// import { validatorDefaultCatch } from '@utils/dialog';
export default {
	name: 'AdminOrderList',
	components: {
		PriceChange,
		Loading
	},
	props: {},
	data: function() {
		return {
			current: '',
			change: false,
			types: 0,
			where: {
				page: 1,
				limit: 5,
				status: 0
			},
			list: [],
			loaded: false,
			loading: false,
			orderInfo: {},
			status: null
		};
	},
	mounted: function() {
	},
	onLoad(options){
		const {status=0} = options ;
		this.where.status = Number(status);
		this.current = '';
		this.getIndex();
	},
	methods: {
		goPages(path){
			this.$navigator(path)
		},
		more: function(index) {
			if (this.current === index) this.current = '';
			else this.current = index;
		},
		modify: function(item, status) {
			this.change = true;
			this.orderInfo = item;
			this.status = status;
		},
		changeclose: function(msg) {
			this.change = msg;
		},
		async savePrice(opt) {
			let that = this,
				data = {},
				price = opt.price,
				refund_price = opt.refund_price,
				refund_status = that.orderInfo.refund_status,
				remark = opt.remark;
			data.order_id = that.orderInfo.order_id;
			if (that.status == 0 && refund_status === 0) {
				if(!RegMoney(price)){
					return that.$showToast(price.trim().length ? '请输入正确价格':'请输入价格')
				}
				data.price = price;
				setAdminOrderPrice(data).then(
					function() {
						that.change = false;
						that.$showToast('改价成功','success')
						that.init();
					},
					function() {
						that.change = false;
						that.$showToast('改价失败','error')
					}
				);
			} else if (that.status == 0 && refund_status === 1) {

				if(!RegMoney(refund_price)){
					return that.$showToast(refund_price.trim().length ? '请输入正确价格':'请输入价格')
				}
				
				data.price = refund_price;
				data.type = opt.type;
				setOrderRefund(data).then(
					res => {
						that.change = false;
						that.$showToast(res.msg,'success')
						that.init();
					},
					err => {
						that.change = false;
						that.$showToast(err.msg || err)
					}
				);
			} else {
				if(!remark.length){
					return that.$showToast('备注内容不能为空')
				}
				data.remark = remark;
				setAdminOrderRemark(data).then(
					res => {
						that.change = false;
						that.$showToast(res.msg)
						that.init();
					},
					err => {
						that.change = false;
						res.msg
					}
				);
			}
		},
		init: function() {
			this.list = [];
			this.where.page = 1;
			this.loaded = false;
			this.loading = false;
			this.getIndex();
			this.current = '';
		},
		getIndex: function() {
			let that = this;
			if (that.loading || that.loaded) return;
			that.loading = true;
			getAdminOrderList(that.where).then(
				res => {
					that.loading = false;
					that.loaded = res.data.length < that.where.limit;
					that.list.push.apply(that.list, res.data);
					that.where.page = that.where.page + 1;
				},
				err => {
					that.$showToast(err.msg || err)
				}
			);
		},
		changeStatus: function(val) {
			if (this.where.status != val) {
				this.where.status = val;
				this.init();
			}
		},
		offlinePay: function(item) {
			setOfflinePay({ order_id: item.order_id }).then(
				res => {
					this.$showToast(res.msg);
					this.init();
				},
				error => {
					this.$showToast(error.msg);
				}
			);
		}
	},
	onReachBottom() {
		 this.getIndex();
	}
};
</script>
<style scoped>
	.pos-order-list .nav {
		width: 100%;
		height: 96rpx;
		background-color: #fff;
		font-size: 30rpx;
		color: #282828;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 9999;
	}
	
	.pos-order-list .nav .item.on {
		color: #2291f8;
	}
	
	.pos-order-list .list {
		margin-top: 120rpx;
	}
	
	.pos-order-list .list .item {
		background-color: #fff;
		width: 100%;
	}
	
	.pos-order-list .list .item~.item {
		margin-top: 24rpx;
	}
	
	.pos-order-list .list .item .order-num {
		height: 124rpx;
		border-bottom: 1px solid #eee;
		font-size: 30rpx;
		font-weight: bold;
		color: #282828;
		padding: 0 30rpx;
	}
	
	.pos-order-list .list .item .order-num .time {
		font-size: 26rpx;
		font-weight: normal;
		color: #999;
		margin-top: -40rpx;
	}
	
	.pos-order-list .list .item .operation {
		padding: 20rpx 30rpx;
		margin-top: 3rpx;
	}
	
	.pos-order-list .list .item .operation .more {
		position: relative;
	}
	
	.pos-order-list .list .item .operation .icon-gengduo {
		font-size: 50rpx;
		color: #aaa;
	}
	
	.pos-order-list .list .item .operation .order .arrow {
		width: 0;
		height: 0;
		border-left: 11rpx solid transparent;
		border-right: 11rpx solid transparent;
		border-top: 20rpx solid #e5e5e5;
		position: absolute;
		left: 15rpx;
		bottom: -18rpx;
	}
	
	.pos-order-list .list .item .operation .order .arrow:before {
		content: '';
		width: 0;
		height: 0;
		border-left: 7rpx solid transparent;
		border-right: 7rpx solid transparent;
		border-top: 20rpx solid #fff;
		position: absolute;
		left: -7rpx;
		bottom: 0;
	}
	
	.pos-order-list .list .item .operation .order {
		width: 200rpx;
		background-color: #fff;
		border: 1px solid #eee;
		border-radius: 10rpx;
		position: absolute;
		top: -100rpx;
		z-index: 9;
	}
	
	.pos-order-list .list .item .operation .order .items {
		height: 77rpx;
		line-height: 77rpx;
		text-align: center;
	}
	
	.pos-order-list .list .item .operation .order .items~.items {
		border-top: 1px solid #f5f5f5;
	}
	
	.pos-order-list .list .item .operation .bnt {
		font-size: 28rpx;
		color: #5c5c5c;
		width: 170rpx;
		height: 60rpx;
		border-radius: 30rpx;
		border: 1px solid #bbb;
		text-align: center;
		line-height: 60rpx;
	}
	
	.pos-order-list .list .item .operation .bnt~.bnt {
		margin-left: 14rpx;
	}

</style>
