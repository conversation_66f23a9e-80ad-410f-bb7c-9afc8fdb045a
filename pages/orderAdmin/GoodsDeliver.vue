<template>
	<view class="deliver-goods">
		<header>
			<view class="order-num acea-row row-between-wrapper">
				<view class="num line1">订单号：{{ order_id }}</view>
				<view class="name line1 acea-row row-middle row-center">
					<span class="iconfont icon-yonghu2"></span>
					{{ delivery.nickname }}
				</view>
			</view>
			<view class="address">
				<view class="name">
					{{ delivery.real_name }}
					<span class="phone">{{ delivery.user_phone }}</span>
				</view>
				<view>{{ delivery.user_address }}</view>
			</view>
			<view class="line"><image src="@/static/images/line.jpg" /></view>
		</header>
		<view class="wrapper">
			<view class="item acea-row row-between-wrapper">
				<view>发货方式</view>
				<view class="mode acea-row row-middle row-right">
					<view class="goods" :class="active === index ? 'on' : ''" v-for="(item, index) in types" :key="index" @click="changeType(item, index)">
						{{ item.title }}
						<span class="iconfont icon-xuanzhong2"></span>
					</view>
				</view>
			</view>
			<view class="list" v-show="active === 0">
				<view class="item acea-row row-between-wrapper">
					<view>发货方式</view>
					<picker class="mode" @change="bindPickerChange" :value="index" :range="logistics">
						<view class="picker acea-row  row-middle row-right">
							<view class="reason">{{ logistics[index] }}</view>
							<text class="iconfont icon-jiantou"></text>
						</view>
					</picker>
				</view>
				<view class="item acea-row row-between-wrapper">
					<view>快递单号</view>
					<input type="text" placeholder="填写快递单号" v-model="delivery_id" class="mode" />
				</view>
			</view>
			<view class="list" v-show="active === 1">
				<view class="item acea-row row-between-wrapper">
					<view>送货人</view>
					<input type="text" placeholder="填写送货人" v-model="delivery_name" class="mode" />
				</view>
				<view class="item acea-row row-between-wrapper">
					<view>送货电话</view>
					<input type="text" placeholder="填写送货电话" v-model="delivery_id" class="mode" />
				</view>
			</view>
		</view>
		<x-home></x-home>
		<view style="height:120rpx;"></view>
		<view class="confirm" @click="saveInfo">确认提交</view>
	</view>
</template>
<script>
import { getAdminOrderDelivery, setAdminOrderDelivery } from '@/api/admin';
import { getLogistics } from '@/api/public';
import { RegPhone } from '@/utils/validate.js';

export default {
	name: 'GoodsDeliver',
	components: {},
	props: {},
	data: function() {
		return {
			types: [
				{
					type: 'express',
					title: '发货'
				},
				{
					type: 'send',
					title: '送货'
				},
				{
					type: 'fictitious',
					title: '无需发货'
				}
			],
			active: 0,
			order_id: '',
			delivery: [],
			logistics: [],
			index: 0,

			delivery_type: 'express',
			delivery_name: '',
			delivery_id: ''
		};
	},
	mounted: function() {},
	onLoad(options) {
		const { order_id } = options;
		this.order_id = order_id;
		this.getIndex();

		this.getLogistics();
	},
	methods: {
		changeType: function(item, index) {
			this.active = index;
			this.delivery_type = item.type;
			this.delivery_name = '';
			this.delivery_id = '';
		},
		getIndex: function() {
			let that = this;
			getAdminOrderDelivery(that.order_id).then(
				res => {
					that.delivery = res.data;
				},
				err => {
					that.$showToast(err.msg || err);
				}
			);
		},
		bindPickerChange(e) {
			this.index = e.detail.value;
			this.delivery_name = this.logistics[e.detail.value];
		},
		getLogistics: function() {
			let that = this;
			let logisticsArr = [];
			getLogistics().then(
				res => {
					res.data.map(item => {
						logisticsArr.push(item.name);
					});
					that.logistics = logisticsArr;
				},
				err => {
					that.$showToast(err.msg || err);
				}
			);
		},
		async saveInfo() {
			let that = this,
				delivery_type = that.delivery_type,
				delivery_name = that.delivery_name,
				delivery_id = that.delivery_id,
				save = { order_id: that.order_id, delivery_type: delivery_type };
			switch (delivery_type) {
				case 'send':
					if (!delivery_id.trim().length) {
						return that.$showToast('请填写送货人');
					}
					if (!RegPhone(delivery_id)) {
						return that.$showToast(delivery_id.trim().length ? '输入正确的手机号码' : '请填写送货电话');
					}
					save.delivery_name = delivery_name;
					save.delivery_id = delivery_id;
					that.setInfo(save);
					break;
				case 'express':
					if (!delivery_id.trim().length) {
						return that.$showToast('请填写快递单号');
					}
					save.delivery_name = delivery_name;
					save.delivery_id = delivery_id;
					that.setInfo(save);
					break;
				case 'fictitious':
					that.setInfo(save);
					break;
			}
		},
		setInfo: function(item) {
			let that = this;
			console.log(item);
			setAdminOrderDelivery(item).then(
				res => {
					that.$showToast(res.msg);
					that.$navigator(-1);
				},
				err => {
					that.$showToast(err.msg || err);
				}
			);
		}
	}
};
</script>

<style scoped>
.deliver-goods header {
	width: 100%;
	background-color: #fff;
	padding-top: 10rpx;
}

.deliver-goods header .order-num {
	padding: 0 30rpx;
	border-bottom: 1px solid #f5f5f5;
	height: 67rpx;
}

.deliver-goods header .order-num .num {
	width: 430rpx;
	font-size: 26rpx;
	color: #282828;
	position: relative;
}

.deliver-goods header .order-num .num:after {
	position: absolute;
	content: '';
	width: 1px;
	height: 30rpx;
	background-color: #ddd;
	top: 50%;
	margin-top: -15rpx;
	right: 0;
}

.deliver-goods header .order-num .name {
	width: 260rpx;
	font-size: 26rpx;
	color: #282828;
	text-align: center;
}

.deliver-goods header .order-num .name .iconfont {
	font-size: 35rpx;
	color: #477ef3;
	vertical-align: middle;
	margin-right: 10rpx;
}

.deliver-goods header .address {
	font-size: 26rpx;
	color: #868686;
	background-color: #fff;
	padding: 30rpx;
}

.deliver-goods header .address .name {
	font-size: 30rpx;
	color: #282828;
	margin-bottom: 10rpx;
}

.deliver-goods header .address .name .phone {
	margin-left: 40rpx;
}

.deliver-goods header .line {
	width: 100%;
	height: 3rpx;
}

.deliver-goods header .line image {
	width: 100%;
	height: 100%;
	display: block;
}

.deliver-goods .wrapper {
	width: 100%;
	background-color: #fff;
}

.deliver-goods .wrapper .item {
	border-bottom: 1px solid #f0f0f0;
	padding: 0 30rpx;
	height: 96rpx;
	font-size: 32rpx;
	color: #282828;
	position: relative;
}

.deliver-goods .wrapper .item .mode {
	width: 459rpx;
	text-align: right;
}

.deliver-goods .wrapper .item .mode .iconfont {
	font-size: 30rpx;
	margin-left: 13rpx;
}

.deliver-goods .wrapper .item .mode .goods ~ .goods {
	margin-left: 30rpx;
}

.deliver-goods .wrapper .item .mode .goods {
	color: #bbb;
}

.deliver-goods .wrapper .item .mode .goods.on {
	color: #477ef3;
}

.deliver-goods .wrapper .item .icon-up {
	position: absolute;
	font-size: 35rpx;
	color: #2c2c2c;
	right: 30rpx;
}

.deliver-goods .wrapper .item select {
	direction: rtl;
	padding-right: 60rpx;
	position: relative;
	z-index: 2;
}

.deliver-goods .wrapper .item input::placeholder {
	color: #bbb;
}

.deliver-goods .confirm {
	font-size: 32rpx;
	color: #fff;
	width: 100%;
	height: 100rpx;
	background-color: #477ef3;
	text-align: center;
	line-height: 100rpx;
	position: fixed;
	bottom: 0;
}
</style>
