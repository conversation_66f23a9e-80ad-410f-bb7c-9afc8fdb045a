<template>
	<view class="order-details pos-order-details">
		<view class="header acea-row row-middle">
			<view class="state">{{ title }}</view>
			<view class="data">
				<view class="order-num">订单：{{ orderInfo.order_id }}</view>
				<view>
					<span class="time">{{ orderInfo.add_time }}</span>
				</view>
			</view>
		</view>
		<view class="remarks acea-row row-between-wrapper" v-if="goname != 'looks'">
			<span class="iconfont icon-zhinengkefu-"></span>
			<input type="button" class="line1" style="text-align: left;" :value="orderInfo.remark ? orderInfo.remark : '订单未备注，点击添加备注信息'" @click="modify(1)" />
		</view>
		<view class="orderingUser acea-row row-middle">
			<span class="iconfont icon-yonghu2"></span>
			{{ orderInfo.nickname }}
		</view>
		<view class="address">
			<view class="name">
				{{ orderInfo.real_name }}
				<span class="phone">{{ orderInfo.user_phone }}</span>
			</view>
			<view>{{ orderInfo.user_address }}</view>
		</view>
		<view class="line"><image src="@/static/images/line.jpg" /></view>
		<view class="pos-order-goods">
			<view class="goods acea-row row-between-wrapper" v-for="(item, index) in orderInfo._info" :key="index">
				<view class="picTxt acea-row row-between-wrapper">
					<view class="pictrue"><image :src="item.cart_info.productInfo.image" /></view>
					<view class="text acea-row row-between row-column">
						<view class="info line2">{{ item.cart_info.productInfo.store_name }}</view>
						<view class="attr">{{ item.cart_info.productInfo.suk }}</view>
					</view>
				</view>
				<view class="money">
					<view class="x-money">￥{{ item.cart_info.productInfo.price }}</view>
					<view class="num">x{{ item.cart_info.cart_num }}</view>
					<view class="y-money">￥{{ item.cart_info.productInfo.ot_price }}</view>
				</view>
			</view>
		</view>
		<view class="public-total">
			共{{ orderInfo.total_num }}件商品，应支付
			<span class="money">￥{{ orderInfo.pay_price }}</span>
			( 邮费 ¥{{ orderInfo.pay_postage }} )
		</view>
		<view class="wrapper">
			<view class="item acea-row row-between">
				<view>订单编号：</view>
				<view class="conter acea-row row-middle row-right">
					{{ orderInfo.order_id }}
					<span class="copy copy-data" @click="copy(orderInfo.order_id)">复制</span>
				</view>
			</view>
			<view class="item acea-row row-between">
				<view>下单时间：</view>
				<view class="conter">{{ orderInfo.add_time }}</view>
			</view>
			<view class="item acea-row row-between">
				<view>支付状态：</view>
				<view class="conter">{{ orderInfo.paid == 1 ? '已支付' : '未支付' }}</view>
			</view>
			<view class="item acea-row row-between">
				<view>支付方式：</view>
				<view class="conter">{{ payType }}</view>
			</view>
			<view class="item acea-row row-between">
				<view>买家留言：</view>
				<view class="conter">{{ orderInfo.mark }}</view>
			</view>
		</view>
		<view class="wrapper">
			<view class="item acea-row row-between">
				<view>支付金额：</view>
				<view class="conter">￥{{ orderInfo.total_price }}</view>
			</view>
			<view class="item acea-row row-between">
				<view>优惠券抵扣：</view>
				<view class="conter">-￥{{ orderInfo.coupon_price }}</view>
			</view>
			<view class="item acea-row row-between">
				<view>运费：</view>
				<view class="conter">￥{{ orderInfo.freight_price }}</view>
			</view>
			<view class="actualPay acea-row row-right">
				实付款：
				<span class="money font-color-red">￥{{ orderInfo.pay_price }}</span>
			</view>
		</view>
		<view class="wrapper" v-if="orderInfo.delivery_type != 'fictitious' && orderInfo._status._type === 2">
			<view class="item acea-row row-between">
				<view>配送方式：</view>
				<view class="conter" v-if="orderInfo.delivery_type === 'express'">快递</view>
				<view class="conter" v-if="orderInfo.delivery_type === 'send'">送货</view>
			</view>
			<view class="item acea-row row-between">
				<view v-if="orderInfo.delivery_type === 'express'">快递公司：</view>
				<view v-if="orderInfo.delivery_type === 'send'">送货人：</view>
				<view class="conter">{{ orderInfo.delivery_name }}</view>
			</view>
			<view class="item acea-row row-between">
				<view v-if="orderInfo.delivery_type === 'express'">快递单号：</view>
				<view v-if="orderInfo.delivery_type === 'send'">送货人电话：</view>
				<view class="conter">
					{{ orderInfo.delivery_id }}
					<span class="copy copy-data" @click="copy(orderInfo.delivery_id)">复制</span>
				</view>
			</view>
		</view>
		<view style="height:120rpx;"></view>
		<view class="footer acea-row row-right row-middle" v-if="goname != 'looks'">
			<view class="more"></view>
			<view class="bnt cancel" @click="modify(0)" v-if="types == 0">一键改价</view>
			<view class="bnt cancel" @click="modify(0)" v-if="types == -1">立即退款</view>
			<view class="bnt cancel" @click="modify(1)">订单备注</view>
			<view class="bnt cancel" v-if="orderInfo.pay_type === 'offline' && orderInfo.paid === 0" @click="offlinePay">确认付款</view>
			<view class="bnt delivery" v-if="types == 1" @click="goPages(`/pages/orderAdmin/GoodsDeliver?order_id=${orderInfo.order_id}`)" >去发货</view>
		</view>
		<x-home></x-home>
		<PriceChange :change="change" :orderInfo="orderInfo" v-on:closechange="changeclose($event)" v-on:savePrice="savePrice" :status="status"></PriceChange>
	</view>
</template>
<script>
import PriceChange from '@/components/PriceChange';
import { setClipboardData } from '@/utils/common.js';
import { getAdminOrderDetail, setAdminOrderPrice, setAdminOrderRemark, setOfflinePay, setOrderRefund } from '@/api/admin';
import { RegMoney } from '@/utils/validate';
export default {
	name: 'AdminOrder',
	components: {
		PriceChange
	},
	props: {},
	data: function() {
		return {
			order: false,
			change: false,
			order_id: '',
			orderInfo: {
				_status: {}
			},
			status: null,
			title: '',
			payType: '',
			goname:'',
			types: null
		};
	},

	onLoad(options) {
		const { order_id,goname='' } = options;
		this.order_id = order_id;
		this.goname = goname;
		this.getIndex();
	},
	methods: {
		copy(val) {
			setClipboardData(val);
		},
		goPages(path){
			this.$navigator(path)
		},
		more: function() {
			this.order = !this.order;
		},
		modify: function(status) {
			this.change = true;
			this.status = status;
		},
		changeclose: function(msg) {
			this.change = msg;
		},
		getIndex: function() {
			let that = this;
			getAdminOrderDetail(that.order_id).then(
				res => {
					that.orderInfo = res.data;
					that.types = res.data._status._type;
					that.title = res.data._status._title;
					that.payType = res.data._status._payType;
				},
				err => {
					that.$showToast(err.msg || err)
				}
			);
		},
		async savePrice(opt) {
			let that = this,
				data = {},
				price = opt.price,
				remark = opt.remark,
				refund_price = opt.refund_price;
			data.order_id = that.orderInfo.order_id;
			if (that.status == 0 && that.orderInfo.refund_status === 0) {
				if (!RegMoney(price)) {
					return that.$showToast(price.trim().length ? '请输入正确价格' : '请输入价格');
				}
				data.price = price;
				setAdminOrderPrice(data).then(
					function() {
						that.change = false;
							that.$showToast('改价成功','success')
						that.getIndex();
					},
					function() {
						that.change = false;
						that.$showToast('改价失败','error')
					}
				);
			} else if (that.status == 0 && that.orderInfo.refund_status === 1) {
				if (!RegMoney(refund_price)) {
					return that.$showToast(refund_price.trim().length ? '请输入正确价格' : '请输入价格');
				}
				data.price = refund_price;
				data.type = opt.type;
				setOrderRefund(data).then(
					res => {
						that.change = false;
							that.$showToast(res.msg,'success')
						that.getIndex();
					},
					err => {
						that.change = false;
						that.$showToast(err.msg || err)
						that.getIndex();
					}
				);
			} else {
				if (!remark.length) {
					return that.$showToast('备注内容不能为空');
				}
				data.remark = remark;
				setAdminOrderRemark(data).then(
					res => {
						that.change = false;
							that.$showToast(res.msg,'success')
						that.getIndex();
					},
					err => {
						that.change = false;
						that.$showToast(err.msg || err)
					}
				);
			}
		},
		offlinePay: function() {
			setOfflinePay({ order_id: this.orderInfo.order_id }).then(
				res => {
					that.$showToast(res.msg,'success')
					this.getIndex();
				},
				err => {
					
					that.$showToast(err.msg || err)
				}
			);
		}
	}
};
</script>
<style scoped>
.pos-order-details .header {
	background: linear-gradient(to right, #2291f8 0%, #1cd1dc 100%);
	background: -webkit-linear-gradient(to right, #2291f8 0%, #1cd1dc 100%);
	background: -moz-linear-gradient(to right, #2291f8 0%, #1cd1dc 100%);
}

.pos-order-details .header .state {
	font-size: 36rpx;
	color: #fff;
}

.pos-order-details .header .data {
	margin-left: 35rpx;
	font-size: 28rpx;
}

.pos-order-details .header .data .order-num {
	font-size: 30rpx;
	margin-bottom: 8rpx;
}

.pos-order-details .remarks {
	width: 100%;
	height: 86rpx;
	background-color: #fff;
	padding: 0 30rpx;
}

.pos-order-details .remarks .iconfont {
	font-size: 40rpx;
	color: #2a7efb;
}

.pos-order-details .remarks input {
	width: 630rpx;
	height: 100%;
	font-size: 30rpx;
}

.pos-order-details .remarks input::placeholder {
	color: #666;
}

.pos-order-details .orderingUser {
	font-size: 26rpx;
	color: #282828;
	padding: 0 30rpx;
	height: 67rpx;
	background-color: #fff;
	margin-top: 16rpx;
	border-bottom: 1px solid #f5f5f5;
}

.pos-order-details .orderingUser .iconfont {
	font-size: 40rpx;
	color: #2a7efb;
	margin-right: 15rpx;
}

.pos-order-details .address {
	margin-top: 0;
}

.pos-order-details .pos-order-goods {
	margin-top: 17rpx;
}

.pos-order-details .footer .more {
	font-size: 27rpx;
	color: #aaa;
	width: 100rpx;
	height: 64rpx;
	text-align: center;
	line-height: 64rpx;
	margin-right: 25rpx;
	position: relative;
}

.pos-order-details .footer .delivery {
	background: linear-gradient(to right, #2291f8 0%, #1cd1dc 100%);
	background: -webkit-linear-gradient(to right, #2291f8 0%, #1cd1dc 100%);
	background: -moz-linear-gradient(to right, #2291f8 0%, #1cd1dc 100%);
}

.pos-order-details .footer .more .order .arrow {
	width: 0;
	height: 0;
	border-left: 11rpx solid transparent;
	border-right: 11rpx solid transparent;
	border-top: 20rpx solid #e5e5e5;
	position: absolute;
	left: 15rpx;
	bottom: -18rpx;
}

.pos-order-details .footer .more .order .arrow:before {
	content: '';
	width: 0;
	height: 0;
	border-left: 9rpx solid transparent;
	border-right: 9rpx solid transparent;
	border-top: 19rpx solid #fff;
	position: absolute;
	left: -10rpx;
	bottom: 0;
}

.pos-order-details .footer .more .order {
	width: 200rpx;
	background-color: #fff;
	border: 1px solid #eee;
	border-radius: 10rpx;
	position: absolute;
	top: -200rpx;
	z-index: 9;
}

.pos-order-details .footer .more .order .item {
	height: 77rpx;
	line-height: 77rpx;
}

.pos-order-details .footer .more .order .item ~ .item {
	border-top: 1px solid #f5f5f5;
}

.pos-order-details .footer .more .moreName {
	width: 100%;
	height: 100%;
}
</style>
