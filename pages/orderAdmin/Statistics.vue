<template>
	<view class="statistical-page" ref="container">
		<view class="navs">
			<view class="list">
				<view class="item" :class="time == 'today' ? 'on' : ''" @click="setTime('today')">今天</view>
				<view class="item" :class="time == 'yesterday' ? 'on' : ''" @click="setTime('yesterday')">昨天</view>
				<view class="item" :class="time == 'seven' ? 'on' : ''" @click="setTime('seven')">最近7天</view>
				<view class="item" :class="time == 'month' ? 'on' : ''" @click="setTime('month')">本月</view>
				<view class="item" :class="time == 'date' ? 'on' : ''" @click="dateTitle">
					自定义
				</view>
			</view>
		</view>
		<view class="wrapper">
			<view class="title">{{ title }}{{ this.where.type == 1 ? '营业额（元）' : '订单量（份）' }}</view>
			<view class="money">{{ time_price }}</view>
			<view class="increase acea-row row-between-wrapper">
				<view>
					{{ title }}增长率：
					<span :class="increase_time_status === 1 ? 'red' : 'green'">
						{{ increase_time_status === 1 ? '' : '-' }}{{ growth_rate }}%
						<span class="iconfont" :class="increase_time_status === 1 ? 'icon-xiangshang1' : 'icon-xiangxia2'"></span>
					</span>
				</view>
				<view>
					{{ title }}增长：
					<span :class="increase_time_status === 1 ? 'red' : 'green'">
						{{ increase_time_status === 1 ? '' : '-' }}{{ increase_time }}
						<span class="iconfont" :class="increase_time_status === 1 ? 'icon-xiangshang1' : 'icon-xiangxia2'"></span>
					</span>
				</view>
			</view>
		</view>
		<view class="chart">
			<view class="company">{{ where.type === 1 ? '单位（元）' : '单位（份）' }}</view>
			<canvas
				canvas-id="canvas"
				id="canvas"
				class="ucharts"
				:width="initChartData.width * initChartData.pixelRatio"
				:height="initChartData.height * initChartData.pixelRatio"
				@touchstart="touchLineA"
			></canvas>
		</view>
		<view class="public-wrapper">
			<view class="title">
				<span class="iconfont icon-xiangxishuju"></span>
				详细数据
			</view>
			<view class="nav acea-row row-between-wrapper">
				<view class="data">日期</view>
				<view class="browse">订单量</view>
				<view class="turnover">成交额</view>
			</view>
			<view class="conter">
				<view class="item acea-row row-between-wrapper" v-for="(item, index) in list" :key="index">
					<view class="data">{{ item.time }}</view>
					<view class="browse">{{ item.count }}</view>
					<view class="turnover">{{ item.price }}</view>
				</view>
			</view>
		</view>
		<view class="calendar-wrapper" :class="current === true ? 'on' : ''">
			<view class="calendar"><uni-calendar  :insert="true" :lunar="true" :range="true" :start-date="'2019-3-2'" :end-date="'2090-5-20'" @change="change" /></view>
		</view>
		<view class="mask" @touchmove.prevent v-show="current === true" @click="close"></view>
		<Loading :loaded="loaded" :loading="loading"></Loading>
		<x-home></x-home>
	</view>
</template>
<script>import { getStatisticsMonth, getStatisticsTime } from '@/api/admin';
import Loading from '@/components/Loading';
import uCharts from '@/components/u-charts/u-charts.js';
const year = new Date().getFullYear();
const month = new Date().getMonth() + 1;
const day = new Date().getDate();
export default {
	name: 'Statistics',
	components: {
		Loading
	},
	props: {},
	data: function() {
		return {
			value: [[year, month, day - 1], [year, month, day]],
			isrange: true,
			weekSwitch: false,
			ismulti: false,
			monFirst: true,
			clean: false, //简洁模式
			lunar: false, //显示农历
			renderValues: [],
			monthRange: [],
			current: false,
			where: {
				start: '',
				stop: '',
				type: ''
			},
			types: '', //类型|order=订单数|price=营业额
			time: '', //时间|today=今天|yesterday=昨天|month=本月
			title: '', //时间|today=今天|yesterday=昨天|month=本月
			growth_rate: '', //增长率
			increase_time: '', //增长率
			increase_time_status: '', //增长率
			time_price: '', //增长率
			loaded: false,
			loading: false,
			filter: {
				page: 1,
				limit: 10,
				start: '',
				stop: ''
			},
			list: [],
			initChartData: {
				pixelRatio: 1,
				width: uni.upx2px(640),
				height: uni.upx2px(400),
				categories: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'], //x轴
				series: [
					{
						name: '成交量',
						data: [35, 20, 25, 37, 4, 20],
						color: '#2291f8'
					}
				]
			}
		};
	},
	watch: {
		'$route.params': function(newVal) {
			var that = this;
			if (newVal != undefined) {
				that.setType(newVal.type);
				that.setTime(newVal.time);
				that.getIndex();
			}
		}
	},
	mounted: function() {
		this.initChartData.pixelRatio = this.$store.state.stytemInfo.pixelRatio;
	},
	onLoad(options) {
		const { type, time } = options;
		this.setTime(time);
		this.setType(type);
		this.getInfo();
	},

	methods: {
		getIndex: function() {
			var that = this;
			getStatisticsTime(that.where).then(
				res => {
					var _info = res.data.chart,
						day = [],
						num = [];
					_info.forEach(function(item) {
						day.push(item.time);
						num.push(item.num);
					});

					let obj = this.initChartData;
					obj.categories = day;
					// that.polar.xAxis[0].data = day;
					obj.series[0].data = num;

					that.growth_rate = res.data.growth_rate;
					that.increase_time = res.data.increase_time;
					that.increase_time_status = res.data.increase_time_status;
					that.time_price = res.data.time;

					that.drawLine('canvas', obj);
				},
				error => {
					that.$dialog.error(error.msg);
				}
			);
		},
		drawLine(canvasId, chartData) {
			let _self = this;
			new uCharts({
				$this: _self,
				canvasId: canvasId,
				type: 'line',
				fontSize: 5,
				padding: [15, 15, 0, 15],
				legend: { show: false, padding: 5, lineHeight: 11, margin: 5,borderWidth:0.1 },
				dataLabel: false,
				dataPointShape: false,
				dataPointShapeType: 'hollow',
				background: '#FFFFFF',
				pixelRatio: chartData.pixelRatio,
				categories: chartData.categories,
				series: chartData.series,
				animation: false,
				xAxis: {
					type: 'grid',
					gridColor: '#CCCCCC',
					disableGrid: true,
					gridType: 'dash',
					labelCount: 3,
					dashLength: 8
				},
				yAxis: {
					gridType: 'solid',
					gridColor: '#CCCCCC',
					splitNumber: 5,
					min: 10,
					max: 180,
					format: val => {
						return val.toFixed(0) + '元';
					}
				},
				width: chartData.width,
				height: chartData.height,
				extra: {
					line: {
						type: 'straight',
						width: 0.6
					}
				}
			});
		},
		setTime: function(time) {
			this.time = time;
			var year = new Date().getFullYear(),
				month = new Date().getMonth() + 1,
				day = new Date().getDate();
			this.list = [];
			this.filter.page = 1;
			this.loaded = false;
			this.loading = false;
			switch (time) {
				case 'today':
					this.where.start = new Date(Date.parse(year + '/' + month + '/' + day)).getTime() / 1000;
					this.where.stop = new Date(Date.parse(year + '/' + month + '/' + day)).getTime() / 1000 + 24 * 60 * 60 - 1;
					this.title = '今日';
					this.getIndex();
					this.getInfo();
					break;
				case 'yesterday':
					this.where.start = new Date(Date.parse(year + '/' + month + '/' + day)).getTime() / 1000 - 24 * 60 * 60;
					this.where.stop = new Date(Date.parse(year + '/' + month + '/' + day)).getTime() / 1000 - 1;
					this.title = '昨日';
					this.getIndex();
					this.getInfo();
					break;
				case 'month':
					this.where.start = new Date(year, new Date().getMonth(), 1).getTime() / 1000;
					this.where.stop = new Date(year, month, 1).getTime() / 1000 - 1;
					this.title = '本月';
					this.getIndex();
					this.getInfo();
					break;
				case 'seven':
					this.where.start = new Date(Date.parse(year + '/' + month + '/' + day)).getTime() / 1000 + 24 * 60 * 60 - 7 * 3600 * 24;
					this.where.stop = new Date(Date.parse(year + '/' + month + '/' + day)).getTime() / 1000 + 24 * 60 * 60 - 1;
					this.title = '七日';
					this.getIndex();
					this.getInfo();
					break;
			}
		},
		setType: function(type) {
			switch (type) {
				case 'price':
					this.where.type = 1;
					break;
				case 'order':
					this.where.type = 2;
					break;
			}
		},
		change(e){
			const {range} = e;
			if(range.data.length){
				console.log(range)
				let data = range.data;
				this.list = [];
				this.filter.page = 1;
				this.loaded = false;
				this.loading = false;
				this.time = 'date';
				this.title = '';
				this.where.start = new Date(data[0]).getTime() / 1000;
				this.where.stop = new Date(data[data.length-1]).getTime() / 1000 + 24 * 60 * 60 - 1;
				this.getIndex();
				this.getInfo();
				
			}
		
		},
		dateTitle: function() {
			this.current = true;
		},
		close: function() {
			this.current = false;
		},
		getInfo: function() {
			var that = this;
			if (that.loading || that.loaded) return;
			that.loading = true;
			that.filter.start = that.where.start;
			that.filter.stop = that.where.stop;
			getStatisticsMonth(that.filter).then(
				res => {
					that.loading = false;
					that.loaded = res.data.length < that.filter.limit;
					that.list.push.apply(that.list, res.data);
					that.filter.page = that.filter.page + 1;
				},
				error => {
					that.$dialog.message(error.msg);
				}
			);
		}
	},
	onReachBottom() {
		this.getInfo();
	}
};
</script>
<style scoped>
.ucharts {
	width: 640rpx;
	height: 550rpx;
}
.calendar-wrapper {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	z-index: 777;
	transform: translate3d(0, 100%, 0);
	transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
}
.calendar-wrapper.on {
	transform: translate3d(0, 0, 0);
}
.statistical-page .wrapper .increase {
	font-size: 0.26rem;
}
.statistical-page .wrapper .increase .iconfont {
	margin-left: 0;
}
.statistical-page .navs {
	width: 100%;
	height: 96rpx;
	background-color: #fff;
	overflow: hidden;
	line-height: 96rpx;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 9;
}

.statistical-page .navs .list {
	overflow-y: hidden;
	overflow-x: auto;
	white-space: nowrap;
	-webkit-overflow-scrolling: touch;
	width: 100%;
}

.statistical-page .navs .item {
	font-size: 32rpx;
	color: #282828;
	margin-left: 60rpx;
	display: inline-block;
}

.statistical-page .navs .item.on {
	color: #2291f8;
}

.statistical-page .navs .item .iconfont {
	font-size: 25rpx;
	margin-left: 13rpx;
}

.statistical-page .wrapper {
	width: 740rpx;
	background-color: #fff;
	border-radius: 10rpx;
	margin: 119rpx auto 0 auto;
	padding: 50rpx 60rpx;
}

.statistical-page .wrapper .title {
	font-size: 30rpx;
	color: #999;
	text-align: center;
}

.statistical-page .wrapper .money {
	font-size: 72rpx;
	color: #fba02a;
	text-align: center;
	margin-top: 10rpx;
}

.statistical-page .wrapper .increase {
	font-size: 28rpx;
	color: #999;
	margin-top: 20rpx;
}

.statistical-page .wrapper .increase .red {
	color: #ff6969;
}

.statistical-page .wrapper .increase .green {
	color: #1abb1d;
}

.statistical-page .wrapper .increase .iconfont {
	font-size: 23rpx;
	margin-left: 15rpx;
}

.statistical-page .chart {
	width: 690rpx;
	height: 480rpx;
	background-color: #fff;
	border-radius: 10rpx;
	margin: 23rpx auto 0 auto;
	padding: 25rpx 22rpx 0 22rpx;
}

.statistical-page .chart .company {
	font-size: 26rpx;
	color: #999;
}

.statistical-page .mc-body {
	padding-bottom: 0;
}
</style>
