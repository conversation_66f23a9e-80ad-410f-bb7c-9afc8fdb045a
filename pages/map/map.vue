<template>
    <view>
        <!-- #ifdef H5 -->
        <view>
            <iframe v-if="locationShow && !isWeixin" ref="geoPage" width="0" height="0" frameborder="0"
                style="display:none;" scrolling="no"
                :src="'https://apis.map.qq.com/tools/geolocation?key=' + mapKey + '&referer=myapp'"></iframe>
        </view>
        <view class="geoPage">
            <iframe width="100%" height="100%" frameborder="0" scrolling="no" :src="txMapUrl"></iframe>
        </view>
        <!-- #endif -->

    </view>
</template>

<script>
    import {
        authOpenLocation
    } from '@/utils/common.js';
    // #ifdef H5
    import {
        isWeixin
    } from '@/utils/validate.js';
    const _isWeixin = isWeixin();
    // #endif
    // #ifdef MP
    const _isWeixin = true;
    // #endif
    import {
        LONGITUDE,
        LATITUDE,
        MAPKEY
    } from '@/config.js';
    export default {
        data() {
            return {
                mapKey: this.$storage.get(MAPKEY),
                locationShow: false,
                isWeixin: _isWeixin,
                lnglat: {
                    latitude: '39.833932',
                    longitude: '116.281824'
                }
            }
        },
        computed: {
            txMapUrl() {
                const {
                    latitude,
                    longitude,
                    // name,
                    address
                } = this.lnglat
                return `https://apis.map.qq.com/uri/v1/geocoder?marker=coord:${latitude},${longitude};addr:${address}&referer=${this.mapKey}`
            }
        },
        onLoad(options) {
            const {
                latitude,
                longitude,
                // name,
                address = '',
                detailed_address
            } = this.$store.state.storeItems;
            this.lnglat = {
                latitude,
                longitude,
                // name,
                address: address + detailed_address
            }
            if (_isWeixin) {
                let config = {
                    latitude: parseFloat(latitude),
                    longitude: parseFloat(longitude),
                    // name,
                    address: address + detailed_address
                };
                // #ifdef MP
                this.$navigator(-1);
                // #endif
                authOpenLocation(config);
            } else {
                if (!this.$storage.get(MAPKEY)) {
                    this.$showToast('暂无法使用查看地图，请配置您的腾讯地图key')
                    this.$navigator(-1)
                } else {
                    let loc;
                    let _this = this;
                    //监听定位组件的message事件
                    window.addEventListener(
                        'message',
                        function(event) {
                            loc = event.data; // 接收位置信息 LONGITUDE
                            if (loc && loc.module == 'geolocation') {
                                _this.$storage.set(LATITUDE, loc.lat);
                                _this.$storage.set(LONGITUDE, loc.lng);
                            } else {
                                _this.$storage.remove(LATITUDE);
                                _this.$storage.remove(LONGITUDE);
                                //定位组件在定位失败后，也会触发message, event.data为null
                                // console.log('定位失败', event);
                            }
                        },
                        false
                    );
                };
            }

        }
    }
</script>

<style scoped lang="scss">
    .geoPage {
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: 10000;
    }
</style>