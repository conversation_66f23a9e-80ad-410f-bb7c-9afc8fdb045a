<template>
	<view>
		<view class="collectionGoods" v-if="collectProductList.length > 0">
			<view @click="goPages(`/pages/shop/GoodsCon?id=${item.pid}`)" class="item acea-row row-between-wrapper" v-for="(item, index) in collectProductList" :key="index">
				<view class="pictrue"><img :src="item.image" /></view>
				<view class="text acea-row row-column-between">
					<view class="infor line1">{{ item.store_name }}</view>
					<view class="acea-row row-between-wrapper">
						<view class="money font-color-red">￥{{ item.price }}</view>
						<view class="delete" @click.stop="delCollection(index)">删除</view>
					</view>
				</view>
			</view>
		</view>
		<x-home></x-home>
		<x-authorize :isHidden="true" @login="getUserCollectProduct"></x-authorize>
		<Loading :loaded="loadend" :loading="loading"></Loading>
		<xNodate :arr="collectProductList" :page="page" imgSrc="/wximage/noCollection.png"></xNodate>
	</view>
</template>
<script>
import { getCollectUser, getCollectDel } from '@/api/user';
import xNodate from '@/components/x-nodata/x-nodata.vue';
import Loading from '@/components/Loading';
export default {
	name: 'GoodsCollection',
	components: {
		Loading,
		xNodate
	},
	props: {},
	data: function() {
		return {
			page: 1,
			limit: 20,
			collectProductList: [],
			loadTitle: '',
			loading: false,
			loadend: false
		};
	},
	watch: {
		// #ifdef H5
		$route(n) {
			if (n.name === 'GoodsCollection') {
				this.page = 1;
				this.loading = false;
				this.loadend = false;
				this.getUserCollectProduct();
			}
		}
		// #endif
	},
	mounted: function() {
		this.getUserCollectProduct();
	},
	methods: {
		goPages(path) {
			this.$navigator(path);
		},
		getUserCollectProduct: function() {
			let that = this;
			if (that.loading) return; //阻止下次请求（false可以进行请求）；
			if (that.loadend) return; //阻止结束当前请求（false可以进行请求）；
			that.loading = true;
			getCollectUser(that.page, that.limit).then(res => {
				that.loading = false;
				//apply();js将一个数组插入另一个数组;
				that.collectProductList.push.apply(that.collectProductList, res.data);
				that.loadend = res.data.length < that.limit; //判断所有数据是否加载完成；
				that.page = that.page + 1;
			});
		},
		//删除收藏；
		delCollection: function(index) {
			let that = this,
				id = that.collectProductList[index].pid,
				category = that.collectProductList[index].category;
			getCollectDel(id, category)
				.then(function() {
					that.collectProductList.splice(index, 1);
					that.$set(that, 'collectProductList', that.collectProductList);
					that.$showToast('删除收藏成功!');
				})
				.catch(err => {
					that.$showToast('删除失败!');
				});
		}
	},
	onReachBottom() {
		this.getUserCollectProduct();
	}
};
</script>
<style scoped lang="scss">
page {
	background-color: $uni-bg-color-page;
}
.collectionGoods {
	background-color: #fff;
	border-top: 1px solid #eee;
}

.collectionGoods .item {
	margin-left: 30rpx;
	padding-right: 30rpx;
	border-bottom: 1px solid #eee;
	height: 180rpx;
}

.collectionGoods .item .pictrue {
	width: 130rpx;
	height: 130rpx;
}

.collectionGoods .item .pictrue img {
	width: 100%;
	height: 100%;
	border-radius: 6rpx;
}

.collectionGoods .item .text {
	width: 535rpx;
	height: 130rpx;
	font-size: 28rpx;
	color: #282828;
}

.collectionGoods .item .text .infor {
	width: 100%;
}

.collectionGoods .item .text .money {
	font-size: 26rpx;
}

.collectionGoods .item .text .delete {
	font-size: 26rpx;
	color: #282828;
	width: 113rpx;
	height: 46rpx;
	border: 1px solid #bbb;
	border-radius: 4rpx;
	text-align: center;
	line-height: 46rpx;
}
</style>
