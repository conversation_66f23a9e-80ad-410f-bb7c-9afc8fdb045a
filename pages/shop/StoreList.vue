<template>
	<view>
		<view class="storeBox">
			<view class="storeBox-box" v-for="(item, index) in storeList" :key="index" @click.stop="checked(item)">
				<view class="store-img"><image :src="item.image" lazy-load="true" /></view>
				<view class="store-cent-left">
					<view class="store-name">{{ item.name }}</view>
					<view class="store-address line1">{{ item.address }}{{ ', ' + item.detailed_address }}</view>
				</view>
				<view class="row-right">
					<view>
						<!-- #ifdef H5 -->
						<a class="store-phone" :href="'tel:' + item.phone"><span class="iconfont icon-dadianhua01"></span></a>
						<!-- #endif -->
						<!-- #ifdef MP -->
						<view class="store-phone" @click="makePhone(item.phone)"><text class="iconfont icon-dadianhua01"></text></view>
						<!-- #endif -->
					</view>
					<view class="store-distance" @click.stop="showMaoLocation(item)">
						<span class="addressTxt" v-if="item.range">距离{{ item.range }}千米</span>
						<span class="addressTxt" v-else>查看地图</span>
						<span class="iconfont icon-youjian"></span>
					</view>
				</view>
			</view>
			<Loading :loaded="loaded" :loading="loading"></Loading>
		</view>
		<!-- #ifdef H5 -->
		<view>
			<iframe
				v-if="locationShow && !isWeixin"
				ref="geoPage"
				width="0"
				height="0"
				frameborder="0"
				style="display:none;"
				scrolling="no"
				:src="'https://apis.map.qq.com/tools/geolocation?key=' + mapKey + '&referer=myapp'"
			></iframe>
		</view>
		<view class="geoPage" v-if="mapShow">
			<iframe
				width="100%"
				height="100%"
				frameborder="0"
				scrolling="no"
				:src="'https://apis.map.qq.com/uri/v1/geocoder?coord=' + system_store.latitude + ',' + system_store.longitude + '&referer=' + mapKey"
			></iframe>
		</view>
		<!-- #endif -->
		<x-home></x-home>
        <!-- #ifdef MP-WEIXIN -->
        <x-agreement ref="agreement"></x-agreement>
        <!-- #endif -->
	</view>
</template>

<script>
import Loading from '@/components/Loading';
import { storeListApi } from '@/api/store';
import { authGetLocation, authOpenLocation } from '@/utils/common.js';
import { mapGetters } from 'vuex';
// import cookie from '@utils/store/cookie';
// #ifdef H5 
import { isWeixin } from '@/utils/validate.js';
const _isWeixin = isWeixin();
// #endif

// #ifdef MP
const _isWeixin = true;
// #endif


import { LONGITUDE, LATITUDE, MAPKEY } from '@/config.js';
export default {
	name: 'storeList',
	components: { Loading },
	computed: mapGetters(['goName']),
	data() {
		return {
			page: 1,
			limit: 20,
			loaded: false,
			loading: false,
			storeList: [],
			mapShow: false,
			system_store: {},
			mapKey: this.$storage.get(MAPKEY),
			locationShow: false,
			isWeixin:_isWeixin
		};
	},
	mounted() {
		
	},
	onLoad(options){
		const {id} = options;
		if (this.$storage.get(LONGITUDE) && this.$storage.get(LATITUDE)) {
			this.getList(id);
		} else {
			this.selfLocation(id);
		}
	},
	methods: {
		selfLocation(id) {
			if (_isWeixin) {
				authGetLocation()
					.then(res => {
						this.getList(id);
					})
					.catch(err => {
						this.$showToast(err.msg || err);
						this.getList(id);
					});
			} else {
				if (!this.$storage.get(MAPKEY)) return this.$showToast('暂无法使用查看地图，请配置您的腾讯地图key');
				let loc;
				let _this = this;
				if (this.$storage.get(MAPKEY)) _this.locationShow = true;
				//监听定位组件的message事件
				window.addEventListener(
					'message',
					function(event) {
						loc = event.data; // 接收位置信息 LONGITUDE
						console.log('location', loc);
						if (loc && loc.module == 'geolocation') {
							_this.$storage.set(LATITUDE, loc.lat);
							_this.$storage.set(LONGITUDE, loc.lng);
							_this.getList(id);
						} else {
							_this.$storage.remove(LATITUDE);
							_this.$storage.remove(LONGITUDE);
							_this.getList(id);
							//定位组件在定位失败后，也会触发message, event.data为null
							console.log('定位失败');
						}
					},
					false
				);
			}
		},
		showMaoLocation(e) {
			this.system_store = e;
			if (_isWeixin) {
				let config = {
					latitude: parseFloat(this.system_store.latitude),
					longitude: parseFloat(this.system_store.longitude),
					name: this.system_store.name,
					address: this.system_store.address + this.system_store.detailed_address
				};
				authOpenLocation(config);
			} else {
				if (!this.$storage.get(MAPKEY)) return this.$showToast('暂无法使用查看地图，请配置您的腾讯地图key');
				this.mapShow = true;
			}
		},
		// 选中门店
		checked(item) {
			if (this.goName === 'orders') {
				this.$store.commit('GET_STORE', item);
				this.$navigator(-1);
			}
		},
		makePhone(phone) {
			uni.makePhoneCall({
				phoneNumber: phone
			});
		},
		// 获取门店列表数据
		getList: function(id) {
			if (this.loading || this.loaded) return;
			this.loading = true;
			let data = {
				latitude: this.$storage.get(LATITUDE) || '', //纬度
				longitude: this.$storage.get(LONGITUDE) || '', //经度
				page: this.page,
				limit: this.limit,
				product_id:id || this.id
			};
			storeListApi(data)
				.then(res => {
					this.loading = false;
					this.loaded = res.data.list.length < this.limit;
					this.storeList.push.apply(this.storeList, res.data.list);
					this.page = this.page + 1;
				})
				.catch(err => {
					this.$showToast(err.msg || err);
				});
		}
	},
	onReachBottom() {
		this.getList(this.id);
	}
};
</script>

<style scoped>
a {
	text-decoration: none;
}
.geoPage {
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
	z-index: 10000;
}
.storeBox {
	width: 100%;
	background-color: #fff;
	padding: 0 30rpx;
}
.storeBox-box {
	width: 100%;
	height: auto;
	display: flex;
	align-items: center;
	padding: 23rpx 0;
	justify-content: space-between;
	border-bottom: 1px solid #eee;
}
.store-cent {
	display: flex;
	align-items: center;
	width: 80%;
}
.store-cent-left {
	width: 45%;
}
.store-img {
	width: 120rpx;
	height: 120rpx;
	border-radius: 6rpx;
	margin-right: 22rpx;
}
.store-img image {
	width: 100%;
	height: 100%;
}
.store-name {
	color: #282828;
	font-size: 30rpx;
	margin-bottom: 22rpx;
	font-weight: 800;
}
.store-address {
	color: #666666;
	font-size: 24rpx;
}
.store-phone {
	width: 50rpx;
	height: 50rpx;
	color: #fff;
	border-radius: 50%;
	display: block;
	text-align: center;
	line-height: 50rpx;
	background-color: #e83323;
	margin-bottom: 22rpx;
}
.store-distance {
	font-size: 22rpx;
	color: #e83323;
}
.iconfont {
	font-size: 20rpx;
}
.row-right {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	width: 33.5%;
}
</style>
