<template>
	<view >
		<view class="searchGood">
			<view class="search acea-row row-between-wrapper">
				<view class="input acea-row row-between-wrapper">
					<span class="iconfont icon-sousuo2"></span>
					<form @submit.prevent="submit"></form>
					<input type="text" placeholder="点击搜索商品" v-model="search" @confirm="submit"/>
				</view>
				<view class="bnt" @click="submit">搜索</view>
			</view>
			<view v-if="keywords.length">
				<view class="title">热门搜索</view>
				<view class="list acea-row">
					<view class="item" v-for="key of keywords" :key="key" @click="toSearch(key)">{{ key }}</view>
				</view>
			</view>
			<view class="line"></view>
			<GoodList :goodList="goodList" :isSort="false"></GoodList>
			<Loading :loaded="loadend" :loading="loading" v-if="goodList.length"></Loading>
		</view>
		<x-home></x-home>
		<xNodate :arr="goodList" :page="where.page" imgSrc="/wximage/noSearch.png" ></xNodate>
	</view>
</template>

<script>

import Loading from '@/components/Loading';
import GoodList from '@/components/GoodList';
import { getSearchKeyword, getProducts } from '@/api/store';
import xNodate from '@/components/x-nodata/x-nodata.vue';

export default {
	name: 'GoodSearch',
	components: {
		GoodList,
		Loading,
		xNodate
	},
	data() {
		return {
			keywords: [],
			search: '',
			where: {
				page: 1,
				limit: 20,
				keyword: ''
			},
			loading: false,
			loadend: false,
			goodList: []
		};
	},
	methods: {
		goPages(path) {
			this.$navigator(path);
		},
		submit() {
			const search = this.search.trim() || '';
			if (!search) return;
			this.where.keyword = search;
			this.$set(this, 'goodList', []);
			this.where.page = 1;
			this.loadend = false;
			this.loading = false;
			this.getGoodlist();
		},
		getSearchData() {
			getSearchKeyword().then(res => {
				this.keywords = res.data;
			});
		},

		toSearch: function(key) {
			this.where.keyword = key;
			this.search = key;
			this.$set(this, 'goodList', []);
			this.where.page = 1;
			this.loadend = false;
			this.loading = false;
			this.getGoodlist();
		},
		getGoodlist: function() {
			let that = this;
			if (that.loading) return; //阻止下次请求（false可以进行请求）；
			if (that.loadend) return; //阻止结束当前请求（false可以进行请求）；
			that.loading = true;
			let q = that.where;
			getProducts(q).then(res => {
				that.loading = false;
				that.goodList.push.apply(that.goodList, res.data);
				that.loadend = res.data.length < that.where.limit; //判断所有数据是否加载完成；
				that.where.page = that.where.page + 1;
			});
		}
	},
	mounted() {
		this.getSearchData();
	},
	onReachBottom() {
		!this.loading && this.getGoodlist();
	}
};
</script>
<style lang="scss">
	page{
		background-color: $uni-bg-color;
	}
</style>
<style scoped lang="scss">
.searchGood .search {
	padding-left: 30rpx;
}

.searchGood .search {
	padding-top: 20rpx;
}

.searchGood .search .input {
	width: 598rpx;
	background-color: #f7f7f7;
	border-radius: 33rpx;
	padding: 0 35rpx;
	height: 66rpx;
}

.searchGood .search .input input {
	width: 472rpx;
	font-size: 28rpx;
}

.searchGood .search .input input::placeholder {
	color: #bbb;
}

.searchGood .search .input .iconfont {
	color: #000;
	font-size: 35rpx;
}

.searchGood .search .bnt {
	width: 120rpx;
	text-align: center;
	height: 66rpx;
	line-height: 66rpx;
	font-size: 30rpx;
	color: #282828;
}

.searchGood .title {
	font-size: 28rpx;
	color: #999;
	margin: 50rpx 30rpx 25rpx 30rpx;
}

.searchGood .list {
	padding-left: 10rpx;
}

.searchGood .list .item {
	font-size: 26rpx;
	color: #454545;
	padding: 0 21rpx;
	height: 60rpx;
	border-radius: 3rpx;
	line-height: 60rpx;
	border: 1px solid #aaa;
	margin: 0 0 20rpx 20rpx;
}

.searchGood .line {
	border-bottom: 1px solid #eee;
	margin: 20rpx 30rpx 0 30rpx;
}
</style>
