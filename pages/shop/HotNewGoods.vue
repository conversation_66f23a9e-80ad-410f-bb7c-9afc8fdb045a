<template>
	<view class="quality-recommend">
		<view class="slider-banner swiper">
			<view class="swiper-wrapper" :options="RecommendSwiper">
				<xSwiper :arr="imgUrls" height="330rpx" :dots="true" srcName="img">
					<template v-slot="{ item }">
						<view class="swiper-slide" @click="goPages(`/pages/shop/news/NewsDetail?id=${item.id}`)">
							<image :src="item.img" mode="" alt="img" class="slide-image"></image>
						</view>
					</template>
				</xSwiper>
			</view>
			<view class="swiper-pagination"></view>
		</view>
		<view class="title acea-row row-center-wrapper">
			<view class="line"></view>
			<view class="name">
				<span class="iconfont" :class="icon"></span>
				{{ name }}
			</view>
			<view class="line"></view>
		</view>
		<GoodList :good-list="goodsList" :is-sort="false"></GoodList>
		<x-home></x-home>
	</view>
</template>
<script>
import xSwiper from '@/components/x-swiper/x-swiper.vue';
import GoodList from '@/components/GoodList';
import { getGroomList } from '@/api/store';
export default {
	name: 'HotNewGoods',
	components: {
		xSwiper,
		GoodList
	},
	props: {},
	data: function() {
		return {
			imgUrls: [],
			goodsList: [],
			name: '',
			icon: '',
			RecommendSwiper: {
				pagination: {
					el: '.swiper-pagination',
					clickable: true
				},
				autoplay: {
					disableOnInteraction: false,
					delay: 2000
				},
				loop: true,
				speed: 1000,
				observer: true,
				observeParents: true
			}
		};
	},
	onLoad(options){
		const {type=1} = options;
		this.titleInfo(type);
		this.getIndexGroomList(type);
	},
	methods: {
		goPages(path, type) {
			this.$navigator(path);
		},
		titleInfo: function(type) {
			if (type === '1') {
				this.name = '精品推荐';
				this.icon = 'icon-jingpintuijian';
			} else if (type === '2') {
				this.name = '热门榜单';
				this.icon = 'icon-remen';
			} else if (type === '3') {
				this.name = '首发新品';
				this.icon = 'icon-xinpin';
			}
			
			this.$updateTitle(this.name)
		},
		getIndexGroomList: function(type) {
			let that = this;
			getGroomList(type)
				.then(res => {
					that.imgUrls = res.data.banner;
					that.goodsList = res.data.list;
				})
				.catch(function(err) {
					that.$showToast(err.msg || err);
				});
		}
	}
};
</script>
<style scoped>
.quality-recommend .swiper-slide {
		width: 100%;
		height: 330rpx;
	}
	.quality-recommend .slide-image {
		width: 100%;
		height: 100%;
	}
	.quality-recommend .title {
		height: 120rpx;
		font-size: 32rpx;
		color: #282828;
		background-color: #f5f5f5;
	}
	
	.quality-recommend .title .line {
		width: 229rpx;
		height: 2rpx;
		background-color: #e9e9e9;
	}
	
	.quality-recommend .title .name {
		margin: 0 20rpx;
	}
	
	.quality-recommend .title .name .iconfont {
		margin-right: 13rpx;
		font-size: 38rpx;
		vertical-align: -4rpx;
	}
</style>	
