<template>
	<view class="evaluate-list" ref="container">
		<view class="header">
			<view class="generalComment acea-row row-between-wrapper">
				<view class="acea-row row-middle font-color-red">
					<view class="evaluate">评分</view>
					<view class="start" :class="'star' + replyData.reply_star"></view>
				</view>
				<view>
					<span class="font-color-red">{{ replyData.reply_chance || 0 }}%</span>
					好评率
				</view>
			</view>
			<view class="nav acea-row row-middle">
				<view class="acea-row row-center-wrapper" v-for="(item, index) in navList" :key="index" @click="changeType(index)">
					<view class="item" :class="currentActive === index ? 'bg-color-red' : ''" v-if="item.num">{{ item.evaluate }}({{ item.num }})</view>
				</view>
			</view>
		</view>
		<UserEvaluation :reply="reply"></UserEvaluation>
		<Loading :loaded="loadend" :loading="loading"></Loading>
		<x-home></x-home>
	</view>
</template>
<script>
import UserEvaluation from '@/components/UserEvaluation';
import { getReplyConfig, getReplyList } from '@/api/store';
import Loading from '@/components/Loading';
let NAME = 'EvaluateList';
export default {
	name: 'EvaluateList',
	components: {
		UserEvaluation,
		Loading
	},
	props: {},
	data: function() {
		return {
			product_id: 0,
			replyData: {},
			navList: [{ evaluate: '全部', num: 0 }, { evaluate: '好评', num: 0 }, { evaluate: '中评', num: 0 }, { evaluate: '差评', num: 0 }],
			currentActive: 0,
			page: 1,
			limit: 8,
			reply: [],
			loadTitle: '',
			loading: false,
			loadend: false
		};
	},
	mounted: function() {
	},
	onLoad(options) {
		const {id} = options;
		this.product_id = id;
		this.getProductReplyCount();
		this.getProductReplyList();
	},
	methods: {
		getProductReplyCount: function() {
			let that = this;
			getReplyConfig(that.product_id).then(res => {
				that.$set(that, 'replyData', res.data);
				that.navList[0].num = res.data.sum_count;
				that.navList[1].num = res.data.good_count;
				that.navList[2].num = res.data.in_count;
				that.navList[3].num = res.data.poor_count;
			});
		},
		getProductReplyList: function() {
			let that = this;
			if (that.loading) return; //阻止下次请求（false可以进行请求）；
			if (that.loadend) return; //阻止结束当前请求（false可以进行请求）；
			that.loading = true;
			let q = { page: that.page, limit: that.limit, type: that.currentActive };
			getReplyList(that.product_id, q).then(res => {
				that.loading = false;
				//apply();js将一个数组插入另一个数组;
				that.reply.push.apply(that.reply, res.data);
				that.loadend = res.data.length < that.limit; //判断所有数据是否加载完成；
				that.page = that.page + 1;
			});
		},
		changeType: function(index) {
			let that = this;
			that.currentActive = index;
			that.page = 1;
			that.loadend = false;
			that.$set(that, 'reply', []);
			that.getProductReplyList();
		}
	},
	onReachBottom() {
		this.getProductReplyList();
	}
};
</script>
<style scoped>
.evaluate-list .generalComment {
	height: 94rpx;
	padding: 0 30rpx;
	background-color: #fff;
	font-size: 28rpx;
	color: #808080;
}

.evaluate-list .generalComment .evaluate {
	margin-right: 7rpx;
}

.evaluate-list .nav {
	font-size: 24rpx;
	color: #282828;
	padding: 0 30rpx 15rpx 30rpx;
	background-color: #fff;
	border-bottom: 1px solid #f5f5f5;
}

.evaluate-list .nav .item {
	font-size: 24rpx;
	color: #282828;
	border-radius: 6rpx;
	height: 54rpx;
	padding: 0 20rpx;
	background-color: #f4f4f4;
	line-height: 54rpx;
	margin: 0 17rpx 17rpx 0;
}

.evaluate-list .nav .item.bg-color-red {
	color: #fff;
}
</style>
