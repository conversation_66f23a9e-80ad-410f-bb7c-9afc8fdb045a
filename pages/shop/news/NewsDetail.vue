<template>
	<view class="newsDetail">
		<view class="title">{{ articleInfo.title }}</view>
		<view class="list acea-row row-middle">
			<view class="label line1">{{ articleInfo.cart_name }}</view>
			<view class="item">
				<span class="iconfont"></span>
				{{ articleInfo.add_time }}
			</view>
		</view>
		<view class="conter" >
            <mp-html :content="articleInfo.content" />
		</view>
		<view class="picTxt acea-row row-between-wrapper" v-if="storeInfo.id">
			<view class="pictrue">
                <image :src="storeInfo.image" />
            </view>
			<view class="text">
				<view class="name line1">{{ storeInfo.store_name }}</view>
				<view class="money font-color-red">
					￥
					<span class="num">{{ storeInfo.price }}</span>
				</view>
				<view class="y_money">￥{{ storeInfo.ot_price }}</view>
			</view>
			<view @click="goPages(`/pages/shop/GoodsCon?id=${storeInfo.id}`)">
				<view class="label"><span class="span">查看商品</span></view>
			</view>
		</view>
		<!-- #ifdef H5 -->
		<view class="bnt bg-color-red" v-if="isWeixin" @click="setShareInfoStatus">和好友一起分享</view>
		<ShareInfo v-on:setShareInfoStatus="setShareInfoStatus" :shareInfoStatus="shareInfoStatus"></ShareInfo>
		<!-- #endif -->
		<!-- #ifdef MP -->
		<!-- <button class="bnt bg-color" open-type="share" hover-class="none">和好友一起分享</button> -->
        
        <view class="pannel_r">
            <view class="btn flex">
                <!-- #ifdef MP -->
                <button class="btn_l flex flex_around flex_align_center" open-type="share">
                    <!-- #endif -->
                    <!-- #ifdef H5 -->
                    <button class="btn_l flex flex_around flex_align_center" @click="posters = !posters">
                        <!-- #endif -->
                        <view class="flex flex_align_center">
                            <image src="@/static/images/yuanshi/share1.png" mode="widthFix"></image>
                            <view class="tt_margin-right">分享</view>
                        </view>
                    </button>
                </button>
            </view>
        </view>
		<!-- #endif -->
	</view>
</template>

<script>
    import mpHtml from '@/components/mp-html/mp-html'
import { getArticleDetails } from '@/api/public';
import ShareInfo from '@/components/ShareInfo';
import { SHARE_ID } from '@/config.js';
// #ifdef H5
import { isWeixin } from '@/utils/validate.js';
import { openShareAll } from '@/utils/wechat/share.js';
const _isWeixin = isWeixin();
// #endif

// #ifdef MP
const _isWeixin = true;
// #endif


export default {
	name: 'NewsDetail',
	components: { 
        ShareInfo,
        mpHtml
    },
	props: {},
	data: function() {
		return {
			articleInfo: {},
			storeInfo: {},
			shareInfoStatus: false,
			isWeixin: _isWeixin,
			shareObj: {}
		};
	},
	onLoad(options) {
        console.log('id----',id)
		const { id } = options;
		this.articleDetails(id);
	},
	methods: {
		articleDetails(id) {
			let that = this;
			getArticleDetails(id).then(res => {
				that.articleInfo = res.data;
				that.storeInfo = res.data.store_info || {};
				that.$updateTitle(that.articleInfo.title || '新闻详情');
				that.shareObj = {
					desc: that.articleInfo.synopsis,
					title: that.articleInfo.title,
					link: `/pages/shop/news/NewsDetail?id=${id}`,
					imgUrl: that.articleInfo.image_input.length ? that.articleInfo.image_input[0] : ''
				};
				// #ifdef H5
				if (that.isWeixin) {
					openShareAll(that.shareObj);
				}
				// #endif
			}).catch(err=>{
				that.$showToast(err.msg || err);
				that.$navigator(-1)
			});
		},
		setShareInfoStatus: function() {
			this.shareInfoStatus = !this.shareInfoStatus;
		}
	},
	// #ifdef MP
	onShareAppMessage() {
		if (this.shareObj) {
			return {
				title: this.shareObj.title || '新闻详情',
				imageUrl: this.shareObj.imgUrl || '',
				path: this.shareObj.link,
                templateId: SHARE_ID
			};
		}
	}
	// #endif
};
</script>
<style lang="scss">
	page {
		background-color: $uni-bg-color;
	}
::deep .conter .img{
	width:100% !important;
}
</style>
<style scoped>
.newsDetail .title {
	padding: 0 30rpx;
	font-size: 44rpx;
	color: #282828;
	font-weight: bold;
	margin: 45rpx 0 28rpx 0;
	line-height: 1.5;
}

.newsDetail .list {
	margin: 0 30rpx;
	padding-bottom: 38rpx;
}

.newsDetail .list .label {
	font-size: 30rpx;
	height: 38rpx;
	border-radius: 3rpx;
	text-align: center;
	line-height: 36rpx;
	max-width: 190rpx;
	width: unset;
	color: #b1b2b3;
}

.newsDetail .list .item {
	margin-left: 36rpx;
	font-size: 30rpx;
	color: #999;
}

.newsDetail .list .item .iconfont {
	font-size: 30rpx;
	color: #b1b2b3;
}

.newsDetail .list .item .iconfont.icon-shenhezhong {
	font-size: 26rpx;
}

.newsDetail .conter {
	padding: 0 30rpx;
	font-size: 32rpx !important;
	color: #8a8b8c !important;
	line-height: 1.8;
	word-wrap: break-word;
	word-break: normal;
}

.newsDetail .conter p {
	font-size: 32rpx !important;
}

.newsDetail .conter image {
	width: 100% !important;
	display: block;
}
.newsDetail .picTxt {
	width: 690rpx;
	height: 200rpx !important;
	border-radius: 20rpx;
	border: 1px solid #e1e1e1;
	position: relative;
	margin: 30rpx auto 0 auto;
}
.newsDetail .picTxt .pictrue {
	width: 200rpx;
	height: 200rpx;
}
.newsDetail .picTxt .pictrue image {
	width: 100%;
	height: 100%;
	border-radius: 20rpx 0 0 20rpx;
	display: block;
}
.newsDetail .picTxt .text {
	width: 459rpx;
}
.newsDetail .picTxt .text .name {
	font-size: 30rpx;
	color: #282828;
}
.newsDetail .picTxt .text .money {
	font-size: 24rpx;
	margin-top: 40rpx;
	font-weight: bold;
}
.newsDetail .picTxt .text .money .num {
	font-size: 36rpx;
}
.newsDetail .picTxt .text .y_money {
	font-size: 26rpx;
	color: #999;
	text-decoration: line-through;
}
.newsDetail .picTxt .label {
	position: absolute;
	background-color: #303131;
	width: 160rpx;
	height: 50rpx;
	right: -7rpx;
	border-radius: 25rpx 0 6rpx 25rpx;
	text-align: center;
	line-height: 50rpx;
	bottom: 24rpx;
}
.newsDetail .picTxt .label .span {
	background-image: linear-gradient(to right, #fff71e 0%, #f9b513 100%);
	background-image: -webkit-linear-gradient(to right, #fff71e 0%, #f9b513 100%);
	background-image: -moz-linear-gradient(to right, #fff71e 0%, #f9b513 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}
.newsDetail .picTxt .label:after {
	content: ' ';
	position: absolute;
	width: 0;
	height: 0;
	border-bottom: 8rpx solid #303131;
	border-right: 8rpx solid transparent;
	top: -8rpx;
	right: 0;
}
.newsDetail .pannel_r .btn {
  margin-top: 30rpx;
}
.newsDetail .pannel_r .btn button {
  width: 172rpx;
  height: 72rpx;
  line-height: 72rpx;
  background: #ffffff;
  opacity: 1;
  border-radius: 30rpx;
  color: #666666;
  font-size: 24rpx;
  margin: 0 auto;
}
.newsDetail .pannel_r .btn button.btn_l {
  border: 2rpx solid #d2d2d2;
}
.newsDetail .pannel_r .btn button.btn_l image {
  width: 31rpx;
  margin-right: 14rpx;
}
.newsDetail .pannel_r .btn button.btn_r {
  border: 2rpx solid #ff5656;
  color: #ff5656;
}
.tt_margin-right {
    margin-right: 28rpx;
}

</style>