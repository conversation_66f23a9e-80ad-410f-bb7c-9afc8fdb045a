<template>
	<view class="newsList" ref="container">
		<view class="slider-banner swiperNews" v-if="imgUrls.length > 0">
			<view class="swiper-wrapper">
				<!-- #ifndef MP-TOUTIAO -->
				<xSwiper :arr="imgUrls" height="330rpx" :dots="true" indicatorActiveColor="#666">
					<template v-slot="{ item }">
						<view class="swiper-slide" @click="goPages(`/pages/shop/news/NewsDetail?id=${item.id}`)">
							<image :src="item.image_input[0]" mode="" alt="img" class="slide-image"></image>
						</view>
					</template>
				</xSwiper>
				
				<!-- #endif -->
				<!-- #ifdef MP-TOUTIAO -->
				<!-- 头条小程序不支持slot-scope 解构 -->
				<ttSwiper :arr="imgUrls" height="330rpx" :dots="true" indicatorActiveColor="#666" name="swiperNews">
				</ttSwiper>
				<!-- #endif -->
			</view>
		</view>
		<xTab :arr="navLsit" title="title" @change="tabClick"></xTab>
		<view class="list" v-for="(item, index) in articleList" :key="index">
			<view @click="goPages(`/pages/shop/news/NewsDetail?id=${item.id}`)" class="item acea-row row-between-wrapper" v-if="item.image_input.length === 1">
				<view class="text acea-row row-column-between">
					<view class="name line2">{{ item.title }}</view>
					<view>{{ item.add_time }}</view>
				</view>
				<view class="pictrue"><image :src="item.image_input[0]" /></view>
			</view>
			<view @click="goPages(`/pages/shop/news/NewsDetail?id=${item.id}`)" class="item" v-if="item.image_input.length === 2">
				<view class="title line1">{{ item.title }}</view>
				<view class="picList acea-row row-between-wrapper">
					<view class="pictrue" v-for="(itemImg, index) in item.image_input" :key="index"><image :src="itemImg" /></view>
				</view>
				<view class="time">{{ item.add_time }}</view>
			</view>
			<view @click="goPages(`/pages/shop/news/NewsDetail?id=${item.id}`)" class="item" v-if="item.image_input.length === 3">
				<view class="title line1">{{ item.title }}</view>
				<view class="picList on acea-row row-between-wrapper">
					<view class="pictrue" v-for="(itemImg, index) in item.image_input" :key="index"><image :src="itemImg" /></view>
				</view>
				<view class="time">{{ item.add_time }}</view>
			</view>
			<Loading :loaded="loadend" :loading="loading" v-if="index > 0 && articleList.length > 0"></Loading>
		</view>
		<xNodate :arr="articleList" :page="page" imgSrc="/wximage/noNews.png" ></xNodate>
		<x-home></x-home>
	</view>
</template>
<script>
import { getArticleBanner, getArticleCategory, getArticleHotList, getArticleList } from '@/api/public';
import Loading from '@/components/Loading';
import xSwiper from '@/components/x-swiper/x-swiper.vue';
import xTab from '@/components/x-tab/x-tab.vue';
// #ifdef MP-TOUTIAO
import ttSwiper from '@/ttcomponents/x-swiper/x-swiper.vue';
// #endif
import xNodate from '@/components/x-nodata/x-nodata.vue';
export default {
	name: 'NewsList',
	components: {
		xSwiper,
		Loading,
		xTab,
		// #ifdef MP-TOUTIAO
		ttSwiper,
		// #endif
		xNodate
	},
	props: {},
	data: function() {
		return {
			page: 1,
			limit: 20,
			loadTitle: '',
			loading: false,
			loadend: false,
			imgUrls: [],
			navLsit: [],
			articleList: [],
			active: 0,
			cid: 0,
			swiperNew: {
				pagination: {
					el: '.swiper-pagination',
					clickable: true
				},
				autoplay: {
					disableOnInteraction: false,
					delay: 2000
				},
				loop: true,
				speed: 1000,
				observer: true,
				observeParents: true
			}
		};
	},
	mounted: function() {
		this.articleBanner();
		this.articleCategory();
		this.articleHotList();
	},
	methods: {
		goPages(path, type) {
			this.$navigator(path);
		},
		articleBanner: function() {
			let that = this;
			getArticleBanner().then(res => {
				console.log(res);
				that.imgUrls = res.data;
			});
		},
		articleCategory: function() {
			let that = this;
			getArticleCategory().then(res => {
				that.navLsit = res.data;
			});
		},
		articleHotList: function() {
			let that = this;
			getArticleHotList().then(res => {
				that.articleList = res.data;
			});
		},
		getArticleLists: function() {
			let that = this;
			if (that.cid == 0) return;
			if (that.loading) return; //阻止下次请求（false可以进行请求）；
			if (that.loadend) return; //阻止结束当前请求（false可以进行请求）；
			that.loading = true;
			let q = {
				page: that.page,
				limit: that.limit
			};
			getArticleList(q, that.cid).then(res => {
				that.loading = false;
				that.articleList.push.apply(that.articleList, res.data);
				that.loadend = res.data.length < that.limit; //判断所有数据是否加载完成；
				that.page = that.page + 1;
			});
		},
		tabClick(idx,item) {
			const { id, title } = item;
			if (id === 0) {
				this.articleHotList();
			} else {
				this.cid = id;
				this.articleList = [];
				this.page = 1;
				this.loadend = false;
				this.loading = false;
				this.getArticleLists();
			}
		}
	},
	onReachBottom() {
		this.getArticleLists();
	}
};
</script>
<style lang="scss">
	page {
		background-color: $uni-bg-color;
	}

</style>
<style scoped>
.newsList .swiperNews {
	width: 690rpx;
	height: 367rpx;
	padding-top: 30rpx;
	margin:  0 auto;
}

.newsList .swiperNews .swiper-slide {
	width: 100%;
	height: 330rpx;
}

.newsList .swiperNews .slide-image {
	width: 100%;
	height: 100%;
	border-radius: 6rpx;
}



.newsList .nav {
	padding: 0 30rpx;
	width: 100%;
	height: 105rpx;
	overflow: hidden;
	background-color: #fff;
}

.newsList .nav.on {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 5;
}

.newsList .nav .scrollNav {
	white-space: nowrap;
	overflow-y: hidden;
	overflow-x: auto;
	-webkit-overflow-scrolling: touch;
	width: 100%;
}

.newsList .nav .item {
	display: inline-block;
	font-size: 32rpx;
	color: #999;
	margin-top: 40rpx;
}

.newsList .nav .item.on {
	color: #282828;
}

.newsList .nav .item ~ .item {
	margin-left: 46rpx;
}

.newsList .nav .item .line {
	width: 24rpx;
	height: 4rpx;
	border-radius: 2rpx;
	margin: 10rpx auto 0 auto;
}

.newsList .list.on {
	margin-top: 105rpx;
}

.newsList .list .item {
	margin: 0 30rpx;
	border-bottom: 1px solid #f0f0f0;
	padding: 35rpx 0;
}

.newsList .list .item .pictrue {
	width: 250rpx;
	height: 156rpx;
}

.newsList .list .item .pictrue image {
	width: 100%;
	height: 100%;
	border-radius: 6rpx;
}

.newsList .list .item .text {
	width: 420rpx;
	height: 156rpx;
	font-size: 24rpx;
	color: #999;
}

.newsList .list .item .text .name {
	font-size: 30rpx;
	color: #282828;
}

.newsList .list .item .picList .pictrue {
	width: 335rpx;
	height: 210rpx;
	margin-top: 30rpx;
}

.newsList .list .item .picList.on .pictrue {
	width: 217rpx;
	height: 136rpx;
}

.newsList .list .item .picList .pictrue image {
	width: 100%;
	height: 100%;
	border-radius: 6rpx;
}

.newsList .list .item .time {
	text-align: right;
	font-size: 24rpx;
	color: #999;
	margin-top: 22rpx;
}

.newsList .van-tabs__wrap {
	top: 0 !important;
}
</style>
