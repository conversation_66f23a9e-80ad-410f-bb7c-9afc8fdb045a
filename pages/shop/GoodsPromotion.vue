<template>
  <view class="quality-recommend">
    <view class="slider-banner swiper">
      <swiper class="swiper-wrapper" :options="RecommendSwiper">
        <swiperSlide
          class="swiper-slide"
          v-for="(item, index) in imgUrls"
          :key="index"
        >
          <image :src="item.img" class="slide-image" />
        </swiperSlide>
      </swiper>
      <view class="swiper-pagination"></view>
    </view>
    <view class="title acea-row row-center-wrapper">
      <view class="line"></view>
      <view class="name">
        <span class="iconfont icon-cuxiaoguanli"></span>促销单品
      </view>
      <view class="line"></view>
    </view>
    <Promotion-good :benefit="goodsList"></Promotion-good>
		<x-home></x-home>
  </view>
</template>
<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";
import "@/static/css/swiper.min.css";
import PromotionGood from "@/components/PromotionGood";
import { getGroomList } from "@/api/store";
export default {
  name: "GoodsPromotion",
  components: {
    swiper,
    swiperSlide,
    PromotionGood
  },
  props: {},
  data: function() {
    return {
      imgUrls: [],
      goodsList: [],
      RecommendSwiper: {
        pagination: {
          el: ".swiper-pagination",
          clickable: true
        },
        autoplay: {
          disableOnInteraction: false,
          delay: 2000
        },
        loop: true,
        speed: 1000,
        observer: true,
        observeParents: true
      }
    };
  },
  mounted: function() {
    this.getIndexGroomList();
  },
  methods: {
    getIndexGroomList: function() {
      let that = this;
      getGroomList(4)
        .then(res => {
          that.imgUrls = res.data.banner;
          that.goodsList = res.data.list;
        })
        .catch(function(err) {
					this.$showToast(err.msg || err);
        });
    }
  }
};
</script>
