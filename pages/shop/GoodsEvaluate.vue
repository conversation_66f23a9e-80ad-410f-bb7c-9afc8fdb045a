<template>
    <view class="evaluate-con">
        <view class="goodsStyle acea-row row-between" v-if="orderCon.productInfo">
            <view class="pictrues">
                <image :src="orderCon.productInfo.image" class="image" mode="widthFix" />
            </view>
            <view class="text acea-row row-between">
                <view class="name line2">{{ orderCon.productInfo.store_name }}</view>
                <view class="money">
                    <view>￥{{ orderCon.productInfo.price }}</view>
                    <view class="num">x{{ orderCon.cart_num }}</view>
                </view>
            </view>
        </view>
        <view class="score">
            <view class="item acea-row row-middle" v-for="(item, indexw) in scoreList" :key="indexw">
                <view>{{ item.name }}</view>
                <view class="starsList">
                    <span @click="stars(indexn, indexw)" v-for="(itemn, indexn) in item.stars" :key="indexn"
                        class="iconfont"
                        :class="item.index >= indexn ? 'icon-shitixing font-color-red' : 'icon-kongxinxing'"></span>
                </view>
                <span class="evaluate">{{ item.index === -1 ? '' : item.index + 1 + '分' }}</span>
            </view>
            <view class="textarea">
                <view class="textarea-box">
                    <textarea placeholder="商品满足你的期待么？说说你的想法，分享给想买的他们吧~" v-model="expect" maxlength="1000"></textarea>
                    <view class="textarea-txt">{{expect.length}}/1000</view>
                </view>
                <view class="list acea-row row-middle">
                    <view class="btn btn-primary">
                        <xImageUpload :num="3" @chooseImage="chooseImage">
                            <view class="pictrue uploadBnt acea-row row-center-wrapper row-column" style="margin:0">
                                <span class="iconfont icon-icon25201"></span>
                                <view>上传图片</view>
                            </view>
                        </xImageUpload>
                    </view>
                </view>
            </view>
            <!-- #ifdef MP-WEIXIN -->
            <view class="textarea avatar">
                <view class="avatar">
                    <view class="avatar-left">
                        头像
                    </view>
                    <button class="avatar-right" open-type="chooseAvatar" @chooseavatar="onChooseAvatar" type="default">
                        <image class="avatar-right-img"
                            :src="AuthorizedAvatar && avatar?avatar:'../../static/images/noPictrue.png'"></image>
                        <image class="avatar-right-alter" src="../../static/images/alter.png" mode="widthFix" />
                    </button>
                </view>
                <view class="nickname">
                    <view class="nickname-left">
                        昵称
                    </view>
                    <input class="nickname-right" v-model="nickname" @blur="getNickname" @confirm="getNickname"
                        type="nickname" placeholder="请输入微信昵称" placeholder-style="font-size:24rpx;color:#ccc" />
                </view>
            </view>
            <!-- #endif -->
            <view class="evaluateBnt bg-color-red" @click="submit">立即评价</view>
        </view>
        <x-home></x-home>
        <!-- #ifdef MP-WEIXIN -->
        <x-agreement ref="agreement" @cancel="cancel" @confirm="confirm"></x-agreement>
        <x-authorize
            @login="getOrderProduct()">
        </x-authorize>
        <!-- #endif -->
    </view>
</template>

<script>
    import {
        toLogin,
        debounce,
        checkLogin,
        autoAuth,
        authNavigator,
        openWeChatCustomerService,
        zxauthNavigator
    } from '@/utils/common.js'
    import {
        postOrderProduct,
        postOrderComment
    } from '@/api/store';
    import {
        postUserEdit,
        getUser
    } from '@/api/user';
    import xImageUpload from '@/components/x-image-upload/x-image-upload';
    import {
        uploadImg
    } from '@/utils/upload.js';
    const NAME = 'GoodsEvaluate';

    export default {
        name: NAME,
        components: {
            xImageUpload
        },
        props: {},
        data: function() {
            return {
                orderCon: {
                    cartProduct: {
                        productInfo: {}
                    }
                },
                scoreList: [{
                        name: '商品质量',
                        stars: ['', '', '', '', ''],
                        index: -1
                    },
                    {
                        name: '服务态度',
                        stars: ['', '', '', '', ''],
                        index: -1
                    }
                ],
                uploadPictures: [],
                expect: '',
                unique: '',
                userInfo: {},
                avatar: '',
                AuthorizedAvatar: false,
                AuthorizedNickname: false,
                nickname: ''
            };
        },
        onLoad: function(options) {
            const {
                unique
            } = options;
            this.unique = unique;
            this.getOrderProduct();
        },
        watch: {},
        methods: {
            getNickname(e) {
                console.log('昵称', e.detail.value.trim())
                if (e.detail.value.trim() == '微信用户' || e.detail.value.trim() == '') {
                    return this.$showToast('输入昵称不规范')
                } else {
                    this.nickname = e.detail.value
                    this.AuthorizedNickname = true;
                }
            },
            async onChooseAvatar(e) {
                this.avatar = e.detail.avatarUrl;
                this.AuthorizedAvatar = true;
                console.log('头像', this.avatar)
                let res = await uploadImg(this.avatar);
                this.avatar = res[0];
            },
            getUserInfo: function() {
                let that = this;
                getUser().then(res => {
                    this.userInfo = res.data;
                    if (this.userInfo.avatar == '' || !this.userInfo.avatar || this.userInfo.avatar ==
                        'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132' ||
                        this.userInfo.avatar ==
                        'https://thirdwx.qlogo.cn/mmopen/vi_32/Z22RuflpLpHqnZy0icITdAuMrwXdaMyibm58t2dibBOH7a5sWW8lVew8OWfia3oJwKT56dHYSgkIWiaicnZ5kRmenibCw/132'
                        ) {
                        this.AuthorizedAvatar = false
                    } else {
                        this.AuthorizedAvatar = true
                        this.avatar = this.userInfo.avatar;
                    }
                    if (this.userInfo.nickname.trim() == '' || !this.userInfo.nickname || this.userInfo
                        .nickname == '微信用户') {
                        this.AuthorizedNickname = false
                    } else {
                        this.AuthorizedNickname = true
                        this.nickname = this.userInfo.nickname;
                    }

                });
            },
            getOrderProduct: function() {
                let that = this,
                    unique = that.unique;
                postOrderProduct(unique).then(res => {
                    that.orderCon = res.data;
                    that.getUserInfo()
                });
            },
            stars: function(indexn, indexw) {
                this.scoreList[indexw].index = indexn;
            },
            chooseImage(val) {
                this.uploadPictures = val;
            },
            async submit() {
                const expect = this.expect.trim(),
                    product_score = this.scoreList[0].index + 1 === 0 ? '' : this.scoreList[0].index + 1,
                    service_score = this.scoreList[1].index + 1 === 0 ? '' : this.scoreList[1].index + 1;

                let pics = await uploadImg(this.uploadPictures);
                console.log(product_score);
                if (!product_score) {
                    return this.$showToast('请选择商品质量分数');
                }
                if (!service_score) {
                    return this.$showToast('请选择服务态度分数');
                }

                // #ifdef MP-WEIXIN
                if(zxauthNavigator()){
                    if (!this.AuthorizedAvatar || !this.avatar) {
                        return this.$showToast('请完善头像信息')
                    }
                    if (!this.AuthorizedNickname || this.nickname == '') {
                        return this.$showToast('请完善昵称信息')
                    }
                    if (this.nickname == '微信用户' || this.nickname.trim() == '') {
                        return this.$showToast('昵称不规范')
                    }
                    postUserEdit({
                        nickname: this.nickname,
                        avatar: this.avatar
                    }).then(
                        res => {
                            postOrderComment({
                                    product_score: product_score,
                                    service_score: service_score,
                                    unique: this.unique,
                                    pics: pics.join(","),
                                    comment: expect,
                                    from:'routine',
                                })
                                .then(() => {
                                    this.$successToast('评价成功');
                                    this.$navigator(-1)
                                })
                                .catch(res => {
                                    this.$navigator(1)
                                    this.$showToast(res.msg || res);
                                });
                            console.log('res---', res)
                        },
                        error => {
                            console.log('error---', error)
                        }
                    );
                }
                // #endif
                // #ifndef MP-WEIXIN
                postOrderComment({
                        product_score: product_score,
                        service_score: service_score,
                        unique: this.unique,
                        pics: pics.join(","),
                        comment: expect,
                        // #ifdef MP-TOUTIAO
                        from:'bytedance',
                        // #endif
                        // #ifndef MP-TOUTIAO
                        from:'routine'
                        // #endif
                    })
                let param = {
                    product_score: product_score,
                    service_score: service_score,
                    unique: this.unique,
                    pics: pics.join(","),
                    comment: expect,
                    // #ifdef MP-TOUTIAO
                    from:'bytedance',
                    // #endif
                    // #ifndef MP-TOUTIAO
                    from:'routine',
                    // #endif
                }
                postOrderComment(param)
                    .then(() => {
                        this.$successToast('评价成功');
                        this.$navigator(-1)
                    })
                    .catch(res => {
                        this.$navigator(1)
                        this.$showToast(res.msg || res);
                    });
                console.log('res---', res)
                // #endif

            }
        }
    };
</script>
<style scoped>
    .goodsStyle .pictrues {
            width: 120rpx;
            /* height: 120rpx; */
            /* max-height: 168rpx; */
    }
    .goodsStyle .pictrues .image{
            border-radius: 6rpx;
    }
    .evaluate-con .score .textarea .list .pictrue.uploadBnt {
        border: 1px solid #ddd;
    }

    .evaluate-con .score {
        background-color: #fff;
        border-top: 1px solid #f5f5f5;
        font-size: 28rpx;
        color: #282828;
        padding: 48rpx 30rpx 65rpx 30rpx;
    }

    .evaluate-con .score .item~.item {
        margin-top: 25rpx;
    }

    .evaluate-con .score .item .starsList {
        padding: 0 35rpx 0 40rpx;
    }

    .evaluate-con .score .item .starsList .iconfont {
        font-size: 40rpx;
        color: #aaa;
    }

    .evaluate-con .score .item .starsList .iconfont~.iconfont {
        margin-left: 20rpx;
    }

    .evaluate-con .score .item .evaluate {
        color: #aaa;
        font-size: 24rpx;
    }

    .evaluate-con .score .textarea {
        width: 690rpx;
        background-color: #fafafa;
        border-radius: 10rpx;
        margin-top: 48rpx;
    }


    .evaluate-con .score .textarea textarea {
        width: calc(100% - 60rpx);
        font-size: 28rpx;
        padding: 38rpx 30rpx 0 30rpx;
        height: 160rpx;
    }

    .evaluate-con .score .textarea textarea::placeholder {
        color: #bbb;
    }

    .evaluate-con .score .textarea .list {
        margin-top: 25rpx;
        padding-left: 5rpx;
    }

    .textarea.avatar {
        padding: 40rpx;
        box-sizing: border-box;
        font-size: 28rpx;
        color: #282828;

    }

    .textarea .avatar,
    .textarea .nickname {
        width: 100%;
        overflow: auto;
    }

    .textarea .nickname {
        margin-top: 30rpx;
    }

    .avatar-left,
    .nickname-left {
        float: left;
        width: 200rpx;
    }

    .nickname-right {
        float: left;
        width: 200rpx;
        height: 52rpx;
        line-height: 52rpx;
        border: 2rpx solid #ddd;
        border-radius: 6rpx;
        padding-left: 10rpx;
    }

    .avatar-right {
        position: relative;
        float: left;
        width: 96rpx;
        height: 96rpx;
    }

    .avatar-right-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
    }

    .avatar-right-alter {
        width: 30rpx;
        height: 30rpx;
        border-radius: 50%;
        position: absolute;
        bottom: 0;
        right: 0;
    }

    .evaluate-con .score .textarea .list .pictrue {
        width: 140rpx;
        height: 140rpx;
        margin: 0 0 35rpx 25rpx;
        position: relative;
        font-size: 22rpx;
        color: #bbb;
    }

    .evaluate-con .score .textarea .list .pictrue image {
        width: 100%;
        height: 100%;
        border-radius: 3rpx;
    }

    .evaluate-con .score .textarea .list .pictrue .icon-guanbi1 {
        font-size: 45rpx;
        position: absolute;
        top: -20rpx;
        right: -20rpx;
    }

    .evaluate-con .score .textarea .list .pictrue .icon-icon25201 {
        color: #bfbfbf;
        font-size: 50rpx;
    }

    .evaluate-con .score .evaluateBnt {
        font-size: 30rpx;
        color: #fff;
        width: 690rpx;
        height: 86rpx;
        border-radius: 43rpx;
        text-align: center;
        line-height: 86rpx;
        margin-top: 45rpx;
    }
    .textarea-box {
        position: relative;
        width: 100%;
    }
    .textarea-txt {
        position: absolute;
        right: 40rpx;
        bottom: -40rpx;
        text-align: right;
        color: #B7B7B7;
        font-size: 24rpx;
    }
    
</style>
