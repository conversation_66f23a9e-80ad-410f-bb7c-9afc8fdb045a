<template>
	<view class="product-con" @click="show_new = true">
		<view class="header acea-row row-center-wrapper" :style="'opacity:' + opacity" ref="header">
			<view class="item" :class="navActive === index ? 'on' : ''" v-for="(item, index) in navList" :key="index"
				@click="asideTap(index)">{{ item }}</view>
		</view>
		<view id="title0">
			<product-con-swiper :img-urls="storeInfo.slider_image" :videoline="storeInfo.video_link">
			</product-con-swiper>
			<view class="wrapper">
				<view class="share acea-row row-between row-bottom">
					<view class="money font-color-red">
						￥
						<span class="num">{{ storeInfo.price || 0.0 }}</span>
						<!-- <span class="vip-money" v-if="storeInfo.vip_price && storeInfo.vip_price > 0">￥{{ storeInfo.vip_price || 0 }}</span>
						<image src="@/static/images/vip.png" class="image" v-if="storeInfo.vip_price && storeInfo.vip_price > 0"></image> -->
					</view>
					<!-- #ifdef MP-TOUTIAO -->
					<button open-type="share">
                        <!-- 原iconfont库来源已失，此为新增iconfont1库 -->
                        <view class="iconfont iconfont1 icon-fenxiang"></view>
					</button>
					<!-- #endif -->
					<!-- #ifndef MP-TOUTIAO -->
					<view class="iconfont icon-fenxiang" @click="listenerActionSheet"></view>
					<!-- #endif -->
				</view>
				<view class="introduce">{{ storeInfo.store_name }}</view>
				<view class="label acea-row row-between-wrapper">
					<view>原价:￥<text style="text-decoration:line-through">{{ storeInfo.ot_price || 0 }} </text></view>
					<view>库存:{{ storeInfo.stock || 0 }}{{ storeInfo.unit_name || '' }}</view>
					<view>销量:{{ storeInfo.fsales || 0 }}{{ storeInfo.unit_name || '' }}</view>
				</view>
				<view class="coupon acea-row row-between-wrapper" @click="couponTap" v-if="couponList.length">
					<view class="hide line1 acea-row">
						优惠券：
						<view class="activity" v-for="(item, index) in couponList" :key="index">
							满{{ item.use_min_price }}减{{ item.coupon_price }}</view>
					</view>
					<view class="iconfont icon-jiantou"></view>
				</view>
				<view class="coupon acea-row row-between-wrapper" v-if="activity.length">
					<view class="line1 acea-row">
						活&nbsp;&nbsp;&nbsp;动：
						<view v-for="(item, index) in activity" :key="index" @click="goDetail(activity[index], true)">
							<view class="acea-row row-center-wrapper" v-if="item.type === '1'" :class="{
									activity_pin: index === 0,
									activity_miao: index === 1,
									activity_kan: index === 2
								}">
								<span class="iconfonts iconfont icon-shenhezhong"></span>
								<span class="activity_title">&nbsp;参与秒杀</span>
							</view>
							<view class="acea-row row-center-wrapper" v-if="item.type === '2'" :class="{
									activity_pin: index === 0,
									activity_miao: index === 1,
									activity_kan: index === 2
								}">
								<span class="iconfonts iconfont icon-kanjia"></span>
								<span class="activity_title">&nbsp;参与砍价</span>
							</view>
							<view class="acea-row row-center-wrapper" v-if="item.type === '3'" :class="{
									activity_pin: index === 0,
									activity_miao: index === 1,
									activity_kan: index === 2
								}">
								<span class="iconfonts iconfont icon-pintuan"></span>
								<span class="activity_title">&nbsp;参与拼团</span>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="attribute acea-row row-between-wrapper" @click="selecAttrTap" v-if="attr.productAttr.length">
				<view>
					{{ attrTxt }}：
					<span class="atterTxt">{{ attrValue }}</span>
				</view>
				<view class="iconfont icon-jiantou"></view>
			</view>
		</view>

		<view class="userEvaluation" id="title1">
			<view class="title acea-row row-between-wrapper">
				<view>用户评价({{ replyCount || 0 }})</view>
				<view @click="goPages(`/pages/shop/EvaluateList?id=${id}`)" class="praise">
					<span class="font-color-red">{{ replyChance || 0 }}%</span>
					好评率
					<span class="iconfont icon-jiantou"></span>
				</view>
			</view>
			<user-evaluation :reply="reply"></user-evaluation>
		</view>

		<view class="superior" v-if="goodList.length > 0">
			<view class="title acea-row row-center-wrapper">
				<image src="@/static/images/ling.png" />
				<view class="titleTxt">优品推荐</view>
				<image src="@/static/images/ling.png" />
			</view>
			<template>
				<view class="slider-banner banner">
					<!--  #ifndef MP-TOUTIAO -->
					<xSwiper :arr="goodList" :height="superiorHeight">
						<template v-slot="{ item }">
							<view class="list acea-row row-middle">
								<view class="item" v-for="val in item.list" :key="val.image"
									@click="goDetail(val, false)">
									<view class="pictrue">
										<image :src="val.image" />
										<span class="pictrue_log pictrue_log_class"
											v-if="val.activity && val.activity.type === '1'">秒杀</span>
										<span class="pictrue_log pictrue_log_class"
											v-if="val.activity && val.activity.type === '2'">砍价</span>
										<span class="pictrue_log pictrue_log_class"
											v-if="val.activity && val.activity.type === '3'">拼团</span>
									</view>
									<view class="name line1">{{ val.store_name }}</view>
									<view class="money font-color-red">¥{{ val.price }}</view>
								</view>
							</view>
						</template>
					</xSwiper>
					<!--  #endif -->
					<!--  #ifdef MP-TOUTIAO -->
					<ttSwiper :arr="goodList" :height="superiorHeight" name="superior"></ttSwiper>
					<!--  #endif -->
					<!-- <ttSwiper :arr="goodList" :height="superiorHeight" name="superior"></ttSwiper> -->
				</view>
			</template>
		</view>
		<view class="product-intro" id="title2">
			<view class="title">产品介绍</view>
			<view class="conter">
				<u-parse :html="storeInfo.description" :tag-style="parseStyle" @linkpress="$linkpress"></u-parse>
			</view>
		</view>
		<view style="height:120rpx;"></view>
		<view class="footer xfooter acea-row row-between-wrapper">
            <!-- #ifdef MP-WEIXIN -->
            <view @click="openWeChat" class="item">
            	<view class="iconfont icon-kefu"></view>
            	<view>客服</view>
            </view>
            <!-- #endif -->
			<!-- #ifndef MP-WEIXIN -->
			<view @click="goPages(`/pages/user/CustomerList?id=${id}&type=0`)" class="item">
				<view class="iconfont icon-kefu"></view>
				<view>客服</view>
			</view>
			<!-- #endif -->
			<view class="item" @click="setCollect">
				<view class="iconfont" :class="storeInfo.userCollect ? 'icon-shoucang1' : 'icon-shoucang'"></view>
				<view>收藏</view>
			</view>
			<view v-if="!isVideoScene" @click="goPages(`/pages/shopCart/shopCart`)" class="item animated"
				:class="animated === true ? 'bounceIn' : ''">
				<view class="iconfont icon-gouwuche1">
					<span class="num bg-color-red" v-if="CartCount > 0">{{ CartCount }}</span>
				</view>
				<view>购物车</view>
			</view>
			<view class="bnt acea-row">
				<view class="joinCart" :class="!isVideoScene?'':'bg-color-hui'" @click="joinCart">加入购物车</view>
				<view class="buy" @click="tapBuy()" v-if="attr.productSelect.stock > 0">立即购买</view>
				<view class="buy bg-color-hui" v-else>已售罄</view> 
			</view>
		</view>
		<!-- <Share-red-packets :priceName="priceName" v-on:changeFun="listenerActionSheet" v-if="priceName !== 0"></Share-red-packets> -->
		<CouponPop v-on:changeFun="changeFun" :coupon="coupon"></CouponPop>
		<Product-window v-on:changeFun="changeFun" :attr="attr" :iSplus="iSplus" @productAttr="productAttr"></Product-window>
		<ShareInfo v-on:setShareInfoStatus="setShareInfoStatus" :shareInfoStatus="shareInfoStatus"></ShareInfo>
		<xShare v-model="posters" :share="posterData"></xShare>
		<x-authorize v-if="show_new" :isHidden="true" @login="updateData"></x-authorize>
		<x-home></x-home>
	</view>
</template>

<script>
	import readyToPay from '@/mixins/readyToPay';

	import {
		toLogin,
		checkLogin,
		debounce,
		getUrlParams,
        authNavigator,
        openWeChatCustomerService
	} from '@/utils/common.js';
	import {
		goShopDetail
	} from '@/utils/order.js';
	import {
		uniSelectorQueryInfo
	} from '@/utils/uni_api.js';
	import {
		getProductDetail,
		postCartAdd,
		getCartCount,
		getProductCode,
		storeListApi
	} from '@/api/store';
	import {
		getCoupon,
		getCollectAdd,
		getCollectDel,
		getUserInfo
	} from '@/api/user';

	import ProductConSwiper from '@/components/ProductConSwiper';
	import UserEvaluation from '@/components/UserEvaluation';
	import xSwiper from '@/components/x-swiper/x-swiper.vue';

	import CouponPop from '@/components/CouponPop';
	import ProductWindow from '@/components/ProductWindow';
	import ShareInfo from '@/components/ShareInfo';

	import {
		LONGITUDE,
		LATITUDE,
        SHARE_ID,
        WX_KEFU_Link,
        WX_ENTERPRISE_Link
	} from '@/config.js';
	// #ifdef MP-TOUTIAO
	import ttSwiper from '@/ttcomponents/x-swiper/x-swiper.vue';
	// #endif
	import xShare from '@/components/x-share/x-share.vue';
	let NAME = 'GoodsCon';
	export default {
		mixins: [readyToPay],
		components: {
			ProductConSwiper,
			// #ifdef MP-TOUTIAO
			ttSwiper,
			// #endif
			UserEvaluation,
			xSwiper,
			CouponPop,
			ProductWindow,
			ShareInfo,
			xShare
		},
		data() {
			return {
                show_new:false,
                ids:{
                    id:0
                },
				iSplus: true,
				shareInfoStatus: false,
				weixinStatus: false,
				mapShow: false,
				mapKey: '',
				posterData: {
					image: '',
					title: '',
					price: '',
					code: ''
				},
				posterImageStatus: false,
				animated: false,
				coupon: {
					coupon: false,
					list: []
				},
				mixinsParam: {
					productId: 76
				},

				id: 0,

				couponList: [],

				replyCount: '',
				replyChance: '',
				reply: [],
				priceName: 0,
				CartCount: 0,
				posters: false,
				banner: [{}, {}],
				goodList: [],
				superiorHeight: '300rpx',
				system_store: {},
				storeSelfMention: true,
				storeItems: {},
				activity: [],
				navList: [],
				lock: false,
				navActive: 0,
				opacity: 0,
				navTopArr: [],
				isOnPlay: false,
				productCode: '' ,//商品二维码
                scene: 0, // 默认微信场景值
                isVideoScene: false, // 是否视频号相关场景
			};
		},
		watch: {
			// #ifdef H5
			$route(n) {
				if (n.name === NAME) {
					this.updateData();
				}
			}
			// #endif
		},
        onShow() {
            // #ifdef MP-WEIXIN
                this.getScene()
            // #endif
        },
		methods: {
            productAttr(e){
                // console.log('子组件',e)
                this.attr.productAttr = e;
            },
            // changeFun(e){
            //     console.log('子组件e',e)
            // },
            openWeChat(){
                openWeChatCustomerService(WX_KEFU_Link,WX_ENTERPRISE_Link,true,this.posterData.title,'pages/shop/GoodsCon.html?id='+ this.posterData.id,this.posterData.image)
            },
            getScene() {
                let opt = wx.getEnterOptionsSync();
                let sceneList = [1144,1152,1175,1176,1177,1184,1191,
                    1193,1195,1197,1198,1201,1206,1216,10001]
                this.scene = opt.scene;
                if(sceneList.indexOf(this.scene) == -1){
                   this.isVideoScene = false;
                }else {
                    this.isVideoScene = true;
                }
                // console.log('是否视频号相关场景-',this.isVideoScene)
            },
			endVideo() {
				this.isOnPlay = false;
				const oview = this.$refs.videoIds;
				oview.pause();
			},
			videoPlay() {
				if (this.storeInfo.video_link) {
					this.isOnPlay = true;
					const oview = this.$refs.videoIds;
					oview.play();
				}
			},
			async getProductCode() {
				let res = await getProductCode(this.id).catch(err => {
					console.log(err);
				});
                console.log('获取商品二维码-------',res)
				if (res) {
					// #ifdef H5
					this.productCode = res.data.code;
					// #endif
					// #ifdef MP
					this.productCode = res.data.url;
					// #endif
				}
			},
			goPages(path, type) {
				this.$authNavigator(path, type);
			},
			asideTap(index) {
				this.navActive = index;
				// if (!this.goodList.length && index === 2) {
				// 	index = 3;
				// }
				let h = this.$store.state.stytemInfo.statusBarHeight;
				// #ifdef MP
				h = h + this.$store.state.navigationBarHeight;
				// #endif
				uni.pageScrollTo({
					scrollTop: this.navTopArr[index] - h,
					selector: '#title' + index,
					duration: 300
				});
				this.lock = true;
			},
			async initScrollInfo() {
				let topArr = [],
					heightArr = [];
				for (let i = 0; i < this.navList.length; i++) {
					const {
						top
					} = await uniSelectorQueryInfo('#title' + i, this);
					topArr.push(top);
				}
				this.navTopArr = topArr;
			},
			productCon: function() {
				let that = this;
				getProductDetail(that.id)
					.then(res => {
						that.$set(that, 'storeInfo', res.data.storeInfo);
						that.$set(that.attr, 'productAttr', res.data.productAttr);
						that.$set(that, 'productValue', res.data.productValue);
						that.$set(that, 'replyCount', res.data.replyCount);
						that.$set(that, 'replyChance', res.data.replyChance);
						that.reply = res.data.reply ? [res.data.reply] : [];
						that.$set(that, 'reply', that.reply);
						that.$set(that, 'priceName', res.data.priceName);
						that.activity = res.data.activity ? res.data.activity : [];
						that.storeSelfMention = res.data.store_self_mention ? true : false;
						let store_name = that.storeInfo.store_name;
						if (store_name.length > 30) {
							store_name = store_name.substring(0, 30) + '...';
						}
						that.$set(that, 'posterData', {
							title: store_name,
							image: that.storeInfo.image_base,
							price: that.storeInfo.price,
							id: that.id,
							code: that.productCode,
							desc: that.storeInfo.store_info,
							//  #ifdef H5
							href: '/pages/shop/GoodsCon?id=' + this.id
							// #endif
						});

						that.system_store = res.data.system_store;
						let good_list = res.data.good_list || [];
						let goodArray = [];
						let count = Math.ceil(good_list.length / 6);
						for (let i = 0; i < count; i++) {
							var list = good_list.slice(i * 6, i * 6 + 6);
							if (list.length) goodArray.push({
								list: list
							});
						}
						that.mapKey = res.data.mapKey;
						console.log(goodArray);
						that.$set(that, 'goodList', goodArray);
						that.$set(that, 'superiorHeight', good_list.length < 4 ? '300rpx' : '620rpx');
						let navList = ['商品', '评价', '详情'];
						if (goodArray.length) {
							navList.splice(2, 0, '推荐');
						}
						that.navList = navList;
						that.$updateTitle(that.storeInfo.store_name);
						that.DefaultSelect();
						that.getCartCount();
						that.initScrollInfo();
					})
					.catch(err => {
						that.$showToast(err.msg || err, 'error');
						this.$navigator(-1);
					});
			},
			// 获取门店列表数据
			getList() {
				let data = {
					latitude: this.$storage.get(LATITUDE) || '', //纬度
					longitude: this.$storage.get(LONGITUDE) || '', //经度
					page: 1,
					limit: 10
				};
				storeListApi(data)
					.then(res => {
						this.storeItems = res.data.list[0];
					})
					.catch(err => {
						console.log(err);
					});
			},

			//打开优惠券插件；
			couponTap: function() {
				let that = this;
				that.coupons();
				that.coupon.coupon = true;
			},
			changecoupon: function(msg) {
				this.coupon.coupon = msg;
				this.coupons();
			},
			currentcoupon: function(res) {
				let that = this;
				that.coupon.coupon = false;
				that.$set(that.coupon.list[res], 'is_use', true);
			},
			//可领取优惠券接口；
			coupons: function() {
				let that = this,
					q = {
						page: 1,
						limit: 20,
						type: 1,
						product_id: that.id
					};
				getCoupon(q).then(res => {
                    console.log('优惠券===',res)
					that.$set(that, 'couponList', res.data || []);
					that.$set(that.coupon, 'list', res.data);
				});
			},
			//打开属性插件；
			selecAttrTap: function() {
				this.attr.cartAttr = true;
				this.isOpen = true;
			},

			//收藏商品
			setCollect: function() {
				let that = this,
					id = that.storeInfo.id,
					category = 'product';
				if (that.storeInfo.userCollect) {
					getCollectDel(id, category).then(function() {
						that.storeInfo.userCollect = !that.storeInfo.userCollect;
					});
				} else {
					getCollectAdd(id, category).then(function() {
						that.storeInfo.userCollect = !that.storeInfo.userCollect;
					});
				}
			},
			goDetail(item, type = false) {
				if (type) {
					item.activity = item;
				}
				if (parseInt(item.id) === parseInt(this.id) && item.activity.length === 0) return this.$showToast(
					'已在当前商品界面');
				goShopDetail(item);
			},
			//  点击加入购物车按钮
			joinCart: function() {
                if(!this.isVideoScene){
                    //0=加入购物车
                    this.goCat(0);
                }
			},

			//获取购物车数量
			getCartCount: function(isAnima) {
				let that = this;
				if (checkLogin()) {
					getCartCount({
							numType: 0
						})
						.then(res => {
							that.CartCount = res.data.count || '';
							//加入购物车后重置属性
							if (isAnima) {
								that.animated = true;
								setTimeout(function() {
									that.animated = false;
								}, 500);
							}
						})
						.catch(err => {
							console.log('err', err);
						});
				}
			},

			listenerActionSheet: function() {
				this.posters = !this.posters;
                // console.log('this.posters-',this.posters)
			},
			listenerActionClose: function() {
				this.posters = false;
			},
			async updateData(userinfo) {
				await this.getProductCode();
				this.productCon();
				this.coupons();
				this.getList();
			}
		},
		mounted() {},
		onLoad(options) {
            // console.log('options',options)
			//option为object类型，会序列化上个页面传递的参数
			const {
				id,
				spid,
				spread,
				scene
			} = options;
            this.ids = options;
			// 小程序中识别二维码后的相关处理
			//扫码携带参数处理
			if (scene) {
				var value = getUrlParams(decodeURIComponent(scene));
				if (value.id) this.ids.id = value.id;
			}

			if (spid) {
				this.$store.commit('UPDATE_SPID', spid);
			}
			this.id = this.ids.id ;
			this.mixinsParam.productId = this.ids.id ;
			this.storeInfo.slider_image = [];
			this.updateData();
		},
		onPageScroll(e) {
			var that = this,
				scrollY = e.scrollTop;
			var opacity = scrollY / 200;
            // console.log('scrollY',scrollY)
			this.opacity = opacity > 1 ? 1 : opacity;
            // if(0 <= scrollY && scrollY < 604){
            //     this.navActive = 0;
            // }
            // if(604 <= scrollY && scrollY < 658){
            //     this.navActive = 1;
            // }
            // if(scrollY >= 658){
            //     this.navActive = 2;
            // }
			if (this.lock) {
				this.lock = false;
				return;
			}
		},
		onPullDownRefresh() {
			this.stopPullRefresh(1000);
		},
		onReachBottom() {},
		// #ifdef MP
		onShareAppMessage() {
			console.log('this.posterData-',this.posterData)
			if (this.posterData) {
				return {
					title: this.posterData.title || '',
					imageUrl: this.posterData.image || '',
					path: '/pages/shop/GoodsCon?id=' + this.posterData.id + '&spid=' + this.$store.state.userInfo.uid,
                    templateId: SHARE_ID
				};
			}
		},
		onShareTimeline() {
			if (this.posterData) {
				return {
					title: this.posterData.title || '',
					imageUrl: this.posterData.image || '',
					query: 'id=' + this.posterData.id + '&spid=' + this.$store.state.userInfo.uid
				};
			}
		}
		// #endif
	};
</script>
<style lang="scss">
	page {
		background-color: $uni-bg-color-page;
	}
</style>
<style scoped lang="scss">
	.product_swiper {
		height: 750rpx;

		.slide-image {
			width: 100%;
			height: 100%;
		}

		.video_play {
			position: absolute;
			width: 1.5rem;
			height: 1.5rem;
			left: 45%;
			top: 45%;
			z-index: 11;
		}

		.videoBox {
			position: fixed;
			z-index: 999;
			top: 0;
			width: 100%;
			height: 100%;

			video {
				width: 100%;
				height: 100%;
			}

			.video-source {
				width: 100%;
				height: 100%;
			}

			.video_play {
				position: absolute;
				width: 1rem;
				height: 1rem;
				left: 45%;
				top: 45%;
				z-index: 11;
			}
		}
	}

	/*产品详情*/
	// .product-con .footer {
	// 	width: 100%;
	// 	padding: 0 20rpx 0 30rpx;
	// 	position: fixed;
	// 	bottom: 0;
	// 	height: 100rpx;
	// 	background-color: #fff;
	// 	z-index: 777;
	// 	border-top: 1px solid #f0f0f0;
	// }

	// .product-con .footer .item {
	// 	font-size: 18rpx;
	// 	color: #666;
	// 	margin-top: 7rpx;
	// }

	// .product-con .footer .item .iconfont {
	// 	text-align: center;
	// 	font-size: 40rpx;
	// 	height: 40rpx;
	// 	line-height: 40rpx;
	// }
	.xfooter {
		@include fixed_footer(112rpx);
	}

	.product-con .footer .item .iconfont.icon-gouwuche1 {
		position: relative;
	}

	.product-con .footer .item .iconfont.icon-gouwuche1 .num {
		color: #fff;
		position: absolute;
		font-size: 18rpx;
		border-radius: 200rpx;
		top: -10rpx;
		right: 0;
		height: 30rpx;
		line-height: 30rpx;
		// padding: 0 8rpx;
		min-width: 30rpx;
	}

	// .product-con .bnt {
	// 	width: 444rpx;
	// 	height: 76rpx;
	// 	color: #fff;
	// 	font-size: 28rpx;
	// }

	// .product-con .bnt > view {
	// 	width: 222rpx;
	// 	text-align: center;
	// 	line-height: 76rpx;
	// }

	// .product-con .bnt .joinCart {
	// 	border-radius: 50rpx 0 0 50rpx;
	// 	background-image: linear-gradient(to right, #fea10f 0%, #fa8013 100%);
	// 	background-image: -webkit-linear-gradient(to right, #fea10f 0%, #fa8013 100%);
	// 	background-image: -moz-linear-gradient(to right, #fea10f 0%, #fa8013 100%);
	// }

	// .product-con .bnt .buy {
	// 	border-radius: 0 50rpx 50rpx 0;
	// 	background-image: linear-gradient(to right, #fa6514 0%, #e93323 100%);
	// 	background-image: -webkit-linear-gradient(to right, #fa6514 0%, #e93323 100%);
	// 	background-image: -moz-linear-gradient(to right, #fa6514 0%, #e93323 100%);
	// }

	.product-con .superior {
		background-color: #fff;
		margin-top: 20rpx;
		padding-bottom: 10rpx;
	}

	.product-con .superior .title {
		height: 98rpx;
	}

	.product-con .superior .title image {
		width: 30rpx;
		height: 30rpx;
	}

	.product-con .superior .title .titleTxt {
		margin: 0 20rpx;
		font-size: 30rpx;
		background-image: linear-gradient(to right, #f57a37 0%, #f21b07 100%);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	.product-con .superior .slider-banner {
		// width: 690rpx;
		width: 92%;
		margin: 0 auto;
		position: relative;
	}

	.product-con .superior .slider-banner .list {
		width: 100%;
	}

	.product-con .superior .slider-banner .list .item {
		width: 215rpx;
		margin: 0 22rpx 30rpx 0;
		font-size: 26rpx;
	}

	.product-con .superior .slider-banner .list .item:nth-of-type(3n) {
		margin-right: 0;
	}

	.product-con .superior .slider-banner .list .item .pictrue {
		width: 100%;
		height: 215rpx;
		position: relative;
	}

	.product-con .superior .slider-banner .list .item .pictrue image {
		width: 100%;
		height: 100%;
		border-radius: 6rpx;
	}

	.product-con .superior .slider-banner .list .item .name {
		color: #282828;
		margin-top: 12rpx;
	}

	.mask {
		z-index: 888 !important;
	}

	.activity_pin {
		width: auto;
		height: 44rpx;
		background: linear-gradient(90deg, rgba(233, 51, 35, 1) 0%, rgba(250, 101, 20, 1) 100%);
		opacity: 1;
		border-radius: 22rpx;
		padding: 0 20rpx;
		margin-left: 19rpx;
	}

	.activity_miao {
		width: auto;
		height: 44rpx;
		padding: 0 20rpx;
		background: linear-gradient(90deg, rgba(250, 102, 24, 1) 0%, rgba(254, 161, 15, 1) 100%);
		opacity: 1;
		border-radius: 22rpx;
		margin-left: 19rpx;
	}

	.iconfonts {
		color: #fff !important;
		font-size: 28.000000000000004rpx;
		display: block;
	}

	.activity_title {
		font-size: 24rpx;
		color: #fff;
	}

	.activity_kan {
		width: auto;
		height: 44rpx;
		padding: 0 20rpx;
		background: linear-gradient(90deg, rgba(254, 159, 15, 1) 0%, rgba(254, 178, 15, 1) 100%);
		opacity: 1;
		border-radius: 22rpx;
		margin-left: 19rpx;
	}

	.addressBox .phone {
		margin-left: 110.00000000000001rpx;
	}

	.corlor-red {
		color: #fc4141;
	}

	.store-box {
		padding: 0 30rpx;
		border-bottom: 1px solid #f5f5f5;
	}

	.geoPage {
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		z-index: 10000;
	}

	// .product-con .header {
	// 	position: fixed;
	// 	left: 0;
	// 	top: 0;
	// 	width: 100%;
	// 	height: 96rpx;
	// 	font-size: 30rpx;
	// 	color: #050505;
	// 	background-color: #fff;
	// 	z-index: 11;
	// 	border-bottom: 1rpx solid #eee;
	// }
	// .product-con .header .item {
	// 	position: relative;
	// 	margin: 0 35rpx;
	// }
	// .product-con .header .item.on:before {
	// 	position: absolute;
	// 	width: 60rpx;
	// 	height: 5rpx;
	// 	background-repeat: no-repeat;
	// 	content: '';
	// 	background: linear-gradient(to left, #ff3366 0%, #ff6533 100%);
	// 	background: -webkit-linear-gradient(to left, #ff3366 0%, #ff6533 100%);
	// 	background: -moz-linear-gradient(to left, #ff3366 0%, #ff6533 100%);
	// 	bottom: -10rpx;
	// }
	.product-con .store-info {
		margin-top: 20rpx;
		background-color: #fff;
	}

	.product-con .store-info .title {
		font-size: 28.000000000000004rpx;
		color: #282828;
		height: 80rpx;
		line-height: 80rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}

	.product-con .store-info .info {
		padding: 0 30rpx;
		height: 126rpx;
	}

	.store-info .icon-jiantou {
		color: #7a7a7a;
		font-size: 28.000000000000004rpx;
	}

	.product-con .store-info .info .pictrue {
		width: 76rpx;
		height: 76rpx;
	}

	.product-con .store-info .info .pictrue img {
		width: 100%;
		height: 100%;
		border-radius: 6rpx;
	}

	.product-con .store-info .info .text {
		width: 56%;
	}

	.product-con .store-info .info .text .name {
		font-size: 30rpx;
		color: #282828;
	}

	.product-con .store-info .info .text .address {
		font-size: 24rpx;
		color: #666;
		margin-top: 3rpx;
	}

	.product-con .store-info .info .text .address .iconfont {
		color: #707070;
		font-size: 18rpx;
		margin-left: 10rpx;
	}

	.address_tit {
		max-width: 88% !important;
	}

	.addressTxt {
		width: auto;
		font-size: 24rpx;
	}

	.product-con .store-info .info .iconfont {
		font-size: 40rpx;
	}

	.product-con .superior {
		background-color: #fff;
		margin-top: 20rpx;
	}

	.product-con .superior .title {
		height: 98rpx;
	}

	.product-con .superior .title img {
		width: 30rpx;
		height: 30rpx;
	}

	.product-con .superior .title .titleTxt {
		margin: 0 20rpx;
		font-size: 30rpx;
		background-image: linear-gradient(to right, #f57a37 0%, #f21b07 100%);
		background-image: -webkit-linear-gradient(to right, #f57a37 0%, #f21b07 100%);
		background-image: -moz-linear-gradient(to right, #f57a37 0%, #f21b07 100%);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	.product-con .superior .slider-banner {
		// width: 690rpx;
		width: 92%;
		margin: 0 auto;
		padding-bottom: 20rpx;
	}

	.product-con .superior .slider-banner .list {
		width: 100%;
		padding-bottom: 20rpx;
	}

	.product-con .superior .slider-banner .list .item {
		width: 215rpx;
		margin: 0 21rpx 20rpx 0;
		font-size: 26rpx;
	}

	.product-con .superior .slider-banner .list .item:nth-of-type(3n) {
		margin-right: 0;
	}

	.product-con .superior .slider-banner .list .item .pictrue {
		width: 100%;
		height: 215rpx;
		position: relative;
	}

	.product-con .superior .slider-banner .list .item .pictrue img {
		width: 100%;
		height: 100%;
		border-radius: 6rpx;
	}

	.product-con .superior .slider-banner .list .item .name {
		color: #282828;
		margin-top: 12rpx;
	}

	.product-con .superior .slider-banner .swiper-pagination-bullet {
		background-color: #999;
	}

	.product-con .superior .slider-banner .swiper-pagination-bullet-active {
		background-color: #e93323;
	}

	.mask {
		-webkit-filter: blur(2px);
		-moz-filter: blur(2px);
		-ms-filter: blur(2px);
		filter: blur(2px);
		z-index: 888 !important;
	}

	.footer .icon-shoucang1 {
		color: #e93323;
	}

	.product-con .product-intro .conter {
		// width: 100% !important;
		padding: 20rpx;
	}

	.noscroll {
		height: 100%;
		overflow: hidden;
	}
</style>
