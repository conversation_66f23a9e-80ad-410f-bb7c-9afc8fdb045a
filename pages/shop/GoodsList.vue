<template>
	<view class="productList">
		<view class="top_fixed">
		<view class="search bg-color-red acea-row row-between-wrapper">
			<view class="input acea-row row-between-wrapper">
				<span class="iconfont icon-sousuo"></span>
				<input placeholder="搜索商品信息" v-model="where.keyword" @confirm="submitForm" />
			</view>
			<view class="iconfont" :class="Switch === true ? 'icon-pailie' : 'icon-tupianpailie'" @click="switchTap"></view>
		</view>
		<view class="nav acea-row row-middle">
			<view class="item" :class="title ? 'font-color-red' : ''" @click="set_where(0)">{{ title ? title : '默认' }}</view>
			<view class="item" @click="set_where(1)">
				价格
				<image src="@/static/images/horn.png" v-if="price === 0"></image>
				<image src="@/static/images/up.png" v-if="price === 1"></image>
				<image src="@/static/images/down.png" v-if="price === 2"></image>
			</view>
			<view class="item" @click="set_where(2)">
				销量
				<image src="@/static/images/horn.png" v-if="stock === 0"></image>
				<image src="@/static/images/up.png" v-if="stock === 1"></image>
				<image src="@/static/images/down.png" v-if="stock === 2"></image>
			</view>
			<view class="item" :class="nows ? 'font-color-red' : ''" @click="set_where(3)">新品</view>
		</view>
		</view>
		<view class="list acea-row row-between-wrapper" :class="Switch === true ? '' : 'on'" ref="container">
			<view @click="goDetail(item)" v-for="(item, index) in productList" :key="index" class="item" :class="Switch === true ? '' : 'on'" :title="item.store_name">
				<view class="pictrue" :class="Switch === true ? '' : 'on'">
					<image :src="item.image" :class="Switch === true ? '' : 'on'"></image>
					<span class="pictrue_log_class" :class="Switch === true ? 'pictrue_log_big' : 'pictrue_log'" v-if="item.activity && item.activity.type === '1'">秒杀</span>
					<span class="pictrue_log_class" :class="Switch === true ? 'pictrue_log_big' : 'pictrue_log'" v-if="item.activity && item.activity.type === '2'">砍价</span>
					<span class="pictrue_log_class" :class="Switch === true ? 'pictrue_log_big' : 'pictrue_log'" v-if="item.activity && item.activity.type === '3'">拼团</span>
				</view>
				<view class="text" :class="Switch === true ? '' : 'on'">
					<view class="name line1">{{ item.store_name }}</view>
					<view class="money font-color-red" :class="Switch === true ? '' : 'on'">
						￥
						<span class="num">{{ item.price }}</span>
					</view>
					<view class="vip acea-row row-between-wrapper" :class="Switch === true ? '' : 'on'">
						<view class="vip-money" v-if="item.vip_price && item.vip_price > 0">
							￥{{ item.vip_price }}
							<image src="@/static/images/vip.png"></image>
						</view>
						<view>已售{{ item.sales }}件</view>
					</view>
				</view>
			</view>
		</view>
		<Loading :loaded="loadend" :loading="loading"></Loading>

		<xNodate :arr="productList" :page="where.page"  imgSrc="/wximage/noGood.png" ></xNodate>
		<x-home></x-home>
		<x-authorize></x-authorize>
	</view>
</template>

<script>
// #ifdef H5

// #endif
import Loading from '@/components/Loading';
import { getProducts, getHostProducts } from '@/api/store';
import { goShopDetail } from '@/utils/order.js';
import { debounce } from '@/utils/common.js';
import xNodate from '@/components/x-nodata/x-nodata.vue';
export default {
	components: { Loading, xNodate },
	data() {
		return {
			hostProduct: [],
			productList: [],
			Switch: true,
			where: {
				page: 1,
				limit: 8,
				keyword: '',
				sid: '', //二级分类id
				news: 0,
				priceOrder: '',
				salesOrder: ''
			},
			title: '',
			loadTitle: '',
			loading: false,
			loadend: false,
			price: 0,
			stock: 0,
			nows: false
		};
	},
	methods: {
		goDetail(item) {
			goShopDetail(item);
		},
		goPages(path) {
			this.$navigator(path);
		},
		submitForm: function() {
			this.$set(this, 'productList', []);
			this.where.page = 1;
			this.loadend = false;
			this.loading = false;
			this.get_product_list();
		},
		get_product_list: debounce(function() {
			var that = this;
			if (that.loading) return; //阻止下次请求（false可以进行请求）；
			if (that.loadend) return; //阻止结束当前请求（false可以进行请求）；
			that.loading = true;
			this.setWhere();
			let q = that.where;
			getProducts(q).then(res => {
				that.loading = false;
				that.productList.push.apply(that.productList, res.data);
				that.loadend = res.data.length < that.where.limit; //判断所有数据是否加载完成；
				that.where.page = that.where.page + 1;
			});
		}, 300),
		//点击事件处理
		set_where: function(index) {
			let that = this;
			switch (index) {
				case 0:
					return that.$navigator('/pages/shopClass/shopClass');
				case 1:
					if (that.price === 0) that.price = 1;
					else if (that.price === 1) that.price = 2;
					else if (that.price === 2) that.price = 0;
					that.stock = 0;
					break;
				case 2:
					if (that.stock === 0) that.stock = 1;
					else if (that.stock === 1) that.stock = 2;
					else if (that.stock === 2) that.stock = 0;
					that.price = 0;
					break;
				case 3:
					that.nows = !that.nows;
					break;
				default:
					break;
			}
			that.$set(that, 'productList', []);
			that.where.page = 1;
			that.loadend = false;
			that.get_product_list();
		},
		//设置where条件
		setWhere: function() {
			let that = this;
			if (that.price === 0) {
				that.where.priceOrder = '';
			} else if (that.price === 1) {
				that.where.priceOrder = 'asc';
			} else if (that.price === 2) {
				that.where.priceOrder = 'desc';
			}
			if (that.stock === 0) {
				that.where.salesOrder = '';
			} else if (that.stock === 1) {
				that.where.salesOrder = 'asc';
			} else if (that.stock === 2) {
				that.where.salesOrder = 'desc';
			}
			that.where.news = that.nows ? '1' : '0';
		},
		switchTap: function() {
			let that = this;
			that.Switch = !that.Switch;
		}
	},
	mounted() {
		this.$updateTitle(this.title);
		this.get_product_list();
	},
	onLoad(options) {
		//option为object类型，会序列化上个页面传递的参数
		console.log(options);
		const { id, title, s } = options;
		this.where.keyword = s;
		this.where.sid = id;
		this.title = title && id ? title : '';
	},
	onPullDownRefresh() {
		let that = this;
		that.$set(that, 'productList', []);
		that.where.page = 1;
		that.loadend = false;
		that.get_product_list();
		this.$stopPullRefresh(1000);
	},
	onReachBottom() {
		this.get_product_list();
		console.log('到达底部');
	}
};
</script>
<style lang="scss">
page {
	background-color: $uni-bg-color-page;
}
</style>
<style scoped lang="scss">
	.top_fixed{
		height: 172rpx;
	}
.productList .search {
	width: 100%;
	height: 86rpx;
	padding-left: 22rpx;
	position: fixed;
	left: 0;
	top: 0;
	z-index: 5;
}

.productList .search .input {
	width: 600rpx;
	height: 60rpx;
	background-color: #fff;
	border-radius: 50rpx;
	padding: 0 20rpx;
}

.productList .search .input input {
	width: 500rpx;
	height: 100%;
	font-size: 26rpx;
}

.productList .search .input input::placeholder {
	color: #999;
}

.productList .search .input .iconfont {
	font-size: 35rpx;
	color: #555;
}

.productList .search .icon-pailie,
.productList .search .icon-tupianpailie {
	margin: 0 auto;
	color: #fff;
	width: 40rpx;
	font-size: 40rpx;
	height: 86rpx;
	line-height: 86rpx;
}

.productList .nav {
	height: 86rpx;
	color: #454545;
	position: fixed;
	left: 0;
	width: 100%;
	font-size: 28rpx;
	background-color: #fff;
	top: 86rpx;
	z-index: 5;
}

.productList .nav .item {
	width: 25%;
	text-align: center;
}

.productList .nav .item.font-color {
	font-weight: bold;
}

.productList .nav .item image {
	width: 15rpx;
	height: 19rpx;
	margin-left: 10rpx;
}

.productList .list {
	padding:  0 20rpx;
}

.productList .list.on {
	background-color: #fff;
	border-top: 1px solid #f6f6f6;
}

.productList .list .item {
	width: 345rpx;
	margin-top: 20rpx;
	background-color: #fff;
	border-radius: 10rpx;
}

.productList .list .item.on {
	width: 100%;
	display: flex;
	display: -webkit-flex;
	display: -webkit-box;
	border-bottom: 1px solid #f6f6f6;
	padding: 30rpx 0;
	margin: 0;
}

.productList .list .item .pictrue {
	width: 100%;
	height: 345rpx;
	position: relative;
}

.productList .list .item .pictrue.on {
	width: 180rpx;
	height: 180rpx;
	position: relative;
}

.productList .list .item .pictrue image {
	width: 100%;
	height: 100%;
	border-radius: 10rpx 10rpx 0 0;
}

.productList .list .item .pictrue image.on {
	border-radius: 6rpx;
}

.productList .list .item .text {
	padding: 20rpx 17rpx 26rpx 17rpx;
	font-size: 30rpx;
	color: #222;
	text-align: left;
}

.productList .list .item .text.on {
	width: 508rpx;
	padding: 0 0 0 22rpx;
}

.productList .list .item .text .money {
	font-size: 26rpx;
	font-weight: bold;
	margin-top: 8rpx;
}

.productList .list .item .text .money.on {
	margin-top: 40rpx;
}

.productList .list .item .text .money .num {
	font-size: 34rpx;
}

.productList .list .item .text .vip {
	font-size: 22rpx;
	color: #aaa;
	margin-top: 7rpx;
}

.productList .list .item .text .vip.on {
	margin-top: 11rpx;
}

.productList .list .item .text .vip .vip-money {
	font-size: 24rpx;
	color: #282828;
	font-weight: bold;
}

.productList .list .item .text .vip .vip-money image {
	width: 46rpx;
	height: 21rpx;
	margin-left: 4rpx;
}
.noCommodity {
	border-top: 3px solid #f5f5f5;
	padding-bottom: 1px;
	padding-top: 75rpx;
}

.noCommodity .noPictrue {
	width: 413rpx;
	height: 336rpx;
	margin: 0 auto 30rpx auto;
}

.noCommodity .noPictrue .image {
	width: 100%;
	height: 100%;
}
</style>
