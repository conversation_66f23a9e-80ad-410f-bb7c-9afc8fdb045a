<template>
	<view class="not-defined">
		<view class="content">
			<h3 class="title">页面未找到</h3>
			<span>抱歉！您访问的页面不存在，请返回上一级或点击下方按钮返回首页...</span>
		</view>
		<view class="btn" @click="goPages()">返回首页</view>
	</view>
</template>

<script>
	export default {
		name: "NotDefined",
		data() {
			return {

			}
		},
		watch: {},
		methods: {
			goPages() {
				this.$navigator('/pages/tabBar/index/index', 'switchTab');
			}
		}
	};
</script>

<style scoped>
	.not-defined image {
		width: 100%;
		margin-top: 18%;
	}

	.content {
		padding: 0 10rpx;
		text-align: center;
		color: #44405e;
		font-size: 15px;
	}

	.title {
		margin-bottom: 60rpx;
		color: #302c48;
		font-size: 20px;
	}

	.btn {
		color: #fff;
		background-color: #ef4c4c;
		font-size: 16px;
		padding: 16rpx;
		border-radius: 25px;
		text-align: center;
		width: 240rpx;
		margin: 0 auto;
		margin-top: 100rpx;
	}

	.active {
		background: red;
	}
</style>
