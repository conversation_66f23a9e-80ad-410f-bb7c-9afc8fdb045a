<template>
    <view class="index">
        <!-- #ifdef MP-WEIXIN -->
        <!-- <view class="">
            <store-product appid="wx127e3c89c4aef611" product-id="**************">微信小店20节课商品</store-product>
        </view> -->
        <!-- <view class="" @click="goGame">
            进入游戏
        </view> -->
        <!-- #endif -->
        
        <!-- #ifdef MP-WEIXIN -->
        <view class="follow" :class="{ hide: !followHid }">
            <official-account @load="officialAccount" @error="officialAccountErr"></official-account>
            <view class="close" @click="followHid = false">关闭</view>
        </view>
        <!-- #endif -->
        <view class="i_logo flex flex_align_center flex_between">
            <!-- <xLogo :disabled="true"></xLogo> -->
            <view class="i_l_image">
                <image src="@/static/images/zsff/logo.png" mode="widthFix"></image>
            </view>
            <view class="i_l_search" v-show="isToutiaoPay">
                <xSearch @search="search" confirmType="确定" :disabled="false" :focus="false" :fixed="false" :btn="false"
                    :history="false" type="search" :clear="false" :isLink="true"></xSearch>
            </view>
        </view>
        <!-- 顶部banner -->
        <view class="banner">
            <xBanner :arr="detail.banner"></xBanner>
        </view>
        
        <!-- 最新活动 -->
        <view class="new_active" v-if="latestActivity.length">
            <view class="tab flex flex_between flex_align_end">
                <view class="title">
                    <view>最新活动</view>
                    <view class="line"></view>
                </view>
                <view class="more"><text class="font_size20" @click="goPages('/pages/ycommunity/pastActive/pastActive')">往期活动</text></view>
            </view>
            <scroll-view scroll-y="true" class="scroll_view" v-if="latestActivity.length"
                :style="{height:latestActivity.length>1?'600rpx':'300rpx'}">
                <view class="item flex" v-for="(item, index) in latestActivity" :key="index" :id="`t${index}`"
                    @click="goPages('/pages/ycommunity/shop/detail?id=' + item.id )">
                    <view class="item_l relative">
                        <image :src="item.image" alt="最新活动" mode="aspectFill"></image>
                        <view class="status absolute">
                            <text v-if="item.status===0">报名中</text>
                            <text v-if="item.status===1 || item.status===2">进行中</text>
                            <text v-if="item.status===3">已结束</text>
                            <text v-if="item.status===4">已取消</text>
                        </view>
                    </view>
                    <view class="item_r">
                        <view class="r_title">{{item.name}}</view>
                        <view class="r_time">活动时间 {{item.activity_time}}</view>
                        <view class="r_desc">
                        {{item.info}} 
                        <!-- <text v-if="item.end_time">报名时间截止 {{item.end_time}}</text> -->
                        </view>
                        <view class="r_bottom flex flex_between">
                            <!-- #ifndef MP-TOUTIAO -->
                            <view class="price" v-show="item.price">￥{{item.price}}</view>
                            <!-- #endif -->
                            <view class="limit">限制人数：{{item.limit_numbers}}</view>
                        </view>
                    </view>
                </view>
            </scroll-view>
            <view class="no_data" v-else>
                近期暂无活动，查看<text @click="goPages('/pages/ycommunity/pastActive/pastActive')">往期活动</text>
            </view>
        </view>
        <!-- 最新课程 --> 
        <view class="new_active course" v-if="specialLatest.length">
            <view class="tab flex flex_between flex_align_end">
                <view class="title course">
                    <view>{{isToutiaoPay?'最新课程':'推荐内容'}}</view>
                    <view class="line"></view>
                </view>
                <view class="more">
                    <text class="font_size20" @click=""></text>
                </view>
            </view>
            
            <listItem :arr="specialLatest" rspan="rspan" :rbType="2" :nodata="false" @listItemClick="listItemClick" />
        </view>
        <!-- 新评推荐 -->
        <view class="tuijian" v-if="detail.new_comment_recommend.length">
            <xRecom :arr="detail.new_comment_recommend"></xRecom>
        </view>
        <view class="jingxuan" :class="isToutiaoPay?'':'noheight'" v-if="featuredList.length">
            <view class="tab flex flex_between flex_align_center" >
                <view class="title">
                    <image src="@/static/images/yuanshi/yscp.png" mode="widthFix"></image>
                </view>
                <!-- <view class="more"><text class="font_size20" @click="goPages('/pages/yuanshi/evaluate/list')">查看更多</text></view> -->
            </view>
            <xList :arr="featuredList" :btn="true" :vip="false" ></xList>
            <u-loadmore :status="loadmoreStatus" color="#999999" />
        </view>
        <!-- <x-authorize :isAuto="false" @login="loginReload" ></x-authorize> -->
        
        <!-- #ifdef MP-TOUTIAO -->
        <view class="tab-bar">
            <view class="tab-bar-border"></view>
            <view v-for="(item, index) in list" :key="index" class="tab-bar-item" @click="switchTab(item.pagePath)">
                <view class="wrap">
                    <view :class="showAnimate_tt&&index===1?'animate':''">
                        <image :src="selected === index ? item.selectedIconPath : item.iconPath"></image>
                        <!-- <view style="color: {{selected === index ? selectedColor : color}}">{{item.text}}</view> -->
                        <view class="dot" v-if="!systemMessageStatus"></view>
                    </view>
                </view>
            </view>
        </view>
        <!-- #endif -->
    </view>
</template>

<script>
    // #ifdef H5
    import {
        isWeixin
    } from '@/utils/validate.js';
    import {
        openShareAll
    } from '@/utils/wechat/share.js';
    // #endif
    import {
        authGetLocation
    } from '@/utils/common.js';
    import {
        yIndex,
        yFeatured
    } from '@/api/yuanshi/public';
    import {
        getSpecialLatest
    } from '@/api/yknowledge.js'
    import {
        activityPast,
        activityLatest
    } from '@/api/community';
    import xBanner from '@/components/yuanshi/x-banner.vue';
    import xRecom from '@/components/yuanshi/x-recom.vue';
    import xList from '@/components/yuanshi/x-list.vue';
    import xLogo from '@/components/x-logo/x-logo.vue';
    import xTab from '@/components/x-tab/x-tab.vue';
    import {
        LONGITUDE,
        LATITUDE,
        MAPKEY,
        KEFUTOKEN,
        KEFU_URL,
        IS_TOUTIAO_PAY,
        SHARE_ID
    } from '@/config.js';
    import uniLoadMore from '@dcloudio/uni-ui/lib/uni-load-more/uni-load-more';
    import xSearch from '@/components/x-search/x-search.vue';
    import listItem from '@/components/zsff/list-item.vue';
    export default {
        props: {
            value: {
                type: '',
                default: ''
            }
        },
        components: {
            xBanner,
            xList,
            xTab,
            xLogo,
            xRecom,
            uniLoadMore,
            xSearch,
            listItem
        },
        data() {
            return {
                cascaderKey:1,
                isToutiaoPay:IS_TOUTIAO_PAY,
                followHid: false,
                // #ifdef H5
                isWeixin: isWeixin(),
                // #endif
                detail: {
                    new_comment_recommend: []
                },
                featuredList: [],
                page: {
                    page: 1,
                    limit: 10,
                    more: true
                },
                loading: false,
                latestActivity: [],
                specialLatest: [],
                loadmoreStatus: 'loadmore',
                // #ifdef MP-TOUTIAO
                list: [{
                    "pagePath": "pages/tabBar/index/index",
                    "iconPath": "../../../static/images/1-001.png",
                    "selectedIconPath": "../../../static/images/1-002.png"
                }, {
                    "pagePath": "pages/tabBar/course/index",
                    "iconPath": "../../../static/images/zsff/2-002.png",
                    "selectedIconPath": "../../../static/images/zsff/2-002.png"
                }, {
                    "pagePath": "pages/tabBar/user/user",
                    "iconPath": "../../../static/images/4-001.png",
                    "selectedIconPath": "../../../static/images/4-002.png"
                }],
                showAnimate_tt: true,
                selected: 0,
                systemMessageStatus: true,
                // #endif
            };
        },
        methods: {
            // goGame(){
            //     this.goPages('/pages/gameWebview/gameWebview?url=' + 'https://dev.shop.arthorize.com/game/index.html' + '&token=' + this.$storage.get('token') ) 
            // },
            // #ifdef MP-TOUTIAO
            switchTab(path) {
                tt.switchTab({
                    url: '../../../' + path,
                    success(res) {
                        // console.log("switchTab调用成功 ", res);
                    },
                    fail(res) {
                        // console.log("switchTab调用失败 ", res);
                    },
                });
            },
            // #endif
            goPages(path, type) {
                this.$navigator(path, type);
            },
            loginReload() {
                this.featuredList = [];
                this.page = {
                    page: 1,
                    limit: 10,
                    more: true
                };
                this.getFeatured();
            },
            getFeatured() {
                if (!this.page.more || this.loading) return;
                this.loading = true;
                this.loadmoreStatus = 'loading';
                let from = '';
                // #ifndef MP-TOUTIAO
                from = 'routine';
                // #endif
                // #ifdef MP-TOUTIAO
                from = 'bytedance';
                // #endif
                yFeatured({
                        page: this.page.page,
                        limit: this.page.limit,
                        from:from
                    })
                    .then(res => {
                        this.featuredList = this.featuredList.concat(res.data);
                        const isMore = res.data.length === this.page.limit;

                        this.page.more = isMore;
                        this.page.page++;

                        this.loadmoreStatus = isMore ? 'loadmore' : 'nomore';
                        this.loading = false;
                    })
                    .catch(err => {
                        this.loading = false;
                    });
            },
            initData(status) {
                let from = '';
                // #ifndef MP-TOUTIAO
                from = 'routine';
                // #endif
                // #ifdef MP-TOUTIAO
                from = 'bytedance';
                // #endif
                yIndex(from).then(res => {
                    res.data.new_comment_recommend.map((item, index) => {
                        item.yw_index = item.yw_index ? item.yw_index.toFixed(1) : '0.0';
                    });
                    this.detail = res.data;
                    this.$storage.set(MAPKEY, res.data.tengxun_map_key);
                    
                });
                this.getFeatured();
                getSpecialLatest(from).then(res => {
                    // console.log(res)
                    if(status == 1){
                        uni.report('click_status', {  
                          'title':'新书扫码进入统计',  
                        });
                        console.log('新书扫码进入统计')
                    }
                    this.specialLatest = res.data
                    // console.log('最新课程-', this.specialLatest)
                })
            },
            getKEFUTOKEN() {
                uni.request({
                    url: KEFU_URL + '/api/mobile/service/kf',
                    method:'GET',
                    success: (res) => {
                        if(res.data.status == 200){
                            this.$storage.set(KEFUTOKEN, res.data.data.kefu_token);
                        }else {
                            this.$showToast('请检查后台客服token');
                        }
                    }
                });
            },
            setOpenShare() {
                if (isWeixin()) {
                    // getShare().then(res => {
                    // 	var data = res.data.data;
                    // 	var configAppMessage = {
                    // 		desc: data.synopsis,
                    // 		title: data.title,
                    // 		link: '/pages/yuanshi/index/index',
                    // 		imgUrl: data.image
                    // 	};
                    // 	openShareAll(configAppMessage);
                    // });
                }
            },
            officialAccount(e) {
                this.followHid = true;
            },
            officialAccountErr() {
                this.followHid = false;
            },
            getActivityLatest(type) {
                let from = '';
                // #ifndef MP-TOUTIAO
                from = 'routine';
                // #endif
                // #ifdef MP-TOUTIAO
                from = 'bytedance';
                // #endif
                activityLatest({},from).then(res => {
                    this.latestActivity = res.data;
                })
            },
            listItemClick(item, index) {
                const {
                    index_type,
                    id,
                    special_id = 0,
                    url = ''
                } = item;
                if (url) {
                    if (url.indexOf('weixin://') > -1) {
                        this.$navigator('/pages/webview/webview?type=mp&url=' + url);
                    } else {
                        this.$navigator('/pages/webview/webview?url=' + url);
                    }
                } else {
                    switch (Number(index_type)) {
                        case 1:
                            this.$navigator(`/pages/yknowledge/course/detail?sid=${special_id}&id=0`);
                            break;
                        case 2:
                            this.$navigator(`/pages/yknowledge/course/detail_list?sid=${special_id}&id=${id}`)
                            break;
                    }
                }
            }
        },
        mounted() {
            // activityPast().then(res=>{
            // 	console.log('xxx',res)
            // })
            this.getActivityLatest()
        },
        onLoad(option) {
            this.initData(option.isStatistics);
            // #ifndef MP-TOUTIAO
            // 获取用户位置
            // if (!this.$storage.get(LATITUDE) && !this.$storage.get(LONGITUDE)) {
            //     authGetLocation()
            //         .then(res => {
            //             this.$storage.set(LATITUDE, res.latitude);
            //             this.$storage.set(LONGITUDE, res.longitude);
            //         })
            //         .catch(err => {
            //             this.$storage.remove(LATITUDE);
            //             this.$storage.remove(LONGITUDE);
            //             this.$showToast('定位失败');
            //         });
            // }
            // #endif
            // #ifdef MP-TOUTIAO
            let that = this;
            if(!that.$storage.get(LATITUDE) && !that.$storage.get(LONGITUDE) && that.isToutiaoPay){
                tt.showModal({
                    title: "地理位置授权",
                    cancelColor: '#787b7b',
                    confirmColor: '#e93323',
                    cancelText: '取消',
                    confirmText: '同意',
                    content: '你的位置信息将用于获取周边门店信息',
                    success(res) {
                        if (res.confirm) {
                            if (!that.$storage.get(LATITUDE) && !that.$storage.get(LONGITUDE)) {
                                authGetLocation()
                                    .then(res => {
                                        that.$storage.set(LATITUDE, res.latitude);
                                        that.$storage.set(LONGITUDE, res.longitude);
                                    })
                                    .catch(err => {
                                        that.$storage.remove(LATITUDE);
                                        that.$storage.remove(LONGITUDE);
                                        that.$showToast('定位失败');
                                    });
                            }
                        } else if (res.cancel) {
                            // console.log("cancel, cold");
                        } else {
                            // what happend?
                        }
                    },
                    fail(res) {
                        console.log(`showModal调用失败`);
                    },
                });
            }
            // #endif
        },
        onShow() {  
            // #ifdef MP-TOUTIAO
            uni.hideTabBar()
            // #endif
            this.setTabBarIndex(0); //index为当前tab的索引
            // #ifdef MP-TOUTIAO
            this.getKEFUTOKEN()
            // #endif
            
        },
        onShareAppMessage() {
            return {
                title: 'Yusi音乐审美养成',
                templateId: SHARE_ID,
            };
        },
        onShareTimeline() {
            return {
                title: '着调儿',
                imageUrl: '@/static/images/logo2.png',
                query: ''
            };
        },
        onReachBottom() {
            this.getFeatured();
        },
        onPageScroll() {
            this.followHid = false;
        },
        onPullDownRefresh() {
            this.featuredList = [];
            this.page = {
                page: 1,
                limit: this.page.limit,
                more: true
            };
            this.initData();
            this.getActivityLatest(true);
            uni.stopPullDownRefresh();
        }
    };
</script>

<style scoped lang="scss">
    .index {
        min-height: 100vh;

        /* #ifdef MP-TOUTIAO */
        .uni-tabbar-bottom,
        .uni-tabbar {
            display: none;
        }

        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: calc(constant(safe-area-inset-bottom) + 128rpx);
            height: calc(env(safe-area-inset-bottom) + 128rpx);
            background: white;
            display: flex;
            border-radius: 8rpx 8rpx 0px 0px;
            box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
            padding-bottom: constant(safe-area-inset-bottom);
            padding-bottom: env(safe-area-inset-bottom);
            z-index: 99;
        }

        .tab-bar-border {
            /* 	position: absolute;
            	left: 0;
            	top: 0;
            	width: 100%;
            	height: 1px;
            	transform: scaleY(0.5); */
        }

        .tab-bar-item {
            flex: 1;
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .tab-bar-item image {
            width: 52rpx;
            height: 52rpx;
        }

        .tab-bar-item view {
            font-size: 10px;

        }

        .tab-bar-item:nth-child(3) {
            margin-bottom: 12rpx;
            margin-top: -8rpx;
        }

        .tab-bar-item:nth-child(3) .wrap {
            width: 156rpx !important;
            height: 156rpx !important;
            border-radius: 50%;
            background: #fff;
            opacity: 1;
            /* border-radius: 4rpx 4rpx 24rpx 24rpx; */
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .tab-bar-item:nth-child(3) .wrap image {
            /* width: 124rpx;
            	height: 52rpx; */
            width: 156rpx !important;
            height: 156rpx !important;
        }

        .tab-bar-item:nth-child(4) .wrap {
            position: relative;
        }

        .tab-bar-item:nth-child(4) .wrap .dot {
            position: absolute;
            width: 14rpx;
            height: 14rpx;
            top: -2rpx;
            right: 2rpx;
            border-radius: 50%;
            background-color: red;
        }


        .tab-bar-item .animate {
            animation: huXi 5s linear infinite;
        }

        @keyframes rotate {
            from {
                transform: rotate(0);
            }

            30% {
                transform: rotate(90deg);
            }

            50% {
                transform: rotate(180deg);
            }

            70% {
                transform: rotate(270deg);
            }

            to {
                transform: rotate(360deg);
            }
        }

        @keyframes rotateY {
            from {
                transform: rotateY(0);
            }

            30% {
                transform: rotateY(0);
            }

            50% {
                transform: rotateY(360deg);
            }

            70% {
                transform: rotateY(0);
            }

            to {
                transform: rotateY(0);
            }
        }

        @keyframes huXi {
            from {
                transform: scale(1);
            }

            30% {
                transform: scale(1.1);
            }

            40% {
                transform: scale(0.8);
            }

            50% {
                transform: scale(0.6);
            }

            60% {
                transform: scale(0.8);
            }

            70% {
                transform: scale(1.1);
            }

            to {
                transform: scale(1);
            }
        }

        /* #endif */

        image {
            width: 100%;
            height: 100%;
        }

        .i_logo {
            padding: 40rpx;

            .i_l_image {
                width: 242rpx;
                height: 80rpx;

            }

            .i_l_search {
                width: 380rpx;
            }
        }

        .banner {
            margin: -20rpx 0 10rpx 0;
            min-height: 280rpx;
        }

        .tab {
            margin-bottom: 36rpx;

            .title {
                &.course {
                    padding-left: 22rpx;
                }
                font-size: 32rpx;
                padding-left: 12rpx;
                // letter-spacing: 10rpx;
                font-weight: bold;

                color: #333333;

                .line {
                    width: 132rpx;
                    height: 8rpx;
                    background: linear-gradient(90deg, #ff5a73 0%, #ffa969 100%);
                    border-radius: 2rpx;
                }
            }

            .more {
                color: #999999;
            }
        }

        .new_active,
        .tuijian {
            padding-left: 40rpx;
        }
        .tuijian {
            padding-bottom: 70rpx;
        }

        .new_active {
            margin-bottom: 34rpx;
            // .more {
            padding-right: 40rpx;
            
            &.course {
                padding: 0 20rpx 0;
            }
            // }
            .scroll_view {
                height: 600rpx;

                .item {
                    margin-bottom: 34rpx;

                    .item_l {
                        width: 310rpx;
                        height: 260rpx;
                        margin-right: 40rpx;

                        image {
                            border-radius: 30rpx;
                        }
                    }

                    .item_r {
                        flex: 1;
                        text-align: left;

                        .r_title {
                            @include show_line(1);
                            font-weight: 700;
                            font-size: 28rpx;
                            color: #333333;
                        }

                        .r_time {
                            font-size: 24rpx;
                            color: #666;
                        }

                        .r_desc {
                            @include font_size;
                            transform-origin: left;
                            color: #999999;
                            margin: 16rpx 0 34rpx 0;
                        }

                        .r_bottom {
                            .price {
                                font-size: 28rpx;
                                color: #333333;
                            }

                            .limit {
                                @include font_size;
                                @include show_line(1);
                                color: #999999;
                            }
                        }
                    }

                    .status {
                        font-size: 24rpx;
                        color: #ffffff;
                        bottom: 0;
                        right: 0;
                        width: 142rpx;
                        height: 62rpx;
                        text-align: center;
                        line-height: 62rpx;
                        opacity: 1;
                        background: linear-gradient(180deg, #ff6f6f, #ff5656);
                        border-radius: 30rpx 0px 30rpx 0px;
                        box-shadow: 0px 6rpx 12rpx 0px rgba(0, 0, 0, 0.16);
                    }
                }
            }

            .no_data {
                height: 200rpx;
                line-height: 200rpx;
                text-align: center;
                font-size: 24rpx;
                color: #999;

                text {
                    color: #007AFF;
                }
            }
        }

        .jingxuan {
            padding: 70rpx 40rpx;
            min-height: 800rpx;
            &.noheight {
                min-height: 0rpx;
            }
            .title {
                image {
                    width: 152rpx;
                }
            }
        }
    }

    /* #ifdef MP */
    .follow {
        // position: fixed;
        top: 0;
        width: 100%;
        height: 200rpx;
        transition: height 0.3s;

        .close {
            text-align: right;
            padding: 10rpx 20rpx;
            background-color: rgba(0, 0, 0, 0.1);
            color: #303030;
            font-size: 24rpx;
        }

        &.hide {
            height: 0;
            overflow: hidden;
        }
    }

    /* #endif */
</style>