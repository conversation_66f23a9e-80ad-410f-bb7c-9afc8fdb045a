<template>
    <view class="user" v-cloak>
        <template v-if="isLogin">
            <xUsePannel @pannelClick="pannelClick" :info="detail"></xUsePannel>
            <view class="myWrap" :class="{ minHeight: isList }">
                <view class="tab">
                    <view class="flex flex_align_center flex_between">
                        <view class="title">我的课程</view>
                        <view v-show="isToutiaoPay" @click="goPages('/pages/yknowledge/gift/give')">我的赠送</view>
                    </view>
                    <xTab ref="tab" @tabClick="tabClick" :active="tabActive" :reCancel="true" :arr="arr"
                        padding="0 0rpx 0 20rpx" :dotsNum="commentMessageNumber"></xTab>
                </view>
                <template v-if="isList">
                    <view class="xlist">
                        <xList :arr="listArr" :status="tabType === 1" :attention="true" :other="tabType === 0"
                            :btn="true" @change="attentionChange"></xList>
                    </view>
                </template>
                <template v-else>
                    <view class="user_wrap">
                        <view class="order" v-show="isToutiaoPay">
                            <view class="order_t flex flex_between">
                                <view>我的订单</view>
                                <!-- <view class="all" @click="goPages('/pages/order/MyOrder')">查看全部订单</view> -->
                            </view>
                            <view class="order_b flex flex_align_center">
                                <view @click="goPages('/pages/order/MyOrder?type=0')" class="item">
                                    <view class="pictrue flex flex_align_center flex_around">
                                        <image src="@/static/images/yuanshi/dfk.png" mode="widthFix" />
                                        <text class="num" v-if="detail.orderStatusNum.unpaid_count > 0">{{detail.orderStatusNum.unpaid_count}}</text>
                                    </view>
                                    <view><text class="font_size20">待付款</text></view>
                                </view>
                                <view @click="goPages('/pages/order/MyOrder?type=1')" class="item">
                                    <view class="pictrue flex flex_align_center flex_around">
                                        <image src="@/static/images/yuanshi/dfh.png" mode="widthFix" />
                                        <text class="num" v-if="detail.orderStatusNum.unshipped_count > 0">{{ detail.orderStatusNum.unshipped_count }}</text>
                                    </view>
                                    <view><text class="font_size20">待发货</text></view>
                                </view>
                                <view @click="goPages('/pages/order/MyOrder?type=2')" class="item">
                                    <view class="pictrue flex flex_align_center flex_around">
                                        <image src="@/static/images/yuanshi/dsh.png" mode="widthFix" />
                                        <text class="num" v-if="detail.orderStatusNum.received_count > 0">{{ detail.orderStatusNum.received_count }}</text>
                                    </view>
                                    <view><text class="font_size20">待收货</text></view>
                                </view>
                                <view @click="goPages('/pages/order/MyOrder?type=3')" class="item">
                                    <view class="pictrue flex flex_align_center flex_around">
                                        <image src="@/static/images/yuanshi/dpj.png" mode="widthFix" />
                                        <text class="num" v-if="detail.orderStatusNum.evaluated_count > 0">{{ detail.orderStatusNum.evaluated_count }}</text>
                                    </view>
                                    <view><text class="font_size20">待评价</text></view>
                                </view>
                                <view @click="goPages('/pages/order/MyOrder?type=4')" class="item">
                                    <view class="pictrue flex flex_align_center flex_around">
                                        <image src="@/static/images/yuanshi/ywc.png" mode="widthFix" />
                                        <!-- <text class="num">{{ detail.orderStatusNum.complete_count }}</text> -->
                                    </view>
                                    <view><text class="font_size20">已完成</text></view>
                                </view>
                            </view>
                        </view>
                        <view class="list">
                            <view class="item  flex flex_between flex_align_center"
                                @click="goPages(`/pages/ycommunity/order/list?type=0`)">
                                <view>我的活动</view>
                                <image src="@/static/images/yuanshi/ywc.png" mode="widthFix" />
                            </view>
                        </view>
                        <view class="list" style="margin-top: 32rpx;">
                            <template v-for="(item, index) in MyMenus">
                                <view class="item flex flex_between flex_align_center" :key="item.id"
                                    @click="goPages(item.url)"
                                    v-if="item.url!=='/pages/user/CustomerList' && item.url!=='/pages/order/ReturnList' && item.url!== '/pages/user/address/AddressManagement'">
                                    <view>{{ item.name }}
                                        <text class="font_size20"
                                            v-if="item.url==='/pages/yuanshi/vip/ShowVip'">开通即享全场平均5折优惠</text>
                                    </view>
                                    <view v-if="item.url==='/pages/yuanshi/chat/ChatList'" class="relative">
                                        <image :src="item.pic" mode="widthFix"></image>
                                        <view v-if="systemMessageNumber" class="dot absolute"></view>
                                    </view>
                                    <view v-else>
                                        <text v-if="item.id == 141">{{couponsList.length}}</text>
                                        <image v-else :src="item.pic" mode="widthFix"></image>
                                    </view>
                                </view>
                                <!-- #ifdef MP-TOUTIAO -->
                                <!-- <button class="item toutiaoBut" type="default" open-type="contact" :key="item.id" v-if="item.url==='/pages/user/CustomerList'">
                                    <view class="name tt_button_text" >{{ item.name }}</view>
                                    <image class="img-icon" :src="item.pic" mode="widthFix"></image>
                                </button> -->
                                <view class="item flex flex_between flex_align_center" @click="goPages(item.url)"
                                    :key="item.id" v-if="item.url==='/pages/user/CustomerList'">
                                    <view>{{ item.name }}</view>
                                    <image :src="item.pic" mode="widthFix"></image>
                                </view>
                                <view class="item flex flex_between flex_align_center" @click="goPages(item.url)"
                                    :key="item.id" v-if="item.url==='/pages/order/ReturnList' && isToutiaoPay">
                                    <view>{{ item.name }}</view>
                                    <image :src="item.pic" mode="widthFix"></image>
                                </view>
                                <view class="item flex flex_between flex_align_center" @click="goPages(item.url)"
                                    :key="item.id"
                                    v-if="item.url==='/pages/user/address/AddressManagement' && isToutiaoPay">
                                    <view>{{ item.name }}</view>
                                    <image :src="item.pic" mode="widthFix"></image>
                                </view>
                                <!-- #endif -->
                                <!-- #ifdef MP-WEIXIN -->
                                <view class="item flex flex_between flex_align_center" @click="openWeChat"
                                    :key="item.id" v-if="item.url==='/pages/user/CustomerList'">
                                    <view>{{ item.name }}</view>
                                    <image :src="item.pic" mode="widthFix"></image>
                                </view>
                                <view class="item flex flex_between flex_align_center" @click="goPages(item.url)"
                                    :key="item.id" v-if="item.url==='/pages/order/ReturnList' && isToutiaoPay">
                                    <view>{{ item.name }}</view>
                                    <image :src="item.pic" mode="widthFix"></image>
                                </view>
                                <view class="item flex flex_between flex_align_center" @click="goPages(item.url)"
                                    :key="item.id"
                                    v-if="item.url==='/pages/user/address/AddressManagement' && isToutiaoPay">
                                    <view>{{ item.name }}</view>
                                    <image :src="item.pic" mode="widthFix"></image>
                                </view>
                                <!-- #endif -->
                                <!-- #ifdef H5 -->
                                <view class="item flex flex_between flex_align_center" :key="item.id"
                                    @click="goPages(item.url)" v-else>
                                    <view>{{ item.name }}</view>
                                    <image :src="item.pic" mode="widthFix"></image>
                                </view>
                                <!-- #endif -->
                            </template>
                        </view>
                    </view>
                </template>
            </view>
            <view class="copyright">
                <view class="font_size20">北京源未文化发展有限公司版权所有</view>
                <br />
                <view class="font_size20">https://www.arthorize.com</view>
            </view>
        </template>
        <template v-else>
            <view class="unlogin">
                <view class="myWrap">
                    <view class="untop">
                        <image src="@/static/images/yuanshi/logo.png" mode=""></image>
                    </view>
                    <view class="unbottom">
                        <image src="@/static/images/yuanshi/unlogin.png" mode=""></image>
                    </view>
                    <view class="txt text_center"><text class="font_size20">~ 尚未登录 ~</text></view>
                </view>
                <view class="unbtn" @click="init('auth')">
                    <!-- #ifndef MP-TOUTIAO -->
                    <button class="flex_line_height">
                        <view class="flex flex_align_center">
                            <image src="@/static/images/yuanshi/wxicon.png" mode="widthFix" v-if="isWeixin"></image>
                            <view>{{ isWeixin ? '微信一键登录' : '去登录' }}</view>
                        </view>
                    </button>
                    <!-- #endif -->
                    <!-- #ifdef MP-TOUTIAO -->
                    <button class="red">
                        一键登录
                    </button>
                    <view class="">
                        {{err}}
                    </view>
                    <!-- #endif -->
                </view>
            </view>
            <!-- #ifdef MP-TOUTIAO -->
            <uni-popup ref="popup" type="center" :maskClick="false">
                <view class="flex flex_align_center authorize">
                    <view class="text_center relative">
                        <view class="top">
                            <view class="title">授权登录</view>
                            <view class="tip">请授权头像等信息，以便为您提供更好的服务</view>
                        </view>
                        <view class="bottom flex">
                            <button class="btn" @click="close">取消</button>
                            <button class="btn" type="primary" @click="getUserInfo_tt">授权</button>
                        </view>
                    </view>
                </view>
            </uni-popup>
            <!-- #endif -->

        </template>
        <x-authorize v-if="appName != 'Toutiao'" @login="init"></x-authorize>
        <!-- #ifdef MP-TOUTIAO -->
        <view class="tab-bar">
            <view class="tab-bar-border"></view>
            <view v-for="(item, index) in list" :key="index" class="tab-bar-item" @click="switchTab(item.pagePath)">
                <view class="tab-wrap">
                    <view :class="showAnimate_tt&&index===1?'animate':''">
                        <image :src="selected === index ? item.selectedIconPath : item.iconPath"></image>
                        <!-- <view style="color: {{selected === index ? selectedColor : color}}">{{item.text}}</view> -->
                        <view class="dot" v-if="systemMessageNumber"></view>
                    </view>
                </view>
            </view>
        </view>
        <!-- #endif -->
    </view>
</template>

<script>
    import {
        specialCouponsUser
    } from '@/api/activity';
    import xUsePannel from '@/components/yuanshi/x-user-pannel';
    import xTab from '@/components/yuanshi/x-tab.vue';
    import xList from '@/components/yuanshi/x-list.vue';
    import {
        user,
        userWishList,
        messageList,
        systemMessageList,
        getFootprint,
        getAttention
    } from '@/api/yuanshi/user';
    import {
        getMenuUser
    } from '@/api/user';
    import {
        toLogin,
        autoAuth,
        getuserInfo,
        openWeChatCustomerService
    } from '@/utils/common.js';
    import {
        VUE_APP_URL,
        IS_TOUTIAO_PAY,
        KEFU_URL,
        KEFUTOKEN,
        SHARE_ID,
        WX_KEFU_Link,
        WX_ENTERPRISE_Link
    } from '@/config.js';
    import {
        mapGetters
    } from 'vuex';
    // #ifdef H5
    import {
        isWeixin
    } from '@/utils/validate.js';
    const _isWeixin = isWeixin();
    // #endif
    // #ifdef MP
    const _isWeixin = true;
    // #endif

    export default {
        props: {
            value: {
                type: '',
                default: ''
            }
        },
        components: {
            xUsePannel,
            xTab,
            xList
        },
        computed: mapGetters(['isLogin', 'userInfo']),
        data() {
            return {
                appName: '',
                err: '',
                systemMessageStatus: true,
                commentMessageNumber: 0, // 我参与的评论未读数量、
                systemMessageNumber: 0, // 我的消息未读数量
                imagePath: VUE_APP_URL,
                isToutiaoPay: IS_TOUTIAO_PAY,
                pannelType: 'user',
                detail: {
                    orderStatusNum: {
                        order_count: 0,
                        sum_price: 0,
                        unpaid_count: 0,
                        unshipped_count: 0,
                        received_count: 0,
                        evaluated_count: 0,
                        complete_count: 0,
                        refund_count: 0
                    }
                },
                arr: [],
                tabType: 0,
                isList: false,
                isWeixin: _isWeixin,
                requestLoading: false,
                listArr: [],
                page: {
                    page: 1,
                    limit: 20,
                    more: true
                },
                MyMenus: [],
                isNewInfo: false,
                tabActive: -1,
                couponsList:[],
                // #ifdef MP-TOUTIAO
                isAuto: true,
                list: [{
                    "pagePath": "pages/tabBar/index/index",
                    "iconPath": "../../../static/images/1-001.png",
                    "selectedIconPath": "../../../static/images/1-002.png"
                }, {
                    "pagePath": "pages/tabBar/course/index",
                    "iconPath": "../../../static/images/zsff/2-002.png",
                    "selectedIconPath": "../../../static/images/zsff/2-002.png"
                }, {
                    "pagePath": "pages/tabBar/user/user",
                    "iconPath": "../../../static/images/4-001.png",
                    "selectedIconPath": "../../../static/images/4-002.png"
                }],
                showAnimate_tt: true,
                selected: 2,
                showDot_tt: false
                // #endif
                
            };
        },
        methods: {
            openWeChat() {
                openWeChatCustomerService(WX_KEFU_Link, WX_ENTERPRISE_Link)
            },
            // #ifdef MP-TOUTIAO
            close() {
                uni.hideLoading()
                this.$refs.popup.close();
                this.$store.commit('HIDE_AUTH_POPUP_SHOW');
            },
            open() {
                this.$hideToast();
                // #ifdef MP
                if (this.$store.state.isFromTimeline) {
                    this.$showToast('请前往小程序体验完整功能')
                    this.close()
                    return
                }
                this.$refs.popup.open();
                // #endif
            },
            getUserInfo_tt() {
                let _this = this;
                let info = tt.getSystemInfoSync();
                if ((info.appName.toUpperCase() === 'DOUYIN') || (info.appName.toUpperCase() === 'DEVTOOLS')) {
                    _this.isAuto && uni.showLoading({
                        title: '正在登录中'
                    });
                }
                let code;
                uni.login({
                    success(res) {
                        code = res.code
                    },
                    fail(err) {
                        uni.hideLoading();
                    }
                });
                if (tt.getUserProfile) {
                    tt.getUserProfile({
                        success(res) {
                            _this.close();
                            res.code = code;
                            res.version = false;
                            res.app_name = info.appName;
                            // console.log('res：', res)
                            _this.getLoginInfo(res);
                        },
                        fail(res) {
                            uni.hideLoading();
                            _this.open();
                        },
                    });
                } else {
                    tt.getUserInfo({
                        withCredentials: true,
                        success(res) {
                            _this.close();
                            res.code = code;
                            res.version = false;
                            res.app_name = info.appName;
                            // console.log('res：', res)
                            _this.getLoginInfo(res);
                        },
                        fail(res) {
                            tt.openSetting({
                                success: (res) => {
                                    console.log("openSetting success");
                                    uni.showToast({
                                        title: '请授权用户信息',
                                        icon: 'none',
                                        duration: 2500
                                    })
                                },
                                fail: (err) => {
                                    console.log("openSetting fail");
                                },
                                complete: (res) => {
                                    console.log("openSetting complete");
                                },
                            });
                            uni.hideLoading();
                            _this.open();
                        },
                    });
                }
            },
            getLoginInfo(userInfo) {
                let _this = this;
                toLogin(userInfo, function(res) {
                    _this.init();
                    _this.$refs.tab.init();
                    uni.hideLoading();
                });
            },
            // #endif

            switchTab(path) {
                tt.switchTab({
                    url: '../../../' + path,
                    success(res) {
                        // console.log("switchTab调用成功 ", res);
                    },
                    fail(res) {
                        // console.log("switchTab调用失败 ", res);
                    },
                });
            },
            pannelClick(idx, type) {
                this.pannelType = type;
            },
            goPages(path, type) {
                this.$navigator(path, type);
            },
            tabClick(item, type) {
                switch (item.type) {
                    case 0:
                        this.goPages('/pages/yknowledge/user/course?type=collect')
                        break;
                    case 1:
                        this.goPages('/pages/yknowledge/user/course?type=course')
                        break;
                    case 2:
                        this.goPages('/pages/yknowledge/user/course?type=record')
                        break;
                    case 3:
                        this.goPages('/pages/yknowledge/user/comment')
                        break;
                    default:
                        break;

                }
                // if (type) {
                // 	this.isList = false;
                // } else {
                // 	this.isList = true;
                // 	this.requestLoading = false;
                // 	this.page = {
                // 		page: 1,
                // 		limit: 20,
                // 		more: true
                // 	};
                // 	this.listArr = [];
                // 	this.tabType = item.type;
                // 	this.request();
                // }
            },
            request() {
                let type = this.tabType;
                let {
                    page,
                    limit
                } = this.page;
                if (!this.page.more || this.requestLoading) return;
                this.requestLoading = true;
                let req = '';
                if (type === 0) {
                    // 我的关注
                    req = getAttention({
                        user_id: 0,
                        page: page,
                        limit: limit
                    });
                } else if (type === 1) {
                    // 我的心愿
                    req = userWishList({
                        user_id: 0,
                        page: page,
                        limit: limit
                    });
                } else if (type === 2) {
                    // 我的足迹
                    req = getFootprint({
                        page: page,
                        limit: limit
                    });
                }
                req
                    .then(res => {
                        this.requestLoading = false;
                        this.listArr = this.listArr.concat(res.data);
                        this.page.more = res.data.length === limit;
                        this.page.page++;
                    })
                    .catch(err => {
                        this.requestLoading = false;
                    });
            },
            messInfo() {
                this.isNewInfo = false;
                messageList({
                    page: 1,
                    limit: 1
                }).then(res => {
                    if (!this.isNewInfo && res.data.length) {
                        this.isNewInfo = res.data[0].recording[0].remind === 0 ? true : false;
                    }
                });
                systemMessageList({
                    page: 1,
                    limit: 1
                }).then(res => {
                    if (!this.isNewInfo && res.data.length) {
                        this.isNewInfo = res.data[0].remind === 0 ? true : false;
                    }
                });
            },
            init(type) {
                if (this.isLogin) {
                    // this.messInfo();  取消触发原红点逻辑
                    getuserInfo().then(res => {
                        let data = this.$store.getters.userInfo;
                        this.detail = data;
                        // console.log('我的信息-', data)
                        this.arr = [{
                                label: '我的收藏',
                                num: data.special_collect,
                                type: 0
                            },
                            {
                                // label: '付费课程',
                                label: '我的订阅',
                                num: data.specials,
                                type: 1
                            },
                            {
                                label: '观看记录',
                                num: data.learning_records,
                                type: 2
                            }, {
                                label: '我的评论',
                                num: data.my_comments,
                                type: 3
                            }
                        ];
                        this.shareInfo = {
                            image: data.avatar,
                            title: data.nickname
                        };
                    });
                    getMenuUser().then(res => {
                        this.MyMenus = res.data.routine_my_menus;
                    });
                    specialCouponsUser({
                        types: 1
                    })
                    .then(res => {
                        this.couponsList = res.data;
                    })
                    .catch(res => {
                        this.$showToast(res.msg || res);
                    });
                    setTimeout(() => {
                        this.$refs.tab.init();
                    }, 100);
                    
                } else {
                    if (type === 'auth') {
                        // #ifndef MP-TOUTIAO
                        autoAuth();
                        // #endif

                        // #ifdef MP-TOUTIAO
                        this.open()
                        // #endif
                    }
                }
            },
            attentionChange(type) {
                let num = this.arr[0].num;
                if (type) {
                    // console.log('爱了爱了');
                    this.arr[0].num++;
                } else {
                    // console.log('取关了');
                    if (num > 0) {
                        this.arr[0].num--;
                    }
                }
            },
            getKEFUTOKEN() {
                uni.request({
                    url: KEFU_URL + '/api/mobile/service/kf',
                    method: 'GET',
                    success: (res) => {
                        if (res.data.status == 200) {
                            this.$storage.set(KEFUTOKEN, res.data.data.kefu_token);
                        } else {
                            this.$showToast('请检查后台客服token');
                        }
                    }
                });
            },
        },
        mounted() {},
        onLoad() {},
        onShow() {
            // #ifdef MP-TOUTIAO
            uni.hideTabBar()
            this.appName = tt.getSystemInfoSync().appName;
            console.log('this.appName---', this.appName)
            // #endif
            this.isList = false;
            this.setTabBarIndex(2); //index为当前tab的索引
            this.init();
            // #ifdef MP-TOUTIAO
            this.getKEFUTOKEN()
            // #endif
        },
        // // #ifdef MP-TOUTIAO
        // onReady() {
        //     // 字节重置使用
        //     this.$refs.tab.init();
        // },
        // // #endif
        onShareAppMessage() {
            if (this.shareInfo) {
                return {
                    title: this.shareInfo.title || '',
                    imageUrl: this.shareInfo.image || '',
                    path: '/pages/yuanshi/user/home?id=' + this.$store.state.userInfo.uid,
                    templateId: SHARE_ID,
                };
            }
        },
        onReachBottom() {
            if (this.isList) {
                this.request();
            }
        }
    };
</script>

<style>
    [v-cloak] {
        display: none !important;
    }
</style>
<style scoped lang="scss">
    .user {


        /* #ifdef MP-TOUTIAO */
        .authorize {
            width: 600rpx;
            background-color: #fff;
        }

        .authorize .text_center {
            width: 100%;
        }

        .authorize .text_center .top {
            padding: 20rpx 40rpx;
        }

        .authorize .text_center .top .title {
            padding: 20rpx 0;
            font-size: 32rpx;
            font-weight: bold;
        }

        .authorize .text_center .top .tip {
            padding: 30rpx 0;
            font-size: 30rpx;
            height: 150rpx;
        }

        .authorize .text_center .bottom {
            bottom: 0;
            width: 100%;
        }

        .authorize .text_center .bottom .btn {
            width: 50%;
            border-radius: 0;
            height: 80rpx;
            line-height: 80rpx;
        }

        .authorize .text_center .bottom .btn:after {
            border: none;
        }

        .authorize .text_center .bottom .btn:last-child {
            background-color: #f85959;
        }

        .uni-tabbar-bottom,
        .uni-tabbar {
            display: none;
        }

        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: calc(constant(safe-area-inset-bottom) + 128rpx);
            height: calc(env(safe-area-inset-bottom) + 128rpx);
            background: white;
            display: flex;
            border-radius: 8rpx 8rpx 0px 0px;
            box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
            padding-bottom: constant(safe-area-inset-bottom);
            padding-bottom: env(safe-area-inset-bottom);
            z-index: 99;
        }

        .tab-bar-border {
            /* 	position: absolute;
            	left: 0;
            	top: 0;
            	width: 100%;
            	height: 1px;
            	transform: scaleY(0.5); */
        }

        .tab-bar-item {
            flex: 1;
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .tab-bar-item image {
            width: 52rpx;
            height: 52rpx;
        }

        .tab-bar-item view {
            font-size: 10px;

        }

        .tab-bar-item:nth-child(3) {
            margin-bottom: 12rpx;
            margin-top: -8rpx;
        }

        .tab-bar-item:nth-child(3) .tab-wrap {
            width: 156rpx !important;
            height: 156rpx !important;
            border-radius: 50%;
            background: #fff;
            opacity: 1;
            /* border-radius: 4rpx 4rpx 24rpx 24rpx; */
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .tab-bar-item:nth-child(3) .tab-wrap image {
            /* width: 124rpx;
            	height: 52rpx; */
            width: 156rpx !important;
            height: 156rpx !important;
        }

        .tab-bar-item:nth-child(4) .tab-wrap {
            position: relative;
        }

        .tab-bar-item:nth-child(4) .tab-wrap .dot {
            position: absolute;
            width: 14rpx;
            height: 14rpx;
            top: -2rpx;
            right: 2rpx;
            border-radius: 50%;
            background-color: red;
        }


        .tab-bar-item .animate {
            animation: huXi 5s linear infinite;
        }

        @keyframes rotate {
            from {
                transform: rotate(0);
            }

            30% {
                transform: rotate(90deg);
            }

            50% {
                transform: rotate(180deg);
            }

            70% {
                transform: rotate(270deg);
            }

            to {
                transform: rotate(360deg);
            }
        }

        @keyframes rotateY {
            from {
                transform: rotateY(0);
            }

            30% {
                transform: rotateY(0);
            }

            50% {
                transform: rotateY(360deg);
            }

            70% {
                transform: rotateY(0);
            }

            to {
                transform: rotateY(0);
            }
        }

        @keyframes huXi {
            from {
                transform: scale(1);
            }

            30% {
                transform: scale(1.1);
            }

            40% {
                transform: scale(0.8);
            }

            50% {
                transform: scale(0.6);
            }

            60% {
                transform: scale(0.8);
            }

            70% {
                transform: scale(1.1);
            }

            to {
                transform: scale(1);
            }
        }

        /* #endif */

        image {
            width: 100%;
            height: 100%;
        }

        .minHeight {
            min-height: calc(100vh - 120rpx);
        }

        .myWrap {
            margin-top: 30rpx;
            margin-bottom: 50rpx;
            background: $uni-bg-color;
            // min-height: 600rpx;
            border-radius: 16rpx 16rpx 0px 0px;

            .tab {
                padding: 52rpx 40rpx 0 40rpx;

                .title {
                    font-size: 28rpx;
                    font-weight: 700;
                    color: #2680eb;
                    line-height: 38rpx;
                    margin-bottom: 32rpx;
                }
            }

            .xlist {
                padding: 0rpx 40rpx 0 40rpx;
            }

            .user_wrap {
                margin: 0 20rpx;

                .order {
                    border-radius: 12rpx;
                    padding: 0 20rpx;

                    .order_t {
                        padding-bottom: 10rpx;

                        font-weight: bold;
                        font-size: 28rpx;

                        color: #333333;

                        .all {
                            font-size: 24rpx;
                            transform: scale(0.8);

                            color: #b7b7b7;
                        }
                    }

                    .order_b {
                        margin-top: 30rpx;

                        .item {
                            font-size: 24rpx;
                            color: #444b51;
                            flex: 1;
                            text-align: center;

                            // .font_size20 {
                            // 	transform-origin: left;
                            // }
                            .pictrue {
                                width: 112rpx;
                                height: 112rpx;
                                line-height: 112rpx;
                                background: #ffffff;
                                box-shadow: 0px 0px 20rpx rgba(107, 127, 153, 0.2);
                                opacity: 1;
                                border-radius: 40rpx;

                                image {
                                    width: 52rpx;
                                    height: 52rpx;
                                }

                                margin: 0 auto;
                                position: relative;
                                .num {
                                    min-width: 35rpx;
                                    height: 35rpx;
                                    text-align: center;
                                    line-height: 35rpx;
                                    border-radius: 24rpx;
                                    color: #fff;
                                    background: #ff3232;
                                    font-weight: 500;
                                    position: absolute;
                                    right: -8rpx;
                                    top: -8rpx;
                                    font-size: 24rpx;
                                    padding: 0 12rpx;
                                }
                            }
                        }
                    }
                }

                .list {
                    background: #ffffff;
                    box-shadow: 0px 0px 30rpx rgba(107, 127, 153, 0.3);
                    border-radius: 30rpx;
                    padding: 4rpx 40rpx;
                    margin-top: 70rpx;
                    font-size: 28rpx;
                    font-weight: 500;


                    .item {
                        padding: 36rpx 0;

                        .font_size20 {
                            color: #999999;
                        }

                        &:not(:last-child) {
                            border-bottom: 2rpx solid #e2e6ec;
                        }

                        text {}

                        image {
                            width: 40rpx;
                        }

                        .dot {
                            width: 24rpx;
                            height: 24rpx;
                            background: #ff5656;
                            border: 2rpx solid #ffffff;
                            border-radius: 50%;
                            right: -8rpx;
                            top: -8rpx;
                        }
                    }
                }
            }
        }

        .copyright {
            color: #a3a3a3;
            text-align: center;
            margin-bottom: 60rpx;
        }

        .unlogin {
            .myWrap {
                margin-top: 0;
                padding-top: 70rpx;
                // box-shadow: 0px 0px 30rpx rgba(107, 127, 153, 0.3);
                opacity: 1;
                padding-bottom: 60rpx;
                background: #ffffff;
                border-radius: 0px 0px 70rpx 70rpx;

                .untop {
                    width: 430rpx;
                    height: 142rpx;
                    margin: 0 auto;

                    image {
                        width: 100%;
                        height: 100%;
                    }
                }

                .unbottom {
                    width: 670rpx;
                    height: 608rpx;
                    margin: 0 auto;
                }

                .txt {
                    margin-top: -100rpx;
                    color: #999;
                }
            }

            .unbtn {
                image {
                    width: 36rpx;
                    height: 36rpx;
                    margin-right: 10rpx;
                }

                button {
                    width: 670rpx;
                    margin: 0 auto;
                    height: 100rpx;
                    background: #28d669;
                    border-radius: 40rpx;
                    color: #ffffff;
                    font-size: 28rpx;
                }

                button.red {
                    background-color: rgba(248, 89, 89, 1);
                    line-height: 100rpx;
                    text-align: center;
                }
            }
        }
    }

    .toutiaoBut {
        width: 100%;
        font-size: 28rpx !important;
        font-weight: 500 !important;
        overflow: hidden;

        .name {
            float: left;
            color: #303133;
        }

        .img-icon {
            float: right;
        }
    }
</style>
