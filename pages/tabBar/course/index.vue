<template>
    <view class="course">
        <view class="course_top">
            <!-- <xLogo :disabled="true"></xLogo> -->
            <view class="wrap flex flex_align_center flex_between">
                <view class="c_t_search">
                    <xSearch @search="search" @change="change" confirmType="确定" :disabled="false" :focus="false" :fixed="false"
                        :btn="false" :history="false" type="search" :clear="true" :value='keyword' placeholder="搜索你喜欢的素材/专题"></xSearch>
                </view>
                <view class="c_t_image">
                    <image src="@/static/images/zsff/logo1.png" mode="widthFix"></image>
                </view>
            </view>
            <view class="tab flex">
                <view class="item" :class="{active:tabIdx===index}" v-for="(item,index) in tabList" :key="index"
                    @click="tabClick(item,index,1)">{{item.name}}</view>
            </view>
        </view>
        <view class="course_wrap">
            <view class="tab" v-if="tabList1.length">
                <scroll-view scroll-x="true">
                    <view class="flex">
                        <view class="item" v-for="(item,index) in tabList1" :key="index"
                            @click="tabClick(item,index,2)">
                            <view class="item_t">
                                <image v-show="tabIdx1==index" :src="item.pick_pic" mode=""></image>
                                <image v-show="tabIdx1!=index" :src="item.pic" mode=""></image>
                            </view>
                            <view class="txt">{{item.name}}</view>
                        </view>
                    </view>
                </scroll-view>
            </view>
            <view class="line "></view>
            <view class="list common_wrap">
                <listItem :arr="listArr" :nodata="true" rspan="rspan" :rbType="2" :pagemore="page.more"
                    :loadmoreStatus="loadmoreStatus" @listItemClick="listItemClick" />
            </view>
        </view>
        <!-- #ifdef MP-TOUTIAO -->
            <view class="tab-bar">
            	<view class="tab-bar-border"></view>
            	<view v-for="(item, index) in list" :key="index" class="tab-bar-item" @click="switchTab(item.pagePath)">
            		<view class="wrap" >
            			<view :class="showAnimate_tt&&index===1?'animate':''">
            				<image :src="selected === index ? item.selectedIconPath : item.iconPath"></image>
            				<!-- <view style="color: {{selected === index ? selectedColor : color}}">{{item.text}}</view> -->
            				<view class="dot" v-if="!systemMessageStatus"></view>
            			</view>
            		</view>
            	</view>
            </view>
        <!-- #endif -->
    </view>
</template>

<script>
    import {
        openChannelsActivity,
        getChannelsLiveNoticeInfo,
        getChannelsLiveInfo
    } from '@/utils/channelsLive.js'
    import {
        getSpecialCategory,
        getSpecials
    } from '@/api/yknowledge.js'
    import {
        debounce
    } from '@/utils/common.js'
    import xSearch from '@/components/x-search/x-search.vue';
    import listItem from '@/components/zsff/list-item.vue';
    import {
        searchGo
    } from '@/api/yuanshi/public.js';
    export default {
        components: {
            xSearch,
            listItem
        },
        data() {
            return {
                keyword:'',
                tabList: ['音乐审美养成', '付费课程'],
                tabList1: [],
                tabIdx: 0,
                tabIdx1: 0,
                listArr: [],
                requestLoading: false,
                loadmoreStatus: 'loadmore',
                page: {
                    page: 1,
                    limit: 10,
                    more: true
                },
                // #ifdef MP-TOUTIAO
                    list:[
                        {
                            "pagePath": "pages/tabBar/index/index",
                            "iconPath": "../../../static/images/1-001.png",
                            "selectedIconPath": "../../../static/images/1-002.png"
                        }, {
                            "pagePath": "pages/tabBar/course/index",
                            "iconPath": "../../../static/images/zsff/2-002.png",
                            "selectedIconPath": "../../../static/images/zsff/2-002.png"
                        }, {
                            "pagePath": "pages/tabBar/user/user",
                            "iconPath": "../../../static/images/4-001.png",
                            "selectedIconPath": "../../../static/images/4-002.png"
                        }
                    ],
                    showAnimate_tt:false,
                    selected:1,
                    systemMessageStatus: true,
                // #endif
            }
        },
        methods: {
            change(e){
                if(e == ''){
                    this.keyword = ''
                }
            },
            switchTab(path){
                tt.switchTab({
                  url: '../../../'+ path,
                   success(res) {
                    // console.log("switchTab调用成功 ", res);
                  },
                  fail(res) {
                    // console.log("switchTab调用失败 ", res);
                  },
                });
            },
            resetCourseSearchId() {
                this.$store.commit('UPDATE_SOURCE_SEARCH_ID', 0)
            },
            getOpenChannelsActivity() {
                openChannelsActivity().then(res => {
                    console.log(res)
                }).catch(err => {
                    console.log(err)
                })
            },
            search(val) {
                this.resetCourseSearchId()
                // this.tabIdx = -1;
                this.tabIdx1 = -1;
                this.keyword = val;
                this.getList(true, val)
            },
            listItemClick(item, index) {
                const {
                    index_type,
                    id,
                    special_id
                } = item;
                switch (Number(index_type)) {
                    case 1:
                        this.$navigator(`/pages/yknowledge/course/detail?sid=${special_id}&id=0`);
                        break;
                    case 2:
                        this.$navigator(`/pages/yknowledge/course/detail_list?sid=${special_id}&id=${id}`)
                        break;
                }
            },
            getList: debounce(function(init, keyword = '') {
                const {
                    courseSearchId = 0
                } = this.$store.state;
                init && this.initInfo();
                if (courseSearchId > 0) {
                    this.tabIdx1 = -1;
                }
                if (!this.tabList1.length) {
                    this.loadmoreStatus = 'nomore';
                    return this.$showToast('暂无内容')
                }
                const subject_id = keyword ? 0 : this.tabIdx1 > -1 ? this.tabList1[this.tabIdx1].id : 0;
                // console.log(this.requestLoading, this.page.more)
                if (!this.page.more || this.requestLoading) return;
                this.requestLoading = true;
                this.loadmoreStatus = 'loading';
                let from = '';
                // #ifndef MP-TOUTIAO
                from = 'routine';
                // #endif
                // #ifdef MP-TOUTIAO
                from = 'bytedance';
                // #endif
                getSpecials({
                    ...this.page,
                    keyword,
                    subject_id,
                    source_id: courseSearchId,
                    from:from
                }).then(res => {
                    this.requestLoading = false;
                    this.listArr = this.listArr.concat(res.data);
                    // console.log('this.listArr-', this.listArr)
                    const isMore = res.data.length === this.page.limit;
                    this.page.more = isMore;
                    this.page.page++;
                    this.loadmoreStatus = isMore ? 'loadmore' : 'nomore';
                })
            }, 200, false),
            goPages(path) {
                this.$navigator(path || '/pages/yknowledge/course/detail');
            },
            initInfo() {
                this.listArr = [];
                this.requestLoading = false;
                this.page = {
                    page: 1,
                    limit: 10,
                    more: true
                }
            },
            tabClick(item, index, type) {
                this.resetCourseSearchId()
                if (type === 1) {
                    this.tabIdx = index
                    this.tabList1 = item.children;
                    this.tabIdx1 = 0;
                    this.listArr = []
                } else {
                    if (this.tabIdx1 === index) {
                        if (this.tabIdx === 1) {
                            
                            return
                        } else {
                            this.tabIdx1 = -1;
                        }
                    } else {
                        this.tabIdx1 = index;
                    }
                }
                this.getList(true)
            }
        },
        onLoad(option) {
            // console.log('参数',option)
            // if(option.name){
            //     this.keyword = option.name;
            // }
        },
        
        onShow() {
            // #ifdef MP-TOUTIAO
               uni.hideTabBar()
            // #endif
            this.setTabBarIndex(1); //index为当前tab的索引
            const {
                courseSearchId = 0
            } = this.$store.state;
            getSpecialCategory().then(res => {
                this.tabList = res.data;
                // console.log('我的课程tabList-',this.tabList)
                this.tabList1 = res.data[this.tabIdx].children;
                // console.log('tabList1-',this.tabList1)
                this.getList(courseSearchId > 0)
            })
        },
        mounted() {
            // #ifdef MP
            // getChannelsLiveNoticeInfo().then(res=>{
            // 	console.log('getChannelsLiveNoticeInfo',res)
            // }).catch(err=>{
            // 	console.log('getChannelsLiveNoticeInfoerr',err)
            // })
            // getChannelsLiveInfo().then(res=>{
            // 	console.log('getChannelsLiveInfo',res)
            // }).catch(err=>{
            // 	console.log('getChannelsLiveInfoerr',err)
            // })
            // #endif
        },
        onReachBottom() {
            // if (this.tabIdx === -1 || this.tabIdx1 === -1) return;
            this.getList(false, this.keyword)
        },
        onHide() {
            // console.log('onHide')
            this.$store.state.courseSearchId > 0 && this.initInfo()
        }
    }
</script>

<style lang="scss" scoped>
    .course {

        image {
            width: 100%;
            height: 100%;
        }
        
        /* #ifdef MP-TOUTIAO */
        .uni-tabbar-bottom, .uni-tabbar {
            display: none;
        }
            
            .tab-bar {
            	position: fixed;
            	bottom: 0;
            	left: 0;
            	right: 0;
            	height: calc(constant(safe-area-inset-bottom) + 128rpx);
            	height: calc(env(safe-area-inset-bottom) + 128rpx);
            	background: white;
            	display: flex;
            	border-radius: 8rpx 8rpx 0px 0px;
            	box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
            	padding-bottom: constant(safe-area-inset-bottom);
            	padding-bottom: env(safe-area-inset-bottom);
                z-index: 99;
            }
            
            .tab-bar-border {
            	/* 	position: absolute;
            	left: 0;
            	top: 0;
            	width: 100%;
            	height: 1px;
            	transform: scaleY(0.5); */
            }
            
            .tab-bar-item {
            	flex: 1;
            	text-align: center;
            	display: flex;
            	justify-content: center;
            	align-items: center;
            	flex-direction: column;
            }
            
            .tab-bar-item image {
            	width: 52rpx;
            	height: 52rpx;
            }
            
            .tab-bar-item view {
            	font-size: 10px;
            
            }
            
            .tab-bar-item:nth-child(3) {
            	margin-bottom: 12rpx;
            	margin-top: -8rpx;
            }
            
            .tab-bar-item:nth-child(3) .wrap {
            	width: 156rpx !important;
            	height: 156rpx !important;
            	border-radius: 50%;
            	background: #fff;
            	opacity: 1;
            	/* border-radius: 4rpx 4rpx 24rpx 24rpx; */
            	display: flex;
            	align-items: center;
            	justify-content: center;
            }
            
            .tab-bar-item:nth-child(3) .wrap image {
            	/* width: 124rpx;
            	height: 52rpx; */
            	width: 156rpx !important;
            	height: 156rpx !important;
            }
            
            .tab-bar-item:nth-child(4) .wrap {
            	position: relative;
            }
            
            .tab-bar-item:nth-child(4) .wrap .dot {
            	position: absolute;
            	width: 14rpx;
            	height: 14rpx;
            	top: -2rpx;
            	right: 2rpx;
            	border-radius: 50%;
            	background-color: red;
            }
            
            
            .tab-bar-item .animate {
            	animation: huXi 5s linear infinite;
            }
            @keyframes rotate{
            		from {
            			transform: rotate(0);
            		}
            		30%{
            			transform: rotate(90deg);
            		}
            		50%{
            			transform: rotate(180deg);
            		}
            		70%{
            			transform: rotate(270deg);
            		}
            		to{
            			transform: rotate(360deg);
            		}
            	}
                
            @keyframes rotateY {
            	from {
            		transform: rotateY(0);
            	}
            
            	30% {
            		transform: rotateY(0);
            	}
            
            	50% {
            		transform: rotateY(360deg);
            	}
            
            	70% {
            		transform: rotateY(0);
            	}
            
            	to {
            		transform: rotateY(0);
            	}
            }
            
            @keyframes huXi {
            	from {
            		transform: scale(1);
            	}
            
            	30% {
            		transform: scale(1.1);
            	}
            
            	40% {
            		transform: scale(0.8);
            	}
            
            	50% {
            		transform: scale(0.6);
            	}
            
            	60% {
            		transform: scale(0.8);
            	}
            
            	70% {
            		transform: scale(1.1);
            	}
            
            	to {
            		transform: scale(1);
            	}
            }
            
        /* #endif */
        .course_top {
            .wrap {
                padding: 0 40rpx;
                height: 114rpx;
                margin: 24rpx 0 29rpx 0;
                // box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
                // border-bottom-left-radius: 20rpx;
                // border-bottom-right-radius: 20rpx;

                .c_t_image {
                    width: 156rpx;
                    height: 114rpx;
                    margin-left: 20rpx;
                }

                .c_t_search {
                    width: 472rpx;
                }
            }

            .tab {
                margin-left: 56rpx;

                .item {
                    background: #E9F1FB;
                    padding: 0 32rpx;
                    min-width: 192rpx;
                    height: 60rpx;
                    line-height: 60rpx;
                    text-align: center;
                    box-shadow: 0px 0px 12rpx rgba(0, 0, 0, 0.05);
                    border-top-left-radius: 20rpx;
                    border-top-right-radius: 20rpx;
                    // margin-left:56rpx;

                    font-size: 24rpx;
                    font-family: PingFang SC, PingFang SC-Medium;
                    font-weight: 500;
                    color: #61a8ff;

                    &.active {
                        background: #fff;
                        font-size: 28rpx;
                        font-weight: 800;
                        color: #6cb5ff;
                    }
                }
            }
        }

        .course_wrap {
            .tab {
                // border-bottom: 2rpx solid #E2E6EC;
                background-color: #fff;
                width: 672rpx;
                margin: 0 auto;
                border-radius: 16rpx;
                box-shadow: 0px 0px 12rpx 0px rgba(0, 0, 0, 0.05);
                height: 220rpx;
                padding: 36rpx 10rpx 0 24rpx;

                .item {
                    margin-right: 28rpx;

                    .item_t {
                        width: 104rpx;
                        height: 104rpx;
                        font-weight: 700;
                        border-radius: 40rpx;

                        image {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }

                .txt {
                    width: 104rpx;
                    margin-top: 8rpx;
                    font-size: 20rpx;
                    font-family: SF Pro Text, SF Pro Text-Medium;
                    font-weight: 500;
                    text-align: center;
                    color: #6cb5ff;
                    height: auto;
                    white-space: wrap;
                    // @include show_line(2)
                }
            }

            .line {
                width: 672rpx;
                height: 4rpx;
                background-color: #E2E6EC;
                margin: 30rpx auto;
            }

            .list {
                min-height: 400rpx;
                padding-bottom: 70rpx;
            }
        }

    }
</style>
