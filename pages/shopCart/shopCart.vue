<template>
	<view class="shoppingCart">
		<view class="labelNav acea-row row-around row-middle">
			<view class="item">
				<span class="iconfont icon-xuanzhong"></span>
				100%正品保证
			</view>
			<view class="item">
				<span class="iconfont icon-xuanzhong"></span>
				所有商品精挑细选
			</view>
			<view class="item">
				<span class="iconfont icon-xuanzhong"></span>
				售后无忧
			</view>
		</view>
		<view class="nav acea-row row-between-wrapper">
			<view>
				全部（<span class="num font-color-red">{{ cartList.valid.length }}</span> ）
			</view>
			<view v-if="cartList.valid.length > 0" class="administrate acea-row row-center-wrapper" @click="manage">{{ footerswitch ? '取消' : '管理' }}</view>
		</view>
		<view v-if="cartList.valid.length > 0 || cartList.invalid.length > 0">
			<view class="list">
				<checkbox-group @change="checkboxChange">
					<view class="item acea-row row-between-wrapper" v-for="(item, index) in cartList.valid" :key="index">
						<view class="select-btn">
							<view class="checkbox-wrapper">
								<label class="well-check">
									<checkbox :value="item.id + ''" :checked="item.checked" />
								</label>
							</view>
						</view>
						<view class="picTxt acea-row row-between-wrapper">
							<view class="pictrue flex flex_around" @click="goPages(item)">
								<image :src="item.productInfo.attrInfo.image" v-if="item.productInfo.attrInfo"  mode="heightFix"/>
								<image :src="item.productInfo.image" mode="heightFix" v-else />
							</view>
							<view class="text">
								<view class="line1">{{ item.productInfo.store_name }}</view>
								<view class="infor line1" v-if="item.productInfo.attrInfo">属性：{{ item.productInfo.attrInfo.suk }}</view>
								<view class="money">￥{{ item.truePrice }}</view>
							</view>
							<view class="carnum acea-row row-center-wrapper">
								<view class="reduce" :class="cartList.valid[index].cart_num <= 1 ? 'on' : ''" @click.prevent="reduce(index)">-</view>
								<view class="num"><input type="number" v-model="item.cart_num" class="ipt_num" @input.prevent="specifiName(index)" @blur.prevent="blurName(index)" /></view>
								<view
									class="plus"
									v-if="cartList.valid[index].attrInfo"
									:class="cartList.valid[index].cart_num >= cartList.valid[index].attrInfo.stock ? 'on' : ''"
									@click.prevent="plus(index)"
								>
									+
								</view>
								<view class="plus" v-else :class="cartList.valid[index].cart_num >= cartList.valid[index].stock ? 'on' : ''" @click.prevent="plus(index)">+</view>
							</view>
						</view>
					</view>
				</checkbox-group>
			</view>
			<view class="invalidGoods" v-if="cartList.invalid.length > 0">
				<view class="goodsNav acea-row row-between-wrapper">
					<view @click="goodsOpen">
						<span class="iconfont" :class="goodsHidden === true ? 'icon-xiangyou' : 'icon-xiangxia'"></span>
						失效商品
					</view>
					<view class="del" @click="delInvalidGoods">
						<span class="iconfont icon-shanchu1"></span>
						清空
					</view>
				</view>
				<view class="goodsList" :hidden="goodsHidden">
					<view @click="goPages(item)" class="item acea-row row-between-wrapper" v-for="(item, index) in cartList.invalid" :key="index">
						<view class="invalid acea-row row-center-wrapper">失效</view>
						<view class="pictrue">
							<image :src="item.productInfo.attrInfo.image" v-if="item.productInfo.attrInfo" />
							<image :src="item.productInfo.image" v-else />
						</view>
						<view class="text acea-row row-column-between">
							<view class="line1">{{ item.productInfo.store_name }}</view>
							<view class="infor line1" v-if="item.productInfo.attrInfo">属性：{{ item.productInfo.attrInfo.suk }}</view>
							<view class="acea-row row-between-wrapper"><view class="end">该商品已下架</view></view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!--购物车暂无商品-->
		<view class="noCart" v-if="cartList.valid.length === 0 && cartList.invalid.length === 0">
			<view class="pictrue"><image :src="imagePath + '/wximage/noCart.png'" /></view>
			<!-- <Recommend></Recommend> -->
		</view>
		<view style="height:2.1rem"></view>
		<view class="footer acea-row row-between-wrapper" v-if="cartList.valid.length > 0">
			<view class="checkbox-wrapper">
				<checkbox-group @change="allChecked">
					<label class="well-check acea-row row-middle">
						<checkbox value="all" :checked="isAllSelect && cartCount > 0" />
						<text class="checkAll">全选 ({{ cartCount }})</text>
					</label>
				</checkbox-group>
			</view>
			<view class="money acea-row row-middle" v-if="footerswitch === false">
				<span class="font-color-red">￥{{ countmoney }}</span>
				<view class="placeOrder bg-color-red" @click="placeOrder">立即下单</view>
			</view>
			<view class="button acea-row row-middle" v-else>
				<view class="bnt cart-color" @click="collectAll">收藏</view>
				<view class="bnt" @click="delgoods">删除</view>
			</view>
		</view>
		
		<x-authorize @login='updateData'></x-authorize>
	</view>
</template>
<script>
// import Recommend from '@/components/Recommend';
import { getCartList, postCartDel, changeCartNum, getCartCount } from '@/api/store';
import { postCollectAll } from '@/api/user';
import { mul, add } from '@/utils/bc';
// import cookie from '@utils/store/cookie';
	import{VUE_APP_URL} from '@/config.js'
import { toLogin, checkLogin, debounce } from '@/utils/common.js';

const CHECKED_IDS = 'cart_checked';

export default {
	name: 'ShoppingCart',
	components: {
		// Recommend
	},
	props: {},
	data: function() {
		return {
			imagePath:VUE_APP_URL,
			cartList: { invalid: [], valid: [] },
			isAllSelect: false,
			cartCount: 0,
			countmoney: 0,
			goodsHidden: true,
			footerswitch: false,
			count: 0,
			checkedIds: [],
			loaded: false,
			index_num: 0,
			deliveryMethod: -1 //getCartList()中初始化
		};
	},
	watch: {
		// #ifdef H5
		$route(n) {
		  if (n.name === 'ShoppingCart') {
		    this.updateData();
		  }
		}
		// #endif 
	},
	mounted: function() {
		
	},
	onShow() {
		this.updateData()
	},
	methods: {
		goPages(item){
			const {type}=item;
			if(type==="product"){
				this.$navigator(`/pages/shop/GoodsCon?id=${item.product_id}`)
			}
			if(type==="evaluate_product"){
				this.$navigator(`/pages/yuanshi/evaluate/detail?wid=${item.wish_id}&pid=${item.product_id}`)
			}
		},
		updateData(){
			let that = this;
			that.carnum();
			that.countMoney();
			that.getCartList();
			that.gainCount();
		},
		getCartList: function() {
			let that = this;
			getCartList().then(res => {
				that.cartList = res.data;
				let checkedIds = that.$storage.get(CHECKED_IDS) || [];
				if (!Array.isArray(checkedIds)) checkedIds = [];
				this.cartList.valid.forEach(cart => {
					if (that.deliveryMethod < 0) {
						// 不存在默认 deliveryMethod 初始化
						that.deliveryMethod = cart.delivery_method;
					}
					if (checkedIds.length) {
						if (checkedIds.indexOf(cart.id) !== -1) {
							if (that.deliveryMethod < 0) {
								// 缓存中存在购车信息。更新delivery_method
								that.deliveryMethod = cart.delivery_method;
							}
							if (that.deliveryMethod === cart.delivery_method) {
								cart.checked = true;
							} else {
								checkedIds.splice(checkedIds.indexOf(cart.id), 1);
								cart.checked = false;
							}
						} else cart.checked = false;
					} else {
						if (that.deliveryMethod === cart.delivery_method) {
							cart.checked = true;
							that.checkedIds.push(cart.id);
						} else {
							// 任意两个商品支持的物流方式不一致，不允许全选
							that.checkedIds = [];
							return;
						}
					}
				});
				if (checkedIds.length) {
					that.checkedIds = checkedIds;
				}
				that.isAllSelect = that.checkedIds.length === this.cartList.valid.length;
				that.carnum();
				that.countMoney();
				this.loaded = true;
			});
		},
		//删除商品；
		delgoods: function() {
			let that = this,
				id = [],
				valid = [],
				list = that.cartList.valid;
			list.forEach(function(val) {
				if (val.checked === true) {
					id.push(val.id);
				}
			});
			if (id.length === 0) {
				that.$dialog.toast({ mes: '请选择产品' });
				return;
			}
			postCartDel(id).then(function() {
				list.forEach(function(val, i) {
					if (val.checked === false || val.checked === undefined) valid.push(list[i]);
				});
				that.$set(that.cartList, 'valid', valid);
				that.carnum();
				that.countMoney();
				that.gainCount();
			});
		},
		// //获取数量
		gainCount: function() {
			let that = this;
			getCartCount().then(res => {
				that.count = res.data.count;
			});
		},
		//清除失效产品；
		delInvalidGoods: function() {
			let that = this,
				id = [],
				list = that.cartList.invalid;
			list.forEach(function(val) {
				id.push(val.id);
			});
			postCartDel(id).then(function() {
				list.splice(0, list.length);
				that.gainCount();
			});
		},
		//批量收藏;
		collectAll: function() {
			let that = this,
				data = { id: [], category: '' },
				list = that.cartList.valid;
			list.forEach(function(val) {
				if (val.checked === true) {
					data.id.push(val.product_id);
					data.category = val.type;
				}
			});
			if (data.id.length === 0) {
				that.$dialog.toast({ mes: '请选择产品' });
				return;
			}
			postCollectAll(data).then(function() {
				that.$dialog.toast({ mes: '收藏成功!' });
			});
		},
		//立即下单；
		placeOrder: function() {
			let that = this,
				list = that.cartList.valid,
				id = [];
			list.forEach(function(val) {
				if (val.checked === true) {
					id.push(val.id);
				}
			});
			if (id.length === 0) {
				that.$dialog.toast({ mes: '请选择产品' });
				return;
			}
			that.$navigator('/pages/order/OrderSubmission?cartId=' + id);
			// this.$router.push({ path: '/order/submit/' + id });
		},
		manage: function() {
			let that = this;
			that.footerswitch = !that.footerswitch;
			if (!that.footerswitch) {
				that.isAllSelect = false;
				that.allChecked();
				// console.log(that.deliveryMethod)
			}else{
				if(that.isAllSelect){
					that.isAllSelect = false;
					that.allChecked()
				}
			}
		},
		goodsOpen: function() {
			let that = this;
			that.goodsHidden = !that.goodsHidden;
		},
		//加
		plus: function(index) {
			let that = this;
			let list = that.cartList.valid[index];
			list.cart_num++;
			if (list.attrInfo) {
				if (list.cart_num >= list.attrInfo.stock) {
					that.$set(list, 'cart_num', list.attrInfo.stock);
				}
			} else {
				if (list.cart_num >= list.stock) {
					that.$set(list, 'cart_num', list.stock);
				}
			}
			that.carnum();
			that.countMoney();
			that.syncCartNum(list);
		},
		specifiName(index) {
			let list = this.cartList.valid[index];
			this.index_num = index;
			this.carnum();
			this.countMoney();
			this.syncCartNum(list);
		},
		blurName(index) {
			let list = this.cartList.valid[index];
			if (list.cart_num < 1) {
				this.$set(list, 'cart_num', 1);
			}
			this.carnum();
			this.countMoney();
			this.syncCartNum(list);
		},
		//减
		reduce: function(index) {
			let that = this;
			let list = that.cartList.valid[index];
			list.cart_num--;
			if (list.cart_num < 1) {
				that.$set(list, 'cart_num', 1);
			}
			that.carnum();
			that.countMoney();
			that.syncCartNum(list);
		},
		syncCartNum(cart) {
			if (!cart.sync)
				cart.sync = debounce(() => {
					changeCartNum(cart.id, Math.max(cart.cart_num, 1) || 1);
				}, 500);

			cart.sync();
		},
		// 单选
		checkboxChange(e) {
			let that = this,
				val = e.detail.value.map(Number),
				valid = this.cartList.valid,
				deliveryMethod = this.deliveryMethod,
				len = this.cartList.valid.length;
			for (let index in valid) {
				if (val.indexOf(valid[index].id) > -1) {
					valid[index].checked = true;
					if (!this.footerswitch) {
						if (deliveryMethod < 0) {
							this.deliveryMethod = valid[index].delivery_method;
						} else {
							if (deliveryMethod !== valid[index].delivery_method) {
								// 非管理状态下,更新deliveryMethod为当前选中所对应值，同时将非当前值的状态为非选中状态
								valid.forEach((key, idx) => {
									if (key.id !== valid[index].id) {
										if (key.delivery_method !== valid[index].delivery_method) {
											key.checked = false;
											that.deliveryMethod = valid[index].delivery_method;
											val = [valid[index].id];
											if (that.cartCount > 0) {
												that.$showToast('所选商品不支持与其它商品同时结算');
											}
										}
									}
								});
							}
						}
					}
				} else {
					valid[index].checked = false;
				}
			}

			that.checkedIds = val;
			that.isAllSelect = val.length === len;
			that.cartList.valid = valid;
			that.$set(that, 'isAllSelect', that.isAllSelect);
			that.$storage.set(CHECKED_IDS, that.checkedIds);
			that.carnum();
			that.countMoney();
		},

		//全选
		allChecked: function() {
			let that = this;
			let selectAllStatus = that.isAllSelect;
			selectAllStatus = !selectAllStatus;
			let checkedIds = [];
			if (!selectAllStatus || this.footerswitch) {
				// 取消全选 或者管理状态下允许全选
				that.cartList.valid.forEach(cart => {
					cart.checked = selectAllStatus;
					if (selectAllStatus) checkedIds.push(cart.id);
				});
			} else {
				if (that.deliveryMethod < 0) {
					that.deliveryMethod = that.cartList.valid[0].delivery_method;
				}
				that.cartList.valid.forEach(cart => {
					if (that.deliveryMethod == cart.delivery_method) {
						cart.checked = true;
						if (selectAllStatus) checkedIds.push(cart.id);
						if (selectAllStatus) {
							that.$showToast('已选中所有允许同时结算商品');
						}
					} else {
						cart.checked = false;
					}
				});
			}

			that.$set(that, 'cartList', that.cartList);
			that.$set(that, 'isAllSelect', selectAllStatus);
			this.checkedIds = checkedIds;
			that.$storage.set(CHECKED_IDS, checkedIds);
			that.carnum();
			that.countMoney();
		},
		//数量
		carnum: function() {
			let that = this;
			var carnum = 0;
			var array = that.cartList.valid;
			for (let i = 0; i < array.length; i++) {
				if (array[i].checked === true) {
					if (array[i].cart_num) carnum += parseInt(array[i].cart_num);
				}
			}
			that.$set(that, 'cartCount', carnum);
		},
		//总共价钱；
		countMoney: function() {
			let that = this;
			let carmoney = 0;
			let array = that.cartList.valid;
			for (let i = 0; i < array.length; i++) {
				if (array[i].checked === true) {
					carmoney = add(carmoney, mul(array[i].cart_num, array[i].truePrice));
				}
			}
			that.countmoney = carmoney;
		}
	}
};
</script>
<style scoped lang="scss">
.ipt_num {
	width: 100%;
	display: block;
	line-height: 0.44rem;
	text-align: center;
}
.shoppingCart .labelNav {
	height: 76rpx;
	padding: 0 30rpx;
	font-size: 22rpx;
	color: #8c8c8c;
	position: fixed;
	left: 0;
	width: 100%;
	background-color: #f5f5f5;
	z-index: 5;
	top: 0;
}

.shoppingCart .labelNav .item .iconfont {
	font-size: 25rpx;
	margin-right: 10rpx;
}

.shoppingCart .nav {
	width: 100%;
	height: 80rpx;
	background-color: #fff;
	padding: 0 30rpx;
	font-size: 28rpx;
	color: #282828;
	position: fixed;
	left: 0;
	z-index: 5;
	top: 76rpx;
	border-bottom: 1px solid #f5f5f5;
}

.shoppingCart .nav .administrate {
	font-size: 26rpx;
	color: #282828;
	width: 110rpx;
	height: 46rpx;
	border-radius: 6rpx;
	border: 1px solid #868686;
}

.shoppingCart .noCart {
	background-color: #fff;
	padding: 250rpx 0 56rpx 0;
}

.shoppingCart .noCart .pictrue {
	width: 413rpx;
	height: 336rpx;
	margin: 0 auto;
}

.shoppingCart .noCart .pictrue image {
	width: 100%;
	height: 100%;
}

.shoppingCart .list {
	margin-top: 171rpx;
}

.shoppingCart .list .item {
	padding: 25rpx 30rpx;
	background-color: #fff;
	margin-bottom: 15rpx;
}

.shoppingCart .list .item .picTxt {
	width: 627rpx;
	position: relative;
}

.shoppingCart .list .item .picTxt .pictrue {
	width: 160rpx;
	height: 160rpx;
}

.shoppingCart .list .item .picTxt .pictrue image {
	width: 100%;
	height: 100%;
	border-radius: 6rpx;
}

.shoppingCart .list .item .picTxt .text {
	width: 444rpx;
	font-size: 28rpx;
	color: #282828;
	text-align: left;
}

.shoppingCart .list .item .picTxt .text .infor {
	font-size: 24rpx;
	color: #868686;
	margin-top: 16rpx;
}

.shoppingCart .list .item .picTxt .text .money {
	font-size: 32rpx;
	color: #282828;
	margin-top: 26rpx;
}

.shoppingCart .list .item .picTxt .carnum {
	height: 44rpx;
	position: absolute;
	bottom: 7rpx;
	right: 0;
}

.shoppingCart .list .item .picTxt .carnum view {
	border: 1px solid #a4a4a4;
	width: 66rpx;
	text-align: center;
	height: 100%;
	line-height: 44rpx;
	font-size: 28rpx;
	color: #a4a4a4;
}

.shoppingCart .list .item .picTxt .carnum .reduce {
	border-right: 0;
	border-radius: 3rpx 0 0 3rpx;
	line-height: 39rpx;
}

.shoppingCart .list .item .picTxt .carnum .reduce.on {
	border-color: #e3e3e3;
	color: #dedede;
}

.shoppingCart .list .item .picTxt .carnum .plus {
	border-left: 0;
	border-radius: 0 3rpx 3rpx 0;
	line-height: 38rpx;
}

.shoppingCart .list .item .picTxt .carnum .num {
	color: #282828;
}

.shoppingCart .invalidGoods {
	background-color: #fff;
}

.shoppingCart .invalidGoods .goodsNav {
	width: 100%;
	height: 66rpx;
	padding: 0 30rpx;
	font-size: 28rpx;
	color: #282828;
}

.shoppingCart .invalidGoods .goodsNav .iconfont {
	color: #424242;
	font-size: 28rpx;
	margin-right: 17rpx;
}

.shoppingCart .invalidGoods .goodsNav .del {
	font-size: 26rpx;
	color: #999;
}

.shoppingCart .invalidGoods .goodsNav .del .icon-shanchu1 {
	color: #999;
	font-size: 33rpx;
	vertical-align: -2rpx;
	margin-right: 8rpx;
}

.shoppingCart .invalidGoods .goodsList .item {
	padding: 20rpx 30rpx;
	border-top: 1px solid #f5f5f5;
}

.shoppingCart .invalidGoods .goodsList .item .invalid {
	font-size: 22rpx;
	color: #fff;
	width: 70rpx;
	height: 36rpx;
	background-color: #aaa;
	border-radius: 3rpx;
}

.shoppingCart .invalidGoods .goodsList .item .pictrue {
	width: 140rpx;
	height: 140rpx;
}

.shoppingCart .invalidGoods .goodsList .item .pictrue image {
	width: 100%;
	height: 100%;
	border-radius: 6rpx;
}

.shoppingCart .invalidGoods .goodsList .item .text {
	width: 433rpx;
	font-size: 28rpx;
	color: #999;
	height: 140rpx;
	text-align: left;
}

.shoppingCart .invalidGoods .goodsList .item .text .infor {
	font-size: 24rpx;
}

.shoppingCart .invalidGoods .goodsList .item .text .end {
	font-size: 26rpx;
	color: #bbb;
}

.shoppingCart .footer {
	width: 100%;
	background-color: #fafafa;
	@include fixed_footer;
	border-top: 1px solid #eee;
}

.shoppingCart .footer .checkAll {
	font-size: 28rpx;
	color: #282828;
	margin-left: 20rpx;
}

.shoppingCart .footer .money {
	font-size: 30rpx;
}

.shoppingCart .footer .placeOrder {
	color: #fff;
	font-size: 30rpx;
	width: 225rpx;
	height: 70rpx;
	border-radius: 50rpx;
	text-align: center;
	line-height: 70rpx;
	margin-left: 22rpx;
}

.shoppingCart .footer .button .bnt {
	font-size: 28rpx;
	color: #999;
	border-radius: 50rpx;
	border: 1px solid #999;
	width: 160rpx;
	height: 60rpx;
	text-align: center;
	line-height: 60rpx;
}

.shoppingCart .footer .button .bnt ~ .bnt {
	margin-left: 17rpx;
}
</style>
