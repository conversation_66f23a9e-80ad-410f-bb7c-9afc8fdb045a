<template>
    <view>
        <canvas canvas-id="myCanvas" style="position:fixed;width: 750px;height: 1298px;top:0;left: -800px;"></canvas>
        <view class="content">
            <view class="topbg">
                <image class="image" :src="detail.image" mode="widthFix"></image>
            </view>
            <view class="center-box">
                <view class="center-box-top">
                    <view class="title">
                        <view class="title-left">
                            【分享得课程券】流程：
                        </view>
                        <!-- <button class="title-right flex_line_height" open-type="share" @click="share_pop">
                            <view class="flex flex_align_center">
                                <image class="share-icon" src="../../../static/images/share-icon.png" mode=""></image>
                                <text>分享</text>
                            </view>
                        </button> -->
                    </view>
                    <view class="item">
                        <view class="item-desc">
                            1、通过下方蓝色“邀请朋友参与活动”按钮，生成本活动二维码图片
                        </view>
                    </view>
                    <view class="item">
                        <view class="item-desc">
                            2、保存活动二维码图片到手机本地
                        </view>
                    </view>
                    <view class="item">
                        <view class="item-desc">
                            3、通过微信发送二维码图片给好友
                        </view>
                    </view>
                    <view class="item">
                        <view class="item-desc">
                            4、好友在微信中打开图片并长按二维码进入课程页完成购买课程
                        </view>
                    </view>
                    <view class="item">
                        <view class="item-desc">
                            5、好友完成支付后，您将获得20元通用课程券
                        </view>
                    </view>
                </view>
                <view class="center-box-bot">
                    <view class="title">
                        活动说明：
                    </view>
                    <view class="item">
                        <view class="item-title">
                            参与条件：
                        </view>
                        <view class="item-desc">
                            <mp-html :content="detail.join_condition" />
                        </view>
                        <view class="item-but gray" v-if="detail.is_qualify == 1 ">
                            您已满足参与条件
                        </view>
                        <view class="item-but" @click="godetail(detail.need_special_id)" v-else>
                            您不满足参与条件，去购买课程
                        </view>
                    </view>
                    <view class="item">
                        <view class="item-title">
                            参与奖励：
                        </view>
                        <view class="item-desc" style="margin: 0;" v-if="detail.description">
                            <mp-html :content="detail.description" />
                        </view>
                    </view>
                    <view class="item">
                        <view class="item-title">
                            其他说明：
                        </view>
                        <view class="item-desc" v-if="detail.info">
                            <text>{{detail.info}}</text>
                        </view>
                        <view class="item-tips">
                            （着调官方拥有本活动最终解释权）
                        </view>
                    </view>
                    <view class="item" style="border: 0;padding-bottom: 0;" v-if="detail.kf_info">
                        <view class="item-title" style="margin: 0;" @click="goAddService">
                            <mp-html :content="detail.kf_info" />
                        </view>
                    </view>

                </view>
            </view>
            <view class="user-box">
                <view class="center-user">
                    <view class="center-user-img">
                        <image :src="userInfo.avatar" mode=""></image>
                    </view>
                    <view class="center-user-title">{{userInfo.nickname}}</view>
                </view>
            </view>
            <view class="want_go">
                <view class="nav ">
                    <!-- <view class="title">已成功找到{{detail.success_invited_num || '0'}}个音乐搭子:</view> -->
                    <view class="title">已成功分享给{{detail.success_invited_num || '0'}}个好友</view>
                </view>
                <view class="wrap1 ">
                    <scroll-view scroll-x style="white-space:nowrap;" :scroll-with-animation="true">
                        <view class="item" v-for="(item, index) in detail.success_invited_list" :key="item.uid">
                            <image :src="item.avatar" mode="aspectFill"></image>
                            <view class="name">{{ item.nickname }}</view>
                        </view>
                    </scroll-view>
                </view>
            </view>
            <view class="share-but">
                <view class="equity-user-but" @click="sharePoster(ids.id,ids.activityId,detail.is_qualify)">
                    邀请朋友参与活动
                </view>
                <view class="equity-user-but gray" @click="goback">
                    不参与活动
                </view>
            </view>
        </view>
        <uni-popup ref="posterPopup" :zIndex="999">
            <image :src="postImage" mode="widthFix" style="width: 600rpx;display: block;"></image>
            <view class="download" v-if="postImage" @click="savePosterPath">长按保存图片</view>
        </uni-popup>
        <uni-popup ref="sharePopup" :zIndex="999">
            <view style="width: 750rpx;height:100vh;text-align:center">
                <image src="@/static/images/share-info.png" mode="scaleToFill" style="width: 100%;height:100%;"
                    @click="close_share_pop"></image>
            </view>
        </uni-popup>
    </view>
</template>

<script>
    import mpHtml from '@/components/mp-html/mp-html'
    // import mpHtml from '../components/mp-html/mp-html'
    // import mpHtml from '@/pages/yknowledge/components/mp-html/mp-html.vue'
    import {
        specialActivityDetail,
        createInvitationRecord
    } from '@/api/activity';
    import {
        imageBase64
    } from '@/api/public';
    import {
        getSpecialDetail,
    } from '@/api/yknowledge.js'
    import {
        getUser
    }
    from '@/api/user.js';
    import {
        toLogin,
        debounce,
        checkLogin,
        autoAuth,
        authNavigator,
        zxauthNavigator,
        PosterCanvas,
        saveImageToPhotosAlbum,
        SharePoster
    } from '@/utils/common.js'
    
    import {
        IS_TOUTIAO_PAY,
        SHARE_ID,
        WX_KEFU_Link,
        WX_ENTERPRISE_Link,
        OPEN_IOS_PAYMENT,
        VUE_APP_URL,
        WECHAT_AUTH_BACK_URL
    } from '@/config.js';
    
    export default {
        components: {
            mpHtml
        },
        data() {
            return {
                postImage: '',
                posterData: {
                    image: '',
                    title: '',
                    desc: '正在寻找音乐搭子 参与Yusi带你听音乐',
                    code: ''
                },
                ids: {},
                userInfo: {},
                detail: {
                    share_info: ''
                },
                // #ifdef MP-TOUTIAO
                from: 'bytedance',
                // #endif
                // #ifndef MP-TOUTIAO
                from: 'routine',
                // #endif
                shareConfig: {},
            }
        },
        onShareAppMessage() {
            return {
                title: this.shareConfig.title,
                imageUrl: this.shareConfig.imgUrl,
                path: this.shareConfig.link,
            };
        },
        onLoad(option) {
            const {
                id = 0,
                    activityId = 0
            } = option;
            this.ids = option;
            
            // https://dev.shop.arthorize.com/h5/course/share?id=6&activityId=1&uid=1919
            this.$storage.set(WECHAT_AUTH_BACK_URL, VUE_APP_URL + '/h5/course/share?id=' + this.ids.id + '&activityId=' + this.ids.activityId)
            this.getspecialActivityDetail(this.ids.id, this.ids.activityId)
        },
        onShow() {

        },
        methods: {
            share_pop() {
                this.$refs.sharePopup.open();
            },
            close_share_pop() {
                this.$refs.sharePopup.close();
            },
            savePosterPath() {
                let _this = this;
                saveImageToPhotosAlbum(_this.postImage, this.posterData.title, function(res) {
                    _this.$refs.posterPopup.close();
                });
            },
            godetail(special_id) {
                // // #ifdef H5
                // uni.webView.redirectTo({
                //     url:'/course/detail?id=0&sid=' + special_id
                // })
                // // #endif
                // // #ifdef MP
                // uni.webView.redirectTo({
                //     url:'/pages/yknowledge/course/detail?id=0&sid=' + special_id
                // })
                // // #endif
                // #ifdef H5
                this.goPages('/pages/yknowledge/course/detail?id=0&sid=' + special_id, false, 'redirectTo')
                // #endif
            },
            goPages(path, type, pathType) {
                if (type) {
                    this.$authNavigator(path);
                } else {
                    this.$navigator(path, pathType);
                }
            },
            goAddService() {
                if (checkLogin()) {
                    this.goPages('/pages/index/addService', false)
                } else {
                    autoAuth();
                }
            },
            async sharePoster(id, activityId, is_qualify) {
                if (!is_qualify) {
                    return this.$showToast('您不满足参与条件，请查看活动说明');
                }
                createInvitationRecord({
                    type: 0, //活动类型：0=老带新 1=直播课
                    id: id,
                    activity_id: activityId
                }).then(async (res) => {
                    console.log('分享数据', res.data)

                    let productImage = this.posterData.image,
                        bgImage,
                        reg = /(http|https):\/\/([\w.]+\/?)\S*/,
                        _this = this;

                    _this.$refs.posterPopup.open();
                    _this.$loadingToast('海报生成中');
                    let code = res.data[0].qrcode;
                    console.log('分享数据1', code)
                    if (!code) {
                        // #ifdef H5
                        _this.$showToast('商品二维码获取失败');
                        // #endif
                        // _this.close();
                        // _this.$refs.posterPopup.close();
                        return;
                    }
                    if (reg.test(productImage)) {
                        const res = await imageBase64(productImage);
                        productImage = res.data.image;
                    }
                    console.log('分享数据2', productImage)
                    // #ifdef H5
                    bgImage = require('@/static/images/posterbackgd.png');
                    // #endif
                    // #ifdef MP
                    bgImage = '/static/images/posterbackgd.png'; //小程序中不识别@，require引入不识别
                    // #endif
                    let arr = [bgImage, productImage, code];
                    console.log('arr', arr);
                    SharePoster(arr, _this.posterData.title, _this.posterData.desc, _this, function(
                    res) {
                        _this.$hideToast();
                        // _this.close();
                        _this.postImage = res;
                    });

                }).catch(err => {
                    // setTimeout(() => {
                    //     this.$navigator(-1)
                    // }, 500)
                })

            },
            goback() {
                uni.webView.navigateBack({
                    delta: 1
                })
                uni.webView.postMessage({
                    data: {
                        action: 'message'
                    }
                })
            },
            getspecialActivityDetail(id, activityId) {
                specialActivityDetail({
                    id: id,
                    activity_id: activityId
                }).then(res => {
                    this.detail = res.data;
                    this.posterData.desc = this.detail.share_info;
                    this.posterData.image = this.detail.image;
                    this.getUserInfo()
                    console.log('课程详情', this.detail)
                }).catch(err => {
                    // setTimeout(() => {
                    //     this.goback()
                    // }, 500)
                })
            },
            getUserInfo: function() {
                let that = this;
                getUser().then(res => {
                    this.userInfo = res.data;
                    console.log('邀请人详情', this.userInfo)
                    this.posterData.title = '好友 ' + this.userInfo.nickname
                });
            },

        }
    }
</script>

<style>
    page {
        background-color: #f7f8fa;
        padding-bottom: 0;
    }

    .content {
        width: 100%;
        height: auto;
        padding: 0 20rpx;
        box-sizing: border-box;
    }

    .topbg {
        position: relative;
        width: 100%;
        height: 710rpx;
        z-index: 1;
    }

    .topbg .image {
        width: 100%;
        height: 100%;
    }

    .user-box {
        position: relative;
        padding: 80rpx 36rpx 0 28rpx;
        box-sizing: border-box;
    }

    .center-user {
        width: 100%;
        border-bottom: 2rpx solid #e2e6ec;
        padding-bottom: 48rpx;
    }

    .center-user-img {
        width: 244rpx;
        height: 244rpx;
        border: 6rpx solid #000000;
        border-radius: 100rpx;
        margin: 0 auto 14rpx;
        overflow: hidden;
    }

    .center-user-img image {
        width: 100%;
        height: 100%;
        border-radius: 92rpx;
    }

    .center-user-title {
        width: 100%;
        font-size: 44rpx;
        text-align: center !important;
        font-family: PingFang SC, PingFang SC-Heavy;
        font-weight: 800;
        text-align: left;
        color: #333333;
    }

    .center-box {
        position: relative;
        width: 100%;
        height: auto;
        min-height: 500rpx;
        background: #ffffff;
        border: 2rpx solid #f2f5f8;
        border-radius: 30rpx;
        box-shadow: 0 0 40rpx 0 rgba(107, 127, 153, 0.20);
        z-index: 11;
        margin-top: -318rpx;
        padding: 6rpx;
        box-sizing: border-box;
    }

    .center-box-bot {
        width: 100%;
        padding: 40rpx 34rpx 60rpx 28rpx;
        box-sizing: border-box;
        overflow: auto;
    }

    .center-box-bot .title {
        width: 100%;
        font-size: 32rpx;
        font-family: PingFang SC, PingFang SC-Semibold;
        font-weight: 600;
        text-align: left;
        color: #50506f;
    }

    .center-box-bot .item {
        width: 100%;
        overflow: auto;
        border-bottom: 2rpx solid #e2e6ec;
        padding: 40rpx 0rpx 40rpx 48rpx;
        box-sizing: border-box;
    }

    .center-box-bot .item .item-title {
        width: 100%;
        font-size: 24rpx;
        font-family: PingFang SC, PingFang SC-Semibold;
        font-weight: 600;
        text-align: left;
        color: #50506f;
        margin-bottom: 20rpx;
    }

    .center-box-bot .item .item-desc {
        width: 100%;
        font-size: 24rpx;
        font-family: PingFang SC, PingFang SC-Regular;
        font-weight: 400;
        text-align: left;
        color: #666666;
        margin-bottom: 20rpx;
    }

    .center-box-bot .item .item-tips {
        width: 100%;
        font-size: 20rpx;
        font-family: PingFang SC, PingFang SC-Regular;
        font-weight: 400;
        text-align: left;
        color: #999999;
    }

    .center-box-bot .item .item-but {
        width: 564rpx;
        height: 50rpx;
        line-height: 50rpx;
        text-align: center;
        background: #50506f;
        border-radius: 12px;
        font-size: 24rpx;
        font-family: PingFang SC, PingFang SC-Regular;
        font-weight: 400;
        color: #ffffff;
    }

    .center-box-bot .item .item-but.gray {
        background: #999;
        color: #ffffff;
    }

    .center-box-top {
        width: 100%;
        padding: 40rpx 26rpx 54rpx;
        box-sizing: border-box;
        background: #f2f5f8;
        border: 4rpx solid #ffffff;
        border-radius: 30rpx 30rpx 0rpx 0rpx;
        overflow: auto;

    }

    .center-box-top .title {
        width: 100%;
        overflow: auto;
        margin-bottom: 36rpx;
    }

    .center-box-top .title .title-left {
        float: left;
        font-size: 32rpx;
        font-family: PingFang SC, PingFang SC-Semibold;
        font-weight: 600;
        text-align: left;
        color: #50506f;
    }

    .center-box-top .title .title-right {
        float: right;
        width: 144rpx;
        height: 50rpx;
        line-height: 50rpx;
        font-size: 24rpx;
        text-align: center;
        background-color: #50506f;
        color: #FFFFFF;
        border-radius: 24rpx;
    }

    .center-box-top .item {
        width: 100%;
        margin-top: 18rpx;
        overflow: auto;
    }

    .center-box-top .item .item-desc {
        float: left;
        font-size: 24rpx;
        font-family: PingFang SC, PingFang SC-Regular;
        font-weight: 400;
        text-align: left;
        color: #666666;
        padding: 20rpx 23rpx 23rpx 30rpx;
        box-sizing: border-box;
        background: #ffffff;
        border: 2rpx solid #d2d2d2;
        border-radius: 30rpx;
    }

    .center-bot {
        position: relative;
        z-index: 22;
        /* position: absolute;
        bottom: 0;
        left: 0; */
    }

    .share-icon {
        width: 31rpx;
        height: 34rpx;
        margin-right: 12rpx;
    }

    .want_go {
        width: 100%;
        padding: 40rpx 0 0 32rpx;
        box-sizing: border-box;
    }

    .want_go .nav {
        width: 100%;
        overflow: auto;
    }

    .want_go .nav .title {
        float: left;
        color: #333333;
        font-weight: bold;
        font-size: 32rpx;
    }

    .want_go .nav .go {
        float: right;
        width: 148rpx;
        height: 60rpx;
        line-height: 60rpx;
        text-align: center;
        border: 2rpx solid #ff5656;
        border-radius: 24rpx;
        color: #ff5656;
        font-size: 24rpx;
    }

    .want_go .wrap1 {
        margin: 30rpx auto 50rpx;
    }

    .want_go .wrap1 .item {
        display: inline-block;
    }

    .want_go .wrap1 .item:not(:last-child) {
        padding-right: 45rpx;
    }

    .want_go .wrap1 .item image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        background-color: #fff;
    }

    .want_go .wrap1 .item .name {
        text-align: center;

        font-size: 28rpx;
        font-family: PingFang SC, PingFang SC-Bold;
        font-weight: 700;
        text-align: center;
        color: #999999;
        margin: 23rpx 0 0;
        height: 40rpx !important;
        width: 120rpx;
    }

    .share-but {
        width: 100%;
        margin-top: 80rpx;
        padding-bottom: 110rpx;
    }

    .equity-user-but {
        width: 670rpx;
        height: 100rpx;
        line-height: 100rpx;
        background: #50506f !important;
        border-radius: 40rpx;
        font-size: 24rpx;
        font-family: PingFang SC, PingFang SC-Regular;
        font-weight: 400;
        text-align: center;
        color: #ffffff !important;
        margin: 0 auto;

    }

    .equity-user-but.gray {
        color: #50506f !important;
        background: #e4e4e4 !important;
        margin-top: 20rpx;
    }

    .download {
        width: 100%;
        text-align: center;
        background: #000000ed;
        color: #fff;
        height: 66rpx;
        line-height: 66rpx;
    }
</style>