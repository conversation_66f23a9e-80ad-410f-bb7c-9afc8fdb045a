<template>
    <view>
        <view class="equity">
            <view class="equity-top">
                <view class="equity-top-left">
                    邀请您获得课程权益
                </view>
                <view class="equity-top-right" v-if="ids.type == 1 && !ids.receiveType">
                    截止时间：{{receive.endtime || ''}}
                </view>
            </view>
            <view class="equity-title">
                <text v-if="detail.id == 8">《Yusi带你听--波格莱里奇肖邦专场导赏》</text>
                <text v-else>{{detail.title || '加载中···'}}</text>
            </view>
            <view class="equity-desc">
                <view class="item">
                    流程说明：
                </view>
                <view class="item">
                    <text v-if="!ids.receiveType">-确认微信头像和昵称（为确保权益正常领取，请使用与客服人员报名时的微信头像和昵称）</text>
                    <!-- <text v-if="ids.receiveType == 6">-为确保权益正常领取，购课手机号需与购课订单中手机号一致</text>
                    <text v-if="ids.receiveType == 4">-为确保权益正常领取，购课激活码仅能使用一次</text> -->
                    <text v-else>-为确保权益正常领取，购课激活码仅能使用一次</text>
                </view>
                <view class="item">
                    -确认完成权益领取
                </view>
                <view class="item blue" @click="goAddService" v-if="receive.site_wechat">
                    -{{receive.site_wechat}}
                </view>
            </view>
            <view class="equity-user">
                <view class="avatar">
                    <view class="avatar-left">
                        头像：
                    </view>
                    <image class="avatar-right"
                        :src="AuthorizedAvatar && avatar?avatar:'../../../static/images/noPictrue.png'" mode=""></image>
                </view>
                <view class="nickname" v-if="AuthorizedAvatar && (AuthorizedNickname || !AuthorizedNickname)">
                    <view class="nickname-left">
                        昵称：
                    </view>
                    <view class="nickname-right">
                        {{AuthorizedNickname && nickname?nickname:'微信用户'}}
                    </view>
                </view>
                <!-- <view class="nickname" v-if="ids.receiveType == 6">
                    <view class="nickname-left">
                        购课手机号：
                    </view>
                    <view class="nickname-right">
                        <input class="xhs-phone" type="text" v-model="xhsPhone" placeholder="请输入购课手机号" maxlength="11" placeholder-style="font-size:24rpx;color:#ccc">
                    </view>
                </view> -->
                <view class="nickname" v-if="ids.receiveType == 4 || ids.receiveType == 6">
                    <view class="nickname-left">
                        课程激活码：
                    </view>
                    <view class="nickname-right">
                        <input class="xhs-phone" type="text" v-model="ddcode" placeholder="激活码仅能用一次"
                            placeholder-style="font-size:24rpx;color:#ccc">
                    </view>
                </view>
                <button class="equity-user-but" v-if="!AuthorizedAvatar && (!AuthorizedNickname || AuthorizedNickname)"
                    open-type="chooseAvatar" @chooseavatar="onChooseAvatar" type="default"
                    style="background-color: #1988CC;color: #FFFFFF;">
                    选择微信头像
                </button>
                <view class="equity-user-but input-box" v-if="AuthorizedAvatar &&!AuthorizedNickname">
                    <input class="input-item" type="nickname" v-model="nickname" @blur="getNickname"
                        @confirm="getNickname" placeholder="修改昵称:微信用户" placeholder-style="color: #FFFFFF;"
                        confirm-type="done">
                </view>
                <view class="equity-user-but" v-if="AuthorizedAvatar && AuthorizedNickname" @click="submitTo"
                    :class=" !ids.receiveType && beOverdue?'grey':''">
                    完成权益领取
                </view>

            </view>
        </view>
        <uni-popup ref="student_popup" type="center" :maskClick="false">
            <view class="cancelPopup">
                <image class="cancel-icon" src="../../../static/images/cancel-icon.png" mode=""
                    @click="closeStudentPopup"></image>
                <view class="cancel-title">
                    {{guidedSpeech}}
                </view>
                <view class="cancel-button" @click="gostudent">
                    去建档
                </view>
            </view>
        </uni-popup>
        <x-authorize @login="getSpecialDetail(ids.id, '', '',true)"></x-authorize>
        <!-- #ifdef MP-WEIXIN -->
        <x-agreement ref="agreement"></x-agreement>
        <!-- #endif -->
    </view>
</template>

<script>
    import {
        uploadImg
    } from '@/utils/upload.js';
    import {
        getSpecialDetail,
        getReceiveInvitationCode,
        specialGetCodeInfo,
        specialExchangeCode,
        getPollster
    } from '@/api/yknowledge.js'
    import {
        postUserEdit,
        getUser
    }
    from '@/api/user.js';
    import {
        yIndex,
    } from '@/api/yuanshi/public';
    import {
        toLogin,
        debounce,
        checkLogin,
        autoAuth,
        authNavigator,
        zxauthNavigator
    } from '@/utils/common.js'

    export default {
        data() {
            return {
                AuthorizedAvatar: false, // 是否授权头像
                AuthorizedNickname: false, // 是否授权昵称
                avatar: '',
                nickname: '',
                ids: {
                    id: 0,
                    code: '',
                    type: 0,
                    receiveType: 0
                },
                detail: {},
                userInfo: {},
                receive: {},
                beOverdue: false, // 是否过期
                config: {},
                xhsPhone: '', //小红书来源手机号 
                ddcode: '', // 抖店来源激活码
                isSupply: true, // 0 false需要补充学员档案 1 true不需要补充学员档案
                finishStudent: false, // 是否完成学员补充
                // #ifdef MP-TOUTIAO
                from: 'bytedance',
                // #endif
                // #ifndef MP-TOUTIAO
                from: 'routine',
                // #endif
                guidedSpeech: ''
            }
        },
        onLoad(option) {
            const {
                id = 0,
                    code = '',
                    type = 0, //类型：1:多人一码 2:一人一码
                    receiveType = 0 //领取类型：4=抖音小店 5=视频小店 6=小红书
            } = option;
            this.ids = option;
            this.getSpecialDetail(this.ids.id, '', '')
            if (this.ids.receiveType) {
                this.getddcode() // 获取抖店来源复制的激活码
            }
        },
        onShow() {
            console.log('finishStudent', this.finishStudent)
            if (this.finishStudent) {
                this.isSupply = true;
                this.submitTo()
            }
        },
        methods: {
            getPollster(type, id) {
                let that = this;
                let param = {
                    type: type,
                    id: id,
                    from: that.from,
                }
                getPollster(param).then(res => {
                        if (res.data.id) {
                            this.guidedSpeech = res.data.guided_speech;
                            if (res.data.user_pollster_status == 0) {
                                this.isSupply = false;
                            }
                        }
                    })
                    .catch(err => {
                        that.$showToast(err.msg || err);
                    });
            },
            getddcode() {
                let that = this;
                uni.getClipboardData({
                    success: function(res) {
                        let text = res.data;
                        if (text || text != '') {
                            uni.showModal({
                                title: '直接使用复制的激活码',
                                content: text,
                                confirmText: '确定',
                                success: function(res) {
                                    if (res.confirm) {
                                        if (text.toLowerCase().includes('卡号'.toLowerCase())) {
                                            // console.log('包含')
                                            const newStr = text.replace('卡号：', "");
                                            that.ddcode = newStr;
                                        } else {
                                            // console.log('不包含')
                                            that.ddcode = text;
                                        }
                                    } else if (res.cancel) {

                                    }
                                }
                            });

                        }

                    }
                });
            },
            openStudentPopup() {
                if (zxauthNavigator()) {
                    this.$refs.student_popup.open();
                }
            },
            closeStudentPopup() {
                this.$refs.student_popup.close();
            },
            gostudent() {
                this.$refs.student_popup.close();
                this.goPages('/pages/user/studentProfile?type=3&id=' + this.ids.id, true)
            },
            goAddService() {
                if (checkLogin()) {
                    this.goPages('/pages/index/addService', false)
                } else {
                    autoAuth();
                }
            },
            initData() {
                let from = '';
                // #ifndef MP-TOUTIAO
                from = 'routine';
                // #endif
                // #ifdef MP-TOUTIAO
                from = 'bytedance';
                // #endif
                yIndex(from).then(res => {
                    this.config = res.data;
                    console.log('config', this.config)
                });
            },
            submitTo() {
                if (checkLogin()) {
                    if (this.ids.receiveType) {
                        // if(this.ids.receiveType == 6){
                        //     if(this.xhsPhone.trim() == '' || !this.xhsPhone){
                        //         return this.$showToast('请输入购课手机号');
                        //     }
                        // }
                        // if(this.ids.receiveType == 4){
                        //     if(this.ddcode.trim() == '' || !this.ddcode){
                        //         return this.$showToast('请输入课程激活码');
                        //     }
                        // }
                        if (this.ddcode.trim() == '' || !this.ddcode) {
                            return this.$showToast('请输入课程激活码');
                        }
                        if (this.ddcode.toLowerCase().includes('卡号'.toLowerCase())) {
                            const newStr = this.ddcode.replace('卡号：', "");
                            this.ddcode = newStr;
                        }
                        if (this.isSupply) {
                            postUserEdit({
                                nickname: this.nickname,
                                avatar: this.avatar
                            }).then(
                                res => {
                                    // console.log('res---',res)
                                    this.getReceiveInvitationCode(this.ids.id, this.ids.code, this.ids.receiveType,
                                        this.ddcode)
                                    // return this.$showToast('上传成功')
                                },
                                error => {
                                    console.log('error---', error)
                                }
                            );
                        } else {
                            this.openStudentPopup()
                        }
                    } else {
                        if (this.beOverdue) {
                            return this.$showToast('领取已截止');
                        }
                        if (this.isSupply) {
                            postUserEdit({
                                nickname: this.nickname,
                                avatar: this.avatar
                            }).then(
                                res => {
                                    // console.log('res---',res)
                                    this.getReceiveInvitationCode(this.ids.id, this.ids.code, this.ids.receiveType,
                                        this.ddcode)
                                    // return this.$showToast('上传成功')
                                },
                                error => {
                                    console.log('error---', error)
                                }
                            );
                        } else {
                            this.openStudentPopup()
                        }
                    }
                } else {
                    autoAuth();
                }
            },
            getUserInfo: function() {
                let that = this;
                getUser().then(res => {
                    this.userInfo = res.data;
                    if (this.userInfo.avatar == '' || !this.userInfo.avatar || this.userInfo.avatar ==
                        'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132' ||
                        this.userInfo.avatar ==
                        'https://thirdwx.qlogo.cn/mmopen/vi_32/Z22RuflpLpHqnZy0icITdAuMrwXdaMyibm58t2dibBOH7a5sWW8lVew8OWfia3oJwKT56dHYSgkIWiaicnZ5kRmenibCw/132'
                    ) {
                        this.AuthorizedAvatar = false
                    } else {
                        this.AuthorizedAvatar = true
                        this.avatar = this.userInfo.avatar;
                    }
                    if (this.userInfo.nickname.trim() == '' || !this.userInfo.nickname || this.userInfo
                        .nickname == '微信用户') {
                        this.AuthorizedNickname = false
                    } else {
                        this.AuthorizedNickname = true
                        this.nickname = this.userInfo.nickname;
                    }
                    console.log('this.userInfo', this.userInfo)
                });
            },

            getSpecialDetail(id, gift_uid, gift_order_id, reset = false) {
                getSpecialDetail({
                    id,
                    gift_uid,
                    gift_order_id
                }).then(res => {
                    console.log('课程详情', res)
                    if (this.ids.code) {
                        this.specialGetCodeInfo(this.ids.id, this.ids.code)
                    }
                    this.getUserInfo()
                    this.initData()
                    this.getPollster(3, this.ids.id)
                    this.detail = res.data;
                }).catch(err => {
                    // setTimeout(() => {
                    //     this.$navigator(-1)
                    // }, 500)
                })
            },
            getReceiveInvitationCode(id, code, type = 0, ddcode) {
                let that = this;
                if (!type) {
                    let data = {
                        id: id,
                        invitation_code: code,
                        type: type
                    }
                    getReceiveInvitationCode(data).then(res => {
                        console.log('领取邀请码res', res)
                        that.$showToast(res.msg)
                        setTimeout(function() {
                            that.goPages('/pages/yknowledge/course/detail?id=0&sid=' + that.ids.id,
                                false, 'redirectTo')
                        }, 1500);
                    }).catch(err => {
                        console.log('领取邀请码err', err)
                        // setTimeout(function() {
                        //     that.goPages('/pages/yknowledge/course/detail?id=0&sid=' + that.ids.id ,false,'redirectTo')
                        // }, 1500);
                    })
                } else {
                    let data = {
                        id: id,
                        activation_code: ddcode,
                        type: type
                    }
                    specialExchangeCode(data).then(res => {
                        console.log('领取邀请码res', res)
                        that.$showToast(res.msg)
                        setTimeout(function() {
                            that.goPages('/pages/yknowledge/course/detail?id=0&sid=' + that.ids.id,
                                false, 'redirectTo')
                        }, 1500);
                    }).catch(err => {
                        console.log('领取邀请码err', err)
                        // setTimeout(function() {
                        //     that.goPages('/pages/yknowledge/course/detail?id=0&sid=' + that.ids.id ,false,'redirectTo')
                        // }, 1500);
                    })
                }
            },

            specialGetCodeInfo(id, code) {
                let that = this;
                let data = {
                    id: id,
                    invitation_code: code
                }
                specialGetCodeInfo(data).then(res => {
                    console.log('获取分享截止时间', res)
                    that.receive = res.data;
                    that.receive.endtime = that.format(that.receive.end_time * 1000)
                    var timestamp = Date.parse(new Date()) / 1000;
                    if (timestamp >= that.receive.end_time) {
                        that.beOverdue = true;
                        return;
                    }
                }).catch(err => {

                })
            },
            goPages(path, type, pathType) {
                if (type) {
                    this.$authNavigator(path);
                } else {
                    this.$navigator(path, pathType);
                }
            },
            getNickname(e) {
                console.log('昵称', e.detail.value.trim())
                if (e.detail.value.trim() == '微信用户' || e.detail.value.trim() == '') {
                    return this.$showToast('输入昵称不规范')
                } else {
                    this.nickname = e.detail.value
                    this.AuthorizedNickname = true;
                }
            },
            async onChooseAvatar(e) {
                if (checkLogin()) {
                    let avatar = '';
                    this.AuthorizedAvatar = true;
                    this.avatar = avatar = e.detail.avatarUrl;
                    // console.log('头像',this.avatar,avatar)
                    let res = await uploadImg(avatar);
                    this.avatar = res[0];
                    console.log('服务器头像地址', this.avatar, avatar)
                } else {
                    autoAuth();
                }
            },
            add0(m) {
                return m < 10 ? '0' + m : m
            },
            format(shijianchuo) {
                //shijianchuo是整数，否则要parseInt转换
                var time = new Date(shijianchuo);
                var y = time.getFullYear();
                var m = time.getMonth() + 1;
                var d = time.getDate();
                var h = time.getHours();
                var mm = time.getMinutes();
                var s = time.getSeconds();
                return y + '-' + this.add0(m) + '-' + this.add0(d) + ' ' + this.add0(h) + ':' + this.add0(mm) + ':' +
                    this.add0(s);
            },
        }
    }
</script>

<style scoped lang="scss">
    page {
        background-color: #fff;
    }

    .cancelPopup {
        position: relative;
        width: 622rpx;
        min-height: 366rpx;
        max-height: 732rpx;
        border: 4rpx solid #50506f;
        border-radius: 30rpx;
        background-color: #fff;
        box-sizing: border-box;
        padding: 92rpx 80rpx 0;
        overflow: auto;


        .cancel-icon {
            position: absolute;
            right: 12rpx;
            top: 12rpx;
            width: 56rpx;
            height: 56rpx;
        }

        .cancel-title {
            width: 100%;
            font-size: 28rpx;
            font-family: PingFang SC, PingFang SC-Bold;
            font-weight: 700;
            text-align: center;
            color: #50506f;
            line-height: 38rpx;
            min-height: 114rpx;
            max-height: 360rpx;
            word-break: break-all;
            overflow-x: scroll;
        }

        .cancel-button {
            width: 248rpx;
            height: 64rpx;
            line-height: 64rpx;
            text-align: center;
            background: #50506f;
            border-radius: 32rpx;
            font-size: 24rpx;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: center;
            color: #ffffff;
            margin: 38rpx auto;
        }
    }



    .equity {
        width: 100%;
        height: 100vh;
        padding: 50rpx 30rpx;
        box-sizing: border-box;
        background-color: #fff;

        .equity-user {
            width: 100%;

            .equity-user-but {
                width: 360rpx;
                height: 80rpx;
                line-height: 80rpx;
                font-size: 26rpx;
                text-align: center;
                margin: 150rpx auto 0;
                background-color: #1988CC;
                color: #FFFFFF;
                border-radius: 10rpx;

                &.grey {
                    background-color: #999999;
                }

                &.input-box {
                    position: relative;

                    .input-item {
                        position: absolute;
                        left: 0;
                        right: 0;
                        top: 0;
                        bottom: 0;
                        color: #fff;
                        margin-top: 18rpx;
                    }
                }
            }

            .avatar,
            .nickname {
                width: 100%;
                overflow: auto;

                .avatar-left,
                .nickname-left {
                    float: left;
                    width: 250rpx;
                }

                .avatar-right {
                    float: left;
                    width: 200rpx;
                    height: 200rpx;
                    border-radius: 8rpx;
                }
            }

            .nickname {
                margin-top: 40rpx;

                .nickname-right {
                    font-size: 26rpx;

                    .xhs-phone {
                        width: 250rpx;
                        height: 50rpx;
                        line-height: 50rpx;
                        border: 2rpx solid #eaeaea;
                        border-radius: 8rpx;
                        padding-left: 14rpx;
                        box-sizing: border-box;
                    }
                }
            }

        }

        .equity-desc {
            width: 100%;
            overflow: auto;
            font-size: 26rpx;
            color: #999;

            .item {
                width: 100%;
                margin-bottom: 30rpx;

                &.blue {
                    color: #1988CC;
                }
            }

        }

        .equity-title {
            width: 100%;
            font-size: 38rpx;
            font-weight: bold;
            color: #000;
            text-align: center;
            margin: 40rpx auto;
        }

        .equity-top {
            width: 100%;
            overflow: auto;
            font-size: 26rpx;

            .equity-top-left {
                float: left;
                color: #000;
            }

            .equity-top-right {
                float: right;
                color: #999;
                font-size: 24rpx;
            }
        }
    }
</style>