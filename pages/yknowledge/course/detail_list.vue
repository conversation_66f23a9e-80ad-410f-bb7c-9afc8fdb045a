<template>
    <view>
        <xSwiper id="lzx" :detail="detail" :poster="poster" :isBuy="isBuy" @send="getSonValue"
            @currentTime="currentTime" :iswindow="iswindow" @direction="getdirection" :setvideotime="setvideotime"
            :collection="ids.sid?true:false" :resetSpeed="resetSpeed" @isresetSpeed="isresetSpeed" :ispause="ispause"
            @onPause="onPause" @onPlay="onPlay" @getended="getended" />
        <view class="common_wrap">
            <view class="w_top flex flex_between">
                <view class="t_l">
                    <view class="title" :class="detail.type == 1?'active':''">
                        <text v-if="detail.trial_info.is_trial && detail.trial_info.trial_file_type != 0" class="shiting">仅试{{detail.trial_info.trial_file_type==1?'听':'看'}}</text>
                        {{detail.title || ''}}
                    </view>
                    <view class="detail-title" v-if="detail.type == 1">
                        <block v-for="(item, index) in detail.subject_name" :key="index">
                            <text class="detail-title-txt">{{item}}</text>
                        </block>
                    </view>
                    <view class="detail-time" v-if="detail.type == 1">
                        {{detail.add_time}}
                    </view>
                </view>
            </view>
            <view v-if="detail.type != 1">
                <xMpBtnShare :isZan="detail.isCollection" :collectcount="detail.collect_count" @zanclick="collect" />
            </view>
            <view class="audio" v-if="detail.type === 2 || (detail.trial_info.is_trial === 1 && detail.trial_info.trial_file_type === 1)">
                <xPlay ref='xPlay' :music="detail.trial_info.is_trial?detail.trial_link:detail.link" :title="detail.link_title" :img="poster"
                    :setaudiotime="setaudiotime" @yp_getended="yp_getended" @currentTime_audio="currentTime_audio" 
                    @onPause="onPause"  @onStop="onStop" @onPlay="onPlay" :isbackgroundAudio="true"/>
            </view>
            <xDetailShowMore v-if="detail.type != 1" :html="detail.detail" @settime="settime" />
        </view>
        <view class="conter" v-if="detail.type == 1">
            <mp-html :content="detail.content" />
        </view>

        <lhNavScroll v-if="ids.sid != 0 && courseList.length" color="#FC5656" :sid="ids.sid" :cid="ids.id"
            :items="courseList" :height="navHeight" :scroll="true" @currentItem="currentItem" @itempause="itempause"
            :ispause="ispause"></lhNavScroll>

        <xRecom v-if="detail.similar_recommendations.length" :title="detail.recommend_navigator_title"
            :arr="detail.similar_recommendations" />
        <view class="common_wrap" style="min-height: 1000rpx; margin-top: 26rpx;" id="tabLink">
            <view class="flex flex_between tab_title">
                <view class="title">评论 ( {{detail.comments || 0}} )</view>
                <view class="btn" @click="goCommentSubmit(true)">去评论
                </view>
            </view>
            <view class="tab flex">
                <view class="item" :class="{active:index===current}" v-for="(item,index) in tabList" :key="index"
                    @click="tabClick(item,index)">
                    {{item.name}}
                </view>
            </view>
            <view class="message_wrap" v-if="messageInfo.length">
                <xComment ref="xComment" :arr="messageInfo" @showInput="showInput" pageType="list" :isvideo="false"
                    :isgohome="true" @send="getSonCommentstatus" @sendAudio="sendAudio" />
            </view>
            <view class="loadmore">
                <u-loadmore :status="loadMoreStatus" />
            </view>
        </view>
        <xChat :adjustPosition="adjustPosition" :placeholder="placeholder" :inputShow="inputShow" :uid="uid"
            @send="submitComment" desc="comment">
        </xChat>
        <view class="active_footer relative">
            <view class="absolute flex flex_align_center flex_between">
                <view class="flex flex_l flex_align_center">
                    <view class="item" @click="goPages('/pages/tabBar/index/index',false,'switchTab')">
                        <view>
                            <image src="@/static/images/community/home.png" mode="">
                            </image>
                        </view>
                        <view class="font_size20">首页</view>
                    </view>
                    <view class="item" @click="showMoreCourse">
                        <view>
                            <image src="@/static/images/zsff/more-course.png" mode="">
                            </image>
                        </view>
                        <view class="font_size20">更多课程</view>
                    </view>

                    <view class="item" @click="collect" v-if="detail.type == 1">
                        <view>
                            <image src="@/static/images/yuanshi/love1.png" mode="widthFix" v-if="detail.isCollection"></image>
                            <image src="@/static/images/yuanshi/love1_1.png" mode="widthFix" v-else></image>
                        </view>
                        <view class="font_size20">{{detail.isCollection?'已关注':'关注'}}</view>
                    </view>
                    <button v-if="detail.type == 1" class="item" type="default" open-type="share"
                        style="background-color: #f7f8fa;">
                        <view>
                            <image src="@/static/images/yuanshi/share1.png" mode="widthFix"></image>
                        </view>
                        <view class="font_size20">分享</view>
                    </button>
                </view>
                <view class="btn" :class="detail.type== 1?'':'active'">
                    <view @click="goCommentSubmit">发表评论</view>
                </view>
            </view>
        </view>
        <x-authorize @login="init()"></x-authorize>
        <view @click="bakvideo" v-show="scrollTop >= 234"
            style="position: fixed; right: 85rpx; bottom: 300rpx; width: 100rpx; height: 100rpx;line-height: 100rpx;text-align: center;font-size: 26rpx; background-color: #007AFF; color: #fff; border-radius: 50%; opacity: .6;">
            ↑TOP</view>
        <uni-popup ref="subscribe" type="center" :animation="true" :maskClick="false">
            <view class="balance-box">
                <image class="balance-box-close" src="../../../static/images/yuanshi/close1.png" mode=""
                    @click="closeSubscribe"></image>
                <view class="balance-box-title">
                    欢迎来到Yusi音乐审美养成课堂
                </view>
                <view class="balance-box-desc">
                    提示：本课程仅针对订阅用户开放。
                </view>
                <view class="balance-box-btn">
                    <view class="balance-box-btn-left" @click="closeSubscribe">
                        取消
                    </view>
                    <view class="balance-box-btn-right"
                        @click="goPages('/pages/yknowledge/course/detail?sid=' + detail.subscribe_special_id,false,'redirectTo')">
                        去订阅
                    </view>
                </view>
            </view>
        </uni-popup>

        <uni-popup ref="iospay_popup" type="center" :maskClick="false">
            <view class="iospayPopup">
                <image class="iospayPopup-colse" src="../../../static/images/yuanshi/fail.png" mode=""
                    @click="colseIosPop"></image>
                <view class="iospayPopup-title">
                    IOS用户暂不支持此功能。听课请添加客服咨询。
                </view>
                <view class="iospayPopup-button" @click="closeIosPopup">
                    咨询客服<text v-if="countdown != 0">({{countdown}}s)</text>
                </view>
            </view>
        </uni-popup>
        <Payment v-model="pay" ref="Payment" :other="true" :openstudent="false" :paymoney="ff_detail.money"
            :courseId="ff_detail.id" :deductionsPrice="ff_detail.deductions_price" :types="payType" @nowPay="nowPay"
            @getmark="getmark" :balance="userInfo.now_money" :opencoupon="opencoupon" @couponMoney="couponMoney">
        </Payment>
    </view>
</template>
<script>
    // #ifdef H5
    import {
        isWeixin
    } from '@/utils/validate.js';
    import {
        pay
    } from '@/utils/wechat/pay.js';
    import {
        openShareAll
    } from '@/utils/wechat/share.js';
    const _isWeixin = isWeixin();
    // #endif
    // #ifdef MP
    // const _isWeixin = true;
    // #endif

    import {
        mapGetters
    } from 'vuex';
    import Payment from '@/components/PaymentUser';
    import lhNavScroll from '@/components/lh-nav-scroll/lh-nav-scroll.vue';
    import xSwiper from '@/components/zsff/swiper.vue';
    import xDetailShowMore from '@/components/zsff/x-detail-show-more.vue';
    import xChat from '@/components/x-chat/x-chat';
    import xComment from '@/components/x-comment/x-comment/x-comment2.vue';
    // #ifdef H5
    import xPlay from '@/components/x-play/x-play.vue';
    // #endif
    // #ifndef H5
    import xPlay from '@/components/x-play/x-playBg.vue';
    // #endif
    import xRecom from '@/components/zsff/x-recom.vue';
    import xMpBtnShare from '@/components/zsff/x-mp-btn-share.vue';
    import storage from '@/utils/storage.js';
    import {
        uniSelectorQueryInfo
    } from '@/utils/uni_api.js';
    import {
        SHARE_ID,
        VUE_APP_WS_URL,
        OPEN_IOS_PAYMENT,
    } from '@/config.js';
    import {
        getTemlIds
    }
    from '@/api/user.js';
    import {
        subscribe
    } from '@/utils/SubscribeMessage.js';
    import {
        getSpecialDetail,
        getSpecialCourseListDetail,
        getSpecialCommentAdd,
        getSpecialMessage,
        getSpecialJoinLearning,
        getSpecialCollect,
        getSpecialCourseCatalogList, // 获取目录（升级版）

        upSpecialOrderComputed,
        getSpecialOrderCreate
    } from '@/api/yknowledge.js'
    import {
        debounce,
        checkLogin,
        autoAuth,
        initTime
    } from '@/utils/common.js'
    import xCommentChat from '@/mixins/xCommentChat.js';
    import eventBus from '@/utils/eventBus.js';

    export default {
        components: {
            xSwiper,
            xChat,
            xPlay,
            xComment,
            xDetailShowMore,
            xRecom,
            xMpBtnShare,
            lhNavScroll,
            Payment
        },
        mixins: [xCommentChat],

        computed: {
            ...mapGetters(['userInfo', 'isPopPlay']),
            loadMoreStatus() {
                const {
                    more
                } = this.page;
                return more ? 'loadmore' : 'nomore'
            }
        },
        data() {
            return {
                resetSpeed: false,
                navHeight: 70,
                courseList: [],
                message_type: 0, // 0评论 1作业
                iswindow: true, // 是否打开小窗模式 微信：进入上一页或下一页触发；抖音：上划进入后台触发
                isMute: this.$store.state.userInfo.is_mute, // 当前用户是否禁言

                videoCtx: null,
                tmplIds: [],
                oindex: false, // 是否重读评论
                detail: {
                    image: [],
                    video: [],
                    similar_recommendations: []
                },
                tabList: [{
                    name: '全部',
                    id: 0
                }, {
                    name: '热点',
                    id: 1
                }, {
                    name: '作业',
                    id: 2
                }, {
                    name: '精华',
                    id: 2
                }],
                cloumnNavlist: [{
                    'title': '详情'
                }, {
                    'title': '评论'
                }],
                ids: {
                    source: '',
                    sid: 0,
                    id: 0
                },
                uid: -1,
                placeholder: '',
                inputShow: false,
                footerHidden: false,
                footerOpacity: 0,
                current: 0,
                requestLoading: false,
                page: {
                    page: 1,
                    limit: 5,
                    more: true
                },
                messageInfo: [],
                req: getSpecialCommentAdd,
                scrollTop: 0,
                poster: '',
                isBuy: true,
                tabLinkInfo: {},
                adjustPosition: false,
                setvideotime: 0,
                setaudiotime:0,
                realVideoTime: 0, // 视频实时进度
                ispause: false, //暂停视频

                coupon: {},
                opencoupon: true, // 是否使用优惠券
                openIosPayment: OPEN_IOS_PAYMENT,
                platform: '',
                countdown: '',
                s: '',
                timer: null, //重复执行
                setT: null,
                cleartime: 0,
                pay: false,
                ff_detail: {}, // 付费课程
                pay_type_num: 2, //默认2购买 1送朋友

                // #ifndef MP-TOUTIAO
                payType: ['weixin'],
                // #endif
                // #ifdef MP-WEIXIN
                from: 'routine',
                // #endif
                // #ifdef MP-TOUTIAO
                from: 'bytedance',
                payType: ['bytedance'],
                // #endif
                // #ifdef H5
                from: _isWeixin ? 'weixin' : 'weixinh5',
                // #endif
                mark: '',

            };
        },
        methods: {
            onStop(e){
                this.ispause = true;
            },
            onPause(e) {
                this.ispause = true;
            },
            onPlay(e) {
                this.ispause = false;
            },
            yp_getended(e) {
                let that = this;
                this.ispause = true;
                console.log('that.detail.is_pay',that.detail.is_pay)
                if (that.detail.is_pay) {
                    uni.showModal({
                        title: "温馨提示",
                        cancelColor: '#787b7b',
                        confirmColor: '#e93323',
                        cancelText: '取消',
                        confirmText: '确定',
                        content: '更多精彩内容，去订阅？',
                        success(res) {
                            if (res.confirm) {
                                console.log("确定");
                                that.paymentTap(2)
                            }
                        },
                        fail(res) {
                            console.log(`showModal调用失败`);
                        },
                    });
                }else {
                    if(that.detail.is_trial){
                        uni.showModal({
                            title: "温馨提示",
                            cancelColor: '#787b7b',
                            confirmColor: '#e93323',
                            cancelText: '取消',
                            confirmText: '确定',
                            content: '更多精彩内容，去订阅？',
                            success(res) {
                                if (res.confirm) {
                                    console.log("确定");
                                    let filteredList = that.detail.special_list.find(item => item.id !== that.ids.id);
                                    // console.log('收费对象',filteredList)
                                    that.$navigator(`/pages/yknowledge/course/detail?sid=${filteredList.id}&id=0`,
                                        'redirectTo');
                                }
                            },
                            fail(res) {
                                console.log(`showModal调用失败`);
                            },
                        });
                    }
                }
            },
            getended(e) {
                // console.log('播放完成')
                let that = this;
                if (that.detail.is_pay) {
                    uni.showModal({
                        title: "温馨提示",
                        cancelColor: '#787b7b',
                        confirmColor: '#e93323',
                        cancelText: '取消',
                        confirmText: '确定',
                        content: '更多精彩内容，去订阅？',
                        success(res) {
                            if (res.confirm) {
                                console.log("确定");
                                that.paymentTap(2)
                            }
                        },
                        fail(res) {
                            console.log(`showModal调用失败`);
                        },
                    });
                }else {
                    uni.showModal({
                        title: "温馨提示",
                        cancelColor: '#787b7b',
                        confirmColor: '#e93323',
                        cancelText: '取消',
                        confirmText: '确定',
                        content: '更多精彩内容，去订阅？',
                        success(res) {
                            if (res.confirm) {
                                console.log("确定");
                                let filteredList = that.detail.special_list.find(item => item.id !== that.ids.id);
                                // console.log('收费对象',filteredList)
                                that.$navigator(`/pages/yknowledge/course/detail?sid=${filteredList.id}&id=0`,
                                    'redirectTo');
                            }
                        },
                        fail(res) {
                            console.log(`showModal调用失败`);
                        },
                    });
                }
            },
            paymentTap(type) {
                if (checkLogin()) {
                    if (this.openIosPayment && this.platform == 'ios') {
                        this.s = 6;
                        this.cleartime = 0;
                        this.$refs.iospay_popup.open();
                        this.setT = setTimeout(() => {
                            if (this.cleartime === 1) {
                                clearTimeout(this.setT);
                                this.setT = null
                            } else {
                                this.closeIosPopup()
                            }
                        }, 5500);
                        return
                    } else {
                        this.$refs.Payment.getUser();
                        var that = this;
                        this.pay_type_num = type;
                        if (this.paymentType && _isWeixin) {
                            // #ifndef MP-TOUTIAO
                            this.nowPay('weixin', pay_type_num);
                            // #endif
                            // #ifdef MP-TOUTIAO
                            this.nowPay('bytedance');

                            // #endif
                        } else {
                            this.pay = true;
                        }
                    }
                } else {
                    autoAuth();
                }
            },
            colseIosPop() {
                clearTimeout(this.setT);
                this.$refs.iospay_popup.close();
            },
            closeIosPopup() {
                clearInterval(this.timer);
                clearTimeout(this.setT);
                this.$refs.iospay_popup.close();
                this.$navigator('/pages/index/addService');
            },
            // 每次执行减一
            timeCount() {
                --this.s;
                if (this.s < 0) {
                    this.s = 0;
                }
                this.countdown = this.s
            },
            getmark(e) {
                this.mark = e;
                // console.log('传值备注111', this.mark)
            },
            couponMoney(item) {
                this.coupon = item;
                upSpecialOrderComputed({
                    id: this.ff_detail.id,
                    couponId: this.coupon.id
                }).then(res => {
                    // console.log('实时计算金额',res.data)
                    this.ff_detail.money = res.data.pay_price;
                    this.ff_detail.deductions_price = res.data.deductions_price;
                    if (this.ff_detail.money <= 0) {
                        this.ff_detail.money = 0
                    }
                })
            },
            async nowPay(type, types) {
                let param = {
                    pay_type_num: this.pay_type_num,
                    payType: type,
                    from: this.from,
                    id: this.ids.sid,
                    mark: this.mark,
                    link_invite_id: 0,
                    couponId: 0
                }
                if (this.coupon.id) {
                    param.couponId = this.coupon.id; // 优惠券id
                }
                getSpecialOrderCreate(param)
                    .then(res => {
                        const {
                            data
                        } = res, that = this;
                        // console.log(data)
                        switch (data.status) {
                            case "PAY_ERROR":
                            case 'ORDER_EXIST':
                            case 'ORDER_ERROR':
                                this.$showToast(res.msg);
                                break;
                            case 'BYTEDANCE_PAY':
                            case 'BYTEDANCE_ORDER':
                                // #ifdef MP-TOUTIAO
                                let tt_res = res.data.result;
                                tt.pay({
                                    orderInfo: {
                                        order_id: tt_res.orderId,
                                        order_token: tt_res.orderToken,
                                    },
                                    service: 5,
                                    _debug: 1,
                                    success(res) {
                                        console.log('字节支付成功', res)
                                        if (res.code == 0) {
                                            that.paySucess(data.result.orderId)
                                        }
                                    },
                                    fail(res) {
                                        console.log('字节支付失败', res)
                                        that.$showToast('取消支付');
                                    },
                                });
                                // #endif
                                break;
                            case 'WECHAT_PAY':
                                // #ifdef H5
                                pay(data.result.jsConfig)
                                    .finally(() => {
                                        that.$showToast('支付成功');
                                        // that.resetSpecialCourseList()
                                        that.detail.is_pay = 0; //支付完成，不再需要支付
                                        that.$navigator(
                                            `/pages/yknowledge/course/detail?sid=${that.ids.sid}&id=0`,
                                            'redirectTo');
                                    })
                                    .catch(function() {
                                        that.ff_detail.is_pay = 0;
                                        that.$showToast('支付失败');
                                    });

                                // #endif
                                // #ifdef MP-WEIXIN
                                const jsConfig = data.result.jsConfig;
                                console.log('获取支付参数', jsConfig)
                                // requestOrderPayment
                                wx.requestPayment({
                                    timeStamp: jsConfig.timestamp,
                                    nonceStr: jsConfig.nonceStr,
                                    package: jsConfig.package,
                                    signType: jsConfig.signType,
                                    paySign: jsConfig.paySign,
                                    success: function(res) {
                                        console.log('支付成功', res)
                                        that.paySucess(data.result.orderId)
                                    },
                                    fail: function(e) {
                                        console.log('支付失败1', e)
                                        that.$showToast('取消支付');
                                    },
                                    complete: function(e) {
                                        //关闭当前页面跳转至订单状态
                                        console.log('支付失败2', e)
                                        if (res.errMsg == 'requestPayment:cancel') return that
                                            .$showToast('取消支付', e);;
                                    }
                                });
                                // #endif
                                break;
                            case 'WECHAT_H5_PAY':
                                that.$navigator(
                                    `/pages/yknowledge/course/detail?sid=${that.ids.sid}&payOrderId=${data.result.orderId}`,
                                    'redirectTo');
                                setTimeout(() => {
                                    location.href = data.result.jsConfig.mweb_url;
                                }, 100);
                                break;
                            case 'SUCCESS':
                                this.paySucess(data.result.orderId)
                                break;
                            case 'ZHIFUBAO_PAY':
                                this.$showToast('zhifubao');
                                break;
                        }

                    })
                    .catch(err => {
                        this.$showToast(err.msg || err);
                    });
            },
            paySucess(orderId) {
                this.$showToast('支付成功');
                // 立即购买
                if (this.pay_type_num === 2) {
                    this.detail.is_pay = 0; //支付完成，不再需要支付
                    this.$navigator(`/pages/yknowledge/course/detail?sid=${this.ids.sid}&id=0`,
                        'redirectTo');
                }
            },

            getSpecialCourseList: debounce(function() {
                // if (!this.page1.more || this.requestLoading1) return;
                // this.requestLoading1 = true;

                // getSpecialCourseCatalogList
                // getSpecialCourseList
                getSpecialCourseCatalogList({
                    id: this.ids.sid,
                }).then(res => {
                    if (res.data.length) {
                        // console.log('获取升级版目录', res.data.length)
                        if (res.data[0].title == '不设置目录' || !res.data[0].title) {
                            this.courseList = res.data[0].course_list;
                        } else {
                            let datas = res.data;
                            this.courseList = [];
                            for (let i = 0; i < datas.length; i++) {
                                for (let j = 0; j < datas[i].course_list.length; j++) {
                                    this.courseList.push(datas[i].course_list[j])
                                }
                            }
                        }

                        for (let i = 0; i < this.courseList.length; i++) {
                            this.courseList[i].selected = false;
                            if (this.courseList[i].id == this.ids.id) {
                                this.courseList[i].selected = true;
                            }
                        }
                        // console.log('获取目录----',this.courseList)
                    }
                }).catch(err => {
                    this.requestLoading1 = false;
                });
            }, 200, true),

            settime(e) {
                // 如果试听内容，并且当期那跳转时间超过试听。提示“当前是试听版本，听完整版请到付费节目购买”
                // if(){}
                
                if(this.detail.type == 2){
                    this.setaudiotime = Number(e);
                }
                if(this.detail.type == 3){
                    this.setvideotime = Number(e);
                }
                
                // console.log('视频跳转进度（秒）',this.setvideotime)
            },

            getdirection: function(res) {
                console.log('监听全屏', res)
                // if(res == 'horizontal'){
                //     wx.setPageOrientation({ orientation: 'landscape' })
                // }else if(res == 'vertical'){
                //     wx.setPageOrientation({ orientation: 'portrait' })
                // }
                // this.$showToast(res=='horizontal'?'开启全屏':'关闭全屏')
            },
            closeSubscribe() {
                this.goPages('/pages/tabBar/index/index', false, 'switchTab')
                this.$refs.subscribe.close()
            },
            copenSubscribe() {
                this.$refs.subscribe.open()
            },
            getSonValue(res) {
                this.videoCtx = res;
            },
            currentTime(e) {
                // console.log('视频进度',e)
                // this.ispause = false;
                this.realVideoTime = e;
                storage.set('videoPlan' + this.ids.id, this.realVideoTime)
                // this.$store.commit('POPPLAY_TRUE'); // 小窗播放状态true
            },
            currentTime_audio(e) {
                this.realVideoTime = e;
                storage.set('videoPlan' + this.ids.id, this.realVideoTime)
            },
            getSonCommentstatus(res) {
                if (res) {
                    this.videoCtx.pause();
                } else {
                    this.videoCtx.play();
                }
            },
            sendAudio(res) {
                if (res) {
                    this.videoCtx.pause();
                }
            },
            bakvideo() {
                uni.pageScrollTo({
                    selector: '#lzx',
                    scrollTop: 0,
                    duration: 300
                });
            },
            //收藏和取消收藏
            collect() {
                const {
                    sid: special_id,
                    id: source_id
                } = this.ids;
                getSpecialCollect({
                    special_id,
                    source_id,
                }).then(res => {
                    this.$showToast(this.detail.isCollection ? '取消收藏成功' : '收藏成功')
                    this.detail.isCollection = this.detail.isCollection ? false : 1;
                    if (this.detail.isCollection) {
                        this.detail.collect_count += 1;
                    } else {
                        this.detail.collect_count -= 1;
                        if (this.detail.collect_count < 0) {
                            this.detail.collect_count = 0;
                        }
                    }
                    // console.log('this.detail.collect_count',this.detail.collect_count)
                })
            },
            tabClick(item, index) {
                this.current = index;
                const {
                    top,
                    height
                } = this.tabLinkInfo;
                // console.log(this.scrollTop, top)
                if (this.scrollTop < top) {
                    // #ifdef MP
                    uni.pageScrollTo({
                        scrollTop: top,
                        duration: 300
                    });
                    // #endif
                    // #ifdef H5
                    uni.pageScrollTo({
                        scrollTop: top + 44,
                        duration: 300
                    });
                    // #endif
                }
                console.log('current---', this.current)
                if (this.current == 2) {
                    this.message_type = 1;
                } else {
                    this.message_type = 0;
                }
                this.getSpecialComment(true)
            },
            
            goCommentSubmit(status) {
                let that = this;
                if (this.isMute === 1) {
                    return this.$showToast('已被禁言');
                }
                // 未购买的试听用户不允评论 
                if(that.detail.trial_info.is_trial){
                    if(that.detail.is_pay){
                        uni.showModal({
                            title: "温馨提示",
                            cancelColor: '#787b7b',
                            confirmColor: '#e93323',
                            cancelText: '取消',
                            confirmText: '确定',
                            content: '更多精彩内容，去订阅？',
                            success(res) {
                                if (res.confirm) {
                                    console.log("确定");
                                    that.paymentTap(2)
                                }
                            },
                            fail(res) {
                                console.log(`showModal调用失败`);
                            },
                        });
                        return
                    }else {
                        uni.showModal({
                            title: "温馨提示",
                            cancelColor: '#787b7b',
                            confirmColor: '#e93323',
                            cancelText: '取消',
                            confirmText: '确定',
                            content: '更多精彩内容，去订阅？',
                            success(res) {
                                if (res.confirm) {
                                    console.log("确定");
                                    let filteredList = that.detail.special_list.find(item => item.id !== that.ids.id);
                                    // console.log('收费对象',filteredList)
                                    that.$navigator(`/pages/yknowledge/course/detail?sid=${filteredList.id}&id=0`,
                                        'redirectTo');
                                }
                            },
                            fail(res) {
                                console.log(`showModal调用失败`);
                            },
                        });
                        return
                    }
                }
                
                if (status == true) {
                    // #ifdef MP-WEIXIN
                    this.goApply()
                    // #endif
                }
                this.oindex = false;
                const {
                    sid,
                    id
                } = this.ids;
                this.goPages('/pages/yknowledge/comment/submit?id=' + id + '&sid=' + sid, true)
            },
            goApply() {
                let that = this;
                getTemlIds().then(res => {
                    // if (res.data) this.$storage.set(SUBSCRIBE_MESSAGE, JSON.stringify(res.data));
                    let tmplIds = res.data;
                    // that.tmplIds[0] = tmplIds.activity_canceled?tmplIds.activity_canceled:'';
                    // that.tmplIds[1] = tmplIds.final_payment?tmplIds.final_payment:'';
                    that.tmplIds[0] = tmplIds.message_essence || '';
                    // that.tmplIds[1] = tmplIds.pending_payent?tmplIds.pending_payent:'';
                    // that.tmplIds[2] = tmplIds.upcoming_activitys?tmplIds.upcoming_activitys:'';
                    // console.log('that.tmplIds', that.tmplIds)
                    uni.getSetting({
                        withSubscriptions: true,
                        success(res) {
                            // console.log(res.authSetting)
                            // console.log(res.subscriptionsSetting.mainSwitch)
                            if (res.authSetting['scope.subscribeMessage']) { //用户点击了“总是保持以上，不再询问”
                                uni.openSetting({ // 打开设置页
                                    success(res) {
                                        // console.log(res.authSetting) 
                                    }
                                });
                            } else { // 用户没有点击“总是保持以上，不再询问”则每次都会调起订阅消息
                                // console.log('订阅消息---',that.tmplIds)
                                subscribe(that.tmplIds).then(() => {})
                            }
                        }
                    })
                });
            },
            goPages(path, type, pathType) {
                if (type) {
                    this.$authNavigator(path);
                } else {
                    this.$navigator(path, pathType);
                }
            },
            showMoreCourse() {
                const {
                    special_list = [], id = 0
                } = this.detail;
                if (this.ids.source === 'detail') {
                    this.$navigator(1)
                } else {
                    if (special_list.length === 1) {
                        this.$navigator(`/pages/yknowledge/course/detail?sid=${this.ids.sid}&id=0`);
                    } else {
                        if (special_list.length > 1) {
                            this.$store.commit('UPDATE_SOURCE_SEARCH_ID', id)
                        }
                        this.goPages('/pages/tabBar/course/index', false, 'switchTab')
                    }
                }
            },
            showInput(info, placeholder) {
                this.chatInfo = info;
                this.footerHidden = true;
                this.inputShow = true;
                this.placeholder = placeholder;
            },
            submitComment(val) {
                const ninfo = {
                    ...this.getSubmitInfo(),
                    comment: val,
                    // #ifdef MP-TOUTIAO
                    from: 'bytedance',
                    // #endif
                    // #ifndef MP-TOUTIAO
                    from: 'routine',
                    // #endif
                }
                getSpecialCommentAdd(ninfo).then(res => {
                    this.updateAddInfo(res.data)
                    this.inputShow = false;
                    this.$showToast('评论提交成功')
                    // this.getSpecialComment(true)
                })

            },
            sendSuccess() {
                this.inputShow = false;
                this.footerHidden = false;
                this.opacity = 1;
            },
            initInfo() {
                this.requestLoading = false;
                this.messageInfo = [];
                this.page = {
                    page: 1,
                    limit: 10,
                    more: true
                }
            },
            getSpecialComment: debounce(function(init) {
                init && this.initInfo();
                const {
                    sid: special_id,
                    id: source_id
                } = this.ids;
                if (!this.page.more || this.requestLoading) return;
                this.requestLoading = true;
                getSpecialMessage({
                    ...this.page,
                    special_id,
                    source_id,
                    // sort: this.tabList[this.current].id,
                    sort: this.message_type == 0 ? this.tabList[this.current].id : 0,
                    message_type: this.message_type
                }).then(res => {
                    let info = res.data;
                    info.forEach((item, index) => {
                        if (typeof item.comment === 'string') {
                            item.content = [item.comment];
                        }
                    });
                    this.messageInfo = this.messageInfo.concat(info);
                    this.messageInfo.forEach(item => {
                        if (item.comment.length > 90) {
                            item.isMore = true
                            item.contentAll = true
                            if (this.ids.open_comment) {
                                item.contentAll = false
                            }
                        } else {
                            item.isMore = false
                            item.contentAll = false
                        }
                    })
                    // console.log('课程评论-', this.messageInfo)
                    this.page.more = res.data.length === this.page.limit;
                    this.page.page++;
                    this.requestLoading = false;
                })
            }, 200, true),
            async init() {
                this.initInfo();
                const {
                    sid,
                    id,
                    source,
                    is_free
                } = this.ids;
                if ((source === 'share' && sid !== '0') && !is_free) {
                    const res = await getSpecialDetail({
                        id: sid,
                    })
                    this.ff_detail = res.data;
                    const {
                        pay_type,
                        is_pay,
                        abstract
                    } = res.data;
                    console.log('pay_type', pay_type)
                    this.isBuy = pay_type === 0 || (pay_type === 1 && is_pay === 1)
                    if (!this.isBuy) {
                        this.$showToast('未购买，即将返回购买页');
                        setTimeout(() => {
                            this.$navigator(`/pages/yknowledge/course/detail?sid=${sid}&id=0`,
                                'redirectTo');
                        }, 1500)
                    }
                }
                getSpecialCourseListDetail({
                    special_id: sid,
                    id
                }).then(res => {
                    let detail = res.data;
                    const {
                        type,
                        image,
                        banner = [],
                    } = detail;
                    this.poster = image;
                    if (banner && banner.length) {
                        // console.log(banner)
                        if (typeof banner === 'string') {
                            detail.image = [banner];
                        } else {
                            detail.image = banner
                        }
                    } else {
                        if (type === 1) {
                            detail.image = [detail.image]
                        } else {
                            detail.image = []
                        }
                    }
                    detail.video = [];
                    if ((type === 3 && detail.link) && detail.trial_info.is_trial === 0) {
                        if (typeof detail.link === 'string') {
                            detail.video = [detail.link]
                        } else {
                            detail.video = detail.link
                        }
                    }
                    this.detail = detail;
                    console.log('this.detail------',this.detail)
                    this.getSpecialCourseList(); // 获取目录；
                    this.getSpecialComment(); //获取评论
                    
                    // console.log('素材详情-', this.detail)
                    if (this.detail.isSubscribe == 1) {
                        this.isBuy = false
                        this.copenSubscribe()
                    }
                    this.$updateTitle(detail.title);
                    this.shareConfig = {
                        desc: detail.title,
                        title: detail.title,
                        link: `/pages/yknowledge/course/detail_list?sid=${sid}&id=${id}&source=share&is_free=${this.detail.subscribe_special_id}`,
                        imgUrl: image
                    };
                    // #ifdef MP-TOUTIAO 
                    if (this.detail.type == 1) {
                        this.shareConfig.imgUrl = '';
                    }
                    // #endif
                    // #ifdef H5
                    openShareAll(this.shareConfig);
                    // #endif

                }).catch(err => {
                    setTimeout(() => {
                        this.$navigator(1)
                    }, 500)

                })
                // 
                if (sid !== '0') {
                    const ress = await getSpecialDetail({
                        id: sid,
                    })
                    this.ff_detail = ress.data;
                }
                // console.log('-------', this.ff_detail)
                sid > 0 && getSpecialJoinLearning(sid)
            },
            itempause(e) {
                this.ispause = true;
            },
            currentItem(item) {
                
                let that = this;
                this.ids.id = item.id;
                this.resetSpeed = true;
                // this.ispause = true;

                this.init(this.ids.sid, this.ids.id);
                // this.getSpecialComment();
                this.$store.commit('POPPLAY_TRUE'); // 小窗播放状态true
                if (storage.get('videoPlan' + this.ids.id)) {
                    that.setvideotime = Number(storage.get('videoPlan' + that.ids.id))
                    that.setaudiotime = Number(storage.get('videoPlan' + that.ids.id))
                    // console.log('that.setvideotime', that.setvideotime)
                    // console.log('that.setaudiotime', that.setaudiotime)
                }
                
            },
            isresetSpeed(e) {
                this.resetSpeed = false;
            },
        },
        onUnload() {
            let that = this;
            that.videoCtx = uni.createVideoContext(`playVideo0`, that);
            that.videoCtx.pause();
            this.$store.commit('POPPLAY_FALSE');

            if (that.ids.fromdetail && that.detail.is_pay) {
                uni.showModal({
                    title: "温馨提示",
                    cancelColor: '#787b7b',
                    confirmColor: '#e93323',
                    cancelText: '取消',
                    confirmText: '确定',
                    content: '更多精彩内容，去订阅？',
                    success(res) {
                        if (res.confirm) {
                            console.log("确定1");
                            // that.paymentTap(2)
                            eventBus.emit('pageBUnload', {
                                gopay: true,
                            });
                        }
                    },
                    fail(res) {
                        console.log(`showModal调用失败`);
                    },
                });
            }
            
        },
        onLoad(options) {
            // console.log('options',options)
            this.platform = uni.getSystemInfoSync().platform;
            const {
                source = '', //
                    sid = 0, // 课程id
                    id = 0, // 素材id
                    is_free = 0,
                    fromdetail = false
            } = options;

            const sort = '0';
            this.ids = options;
            this.init(this.ids.sid, this.ids.id);
            if (sort === '0') {
                this.getSpecialComment();
            } else {
                this.tabClick({}, Number(sort))
            }
            this.$store.commit('POPPLAY_TRUE'); // 小窗播放状态true
            // console.log('获取视频进度',storage.get('videoPlan'+this.ids.id))
            setTimeout(() => {
                 if (storage.get('videoPlan' + this.ids.id)) {
                     this.setvideotime = Number(storage.get('videoPlan' + this.ids.id))
                     this.setaudiotime = Number(storage.get('videoPlan' + this.ids.id))
                     // console.log('onLoad setaudiotime:', this.setaudiotime)
                 }
             }, 100)

        },
        onShow() {
            // #ifndef MP-TOUTIAO
            this.adjustPosition = false
            // #endif
            // #ifdef MP-TOUTIAO
            this.adjustPosition = true
            // #endif
            let timId = setTimeout(() => {
                uniSelectorQueryInfo('#tabLink', this).then(res => {
                    // console.log("得到布局位置信息-", res);
                    this.tabLinkInfo = res;
                    clearTimeout(timId)
                }).catch(err => {});
            }, 800)
            // console.log('oindex',this.oindex)
            if (this.oindex) {
                this.init(this.ids.sid, this.ids.id);
                // this.getSpecialComment();
            }
            this.timer = setInterval(() => {
                this.timeCount()
                this.$forceUpdate();
            }, 1000)

        },
        onPageScroll(e) {
            this.scrollTop = e.scrollTop;
            // console.log('滑动',this.scrollTop)
            this.inputShow = false;
            this.footerHidden = false;
            this.opacity = (e.scrollTop / 400).toFixed(1);
            if (e.scrollTop > 200) {
                this.footerOpacity = (e.scrollTop / 400).toFixed(1);
            } else {
                this.footerOpacity = 0;
            }
        },
        onReachBottom() {
            if(!this.detail.trial_info.is_trial){
                this.getSpecialComment()
            }
        },
        // #ifdef MP
        onShareAppMessage(res) {
            if (res.from === 'button') { // 来自页面内分享按钮
                // console.log(res.target)
                // Yusi音乐审美养成
            }
            return {
                title: this.shareConfig.title,
                desc: this.shareConfig.title,
                imageUrl: this.shareConfig.imgUrl,
                path: this.shareConfig.link,
                templateId: SHARE_ID
            };
        },
        onShareTimeline() {
            if (this.shareConfig) {
                const {
                    sid,
                    id
                } = this.ids;
                return {
                    title: this.shareConfig.title || '',
                    imageUrl: this.shareConfig.imgUrl || '',
                    query: `sid=${sid}&id=${id}&spid=${this.$store.state.userInfo.uid}&source=share`
                };
            }
        }
        // #endif
    }
</script>
<style lang='scss' scoped>
    movable-view {
        height: 150rpx;
        width: 150rpx;
        background-color: #007AFF;
        color: #fff;
        z-index: 9999999;
    }

    movable-area {
        position: fixed;
        left: 0;
        top: 0;
        height: 100vh;
        width: 100%;
    }

    .iospayPopup {
        position: relative;
        width: 500rpx;
        height: 350rpx;
        background-color: #fff;
        border-radius: 16rpx;
        border: 1rpx solid #999;
        padding: 50rpx 30rpx;
        box-sizing: border-box;
    }

    .iospayPopup-colse {
        position: absolute;
        right: 2rpx;
        top: 2rpx;
        width: 50rpx;
        height: 50rpx;
    }

    .iospayPopup-title {
        width: 100%;
        font-size: 30rpx;
        color: #000;
    }

    .iospayPopup-button {
        width: 220rpx;
        height: 70rpx;
        line-height: 70rpx;
        text-align: center;
        color: #fff;
        font-size: 24rpx;
        background-color: #3399ff;
        border-radius: 10rpx;
        margin: 69rpx auto 0;
    }

    .balance-box {
        position: relative;
        width: 600rpx;
        background-color: #fff;
        border-radius: 20rpx;
        padding: 40rpx 30rpx 50rpx;
        box-sizing: border-box;

    }

    .balance-box-close {
        position: absolute;
        top: 2rpx;
        right: 2rpx;
        width: 50rpx !important;
        height: 50rpx !important;
    }

    .balance-box-title {
        width: 100%;
        text-align: left;
        color: #000;
        font-weight: bold;
        font-size: 32rpx;
        margin-top: 50rpx;

    }

    .balance-box-desc {
        width: 100%;
        text-align: center;
        color: #000;
        font-size: 26rpx;
        margin-top: 50rpx;
    }

    .balance-box-btn {
        width: 100%;
        margin-top: 60rpx;
        overflow: auto;

        .balance-box-btn-left {
            float: left;
            width: 240rpx;
            height: 68rpx;
            line-height: 68rpx;
            text-align: center;
            background-color: #7f7f7f;
            font-size: 26rpx;
            color: #fff;
            border-radius: 8rpx;
        }

        .balance-box-btn-right {
            float: right;
            width: 240rpx;
            height: 68rpx;
            line-height: 68rpx;
            text-align: center;
            background-color: #fc5656;
            font-size: 26rpx;
            color: #fff;
            border-radius: 8rpx;
        }

    }

    .conter {
        width: 100%;
        padding: 0 30rpx;
        box-sizing: border-box;
    }

    .conter p {
        font-size: 32rpx !important;
    }

    .conter image {
        width: 100% !important;
        display: block;
    }

    .max {
        width: 500rpx;
        height: 500rpx;
    }

    .loadmore {
        padding: 40rpx 0;
    }

    .comment_height {
        height: calc(100vh - 200rpx - 100rpx);
    }

    .w_top {
        margin-top: 50rpx;

        .t_l {
            width: 100%;

            .title {
                font-size: 32rpx;
                font-weight: 700;
                text-align: left;
                color: #333333;
                @include show_line(3);

                &.active {
                    font-family: PingFang SC, PingFang SC-Bold;
                    font-weight: 700;
                    font-size: 44rpx;
                    color: #000000;
                }
            }


            .detail-title {
                width: 100%;
                font-size: 28rpx;
                font-family: PingFang SC, PingFang SC-Bold;
                font-weight: 700;
                text-align: left;
                color: #64686b;
                margin-top: 26rpx;

                .detail-title-txt {
                    margin-right: 20rpx;

                    &:last-child {
                        margin: 0;
                    }
                }

            }

            .detail-time {
                width: 100%;
                font-size: 28rpx;
                font-family: SF Compact Display, SF Compact Display-Regular;
                font-weight: 400;
                text-align: left;
                color: #64686b;
                margin-top: 18rpx;
                margin-bottom: 64rpx;
            }

        }

        .t_r {
            width: 56rpx;
        }
    }

    .info {
        margin-top: 30rpx;
        border-radius: 16rpx;
        overflow: hidden;
        margin-bottom: 50rpx;

        .item {
            text-align: center;
            padding: 22rpx 0;
            background: #e9f1fb;

            .title {
                @include font_size(20);
                font-weight: 400;
                color: #666666;
            }

            .num {
                margin-top: 14rpx;
                font-size: 28rpx;
                font-weight: 700;
                text-align: center;
                color: #333333;
                line-height: 38rpx;
            }
        }
    }

    .audio {
        /* margin: 20rpx; */
        margin-top: 60rpx;
        margin-bottom: 48rpx;
    }

    .tab_title {
        .title {
            font-size: 32rpx;
            font-weight: 700;
            text-align: left;
            color: #333333;
        }

        .btn {
            background-color: #6bb4ff;
            border-radius: 20rpx;
            width: 108rpx;
            height: 50rpx;
            line-height: 50rpx;
            text-align: center;
            font-size: 24rpx;
            font-weight: 400;
            text-align: center;
            color: #ffffff;
        }
    }

    .tab {
        border-top: 2rpx solid #d8d8d8;
        border-bottom: 2rpx solid #d8d8d8;
        margin-top: 32rpx;
        margin-bottom: 40rpx;

        .item {
            width: 25%;
            /* text-align: center; */
            padding-left: 20rpx;
            height: 64rpx;
            line-height: 64rpx;
            font-size: 24rpx;
            background: #ffffff;
            font-weight: 500;
            color: #999999;

            &:not(:last-child) {
                border-right: 2rpx solid #d8d8d8;
            }

            &.active {
                background: #f4f4f4;
                font-weight: 700;
                color: #333;
            }
        }
    }

    .message_wrap {
        background: none;

        >view.item {
            padding-bottom: 20rpx;

            >view:not(.mess_avatar) {
                padding-left: 52rpx;
            }

            &:not(:last-child) {
                border-bottom: 2rpx solid #e2e6ec;
            }
        }

        .mess_avatar {
            margin: 50rpx 0 20rpx 0;
            padding-bottom: 14rpx;

            .avatar {
                image {
                    width: 60rpx;
                    height: 60rpx;
                    border-radius: 50%;
                    margin-right: 32rpx;
                }

                text {
                    color: #101010;
                    font-size: 28rpx;
                }
            }

            .btn {
                width: 108rpx;
                height: 50rpx;
                margin-left: 20rpx;
                border: 1px solid rgba(0, 0, 0, 0.078);
                border-radius: 32rpx;

                font-size: 24rpx;

                color: #666666;
            }
        }

        .score {
            margin-bottom: 40rpx;

            .txt {
                padding: 8rpx 16rpx;

                border-radius: 20rpx;
                border: 2rpx solid #ff5656;
                color: #ff5656;
                // letter-spacing: 12rpx;
                font-weight: 500;
                font-size: 28rpx;
                margin-right: 12rpx;
            }

            .process {
                // width: 276rpx;
                height: 58rpx;
                padding: 14rpx 20rpx;
                background: #ffffff;
                opacity: 1;
                border-radius: 20rpx;

                .num {
                    margin-left: 14rpx;
                    color: #ff5656;
                    font-weight: bold;
                    font-size: 40rpx;
                }
            }
        }

        .label {
            ::v-deep.xlabel {
                >.wrap {
                    margin-bottom: 36rpx;
                    background: none;

                    .flex {
                        background: none;
                    }

                    .more {
                        width: 60rpx;
                        height: 60rpx;
                        border-radius: 30rpx;

                        text {
                            width: 60rpx;
                            height: 60rpx;
                            border-radius: 30rpx;
                        }
                    }
                }
            }
        }

        .mess_des {
            margin-top: 42rpx;
            font-size: 24rpx;
            line-height: 36rpx;
            color: #101010;
            margin-bottom: 20rpx;

            .del {
                text-align: right;
                color: #999;
            }

            .refining_span {
                position: absolute;
                border: 1rpx solid red;
                top: -40rpx;
                right: 20rpx;
                color: red;
                padding: 2rpx 30rpx;
                font-weight: bold;
                font-size: 30rpx;
                letter-spacing: 6rpx;
                transform: rotate(-30deg);
                opacity: 0.6;
                border-radius: 10rpx;
            }
        }

        .mess_image {
            height: 148rpx;
            margin-bottom: 20rpx;
            margin-left: -20rpx;

            .nav {
                width: 20rpx;
                line-height: 148rpx;
                text-align: center;

                text {
                    font-size: 24rpx;
                    font-weight: bolder;
                }
            }

            .wrap1 {
                width: calc(100% - 40rpx);

                .item {
                    // display: inline-block;
                    width: 136rpx;
                    height: 136rpx;

                    &:not(:last-child) {
                        margin-right: 6rpx;
                    }

                    image {
                        width: 136rpx;
                        height: 136rpx;
                    }

                    &.video {
                        position: relative;

                        .cover {
                            position: absolute;
                            width: 100%;
                            height: 100%;
                            z-index: 10;
                            top: 0;

                            image {
                                width: 120rpx;
                                height: 120rpx;
                            }
                        }
                    }

                    &.audio {
                        // .cover {
                        display: flex;
                        align-items: center;
                        justify-content: space-around;

                        image {
                            width: 120rpx;
                            height: 120rpx;
                        }

                        // }
                    }
                }
            }
        }

        .my_handle {
            background: #fff;
            padding: 12rpx 10rpx 12rpx 82rpx;
            margin-bottom: 30rpx;
            border-radius: 32rpx;
            box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);

            .txt {
                color: #ff5656;
            }

            .btn {
                width: 152rpx;
                height: 60rpx;
                background: #eaeaea;
                opacity: 1;
                border-radius: 24rpx;

                color: #50506f;
                font-size: 24rpx;
                margin-left: 8rpx;
            }
        }

        .mess_handle {
            font-size: 24rpx;
            color: #ff5656;
            margin: 32rpx 0 40rpx 0;

            .time {
                color: #999999;
            }

            .item {
                image {
                    width: 34rpx;
                    height: 24rpx;
                }

                text {
                    padding: 0 16rpx;
                    font-size: 24rpx;

                    color: #ff5656;
                }
            }
        }

        .comment {
            padding-left: 0rpx !important;
            margin-left: 52rpx;
            background: #eaeaea;

            max-height: 600rpx;
            overflow-y: scroll;
        }
    }

    .active_footer {
        @include fixed_footer(112rpx);
        box-shadow: 0px 0px 40px 0px rgba(107, 127, 153, 0.20);
        background: #f7f8fa;

        &.hide {
            display: none;
        }

        .flex_l {
            flex: 1;
            padding: 0 60rpx;

            .item {
                // width: calc((100% - 400rpx) / 2);
                width: 50%;
                text-align: center;
                font-size: 24rpx;

                color: #3e3e3e;

                image {
                    width: 40rpx;
                    height: 40rpx;
                }
            }
        }

        .btn {
            width: 246rpx;
            height: 112rpx;
            line-height: 112rpx;
            text-align: center;

            border-radius: 30rpx 0px 0px 0px;
            font-size: 32rpx;
            background: #6bb4ff;
            color: #ffffff;
            font-weight: bold;

            &.active {
                width: 400rpx;
            }
        }
    }
    .shiting {
        display: inline-block;
        color: #fff;
        font-size: 26rpx;
        padding: 2rpx 10rpx;
        border-radius: 6rpx;
        background-color: #ffa916;
        margin-right: 10rpx;
        margin-bottom: 4rpx;
    }
</style>