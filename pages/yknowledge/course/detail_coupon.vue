<template>
    <view>
        <view class="head-nav">
            <block v-for="(item,index) in list" :key="index">
                <view class="head-nav-item" :class="navIndex==index?'activite':''" @click="checkIndex(item.id,index)">
                    {{item.name}}</view>
            </block>
        </view>
        <view class="coupon-list" v-if="couponsList.length > 0">
            <view class="item acea-row row-center-wrapper" v-cloak v-for="(item, index) in couponsList" :key="index">
                <view class="money" :class="item.is_fail === 1 || item._type === 0 ? 'moneyGray' : ''">
                    <view>
                        ￥<span class="num">{{ item.coupon_price }}</span>
                    </view>
                    <view class="pic-num" v-if="item.use_min_price != '0.00'">满{{ item.use_min_price }}元可用</view>
                    <view class="pic-num" v-else>现金抵扣券</view>
                </view>
                <view class="text">
                    <view class="condition line1">
                        <span v-if="item.applicable_type === 0" class="line-title"
                            :class="item.is_fail === 1 || item._type === 0 ? 'bg-color-huic' : 'bg-color-check'">通用劵</span>
                        <span v-if="item.applicable_type === 1" class="line-title"
                            :class="item.is_fail === 1 || item._type === 0 ? 'bg-color-huic' : 'bg-color-check'">通用商品券</span>
                        <span v-if="item.applicable_type === 2" class="line-title"
                            :class="item.is_fail === 1 || item._type === 0 ? 'bg-color-huic' : 'bg-color-check'">专用商品券</span>
                        <span v-if="item.applicable_type === 3" class="line-title"
                            :class="item.is_fail === 1 || item._type === 0 ? 'bg-color-huic' : 'bg-color-check'">通用课程券</span>
                        <span v-if="item.applicable_type === 4" class="line-title"
                            :class="item.is_fail === 1 || item._type === 0 ? 'bg-color-huic' : 'bg-color-check'">专用课程券</span>
                        <span class="coupon-title">{{ item.coupon_title }}</span>
                    </view>

                    <view v-if="item.is_fail === 1 || item._type === 0" class="data acea-row row-between-wrapper">
                        <view>已失效</view>
                        <view class="deleteCoupon" @click="deleteCoupon(item.id,index)">×</view>
                    </view>
                    <view v-else class="data acea-row row-between-wrapper">
                        <view class="coupon-time" v-if="item._end_time === 0">不限时</view>
                        <view class="coupon-time" v-else>{{ item._add_time }}-{{ item._end_time }}</view>
                        <view class="bnt gray" v-if="item.is_fail === 1 || item._type === 0" @click="deleteCoupon(item.id,index)">x</view>
                        <view class="bnt bg-color-red" v-else @click="goUse(item)">使用</view>
                    </view>
                </view>
            </view>
        </view>
        <!--暂无优惠券-->
        <xNodate :arr="couponsList" :page="2" :isR="false" imgSrc="/wximage/noCoupon.png"></xNodate>
        <x-authorize :isHidden="true" @login="getUseCoupons"></x-authorize>
        <x-home></x-home>
    </view>
</template>
<script>
    
    import {
        specialCouponsUser,
        specialCouponDestroy
    } from '@/api/activity';
    import xNodate from '@/components/x-nodata/x-nodata.vue';
    const NAME = "UserCoupon";
    export default {
        name: "UserCoupon",
        components: {
            xNodate
        },
        props: {},
        data: function() {
            return {
                navIndex: 0,
                list: [{
                        name: '可使用',
                        id:1
                    },
                    {
                        name: '已用/失效',
                        id:2
                    },
                ],
                couponsList: [],
            };
        },
        onLoad() {
            this.getspecialCouponsUser(1)
        },
        onShow() {},
        methods: {
            getspecialCouponsUser: function(types) {
                var that = this;
                specialCouponsUser({
                        types: types
                    })
                    .then(res => {
                        // console.log('res', res)
                        this.couponsList = res.data;
                    })
                    .catch(res => {
                        that.$showToast(res.msg || res);
                    });
            },
            goUse(item) {
                // console.log('item', item)
                this.$navigator('/pages/tabBar/index/index', 'switchTab');
            },
            deleteCoupon(id,index) {
                let that = this;
                that.$showModal('提醒', '是否删除此失效优惠券', {
                    confirmText: '确认',
                    cancelText: '取消',
                    success: function(res) {
                        // console.log('点击按钮触发', res)
                        if (res.confirm) {
                            specialCouponDestroy({
                                id: id
                            })
                            .then(res => {
                                // console.log('删除成功', res)
                                that.couponsList.splice(index, 1)
                                return that.$showToast('删除成功')
                            })
                            .catch(res => {
                                that.$showToast(res.msg || res);
                            });
                        } else if (res.cancel) {

                        }
                    },
                    fail() {}
                });
            },
            checkIndex(id,index) {
                this.navIndex = index;
                this.getspecialCouponsUser(id)
            },
        }
    };
</script>
<style lang="scss">
    page {
        background-color: $uni-bg-color-page;
    }

    .head-nav {
        width: 100%;
        height: 100rpx;
        background-color: #ffffff;
        line-height: 100rpx;
        margin-bottom: 20rpx;
    }

    .head-nav-item {
        position: relative;
        float: left;
        text-align: center;
        width: 50%;
    }

    .head-nav-item.activite::after {
        content: "";
        display: block;
        position: absolute;
        width: 76rpx;
        height: 6rpx;
        background-color: #EB0F1D;
        left: 50%;
        transform: translatex(-50%);
        top: 80%;
        z-index: 1;
    }

    .deleteCoupon {
        padding: 0 20rpx;
        color: #000;
        font-size: 40rpx;
        box-sizing: border-box;
    }

    .coupon-title {
        font-size: 24rpx;
        -webkit-text-size-adjust: none;
        text-size-adjust: none;
        transform: scale(.8); //缩放比例
        transform-origin: left;
    }

    .coupon-time {
        /* #ifdef MP */
        font-size: 20rpx;
        /* #endif */

        /* #ifdef H5 */
        font-size: 24rpx;
        // transform: scale(.8);
        // transform-origin: left;
        /* #endif */

    }
</style>
