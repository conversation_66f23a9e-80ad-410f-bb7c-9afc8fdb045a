<template>
    <view>
        <view class="">
            <web-view :webview-styles="webviewStyles" :src="url_link" @message="message" @load="load"
                @onPostMessage="onPostMessage"></web-view>
        </view>
        
    </view>
</template>

<script>
    import {
        specialActivityDetail,
    } from '@/api/activity';
    import {
        regHref,
    } from '@/utils/validate.js'; 
    import {
        reportWebView
    } from '@/utils/ReportAnalytics.js';
    import {
        getWechatConfig
    } from '@/api/public';
    import {
        IS_TOUTIAO_PAY,
        SHARE_ID,
        WX_KEFU_Link,
        WX_ENTERPRISE_Link,
        OPEN_IOS_PAYMENT,
        VUE_APP_URL,
        WECHAT_AUTH_BACK_URL
    } from '@/config.js';
    
    export default {
        data() {
            return {
                ids:{},
                detail: {},
                url_link: '',
                webviewStyles: {
                    progress: {
                        color: '#FF3333'
                    }
                },
            }
        },
        onLoad(option) {
            const {
                type = 'h5',
                id = 0,
                activityId = 0,
                url
            } = option
            this.ids = option;
            this.url_link = option.url + '?id=' + option.id + '&activityId=' + option.activityId;
            // https://dev.shop.arthorize.com/h5/course/share?id=6&activityId=1&uid=1919
            // this.$storage.set(WECHAT_AUTH_BACK_URL, this.url_link)
            this.getspecialActivityDetail(id, activityId)
        },
        onShareAppMessage() {
            return {
                title: '分享得课程券',
                imageUrl: this.detail.image,
                path:'/pages/yknowledge/course/detail_webview?url=' + this.ids.url + '&id=' + this.ids.id + '&activityId=' + this.ids.activityId
            };
        },
        methods:{
            getspecialActivityDetail(id, activityId) {
                specialActivityDetail({
                    id:id,
                    activity_id:activityId
                }).then(res => {
                    this.detail = res.data;
                    console.log('课程详情res', this.detail)
                }).catch(err => {
                    console.log('课程详情err', err)
                    // setTimeout(() => {
                    //     this.$navigator(-1)
                    // }, 500)
                })
            },
            load(e) {
                console.log('load', e)
            },
            message(e) {
                console.log('message', e)
                console.log('e.detail.data===' + JSON.stringify(e.detail.data))
            },
            onPostMessage(e) {
                console.log('onPostMessage', e)
            },
            goPages(path, type) {
                this.$navigator(path, type);
            },
            openMp() {
                location.href = this.url
            }
        }
    }
</script>

<style scoped lang="scss">
    .mp-btn {
        width: 360rpx;
        padding-top: 60%;
        margin: 0 auto;
        text-align: center;

        .txt {
            padding: 60rpx 0;
            font-size: 32rpx;
        }

        .btn {
            font-size: 34rpx;
            padding: 10rpx 0;
        }

    }
</style>