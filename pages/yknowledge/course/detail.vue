<template>
    <view @click="show_new = true">
        <!-- #ifdef MP-WEIXIN -->
        <view class="swiper-set" v-if="isActiveAdmin" @click="openAdmin">
            <image src="../../../static/images/yuanshi/edit.png" mode=""></image>
        </view>
        <!-- #endif -->
        <xSwiper id="swiperTop" :detail="detail" :poster="poster" @send="getSonValue" />
        <view class="common_wrap">
            <view class="w_top flex flex_between">
                <view class="t_l">
                    <view class="title">
                        {{detail.title || ''}}
                    </view>
                    <view class="span flex">
                        <view class="span1"><text>{{detail.top_subject_name}}</text></view>
                        <view class="span2"><text>{{detail.subject_name}}</text></view>
                    </view>
                </view>
            </view>
            <view v-if="detail.service_status == 1" class="join flex flex_align_center flex_between">
                <view class="txt">
                    {{detail.service_text || ''}}
                </view>
                <view class="btn" @click="addChatGroup">
                    {{detail.service_button_text || '加入群聊'}}
                </view>
            </view>
        </view>
        <view class="live-notice" v-if="is_live_info">
            <uni-notice-bar showIcon scrollable single :speed="speed" background-color="#fffbe8" color="#de8c17"
                :text="detail.live_info"></uni-notice-bar>
        </view>
        <xRecom v-if="detail.similar_recommendations.length != 0" :title="detail.recommend_navigator_title"
            :arr="detail.similar_recommendations" />
        <view class="common_wrap" style="margin-top: 26rpx;">
            <view class="tab_link" id="tabLink">
                <view class="flex flex_align_center flex_between">
                    <view class="tab flex">
                        <view class="item" :class="{active:current===index}" v-for="(item,index) in cloumnNavlist"
                            :key="index" @click="tabsChange(index)">
                            <view class="title">{{item.title}}
                                <text v-if="item.title==='目录'"> ({{detail.count || 0}})</text>
                                <text v-if="item.title==='评论'"> ({{detail.comments || 0}}) </text>
                                <text v-if="item.title==='作业区'">({{detail.works || 0}})</text>
                            </view>
                            <view class="item_line"></view>
                        </view>
                    </view>
                    <view v-if="current == 3" class="gocommet-btn work" @click="goCommentSubmit(true,2)">提交作业</view>
                    <view v-else class="gocommet-btn" @click="goCommentSubmit(true,1)">去评论</view>
                </view>
                <view class="line"></view>
                <swiper class="swiper" :current="swiperCurrent" @change="swiperChange">
                    <swiper-item class="swiper-item">
                        <scroll-view scroll-y style="height: 100%;width: 100%;">
                            <view class="conter">
                                <mp-html :content="detail.content" />
                            </view>
                            <view class="conter" v-if="detail.is_problems == 1">
                                <mp-html :content="detail.common_problem" />
                            </view>
                        </scroll-view>
                    </swiper-item>
                    <swiper-item class="swiper-item lists">
                        <scroll-view scroll-y style="height: 100%;width: 100%;" @scrolltolower="onreachBottom"
                            v-if="!show_old">
                            <view v-for="(item,index) in courseList" :key="index">
                                <view class="titles" :class="index == courseList.length - 1?'last':''">
                                    <view class="flex flex_align_center flex_between title1"
                                        @click="openDetail(index, item)">
                                        <view class="title1-left">
                                            {{item.title}}
                                        </view>
                                        <view class="flex flex_between title1-right">
                                            <view class="title1-right-txt">
                                                ({{item.play_number || 0}}/{{item.number || 0}})</view>
                                            <image class="title1-right-img" :class="flag[index]?'right':''"
                                                src="../../../static/images/arrow-bottom1.png" mode=""></image>
                                        </view>
                                    </view>
                                    <view v-if="flag[index]">
                                        <view v-for="(items,indexs) in item.course_list" :key="indexs"
                                            class="item flex flex_between flex_align_center"
                                            :class="indexs == item.course_list.length - 1?'last':''"
                                            @click="goPlay(items,2)">
                                            <view class="item_l flex flex_align_center">
                                                <view class="image">
                                                    <template v-if="items.id == videoCourse.courseLastId">
                                                        <image v-if="isPopPlay" class="image-gif"
                                                            src="@/static/images/course.gif" mode=""></image>
                                                        <image v-else class="image-gif" src="@/static/images/course.png"
                                                            mode=""></image>
                                                    </template>
                                                    <image v-else :src="items.image" mode="widthFix"></image>
                                                </view>
                                                <view class="item_l_r "
                                                    :class="[items.is_watch ?'alreadySeen':'',detail.is_pay && detail.is_live && (courseList.length-1) == index ?'livelock':'openlock',(detail.is_pay && (items.is_eggs_class && detail.is_easter_eggs))?'livelock':'',items.id == videoCourse.courseLastId?'lastStyle':'']">
                                                    <!-- {{indexs>8?indexs+1:'0'+(indexs+1)}} | {{items.title}} -->
                                                    {{getContinuousIndex(index,indexs) <= 9? '0'+getContinuousIndex(index,indexs):getContinuousIndex(index,indexs)}}
                                                    | {{items.title}}
                                                </view>
                                            </view>
                                            <view class="item_r" :class="items.is_trial?'item_r_shitinh':''" v-if="!items.is_free">
                                                <!-- !detail.is_pay && -->
                                                <text class="shiting" v-if="items.is_trial && items.trial_file_type != 0">
                                                    试{{items.trial_file_type==1?'听':'看'}}
                                                </text>
                                                <image v-else src="@/static/images/zsff/lock.png" mode="widthFix"></image>
                                            </view>
                                            <view class="item_live work"
                                                v-if="(detail.is_pay && (items.is_eggs_class && detail.is_easter_eggs))">
                                                彩蛋
                                            </view>
                                            <view class="item_live"
                                                v-if="detail.is_pay && detail.is_live && items.type == 4">
                                                <text class="text" v-if="items.live_status == 0">
                                                    未开始
                                                </text>
                                                <text class="text" v-if="items.live_status == 1">
                                                    直播中
                                                </text>
                                                <text class="text" v-if="items.live_status == 2">
                                                    回放
                                                </text>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view class="loadmore1">
                                没有更多了~
                            </view>
                        </scroll-view>
                        <scroll-view v-else scroll-y style="height: 100%;width: 100%;" @scrolltolower="onreachBottom">
                            <view v-for="(item,index) in courseList" :key="index"
                                class="item flex flex_between flex_align_center" @click="goPlay(item,1)">
                                <view class="item_l1 flex flex_align_center"
                                    :class="[item.is_watch ?'alreadySeen':'',]">
                                    <view class="image">
                                        <template v-if="item.id == videoCourse.courseLastId">
                                            <image v-if="isPopPlay" class="image-gif" src="@/static/images/course.gif"
                                                mode=""></image>
                                            <image v-else class="image-gif" src="@/static/images/course.png" mode="">
                                            </image>
                                        </template>
                                        <image v-else :src="item.image" mode="widthFix"></image>
                                    </view>
                                    <view class="item_l_r "
                                        :class="[detail.is_pay && detail.is_live && (courseList.length-1) == index ?'livelock':'openlock',detail.is_pay && (item.is_eggs_class && detail.is_easter_eggs) ?'livelock':'',item.id == videoCourse.courseLastId?'lastStyle':'']">

                                        {{index>8?index+1:'0'+(index+1)}} | {{item.title}}
                                    </view>
                                </view>
                                <view class="item_r" :class="item.is_trial?'item_r_shitinh':''" v-if="!item.is_free">
                                    <!-- !detail.is_pay && -->
                                    <text class="shiting" v-if="item.is_trial && item.trial_file_type != 0">
                                        试{{item.trial_file_type==1?'听':'看'}}
                                    </text>
                                    <image v-else src="@/static/images/zsff/lock.png" mode="widthFix"></image>
                                </view>
                                <view class="item_live work"
                                    v-if="detail.is_pay && (item.is_eggs_class && detail.is_easter_eggs)">
                                    彩蛋
                                </view>
                                <view class="item_live" v-if="detail.is_pay && detail.is_live && item.type == 4">
                                    <text class="text" v-if="item.live_status == 0">
                                        未开始
                                    </text>
                                    <text class="text" v-if="item.live_status == 1">
                                        直播中
                                    </text>
                                    <text class="text" v-if="item.live_status == 2">
                                        回放
                                    </text>
                                </view>
                            </view>
                            <view class="loadmore">
                                <u-loadmore :status="loadMoreStatus" />
                            </view>
                        </scroll-view>
                    </swiper-item>
                    <swiper-item class="swiper-item">
                        <view class="tab-comment flex">
                            <view class="item " :class="{active:index===current1}" v-for="(item,index) in tabList"
                                :key="index" @click="tabClick(item,index)">
                                {{item.name}}
                            </view>
                        </view>
                        <scroll-view scroll-y style="height: 100%;width: 100%;" @scrolltolower="onreachBottom">
                            <view class="message_wrap " v-if="messageInfo.length"
                                @click="oindex_detail = false;workdata.id = 0">
                                <xComment ref="xComment" :arr="messageInfo" @showInput="showInput" :pageType="pageType"
                                    :isvideo="false" :isgohome="true" />
                            </view>
                            <view class="text_center" style="padding-top: 100rpx;" v-else>暂无评论</view>
                        </scroll-view>
                    </swiper-item>
                    <swiper-item class="swiper-item">
                        <view class="tab-comment flex">
                            <view class="item " :class="{active:index===currentWork}" v-for="(item,index) in tabList"
                                :key="index" @click="tabClickWork(item,index)">
                                {{item.name}}
                            </view>
                        </view>
                        <scroll-view scroll-y style="height: 100%;width: 100%;" @scrolltolower="onreachBottom">
                            <view class="text_center" style="padding-top: 100rpx;"
                                v-if="detail.pay_type===1&&!detail.is_pay">作业区仅学员可见~</view>
                            <view v-else>
                                <view class="message_wrap" v-if="messageInfo.length"
                                    @click="oindex_detail = false;workdata.id = 0">
                                    <xComment ref="xCommentWork" :arr="messageInfo" @showInput="showInput"
                                        :pageType="pageType" :isvideo="false" :isgohome="true" :isWorkReply="true" />
                                </view>
                                <view class="text_center" style="padding-top: 100rpx;" v-else>暂无作业</view>
                            </view>
                        </scroll-view>
                    </swiper-item>
                </swiper>
            </view>
        </view>
        <uni-popup ref="balancePopups" type="center" :animation="true">
            <view class="balance-box">
                <image class="balance-box-close" src="../../../static/images/yuanshi/close1.png" mode=""
                    @click="closeBalancePopups"></image>
                <view class="balance-box-title">
                    <view>
                        直播课程即将开始
                    </view>
                    <view>
                        {{detail.notice_time}}
                    </view>
                </view>
                <view class="balance-box-btn">
                    <view class="balance-box-btn-center" @click="closeBalancePopups">
                        知道了
                    </view>
                </view>
            </view>
        </uni-popup>
        <uni-popup ref="popupAdmin" type="center" :animation="true">
            <view class="Adminpop">
                <span class="iconfont icon-guanbi Adminpop-close" @click="closeAdmin"></span>
                <view class="Adminpop-title">
                    管理员操作面板
                </view>
                <view class="Adminpop-text">
                    <view class="item">
                        注意：
                    </view>
                    <view class="item">
                        1、用户通过入口访问课程页自动获得该课程观看权益。
                    </view>
                    <view class="item">
                        2、课程权益领取入口(多户一码仅当天有效)。
                    </view>
                </view>
                <view class="Adminpop-but" @click="openpopupAdmin(2)">
                    一户一码（领取后失效）
                </view>
                <view class="Adminpop-but" @click="openpopupAdmin(1)">
                    多户一码
                </view>
            </view>
        </uni-popup>
        <uni-popup ref="popupAdmin1" type="center" :animation="true">
            <view class="Adminpop send">
                <span class="iconfont icon-guanbi Adminpop-close" @click="closeAdmin1"></span>
                <view class="Adminpop-title">
                    <text v-if="codetype == 2">一户一码</text><text v-if="codetype == 1">多户一码</text> 邀请码
                </view>
                <view class="Adminpop-text">
                    <view class="item">
                        本次邀请码：（{{invitation_code}}）
                    </view>
                </view>
                <button class="Adminpop-but" @click="shareCourse(true)" type="default" open-type="share"
                    style="background-color: #1988CC;color: #FFFFFF;">
                    发送领取课程权益给用户
                </button>
            </view>
        </uni-popup>
        <uni-popup ref="popup1" type="center" :animation="true" @click="addChatGroupClose">
            <view class="" @click.stop=""
                style="width: 400rpx;background-color: #fff;border-radius: 20rpx;padding:20rpx;">
                <view>
                    <!-- <u-image width="100%" height="300rpx" :src="detail.service_code"></u-image> -->
                    <!-- #ifndef MP-TOUTIAO -->
                    <image style="width: 100;height: 300rpx;" :src="detail.service_code" mode="aspectFit"
                        show-menu-by-longpress="true"></image>
                    <!-- #endif -->
                    <!-- #ifdef MP-TOUTIAO -->
                    <image style="width: 100;height: 300rpx;" :src="detail.byte_service_code" mode="aspectFit"
                        show-menu-by-longpress="true"></image>
                    <!-- #endif -->
                </view>
                <!-- #ifndef MP-TOUTIAO -->
                <view class="text_left" style="padding:20rpx">{{detail.alert_text}}</view>
                <!-- #endif -->
                <!-- #ifdef MP-TOUTIAO -->
                <view class="text_left" style="padding:20rpx">保存图片从抖音中扫描添加Yusi音乐审美养成为好友</view>
                <view class="tt_text_left">
                    <view class="tt_text_left_btn1" @click="addChatGroupClose">
                        关闭
                    </view>
                    <view class="tt_text_left_btn2" @click="saveimg(detail.byte_service_code)">
                        保存
                    </view>
                </view>
                <!-- #endif -->
            </view>
        </uni-popup>
        <xChat :adjustPosition="adjustPosition" :placeholder="placeholder" :inputShow="inputShow" :uid="uid"
            @send="submitComment" desc="comment">
        </xChat>
        <view class="active_footer relative">
            <view class="absolute flex flex_align_center flex_between">
                <view class="flex flex_l flex_align_center">
                    <view class="item" @click="goPages('/pages/tabBar/index/index',false,'switchTab')">
                        <view>
                            <image src="@/static/images/community/home.png" mode="">
                            </image>
                        </view>
                        <view class="font_size20">首页</view>
                    </view>
                    <!-- #ifdef MP-WEIXIN -->
                    <view class="item" @click="openWeChat">
                        <view>
                            <image src="@/static/images/community/chat.png" mode=""></image>
                        </view>
                        <view class="font_size20">客服</view>
                    </view>
                    <!-- #endif -->
                    <!-- #ifndef MP-WEIXIN -->
                    <view class="item"
                        @click="goPages(`/pages/user/CustomerList?id=${ids.sid}&type=0&scence=zsff`,true)">
                        <view>
                            <image src="@/static/images/community/chat.png" mode=""></image>
                        </view>
                        <view class="font_size20">客服</view>
                    </view>
                    <!-- #endif -->
                    <view class="item" @click="collect">
                        <view>
                            <image v-if="detail.isCollection" src="@/static/images/yuanshi/love1.png" mode="widthFix">
                                <image v-else src="@/static/images/yuanshi/love1_1.png" mode="widthFix">
                        </view>
                        <view class="font_size20">{{detail.isCollection?'已关注':'关注'}}</view>
                    </view>
                    <view v-if="oldBringsNew" class="item onlineCls" @click="goshare">
                        <view>
                            <image src="@/static/images/yuanshi/share1.png" mode="widthFix"></image>
                        </view>
                        <view class="font_size20">分享</view>
                    </view>
                    <button v-else class="item" type="default" @click="shareCourse(false)" open-type="share"
                        style="background-color: #f7f8fa;">
                        <view>
                            <image src="@/static/images/yuanshi/share1.png" mode="widthFix"></image>
                        </view>
                        <view class="font_size20">分享</view>
                    </button>
                    <view class="item"
                        v-if="(detail.pay_type == 1 || detail.money1>0)&&!detail.gift_uid && isToutiaoPay"
                        @click="paymentTap(1)">
                        <view>
                            <image src="@/static/images/zsff/gift.png" mode=""></image>
                        </view>
                        <view class="font_size20">送朋友</view>
                    </view>
                </view>
                <view>
                    <template v-if="is_gift">
                        <view class="btn active" @click="getGiveGift">领取课程</view>
                    </template>
                    <template v-else>
                        <view class="btn" @click="paymentTap(2)" v-if="detail.pay_type===1&&!detail.is_pay">
                            {{openIosPayment && platform == 'ios' ? '温馨提示' : (detail.type == 2 ? '去收听' : '去学习')}}
                        </view>
                        <view class="btn active" v-else @click="goStudy">
                            {{detail.type==2?'去收听':'去学习'}}
                        </view>
                    </template>
                </view>
            </view>
        </view>
        <!-- #ifdef MP-TOUTIAO -->
        <uni-popup ref="popup" type="center" :maskClick="false">
            <view class="flex flex_align_center authorize">
                <view class="text_center relative">
                    <view class="top">
                        <view class="title">授权登录</view>
                        <view class="tip">请授权头像等信息，以便为您提供更好的服务</view>
                    </view>
                    <view class="bottom flex">
                        <button class="btn" @click="close">取消</button>
                        <button class="btn" type="primary" @click="getUserInfo_tt">授权</button>
                    </view>
                </view>
            </view>
        </uni-popup>
        <!-- #endif -->
        <u-modal v-model="modalShow" title='支付成功' :mask-close-able="true" :closeOnClickOverlay="true"
            :show-confirm-button="false">
            <view class="x_modal">
                <view class="title">点击赠送好友</view>
                <view class="flex">
                    <view class="btn_l" @click="modalShow=false">稍后再说</view>
                    <button class="btn_r" open-type="share" @click="gogive">去赠送</button>
                </view>
            </view>
        </u-modal>
        <x-authorize v-if="show_new"
            @login="getSpecialDetail(ids.sid, ids.gift_uid || '', ids.gift_order_id || '',true,ids.invitationId || 0,ids.isJumpLive || 0)">
        </x-authorize>
        <Payment v-model="pay" ref="Payment" :other="true" :openstudent="false" :paymoney="detail.money"
            :courseId="detail.id" :deductionsPrice="detail.deductions_price" :types="payType" @nowPay="nowPay"
            @getmark="getmark" :balance="userInfo.now_money" :opencoupon="opencoupon" @couponMoney="couponMoney">
        </Payment>
        <PayIncomplete v-model="noPay" ref="PayIncomplete" :paymoney="pay_price" :other="true" @notNowPay="notNowPay"
            @cancelOrder="cancelOrder"></PayIncomplete>
        <view @click="bakvideo" v-show="scrollTop >= 234"
            style="position: fixed; right: 85rpx; bottom: 300rpx; width: 100rpx; height: 100rpx;line-height: 100rpx;text-align: center;font-size: 26rpx; background-color: #007AFF; color: #fff; border-radius: 50%; opacity: .6;">
            ↑TOP</view>
        <uni-popup ref="iospay_popup" type="center" :maskClick="false">
            <view class="iospayPopup">
                <image class="iospayPopup-colse" src="../../../static/images/yuanshi/fail.png" mode=""
                    @click="colseIosPop"></image>
                <view class="iospayPopup-title">
                    IOS用户暂不支持订阅，其它问题请添加客服咨询。
                </view>
                <view class="iospayPopup-button" @click="closeIosPopup">
                    咨询客服<text v-if="countdown != 0">({{countdown}}s)</text>
                </view>
            </view>
        </uni-popup>
        <uni-popup ref="sharePopup" :zIndex="999">
            <view style="width: 750rpx;height:100vh;text-align:center">
                <image src="@/static/images/share-f.png" mode="scaleToFill" style="width: 100%;height:100%;"
                    @click="close_share_pop"></image>
            </view>
        </uni-popup>
        <uni-popup ref="student_popup" type="center" :maskClick="false">
            <view class="cancelPopup">
                <image class="cancel-icon" src="../../../static/images/cancel-icon.png" mode=""
                    @click="closeStudentPopup"></image>
                <view class="cancel-title">{{guidedSpeech}}</view>
                <view class="cancel-button" @click="gostudent">
                    去建档
                </view>
            </view>
        </uni-popup>
        <uni-popup ref="work_popup" type="center" :maskClick="false">
            <view class="cancelPopup work">
                <image class="cancel-icon" src="../../../static/images/cancel-icon.png" mode="" @click="closeWorkPopup">
                </image>
                <view class="cancel-title work">您需要提交作业才可以解锁彩蛋哦~</view>
                <view class="cancel-title work">如已提交作业请等待审核通过</view>
                <view class="cancel-button work">
                    <view class="left-work-button" @click="goCommentSubmit(true,3)">去交作业</view>
                    <view class="right-work-button" @click="closeWorkPopup">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
    import {
        specialCouponReceive,
        specialOrderCreate
    } from '@/api/activity';
    import {
        getSpecialDetail,
        getCreatedInvitationCode,
        getSpecialCourseCatalogList, // 目录（升级版）
        getSpecialMessageLike,
        getSpecialMessageUnlike,
        getSpecialCommentAdd,
        getSpecialMessage,
        getSpecialOrderCreate,
        upSpecialOrderComputed,
        getSpecialCollect,
        getSpecialJoinLearning,
        getSpecialLearningRecords,
        getSpecialReceiveGift,
        getSpecialGiftReceive,
        getSpecialPayOrderStatuts,
        getSpecialLastOrder,
        specialOrderPay,
        specialOrderCancel,
        getPollster,
        specialUnlockSurprise
    } from '@/api/yknowledge.js'
    import {
        getTemlIds
    }
    from '@/api/user.js';
    import {
        IS_TOUTIAO_PAY,
        SHARE_ID,
        WX_KEFU_Link,
        WX_ENTERPRISE_Link,
        OPEN_IOS_PAYMENT,
        VUE_APP_URL,
        WECHAT_AUTH_BACK_URL
    } from '@/config.js';

    import {
        subscribe
    } from '@/utils/SubscribeMessage.js';
    import mpHtml from '@/components/mp-html/mp-html'
    import xDetailShowMore from '@/components/zsff/x-detail-show-more.vue';
    import xMpBtnShare from '@/components/zsff/x-mp-btn-share.vue';
    import xRecom from '@/components/zsff/x-recom.vue';
    import xPlay from '@/components/x-play/x-play.vue';
    import Payment from '@/components/PaymentUser';
    import PayIncomplete from '@/components/PayIncomplete';
    // import xComment from '@/components/x-comment/x-comment';
    import xComment from '@/components/x-comment/x-comment/x-comment2.vue';
    import xChat from '@/components/x-chat/x-chat';
    import xSwiper from '@/components/zsff/swiper.vue';
    import storage from '@/utils/storage.js';
    import eventBus from '@/utils/eventBus.js';

    import {
        mapGetters
    } from 'vuex';
    import {
        uniSelectorQueryInfo,
    } from '@/utils/uni_api.js';
    // #ifdef H5
    import {
        isWeixin
    } from '@/utils/validate.js';
    import {

    } from '@/api/user.js';
    import {
        pay
    } from '@/utils/wechat/pay.js';
    import {
        openShareAll
    } from '@/utils/wechat/share.js';
    const _isWeixin = isWeixin();
    // #endif
    // #ifdef MP
    const _isWeixin = true;
    // #endif
    import {
        toLogin,
        debounce,
        checkLogin,
        autoAuth,
        authNavigator,
        openWeChatCustomerService,
        zxauthNavigator
    } from '@/utils/common.js'

    import xCommentChat from '@/mixins/xCommentChat.js';
    export default {
        components: {
            Payment,
            PayIncomplete,
            xPlay,
            xChat,
            xComment,
            xSwiper,
            xDetailShowMore,
            xRecom,
            xMpBtnShare,
            mpHtml
        },
        mixins: [xCommentChat],
        data() {
            return {
                show_new:false, 
                guidedSpeech: '',
                openIosPayment: OPEN_IOS_PAYMENT,
                pageType: 'list',
                codetype: 0,
                scene: 0, // 默认微信场景值
                traceId: '', // 分享id
                isActiveAdmin: false, // 是否管理员
                shareImg: '',
                invitation_code: '',
                sharecode: false,
                // #ifdef H5
                speed: 25,
                // #endif
                // #ifdef MP
                speed: 50,
                // #endif
                isMute: this.$store.state.userInfo.is_mute, // 当前用户是否禁言
                videoCtx: null,
                tmplIds: ['nlD298UK462gFa5oZjL9oX-Ajk2WDWZjy_oLx4Ic5Ls'],
                message_type: 0, // 0评论 1作业
                tabList: [{
                    name: '全部',
                    id: 0
                }, {
                    name: '热点',
                    id: 1
                }, {
                    name: '精华',
                    id: 2
                }],
                isToutiaoPay: IS_TOUTIAO_PAY,

                oindex_detail: false, // 是否重读评论

                workdata: {
                    id: 0
                },
                workAdd: false, // 是否刷新局部作业区
                ids: {
                    id: null,
                    gift_uid: null,
                    gift_order_id: null,
                    gift: null,
                },
                // --------------------banner------------------------------
                detail: {
                    video: [],
                    audio: [],
                    image: [],
                    similar_recommendations: [],
                    live_info: ''
                },
                poster: '',
                shareConfig: {},
                tabLinkInfo: {},
                tabLinkInfo1: {},
                // --------------------banner------------------------------
                current: 0, // tabs组件的current值，表示当前活动的tab选项
                current1: 0, // 评论筛选初始值
                currentWork: 0, // 作业筛选初始值
                swiperCurrent: 0, // 
                cloumnNavlist: [{
                    'title': '简介'
                }, {
                    'title': '目录'
                }, {
                    'title': '评论'
                }, {
                    'title': '作业区'
                }],
                active: 0,
                is_gift: false,
                courseList: [],
                requestLoading1: false,
                page1: {
                    page: 1,
                    limit: 20,
                    more: true
                },
                unSeeId: -1, //未观看素材id
                // ------------------------评论--------------------------------
                initCalcItemScroll: 0,
                footerHidden: false,
                footerOpacity: 0,
                uid: -1,
                placeholder: '',
                inputShow: false,
                isOnlyMy: false,
                messageInfo: [],
                requestLoading: false,
                page: {
                    page: 1,
                    limit: 20,
                    more: true
                },
                // ----------------------评论--------------------------------
                // --------------------------------支付
                pay: false,
                noPay: false, // 是否有未完成支付的当前课程订单
                // #ifndef MP-TOUTIAO
                payType: ['weixin'],
                // #endif
                pay_type_num: 2, //默认2购买 1送朋友
                // #ifdef MP-WEIXIN
                from: 'routine',
                // #endif
                // #ifdef MP-TOUTIAO
                isAuto: true,
                from: 'bytedance',
                payType: ['bytedance'],
                // #endif
                // #ifdef H5
                from: _isWeixin ? 'weixin' : 'weixinh5',
                // #endif
                scrollTop: 0,
                learnRecord: 0,
                giftOrderId: '', //礼物订单号
                modalShow: false,
                chatInfo: {},
                platform: '',
                adjustPosition: false,
                mark: '',
                countdown: '',
                s: '',
                timer: null, //重复执行
                setT: null,
                cleartime: 0,
                oldBringsNew: false,
                opencoupon: true, // 是否使用优惠券
                coupon: {},
                is_live_info: 0,
                StudentProfile: true,
                isSupply: true, // 0 false需要补充学员档案 1 true不需要补充学员档案
                finishStudent: false, // 是否完成学员档案补充
                pay_price: 0, // 查询最后订单支付金额
                lastOrderId: 0, // 最后未支付订单号
                flag: [],
                show_old: false, // 显示旧版目录
                videoCourse: { // 最后课程观看
                    courseLastId: 0, // 合集内素材id
                    videoCourseId: 0 //合集id
                },
                testdata: ''
            }
        },
        computed: {
            ...mapGetters(['userInfo', 'isPopPlay']),
            loadMoreStatus() {
                if (this.current === this.cloumnNavlist.length - 1) {
                    // 评论相关
                    const {
                        more
                    } = this.page;
                    return more ? 'loadmore' : 'nomore'
                } else {
                    // 目录相关
                    const {
                        more
                    } = this.page1;
                    return more ? 'loadmore' : 'nomore'
                }
            }
        },
        watch: {
            // isPopPlay: function(newVal, oldVal) {
            //     let _this = this;
            //     if (newVal) {
            //         this.$nextTick(() => {
            //             _this.$store.commit('POPPLAY_TRUE');
            //             _this.isPopPlays = true;
            //         });
            //     } else {
            //         _this.$store.commit('POPPLAY_FALSE');
            //         _this.isPopPlays = false;
            //     }
            // }
        },
        methods: {

            getContinuousIndex(groupIndex, itemIndex) {
                let continuousIndex = 1;
                if (groupIndex != 0) {
                    for (let i = 0; i <= groupIndex; i++) {
                        continuousIndex += this.courseList[(i ? (i - 1) : i)].number;
                        if (i == 0) {
                            continuousIndex = continuousIndex - this.courseList[0].number
                        }
                    }
                }
                return continuousIndex + itemIndex;
            },
            openDetail(index) {
                this.flag[index] = !this.flag[index];
                this.$forceUpdate();
            },
            getPollster(type, id) {
                let that = this;
                let param = {
                    type: type,
                    id: id,
                    from: that.from,
                }
                getPollster(param).then(res => {
                        // console.log('res',res)
                        if (res.data.id) {
                            this.guidedSpeech = res.data.guided_speech;
                            if (res.data.user_pollster_status == 0) {
                                this.isSupply = false;
                            }
                        }
                    })
                    .catch(err => {
                        that.$showToast(err.msg || err);
                    });
            },
            closeBalancePopups() {
                this.$refs.balancePopups.close()
            },
            close_share_pop() {
                this.$refs.sharePopup.close();
            },
            goshare() {
                // #ifdef MP
                console.log('小程序跳转')
                this.goPages('/pages/yknowledge/course/detail_webview?url=' + VUE_APP_URL + '/h5/course/share&id=' +
                    this.detail.id + '&activityId=' + this.detail.activity_id, true)

                // #endif
                // #ifdef H5
                console.log('h5跳转')
                this.goPages('/pages/yknowledge/course/detail_share?id=' + this.detail.id + '&activityId=' + this.detail
                    .activity_id, true)
                // #endif
            },
            openWeChat() {
                openWeChatCustomerService(WX_KEFU_Link, WX_ENTERPRISE_Link, true, this.detail.title,
                    'pages/yknowledge/course/detail.html?id=0&sid=' + this.ids.sid, this.shareImg)
            },
            getmark(e) {
                this.mark = e;
                // console.log('传值备注111', this.mark)
            },
            getScene() {
                let that = this;
                let opt = wx.getEnterOptionsSync();
                this.scene = opt.scene;
            },
            getTraceId() {
                let that = this;
                let traceId = '';
                return new Promise((resolve, reject) => {
                    wx.checkBeforeAddOrder({
                        success(res) {
                            traceId = res.data.traceId;
                            // console.log('获取分享id', traceId)
                            resolve(traceId);
                        },
                        fail(err) {
                            reject(err);
                            // console.log('获取分享id失败', err)
                        }
                    })
                });
            },
            gogive() {
                this.modalShow = false;
                this.shareCourse(false)
            },
            shareCourse(type) {
                this.sharecode = type
            },
            openAdmin() {
                this.$refs.popupAdmin.open()
            },
            closeAdmin() {
                this.$refs.popupAdmin.close()
            },
            openpopupAdmin(type) {
                this.codetype = type;
                this.getCreatedInvitationCode(this.detail.id, type) // 一人多码
                this.$refs.popupAdmin.close()
                this.$refs.popupAdmin1.open()
            },
            closeAdmin1() {
                this.getSpecialDetail(this.ids.sid, this.ids.gift_uid || '', this.ids.gift_order_id || '', false, this
                    .ids.invitationId || 0, this.ids.isJumpLive || 0);
                this.shareCourse(false)
                this.$refs.popupAdmin1.close()
            },

            // #ifdef MP-TOUTIAO
            close() {
                this.$refs.popup.close();
                this.$store.commit('HIDE_AUTH_POPUP_SHOW');
            },
            open() {
                this.$hideToast();
                // #ifdef MP
                if (this.$store.state.isFromTimeline) {
                    this.$showToast('请前往小程序体验完整功能')
                    this.close()
                    return
                }
                this.$refs.popup.open();
                // #endif
            },
            getUserInfo_tt() {
                let _this = this;
                this.isAuto && uni.showLoading({
                    title: '正在登录中'
                });
                let code;
                uni.login({
                    success(res) {
                        code = res.code
                    },
                    fail(err) {
                        uni.hideLoading();
                    }
                });
                uni.getUserProfile({
                    success(res) {
                        _this.close();
                        res.code = code;
                        res.version = false;
                        console.log('res：', res)
                        _this.getLoginInfo(res);
                    },
                    fail(err) {
                        uni.hideLoading();
                        _this.open();
                    },
                });
            },
            getLoginInfo(userInfo) {
                toLogin(userInfo, function(res) {
                    uni.hideLoading();
                });
            },
            saveimg(url) {
                let that = this;
                uni.downloadFile({
                    url: url,
                    header: {
                        "content-type": "application/json",
                    },
                    success: (res) => {
                        console.log('res', res)
                        if (res.statusCode === 200) {
                            uni.saveImageToPhotosAlbum({
                                filePath: res.tempFilePath,
                                success(res) {
                                    //console.log(res);
                                    uni.showToast({
                                        title: '已保存相册',
                                        icon: 'success',
                                        duration: 2000
                                    });
                                },
                                fail(err) {
                                    if (err.errMsg == 'saveImageToPhotosAlbum:fail auth deny' || err
                                        .errMsg.indexOf('deny') != -1) {
                                        uni.showModal({
                                            title: '您已经拒绝保存图片到相册',
                                            content: '请先打开相册设置',
                                            confirmText: '去允许',
                                            success: function(res1) {
                                                if (res1.cancel) {
                                                    uni.showToast({
                                                        title: '已取消授权',
                                                        icon: 'none',
                                                        duration: 1000
                                                    })
                                                    return
                                                } else if (res1.confirm) {
                                                    uni.openSetting({
                                                        success: function(
                                                            res2) {
                                                            console.log(
                                                                'res2',
                                                                res2)
                                                            // #ifndef MP-TOUTIAO
                                                            if (res2
                                                                .authSetting[
                                                                    "scope.writePhotosAlbum"
                                                                ]) {
                                                                uni.showToast({
                                                                    title: '已开启相册设置',
                                                                    icon: 'none',
                                                                    duration: 2000
                                                                })
                                                            } else {
                                                                uni.showToast({
                                                                    title: '未开启相册设置',
                                                                    icon: 'none',
                                                                    duration: 1000
                                                                })
                                                            }
                                                            // #endif
                                                            // #ifdef MP-TOUTIAO
                                                            if (res2
                                                                .authSetting[
                                                                    "scope.album"
                                                                ]) {
                                                                uni.showToast({
                                                                    title: '已开启相册设置',
                                                                    icon: 'none',
                                                                    duration: 2000
                                                                })
                                                            } else {
                                                                uni.showToast({
                                                                    title: '未开启相册设置',
                                                                    icon: 'none',
                                                                    duration: 1000
                                                                })
                                                            }
                                                            // #endif
                                                        },
                                                        fail: function(err) {
                                                            console.log(
                                                                'xxx',
                                                                err)
                                                        }
                                                    })
                                                }
                                            },
                                        })
                                    }
                                }
                            });
                        }
                    }
                })
            },
            // #endif

            getSonValue(res) {
                this.videoCtx = res;
            },
            bakvideo() {
                uni.pageScrollTo({
                    selector: '#swiperTop',
                    scrollTop: 0,
                    duration: 300
                });
            },
            tabClick(item, index) {
                this.current1 = index;
                const {
                    top,
                    height
                } = this.tabLinkInfo1;
                // console.log(this.scrollTop, top)
                if (this.scrollTop < top) {
                    // #ifdef MP
                    uni.pageScrollTo({
                        scrollTop: top,
                        duration: 300
                    });
                    // #endif
                    // #ifdef H5
                    uni.pageScrollTo({
                        scrollTop: top + 44,
                        duration: 300
                    });
                    // #endif
                }
                this.getSpecialComment(true)
            },
            tabClickWork(item, index) {
                this.currentWork = index;
                const {
                    top,
                    height
                } = this.tabLinkInfo1;
                if (this.scrollTop < top) {
                    // #ifdef MP
                    uni.pageScrollTo({
                        scrollTop: top,
                        duration: 300
                    });
                    // #endif
                    // #ifdef H5
                    uni.pageScrollTo({
                        scrollTop: top + 44,
                        duration: 300
                    });
                    // #endif
                }
                this.getSpecialComment(true)
            },
            getSpecialComment: debounce(function(init) {
                init && this.initInfo();
                const {
                    sid: special_id,
                    id: source_id
                } = this.ids;
                if (!this.page.more || this.requestLoading) return;
                this.requestLoading = true;
                getSpecialMessage({
                    ...this.page,
                    special_id,
                    source_id,
                    sort: this.message_type == 1 ? this.tabList[this.currentWork].id : this.tabList[this
                        .current1].id,
                    message_type: this.message_type
                }).then(res => {
                    let info = res.data;
                    info.forEach((item, index) => {
                        if (typeof item.comment === 'string') {
                            item.content = [item.comment];
                        }
                    });
                    this.messageInfo = this.messageInfo.concat(info);
                    // console.log('课程评论-', this.messageInfo)
                    this.messageInfo.forEach(item => {
                        if (item.comment.length > 90) {
                            item.isMore = true
                            item.contentAll = true
                            if (this.ids.open_comment) {
                                item.contentAll = false
                            }
                        } else {
                            item.isMore = false
                            item.contentAll = false
                        }

                        if (item.children_reply.length >= 1) {
                            item.children_reply.forEach(item1 => {
                                if (item1.comment.length > 36) {
                                    item1.isMore = true
                                    item1.contentAll = true
                                } else {
                                    item1.isMore = false
                                    item1.contentAll = false
                                }

                                if (item1.children_reply.length >= 1) {
                                    item1.children_reply.forEach(item2 => {
                                        if (item2.comment.length > 36) {
                                            item2.isMore = true
                                            item2.contentAll = true
                                        } else {
                                            item2.isMore = false
                                            item2.contentAll = false
                                        }
                                    })
                                }

                            })
                        }

                    })
                    this.page.more = res.data.length === this.page.limit;
                    this.page.page++;
                    this.requestLoading = false;

                })
            }, 200, true),
            goCommentSubmit(status, type) {
                let that = this;
                this.oindex_detail = false;
                this.workdata.id = 0;
                if (type == 1) {
                    if (this.isMute === 1) {
                        return this.$showToast('已被禁言');
                    }
                    // if (status == true) {
                    //     this.goApply()
                    // }
                    const {
                        sid,
                        id
                    } = this.ids;
                    this.goPages('/pages/yknowledge/comment/submit?id=' + id + '&sid=' + sid, true)
                } else {
                    if (this.detail.pay_type === 1 && !this.detail.is_pay) {
                        return this.$showToast('仅学员可提交作业');
                    } else {
                        if (this.isMute === 1) {
                            return this.$showToast('已被禁言');
                        }
                        // #ifdef MP-WEIXIN
                        uni.getSetting({
                            withSubscriptions: true,
                            success(res) {
                                console.log('获取设置res++', res.subscriptionsSetting.mainSwitch)
                                if (res.subscriptionsSetting.mainSwitch) {
                                    uni.requestSubscribeMessage({
                                        tmplIds: that.tmplIds,
                                        success(res) {},
                                        fail(err) {},
                                        complete: function(res) {
                                            console.log('获取设置res--', res[
                                                'nlD298UK462gFa5oZjL9oX-Ajk2WDWZjy_oLx4Ic5Ls'
                                            ])
                                            if (res[
                                                    'nlD298UK462gFa5oZjL9oX-Ajk2WDWZjy_oLx4Ic5Ls'
                                                    ] ==
                                                'accept') {
                                                // reject
                                                const {
                                                    sid,
                                                    id
                                                } = that.ids;
                                                if (type == 3) {
                                                    that.closeWorkPopup()
                                                }
                                                that.goPages(
                                                    '/pages/yknowledge/comment/submitWork?id=' +
                                                    id + '&sid=' + sid, true)
                                            } else {
                                                uni.showModal({
                                                    title: '您已经拒绝课程解锁成功通知',
                                                    content: '请先打开消息通知设置',
                                                    confirmText: '去允许',
                                                    success: function(res1) {
                                                        if (res1.cancel) {
                                                            uni.showToast({
                                                                title: '已取消',
                                                                icon: 'none',
                                                                duration: 1000
                                                            })
                                                            return
                                                        } else if (res1.confirm) {
                                                            uni.openSetting({
                                                                success: function(
                                                                    res2) {
                                                                    console
                                                                        .log(
                                                                            'res2',
                                                                            res2
                                                                            )
                                                                },
                                                                fail: function(
                                                                    err) {
                                                                    console
                                                                        .log(
                                                                            'xxx',
                                                                            err
                                                                            )
                                                                }
                                                            })
                                                        }
                                                    },
                                                })
                                            }
                                        },
                                    })
                                } else { // 用户没有点击“总是保持以上，不再询问”则每次都会调起订阅消息
                                    uni.openSetting({ // 打开设置页
                                        success(res) {
                                            // console.log(res.authSetting) 
                                        }
                                    });
                                }
                            },
                            fail(err) {
                                console.log('获取设置err', err)
                            },
                        })
                        // #endif
                        // #ifndef MP-WEIXIN
                        const {
                            sid,
                            id
                        } = this.ids;
                        if (type == 3) {
                            this.closeWorkPopup()
                        }
                        this.goPages('/pages/yknowledge/comment/submitWork?id=' + id + '&sid=' + sid, true)
                        // #endif
                    }
                }
            },
            previewImage(urls, current) {
                // console.log(urls, current)
                uni.previewImage({
                    urls: urls.map(item => item.name),
                    current: current //地址需为https
                });
            },
            getSpecialCouponReceive(id, activity_id, link_invite_id) {
                specialCouponReceive({
                    id: id,
                    activity_id: activity_id,
                    link_invite_id: link_invite_id,
                }).then(res => {
                    if (res.status == 200 && res.msg != '') {
                        this.$showToast(res.msg)
                    }
                }).catch(err => {
                    // console.log('领取优惠券',res)
                    console.log('领取优惠券2', err)
                    if (err.msg != '') {
                        this.$showToast(err.msg)
                    }
                })
            },
            getSpecialDetail(id, gift_uid, gift_order_id, reset = false, invitationId = 0, isJumpLive = 0) {
                if (this.oindex_detail) {
                    this.oindex_detail = false;
                }
                if (reset) {
                    this.courseList = [];
                    this.flag = [];
                    this.page1 = {
                        page: 1,
                        limit: 20,
                        more: true
                    }
                    this.$refs.Payment.getUser();
                }
                let param = {
                    id,
                    gift_uid,
                    gift_order_id,
                    from: this.from,
                    platform: this.platform
                }
                if (isJumpLive) {
                    param.is_jump_live = 1;
                }
                getSpecialDetail(param).then(res => {
                    const {
                        title,
                        id,
                        image,
                        banner = [],
                        type
                    } = res.data;
                    let detail = res.data;
                    console.log('详情---', detail)
                    this.getPollster(3, id)
                    this.shareImg = res.data.image;
                    this.$updateTitle(detail.title);
                    detail.money1 = Number(detail.money)
                    // detail.setmoney = Number(detail.money)
                    this.poster = image;
                    if (banner && banner.length) {
                        // console.log(banner)
                        if (typeof banner === 'string') {
                            detail.image = [banner];
                        } else {
                            detail.image = banner
                        }
                    } else {
                        detail.image = []
                    }
                    detail.video = [];
                    if (type === 3 && detail.link) {
                        if (typeof detail.link === 'string') {
                            detail.video = [detail.link]
                        } else {
                            detail.video = detail.link
                        }
                    }
                    this.detail = detail;
                    if(this.detail.pay_type===1&&!detail.is_pay){}else {
                        // 已购买的直接跳转目录
                        this.current = 1;
                        this.swiperCurrent = 1;
                    }
                    
                    if (this.detail.is_activity == 1) {
                        this.oldBringsNew = true
                    }
                    this.getSpecialCourseList();
                    if (this.ids.tabindex) {
                        this.tabsChange(Number(this.ids.tabindex))
                    }
                    // console.log('详情', this.detail)
                    if (this.$store.state.userInfo.is_official_role == 1) {
                        this.isActiveAdmin = true;
                        // console.log('是否管理', this.isActiveAdmin)
                    }
                    this.initInfo();
                    this.getSpecialComment()
                    this.shareConfig = {
                        desc: title,
                        title,
                        link: `/pages/yknowledge/course/detail?share=1&id=0&sid=${this.ids.sid }`,
                        imgUrl: image
                    };
                    // #ifdef H5
                    openShareAll(this.shareConfig);
                    // #endif

                    // 老带新领取优惠券
                    if (invitationId) {
                        this.getSpecialCouponReceive(this.detail.id, this.detail.activity_id, this.ids
                            .invitationId)
                    }
                    // 直播间领取优惠
                    if (isJumpLive) {
                        this.getSpecialCouponReceive(this.detail.id, this.detail.live_activity_id, 0)
                    }
                }).catch(err => {
                    setTimeout(() => {
                        this.$navigator(-1)
                    }, 500)
                })
            },
            getCreatedInvitationCode(id, type) {
                let data = {
                    id: id,
                    type: type //类型：1多人一码、 2一人一码
                }
                getCreatedInvitationCode(data).then(res => {
                    this.invitation_code = res.data.invitation_code;
                    this.shareConfig = {
                        desc: '请领取课程权益-当日有效',
                        title: '请领取课程权益-当日有效',
                        link: '/pages/yknowledge/course/receive-equity?id=' + this.ids.sid + '&code=' + this
                            .invitation_code + '&type=' + type,
                        imgUrl: this.shareImg
                    };
                    if (type == 2) {
                        this.shareConfig.desc = '请领取课程权益';
                        this.shareConfig.title = '请领取课程权益'
                    }
                    console.log('shareConfig', this.shareConfig)
                }).catch(err => {
                    // setTimeout(() => {
                    //     this.$navigator(-1)
                    // }, 500)
                })
            },
            resetSpecialCourseList() {
                if (this.detail.count > 0) {
                    this.courseList = [];
                    this.requestLoading1 = false;
                    this.flag = [];
                    this.page1 = {
                        page: 1,
                        limit: 20,
                        more: true
                    }
                    this.getSpecialCourseList()
                }
            },
            getSpecialCourseList: debounce(function() {
                // if (!this.page1.more || this.requestLoading1) return;
                // this.requestLoading1 = true;

                // getSpecialCourseCatalogList
                // getSpecialCourseList
                getSpecialCourseCatalogList({
                    id: this.ids.sid,
                }).then(res => {
                    if (res.data.length) {
                        console.log('获取升级版目录', res.data.length)
                        if (res.data[0].title == '不设置目录' || !res.data[0].title) {
                            this.show_old = true;
                            this.courseList = res.data[0].course_list;
                            if (this.unSeeId < 0) {
                                let unSeeId = -1;
                                res.data[0].course_list.forEach((item, index) => {
                                    if (unSeeId < 0 && !item.taskTrue) {
                                        return unSeeId = item.id
                                    }
                                })
                                this.unSeeId = unSeeId < 0 ? res.data[0].course_list[0].id : unSeeId;
                            }
                            this.page1.more = res.data[0].course_list.length === this.page1.limit;
                            for (let i = 0; i < this.courseList.length; i++) {
                                this.courseList[i].is_eggs_class = false
                                if (this.courseList[i].type == 4) {
                                    this.detail.notice_time = this.courseList[i].notice_time;
                                    this.is_live_info = this.courseList[i].is_live_info;
                                    this.detail.live_info = this.courseList[i].live_info;
                                    this.detail.live_activity_id = this.courseList[i].live_activity_id;
                                } else {
                                    this.is_live_info = 0;
                                    this.detail.live_info = '';
                                }
                            }
                            if (this.detail.is_easter_eggs) {
                                // 有彩蛋课
                                if (this.detail.get_easter_eggs_status) {
                                    this.courseList[this.courseList.length - 1].is_eggs_class = true
                                }
                            }
                        } else {
                            this.courseList = res.data;
                            if (this.unSeeId < 0) {
                                let unSeeId = -1;
                                res.data.forEach((item, index) => {
                                    item.course_list.forEach((items, indexs) => {
                                        if (unSeeId < 0 && !items.taskTrue) {
                                            return unSeeId = items.id
                                        }
                                    })
                                })
                                this.unSeeId = unSeeId < 0 ? res.data[0].course_list[0].id : unSeeId;
                            }
                            for (let i = 0; i < this.courseList.length; i++) {
                                for (let j = 0; j < this.courseList[i].course_list.length; j++) {
                                    if (this.courseList[i].course_list[j].type == 4) {
                                        this.detail.notice_time = this.courseList[i].course_list[j]
                                            .notice_time;
                                        this.is_live_info = this.courseList[i].course_list[j]
                                            .is_live_info;
                                        this.detail.live_info = this.courseList[i].course_list[j]
                                            .live_info;
                                        this.detail.live_activity_id = this.courseList[i].course_list[j]
                                            .live_activity_id;
                                    } else {
                                        this.is_live_info = 0;
                                        this.detail.live_info = '';
                                    }
                                }
                                this.flag.push(true)
                            }
                            if (this.detail.is_easter_eggs) {
                                // 有彩蛋课
                                if (this.detail.get_easter_eggs_status) {
                                    for (let i = 0; i < this.courseList.length; i++) {
                                        for (let j = 0; j < this.courseList[i].course_list
                                            .length; j++) {
                                            this.courseList[i].course_list[j].is_eggs_class = false
                                        }
                                    }
                                    this.courseList[this.courseList.length - 1].course_list[this
                                        .courseList[this.courseList.length - 1].course_list.length -
                                        1].is_eggs_class = true;
                                }
                            }
                        }
                    }
                }).catch(err => {
                    this.requestLoading1 = false;
                });
            }, 200, true),

            // 客服二维码弹窗开关
            addChatGroup() {
                this.$refs.popup1.open()
            },
            addChatGroupClose() {
                this.$refs.popup1.close()
            },
            // tabs通知swiper切换
            tabsChange(index) {
                let _this = this;
                const {
                    top,
                    height
                } = this.tabLinkInfo;
                if (this.scrollTop < top) {
                    // #ifdef MP
                    uni.pageScrollTo({
                        scrollTop: top,
                        duration: 300
                    });
                    // #endif
                    // #ifdef H5
                    uni.pageScrollTo({
                        scrollTop: top + 44,
                        duration: 300
                    });
                    // #endif
                }
                _this.current = index;
                _this.swiperCurrent = index
                // console.log('==========', index)
                _this.inputShow = false;
                _this.footerHidden = false;
                if (_this.current == 2) {
                    _this.message_type = 0;
                    this.getSpecialComment(true)
                }
                if (_this.current == 3) {
                    _this.message_type = 1;
                    this.getSpecialComment(true)
                }

            },
            swiperChange(e) {
                // console.log('---------', e.detail.current)
                this.current = e.detail.current;
                this.swiperCurrent = e.detail.current
                if (this.current == 2) {
                    this.message_type = 0;
                    this.getSpecialComment(true)
                }
                if (this.current == 3) {
                    this.message_type = 1;
                    this.getSpecialComment(true)
                }
            },
            // ----------------------------------------------------评论--------------------------------
            updateData() {
                this.messageInfo = [];
                this.getSpecialMessage(true)
            },
            initInfo() {
                this.requestLoading = false;
                this.messageInfo = [];
                this.page = {
                    page: 1,
                    limit: 10,
                    more: true
                }
            },
            getSpecialMessage: debounce(function(init) {
                init && this.initInfo();
                if (!this.page.more || this.requestLoading) return;
                this.requestLoading = true;
                let obj = {
                    special_id: this.ids.sid,
                    source_id: 0,
                    page: this.page.page,
                    limit: this.page.limit,
                    message_type: this.message_type
                };
                getSpecialMessage(obj).then(res => {
                        let info = res.data;
                        info.forEach((item, index) => {
                            if (typeof item.comment === 'string') {
                                item.content = [item.comment];
                            }
                        });
                        this.messageInfo = this.messageInfo.concat(info);
                        // console.log('课程评论-', this.messageInfo)
                        this.messageInfo.forEach(item => {
                            if (item.comment.length > 90) {
                                item.isMore = true
                                item.contentAll = true
                                if (this.ids.open_comment) {
                                    item.contentAll = false
                                }
                            } else {
                                item.isMore = false
                                item.contentAll = false
                            }

                            if (item.children_reply.length >= 1) {
                                item.children_reply.forEach(item1 => {
                                    if (item1.comment.length > 36) {
                                        item1.isMore = true
                                        item1.contentAll = true
                                    } else {
                                        item1.isMore = false
                                        item1.contentAll = false
                                    }

                                    if (item1.children_reply.length >= 1) {
                                        item1.children_reply.forEach(item2 => {
                                            if (item2.comment.length > 36) {
                                                item2.isMore = true
                                                item2.contentAll = true
                                            } else {
                                                item2.isMore = false
                                                item2.contentAll = false
                                            }
                                        })
                                    }

                                })
                            }


                        })
                        this.page.more = res.data.length === this.page.limit;
                        this.page.page++;
                        this.requestLoading = false;
                    })
                    .catch(err => {

                        this.requestLoading = false;
                    });
            }, 200, true),
            //-------------------------------------- 评论相关  注意 mixin
            showInput(info, placeholder) {
                this.chatInfo = info;
                if (this.current != 3) {
                    this.footerHidden = true;
                    this.inputShow = true;
                }
                this.placeholder = placeholder;
            },
            submitComment(val) {
                const info = {
                    ...this.getSubmitInfo(),
                    comment: val
                };
                // #ifndef MP-TOUTIAO
                info.from = 'routine'
                // #endif
                // #ifdef MP-TOUTIAO
                info.from = 'bytedance'
                // #endif
                getSpecialCommentAdd(info).then(res => {
                    this.updateAddInfo(res.data)
                    this.inputShow = false;
                    this.$showToast('评论提交成功')
                    // this.getSpecialComment(true)
                }).catch(err => {
                    this.$showToast(err.msg || err);
                });
            },
            sendSuccess() {
                this.inputShow = false;
                this.footerHidden = false;
                this.opacity = 1;
            },
            //-------------------------------------- 评论相关
            onreachBottom() {
                // console.log('底部')
                if (this.detail.count >= 1 && this.current == 2) {
                    console.log('加载评价')
                    this.getSpecialComment()
                }
                if (this.current == 3) {
                    console.log('加载作业')
                    this.getSpecialComment()
                }
                // if((this.detail.count >= 1 && this.current == 1)){
                //     // console.log('加载目录')
                //     this.getSpecialCourseList()
                // }
            },
            goPlay(items, types) {
                console.log('items', items)
                const {
                    special_id,
                    id,
                    live_status,
                    live_id,
                    type,

                    pay_status,
                    is_free,
                    is_trial
                } = items;
                const {
                    pay_type,
                    is_pay
                } = this.detail;
                // 如果是免费或者试听
                console.log('is_trial',is_trial)
                console.log('is_free',is_free)
                if (is_trial || is_free) {
                    if (this.isSupply) {
                        if (type == 4) {
                            // #ifndef MP-WEIXIN
                            let obj = {
                                duration: 3500,
                            }
                            this.$showToast('请前往「微信小程序」进入直播间', 'none', obj)
                            // #endif
                            // #ifdef MP-WEIXIN
                            if (live_status == 0) {
                                this.$refs.balancePopups.open()
                            } else {
                                items.is_watch = 1; //已观看
                                this.goPages('plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=' +
                                    live_id, true)
                            }
                            // #endif
                        } else {
                            if (this.detail.is_easter_eggs) {
                                if (types == 2) {
                                    if (items.is_eggs_class) {
                                        if (this.detail.get_easter_eggs_condition) {
                                            // 交作业可领取彩蛋
                                            if (!this.detail.user_work_submit_status) {
                                                // 作业未提交 弹出提交作业
                                                this.openWorkPopup()
                                            }
                                            if (this.detail.user_work_submit_status == 1) {
                                                // 作业已提交 弹出彩蛋已解锁
                                                if (!this.detail.get_easter_eggs_status) {
                                                    // 未领取并不限制彩蛋领取条件
                                                    specialUnlockSurprise({
                                                        special_id: this.ids.sid,
                                                        source_id: 0,
                                                        category: 'easter_egg'
                                                    }).then(res => {
                                                        this.detail.get_easter_eggs_status = 1;
                                                        this.$showToast('彩蛋已解锁')
                                                        setTimeout(() => {
                                                            items.is_watch = 1; //已观看
                                                            this.workdata.id = 0; // 初始化作业区刷新

                                                            this.goPages(
                                                                `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                                            )
                                                        }, 1000)
                                                    })

                                                } else {
                                                    this.$showToast('彩蛋已解锁')
                                                    setTimeout(() => {
                                                        items.is_watch = 1; //已观看
                                                        this.workdata.id = 0; // 初始化作业区刷新
                                                        this.oindex_detail = false;
                                                        this.goPages(
                                                            `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                                        )

                                                    }, 1000)
                                                }
                                            }
                                        } else {
                                            // （不限制） 不需提交作业 也可领彩蛋
                                            if (!this.detail.get_easter_eggs_status) {
                                                // 未领取并不限制彩蛋领取条件
                                                specialUnlockSurprise({
                                                    special_id: this.ids.sid,
                                                    source_id: 0,
                                                    category: 'easter_egg'
                                                }).then(res => {
                                                    this.detail.get_easter_eggs_status = 1;
                                                    this.$showToast('彩蛋已解锁')
                                                    setTimeout(() => {
                                                        items.is_watch = 1; //已观看
                                                        this.workdata.id = 0; // 初始化作业区刷新
                                                        this.oindex_detail = false;
                                                        this.goPages(
                                                            `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                                        )
                                                    }, 1000)
                                                })
                                            } else {
                                                this.$showToast('彩蛋已解锁')
                                                setTimeout(() => {
                                                    items.is_watch = 1; //已观看
                                                    this.workdata.id = 0; // 初始化作业区刷新
                                                    this.oindex_detail = false;
                                                    this.goPages(
                                                        `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                                    )
                                                }, 1000)
                                            }
                                        }

                                    } else {
                                        items.is_watch = 1; //已观看
                                        this.goPages(
                                            `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                        )
                                    }
                                }
                                if (types == 1) {
                                    if (items.is_eggs_class) {
                                        if (this.detail.get_easter_eggs_condition) {
                                            // 需提交作业才可领彩蛋
                                            if (!this.detail.user_work_submit_status) {
                                                this.openWorkPopup()
                                            }
                                            if (this.detail.user_work_submit_status == 1) {
                                                if (!this.detail.get_easter_eggs_status) {
                                                    specialUnlockSurprise({
                                                        special_id: this.ids.sid,
                                                        source_id: 0,
                                                        category: 'easter_egg'
                                                    }).then(res => {
                                                        this.detail.get_easter_eggs_status = 1;
                                                        this.$showToast('彩蛋已解锁')
                                                        setTimeout(() => {
                                                            items.is_watch = 1; //已观看
                                                            this.goPages(
                                                                `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                                            )
                                                        }, 1000)
                                                        console.log('解锁彩蛋')
                                                    })
                                                } else {
                                                    this.$showToast('彩蛋已解锁')
                                                    setTimeout(() => {
                                                        items.is_watch = 1; //已观看
                                                        this.goPages(
                                                            `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                                        )
                                                    }, 1000)
                                                }
                                            }
                                        } else {
                                            // 不限制交作业 即可领彩蛋
                                            if (!this.detail.get_easter_eggs_status) {
                                                specialUnlockSurprise({
                                                    special_id: this.ids.sid,
                                                    source_id: 0,
                                                    category: 'easter_egg'
                                                }).then(res => {
                                                    this.detail.get_easter_eggs_status = 1;
                                                    this.$showToast('彩蛋已解锁')
                                                    setTimeout(() => {
                                                        items.is_watch = 1; //已观看
                                                        this.goPages(
                                                            `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                                        )
                                                    }, 1000)
                                                    console.log('解锁彩蛋')
                                                })
                                            } else {
                                                this.$showToast('彩蛋已解锁')
                                                setTimeout(() => {
                                                    items.is_watch = 1; //已观看
                                                    this.goPages(
                                                        `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                                    )
                                                }, 1000)
                                            }
                                        }

                                    } else {
                                        items.is_watch = 1; //已观看
                                        this.goPages(
                                            `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                        )
                                    }
                                }
                            } else {
                                items.is_watch = 1; //已观看
                                this.goPages(
                                    `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`)
                            }
                        }
                    } else {
                        this.openStudentPopup()
                    }
                } else {
                    
                    // 免费课程但目录收费
                    // if(pay_type == 0 && is_pay == 1){
                    //     this.$showToast('当前暂不可试听')
                    //     return;
                    // }
                    
                    // 收费的
                    if (pay_type === 1 == 1 && is_pay === 0) return this.$showToast((this.openIosPayment && this
                        .platform == 'ios') ? '请先订阅~' : '请先购买~');
                        
                    if (this.isSupply) {
                        if (type == 4) {
                            // #ifndef MP-WEIXIN
                            let obj = {
                                duration: 3500,
                            }
                            this.$showToast('请前往「微信小程序」进入直播间', 'none', obj)
                            // #endif
                            // #ifdef MP-WEIXIN
                            if (live_status == 0) {
                                this.$refs.balancePopups.open()
                            } else {
                                items.is_watch = 1; //已观看
                                this.goPages('plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=' +
                                    live_id, true)
                            }
                            // #endif
                        } else {
                            if (this.detail.is_easter_eggs) {
                                if (types == 2) {
                                    if (items.is_eggs_class) {
                                        if (this.detail.get_easter_eggs_condition) {
                                            // 交作业可领取彩蛋
                                            if (!this.detail.user_work_submit_status) {
                                                // 作业未提交 弹出提交作业
                                                this.openWorkPopup()
                                            }
                                            if (this.detail.user_work_submit_status == 1) {
                                                // 作业已提交 弹出彩蛋已解锁
                                                if (!this.detail.get_easter_eggs_status) {
                                                    // 未领取并不限制彩蛋领取条件
                                                    specialUnlockSurprise({
                                                        special_id: this.ids.sid,
                                                        source_id: 0,
                                                        category: 'easter_egg'
                                                    }).then(res => {
                                                        this.detail.get_easter_eggs_status = 1;
                                                        this.$showToast('彩蛋已解锁')
                                                        setTimeout(() => {
                                                            items.is_watch = 1; //已观看
                                                            this.workdata.id = 0; // 初始化作业区刷新

                                                            this.goPages(
                                                                `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                                            )
                                                        }, 1000)
                                                    })

                                                } else {
                                                    this.$showToast('彩蛋已解锁')
                                                    setTimeout(() => {
                                                        items.is_watch = 1; //已观看
                                                        this.workdata.id = 0; // 初始化作业区刷新
                                                        this.oindex_detail = false;
                                                        this.goPages(
                                                            `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                                        )

                                                    }, 1000)
                                                }
                                            }
                                        } else {
                                            // （不限制） 不需提交作业 也可领彩蛋
                                            if (!this.detail.get_easter_eggs_status) {
                                                // 未领取并不限制彩蛋领取条件
                                                specialUnlockSurprise({
                                                    special_id: this.ids.sid,
                                                    source_id: 0,
                                                    category: 'easter_egg'
                                                }).then(res => {
                                                    this.detail.get_easter_eggs_status = 1;
                                                    this.$showToast('彩蛋已解锁')
                                                    setTimeout(() => {
                                                        items.is_watch = 1; //已观看
                                                        this.workdata.id = 0; // 初始化作业区刷新
                                                        this.oindex_detail = false;
                                                        this.goPages(
                                                            `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                                        )
                                                    }, 1000)
                                                })
                                            } else {
                                                this.$showToast('彩蛋已解锁')
                                                setTimeout(() => {
                                                    items.is_watch = 1; //已观看
                                                    this.workdata.id = 0; // 初始化作业区刷新
                                                    this.oindex_detail = false;
                                                    this.goPages(
                                                        `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                                    )
                                                }, 1000)
                                            }
                                        }

                                    } else {
                                        items.is_watch = 1; //已观看
                                        this.goPages(
                                            `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                        )
                                    }
                                }
                                if (types == 1) {
                                    if (items.is_eggs_class) {
                                        if (this.detail.get_easter_eggs_condition) {
                                            // 需提交作业才可领彩蛋
                                            if (!this.detail.user_work_submit_status) {
                                                this.openWorkPopup()
                                            }
                                            if (this.detail.user_work_submit_status == 1) {
                                                if (!this.detail.get_easter_eggs_status) {
                                                    specialUnlockSurprise({
                                                        special_id: this.ids.sid,
                                                        source_id: 0,
                                                        category: 'easter_egg'
                                                    }).then(res => {
                                                        this.detail.get_easter_eggs_status = 1;
                                                        this.$showToast('彩蛋已解锁')
                                                        setTimeout(() => {
                                                            items.is_watch = 1; //已观看
                                                            this.goPages(
                                                                `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                                            )
                                                        }, 1000)
                                                        console.log('解锁彩蛋')
                                                    })
                                                } else {
                                                    this.$showToast('彩蛋已解锁')
                                                    setTimeout(() => {
                                                        items.is_watch = 1; //已观看
                                                        this.goPages(
                                                            `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                                        )
                                                    }, 1000)
                                                }
                                            }
                                        } else {
                                            // 不限制交作业 即可领彩蛋
                                            if (!this.detail.get_easter_eggs_status) {
                                                specialUnlockSurprise({
                                                    special_id: this.ids.sid,
                                                    source_id: 0,
                                                    category: 'easter_egg'
                                                }).then(res => {
                                                    this.detail.get_easter_eggs_status = 1;
                                                    this.$showToast('彩蛋已解锁')
                                                    setTimeout(() => {
                                                        items.is_watch = 1; //已观看
                                                        this.goPages(
                                                            `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                                        )
                                                    }, 1000)
                                                    console.log('解锁彩蛋')
                                                })
                                            } else {
                                                this.$showToast('彩蛋已解锁')
                                                setTimeout(() => {
                                                    items.is_watch = 1; //已观看
                                                    this.goPages(
                                                        `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                                    )
                                                }, 1000)
                                            }
                                        }

                                    } else {
                                        items.is_watch = 1; //已观看
                                        this.goPages(
                                            `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`
                                        )
                                    }
                                }
                            } else {
                                items.is_watch = 1; //已观看
                                this.goPages(
                                    `/pages/yknowledge/course/detail_list?source=detail&sid=${special_id}&id=${id}&fromdetail=true`)
                            }
                        }
                    } else {
                        this.openStudentPopup()
                    }
                }

            },
            // ----------------------------------------------------评论--------------------------------
            goPages(path, type, pathType) {
                if (type) {
                    this.$authNavigator(path);
                } else {
                    this.$navigator(path, pathType);
                }
            },
            // 彩蛋弹窗开关
            openWorkPopup() {
                this.$refs.work_popup.open();
            },
            closeWorkPopup() {
                this.$refs.work_popup.close();
            },

            // 学员卡弹窗开关
            openStudentPopup() {
                if (zxauthNavigator()) {
                    this.$refs.student_popup.open();
                }
            },
            closeStudentPopup() {
                this.$refs.student_popup.close();
            },
            gostudent() {
                this.$refs.student_popup.close();
                this.goPages('/pages/user/studentProfile?type=3&id=' + this.ids.sid, true)
            },
            //收藏和取消收藏
            collect() {
                const {
                    sid: special_id,
                    id: source_id
                } = this.ids;
                getSpecialCollect({
                    special_id,
                    source_id,
                }).then(res => {
                    this.$showToast(this.detail.isCollection ? '取消收藏成功' : '收藏成功')
                    this.detail.isCollection = this.detail.isCollection ? false : 1;
                    if (this.detail.isCollection) {
                        this.detail.collect_count = this.detail.collect_count + 1
                    } else {
                        this.detail.collect_count = this.detail.collect_count - 1
                    }
                })
            },
            colseIosPop() {
                clearTimeout(this.setT);
                this.$refs.iospay_popup.close();
            },
            closeIosPopup() {
                clearInterval(this.timer);
                clearTimeout(this.setT);
                this.$refs.iospay_popup.close();
                this.$navigator('/pages/index/addService');
            },
            // 每次执行减一
            timeCount() {
                --this.s;
                if (this.s < 0) {
                    this.s = 0;
                }
                this.countdown = this.s
            },
            paymentTap(type) {
                if (checkLogin()) {
                    if (this.openIosPayment && this.platform == 'ios') {
                        this.s = 6;
                        this.cleartime = 0;
                        this.$refs.iospay_popup.open();
                        this.setT = setTimeout(() => {
                            if (this.cleartime === 1) {
                                clearTimeout(this.setT);
                                this.setT = null
                            } else {
                                this.closeIosPopup()
                            }
                        }, 5500);
                        return
                    } else {

                        if (this.isSupply) {
                            getSpecialLastOrder(this.ids.sid).then(res => {
                                this.lastOrderId = res.data.order_id;
                                this.pay_price = res.data.pay_price;
                                // console.log('最后订单',this.lastOrderId)
                                if (this.lastOrderId) {
                                    this.noPay = true
                                } else {
                                    this.$refs.Payment.getUser();
                                    var that = this;
                                    this.pay_type_num = type;
                                    if (this.paymentType && _isWeixin) {
                                        // #ifndef MP-TOUTIAO
                                        this.nowPay('weixin', pay_type_num);
                                        // #endif
                                        // #ifdef MP-TOUTIAO
                                        this.nowPay('bytedance');

                                        // #endif
                                    } else {
                                        this.pay = true;
                                    }
                                }
                            })
                        } else {
                            this.openStudentPopup()
                        }
                    }
                } else {
                    // #ifndef MP-TOUTIAO
                    autoAuth();
                    // #endif
                    // #ifdef MP-TOUTIAO
                    this.open()
                    // #endif
                }
            },
            couponMoney(item) {
                console.log('选择优惠券----', item)
                this.coupon = item;
                upSpecialOrderComputed({
                    id: this.detail.id,
                    couponId: this.coupon.id
                }).then(res => {
                    // console.log('实时计算金额',res.data)
                    this.detail.money = res.data.pay_price;
                    this.detail.deductions_price = res.data.deductions_price;
                    if (this.detail.money <= 0) {
                        this.detail.money = 0
                    }
                })
            },
            cancelOrder(e) {
                let param = {
                    uni: this.lastOrderId,
                    paytype: e,
                    from: this.from,
                }
                specialOrderCancel(param)
                    .then(res => {
                        console.log('res', res)
                        if (res.status == 200) {
                            this.$showToast('订单取消成功');
                        }
                    })
                    .catch(err => {
                        this.$showToast(err.msg || err);
                    });
            },
            notNowPay(e) {
                console.log('e', e)
                let param = {
                    uni: this.lastOrderId,
                    paytype: e,
                    from: this.from,
                }
                specialOrderPay(param)
                    .then(res => {
                        const {
                            data
                        } = res, that = this;
                        console.log('data', data)
                        switch (data.status) {
                            case "PAY_ERROR":
                            case 'ORDER_EXIST':
                            case 'ORDER_ERROR':
                                this.$showToast(res.msg);
                                break;
                            case 'BYTEDANCE_PAY':
                            case 'BYTEDANCE_ORDER':
                                // #ifdef MP-TOUTIAO
                                let tt_res = res.data.result;
                                tt.pay({
                                    orderInfo: {
                                        order_id: tt_res.orderId,
                                        order_token: tt_res.orderToken,
                                    },
                                    service: 5,
                                    _debug: 1,
                                    success(res) {
                                        console.log('字节支付成功', res)
                                        if (res.code == 0) {
                                            that.paySucess(data.result.orderId)
                                        }
                                    },
                                    fail(res) {
                                        console.log('字节支付失败', res)
                                        that.$showToast('取消支付');
                                    },
                                });
                                // #endif
                                break;
                            case 'WECHAT_PAY':
                                // #ifdef H5
                                pay(data.result.jsConfig)
                                    .finally(() => {
                                        that.$showToast('支付成功');
                                        that.detail.is_pay = 1;
                                        that.detail.pay_type = 1;
                                        that.resetSpecialCourseList()
                                    })
                                    .catch(function() {
                                        that.detail.is_pay = 0;
                                        that.$showToast('支付失败');
                                    });

                                // #endif
                                // #ifdef MP-WEIXIN
                                const jsConfig = data.result.jsConfig;
                                console.log('获取支付参数', jsConfig)
                                // requestOrderPayment
                                wx.requestPayment({
                                    timeStamp: jsConfig.timestamp,
                                    nonceStr: jsConfig.nonceStr,
                                    package: jsConfig.package,
                                    signType: jsConfig.signType,
                                    paySign: jsConfig.paySign,
                                    success: function(res) {
                                        console.log('支付成功', res)
                                        that.paySucess(data.result.orderId)
                                    },
                                    fail: function(e) {
                                        console.log('支付失败1', e)
                                        that.$showToast('取消支付');
                                    },
                                    complete: function(e) {
                                        //关闭当前页面跳转至订单状态
                                        console.log('支付失败2', e)
                                        if (res.errMsg == 'requestPayment:cancel') return that.$showToast('取消支付', e);;
                                    }
                                });
                                // #endif
                                break;
                            case 'WECHAT_H5_PAY':
                                that.$navigator(
                                    `/pages/yknowledge/course/detail?sid=${that.ids.sid}&payOrderId=${data.result.orderId}`,
                                    'redirectTo');
                                setTimeout(() => {
                                    location.href = data.result.jsConfig.mweb_url;
                                }, 100);
                                break;
                            case 'SUCCESS':
                                this.paySucess(data.result.orderId)
                                break;
                            case 'ZHIFUBAO_PAY':
                                this.$showToast('zhifubao');
                                break;
                        }

                    })
                    .catch(err => {
                        this.$showToast(err.msg || err);
                    });

            },
            async nowPay(type, types) {
                // weixinh5
                // // #ifdef MP-WEIXIN
                // this.traceId = await this.getTraceId(); //获取推广id
                // // #endif
                let param = {
                    pay_type_num: this.pay_type_num,
                    payType: type,
                    from: this.from,
                    id: this.ids.sid,
                    mark: this.mark,
                    link_invite_id: 0,
                    couponId: 0
                    // scene: this.scene,//场景值
                    // trace_id: this.traceId, 
                    // link_share_record_id:''
                }
                if (this.coupon.id) {
                    param.couponId = this.coupon.id; // 优惠券id
                }
                if (this.ids.invitationId) {
                    param.link_invite_id = this.ids.invitationId; // 分享邀请记录id
                }
                getSpecialOrderCreate(param)
                    .then(res => {
                        const {
                            data
                        } = res, that = this;
                        // console.log(data)
                        switch (data.status) {
                            case "PAY_ERROR":
                            case 'ORDER_EXIST':
                            case 'ORDER_ERROR':
                                this.$showToast(res.msg);
                                break;
                            case 'BYTEDANCE_PAY':
                            case 'BYTEDANCE_ORDER':
                                // #ifdef MP-TOUTIAO
                                let tt_res = res.data.result;
                                tt.pay({
                                    orderInfo: {
                                        order_id: tt_res.orderId,
                                        order_token: tt_res.orderToken,
                                    },
                                    service: 5,
                                    _debug: 1,
                                    success(res) {
                                        console.log('字节支付成功', res)
                                        if (res.code == 0) {
                                            that.paySucess(data.result.orderId)
                                        }
                                    },
                                    fail(res) {
                                        console.log('字节支付失败', res)
                                        that.$showToast('取消支付');
                                    },
                                });
                                // #endif
                                break;
                            case 'WECHAT_PAY':
                                // #ifdef H5
                                pay(data.result.jsConfig)
                                    .finally(() => {
                                        that.$showToast('支付成功');
                                        that.detail.is_pay = 1;
                                        that.detail.pay_type = 1;
                                        that.resetSpecialCourseList()
                                    })
                                    .catch(function() {
                                        that.detail.is_pay = 0;
                                        that.$showToast('支付失败');
                                    });

                                // #endif
                                // #ifdef MP-WEIXIN
                                const jsConfig = data.result.jsConfig;
                                console.log('获取支付参数', jsConfig)
                                // requestOrderPayment
                                wx.requestPayment({
                                    timeStamp: jsConfig.timestamp,
                                    nonceStr: jsConfig.nonceStr,
                                    package: jsConfig.package,
                                    signType: jsConfig.signType,
                                    paySign: jsConfig.paySign,
                                    success: function(res) {
                                        console.log('支付成功', res)
                                        that.paySucess(data.result.orderId)
                                    },
                                    fail: function(e) {
                                        console.log('支付失败1', e)
                                        that.$showToast('取消支付');
                                    },
                                    complete: function(e) {
                                        //关闭当前页面跳转至订单状态
                                        console.log('支付失败2', e)
                                        if (res.errMsg == 'requestPayment:cancel') return that
                                            .$showToast('取消支付', e);;
                                    }
                                });
                                // #endif
                                break;
                            case 'WECHAT_H5_PAY':
                                that.$navigator(
                                    `/pages/yknowledge/course/detail?sid=${that.ids.sid}&payOrderId=${data.result.orderId}`,
                                    'redirectTo');
                                setTimeout(() => {
                                    location.href = data.result.jsConfig.mweb_url;
                                }, 100);
                                break;
                            case 'SUCCESS':
                                this.paySucess(data.result.orderId)
                                break;
                            case 'ZHIFUBAO_PAY':
                                this.$showToast('zhifubao');
                                break;
                        }

                    })
                    .catch(err => {
                        this.$showToast(err.msg || err);
                    });
            },
            paySucess(orderId) {
                this.$showToast('支付成功');
                // 立即购买
                if (this.pay_type_num === 2) {
                    this.detail.is_pay = 1;
                    this.detail.pay_type = 1;
                    this.resetSpecialCourseList()
                }
                // 送朋友
                if (this.pay_type_num === 1) {
                    const {
                        uid
                    } = this.userInfo;
                    const {
                        id,
                        sid
                    } = this.ids;
                    const link =
                        `/pages/yknowledge/course/detail?sid=${sid}&gift_uid=${uid}&gift_order_id=${orderId}&gift=1`;
                    this.shareConfig.link = link;
                    // #ifdef H5
                    setTimeout(() => {
                        openShareAll(this.shareConfig, !_isWeixin, '');
                    }, 500)
                    if (_isWeixin) {
                        setTimeout(() => {
                            // this.$showToast('点击右上角分享');
                            this.$refs.sharePopup.open();
                        }, 500)
                    }
                    // #endif
                    // #ifdef MP
                    this.modalShow = true;
                    // #endif
                }
            },
            getGiveGift() {
                if (this.isSupply) {
                    getSpecialReceiveGift(this.giftOrderId).then(res => {
                        this.$showToast('领取成功');
                        this.is_gift = false;
                        this.detail.is_pay = true;
                        this.detail.pay_type = 1;
                        this.detail.gift_uid = 0;
                        this.resetSpecialCourseList();
                    })
                } else {
                    this.openStudentPopup()
                }
            },
            goStudy() {
                const {
                    sid,
                    id
                } = this.ids;
                if (this.unSeeId < 0) {
                    return this.$showToast('暂无素材');
                }
                if (this.isSupply) {
                    if (this.videoCourse.courseLastId != 0) {
                        this.unSeeId = this.videoCourse.courseLastId;
                    }
                    this.goPages('/pages/yknowledge/course/detail_list?source=detail&sid=' + sid + '&id=' + this
                        .unSeeId + '&fromdetail=true', true)
                } else {
                    this.openStudentPopup()
                }
            },
            initSelectorQueryInfo() {
                let _this = this;
                uniSelectorQueryInfo('#calcItem', this)
                    .then(res => {
                        _this.initCalcItemScroll = res.width;
                    })
                    .catch(err => {});

                uniSelectorQueryInfo('#tabLink', this).then(res => {
                    // console.log("得到布局位置信息", res);
                    _this.tabLinkInfo = res;
                    _this.tabLinkInfo1 = res;

                }).catch(err => {});
            }
        },
        onShow() {

            this.platform = uni.getSystemInfoSync().platform;
            console.log('系统', this.platform)
            // #ifdef MP-WEIXIN
            this.getScene()
            this.adjustPosition = false
            // #endif
            // #ifndef MP-WEIXIN
            this.adjustPosition = true
            // #endif
            if (this.oindex_detail) {
                this.getSpecialDetail(this.ids.sid, this.ids.gift_uid || '', this.ids.gift_order_id || '', false, this
                    .ids.invitationId || 0, this.ids
                    .isJumpLive || 0);
            }
            if (this.workdata.id) {
                this.updateAddInfo(this.workdata)
                this.$showToast('作业回复成功')
            }

            this.timer = setInterval(() => {
                this.timeCount()
                this.$forceUpdate();
            }, 1000)

            if (this.finishStudent) {
                this.isSupply = true;
                if (this.ids.gift_order_id && this.ids.gift == 1) {
                    this.getGiveGift()
                }
            }

            if (storage.get('videoCourse' + this.ids.sid)) {
                this.videoCourse = storage.get('videoCourse' + this.ids.sid)
                // console.log('videoCourse------------',this.videoCourse)
            }

        },
        onReady() {
            if (this.ids.invitationId) {
                this.$storage.set(WECHAT_AUTH_BACK_URL, VUE_APP_URL + '/h5/course/detail?id=0&sid=' + sid +
                    '&invitationId=' + this.ids.invitationId + '&invite_code=' + this.ids.invite_code)
            }
        },
        onUnload() {
            eventBus.off('pageBUnload', this.eventHandler);
        },
        onLoad(options) {
            // eventBus处理
            this.eventHandler = (data) => {
                // console.log('收到 B 页面的消息:', data);
                if (data.gopay) {
                    this.paymentTap(2)
                }
            };
            eventBus.on('pageBUnload', this.eventHandler);

            this.platform = uni.getSystemInfoSync().platform;
            const {
                id = 0,
                source_id = 0,
                gift_uid = '',
                gift_order_id = '',
                gift = '',
                spread_uid = '',
                payOrderId = '',
                sid = 0,
                code = '',
                open_comment = false, // 是否默认展开长评论
                tabindex = 0, // 默认切换栏
                invitationId = 0, // 分享邀请活动相关id
                invite_code = '', // 分享邀请活动相关邀请码
                isJumpLive = 0 // 1 是否从直播页面跳转；
            } = options;
            this.ids = options;
            if (invitationId) {
                this.$storage.set(WECHAT_AUTH_BACK_URL, VUE_APP_URL + '/h5/course/detail?id=0&sid=' + sid +
                    '&invitationId=' + invitationId + '&invite_code=' + invite_code)
            }
            let link_pay, link_pay_uid; //非URL传递参数
            this.getSpecialDetail(sid, gift_uid, gift_order_id, false, invitationId, isJumpLive);
            if (gift === '1') {
                this.is_gift = true;
                this.giftOrderId = gift_order_id;
                getSpecialGiftReceive(gift_order_id).then(res => {
                    if (res.data.is_gift) {
                        this.is_gift = false;
                        this.$showToast('已被领取');
                    }
                })
            } else if (payOrderId) {
                getSpecialPayOrderStatuts(payOrderId).then(res => {
                    if (res.data.is_pay) {
                        this.paySucess(payOrderId)
                    } else {
                        this.$showToast('支付失败')
                    }
                }).catch(err => {
                    this.$showToast(err.msg || err)
                })
            } else {
                // this.getSpecialDetail(id, gift_uid, gift_order_id);
            }
            // 学习人数-待考察
            // getSpecialLearningRecords(sid).then(res => {
            //     this.learnRecord = res.data.recordCoujnt;
            // })
        },
        onReady() {
            setTimeout(() => {
                this.initSelectorQueryInfo()
            }, 1000)
        },
        onPageScroll(e) {
            this.scrollTop = e.scrollTop;
            this.inputShow = false;
            this.footerHidden = false;
            this.opacity = (e.scrollTop / 400).toFixed(1);
            if (e.scrollTop > 200) {
                this.footerOpacity = (e.scrollTop / 400).toFixed(1);
            } else {
                this.footerOpacity = 0;
            }
        },
        // #ifdef MP
        onShareAppMessage() {
            console.log('分享触发--', this.sharecode)
            if (!this.sharecode) {
                this.getSpecialDetail(this.ids.sid, this.ids.gift_uid || '', this.ids.gift_order_id || '', false, this
                    .ids.invitationId || 0, this.ids.isJumpLive || 0);
            }
            return {
                title: this.shareConfig.title,
                imageUrl: this.shareConfig.imgUrl,
                path: this.shareConfig.link,
                templateId: SHARE_ID
            };
        },
        onShareTimeline() {
            if (this.shareConfig) {
                const {
                    id,
                    sid
                } = this.ids;
                return {
                    title: this.shareConfig.title || '',
                    imageUrl: this.shareConfig.imgUrl || '',
                    query: `sid=${sid}&id=${id}&spid=${this.$store.state.userInfo.uid}`
                };
            }
        }
        // #endif
    }
</script>

<style scoped lang="scss">
    .Adminpop {
        position: relative;
        width: 680rpx;
        height: 680rpx;
        background-color: #f2f2f2;
        border-radius: 10rpx;
        padding: 32rpx 80rpx;
        box-sizing: border-box;

        &.send {
            height: 400rpx;
        }

        .Adminpop-close {
            position: absolute;
            right: 30rpx;
            top: 30rpx;
            color: #000;

        }

        .Adminpop-title {
            width: 100%;
            text-align: center;
            color: #000;
            font-weight: bold;
            font-size: 36rpx;
        }

        .Adminpop-text {
            width: 100%;
            font-size: 26rpx;
            margin-top: 40rpx;
            overflow: auto;

            .item {
                margin-bottom: 40rpx;
            }
        }

        .Adminpop-but {
            width: 360rpx;
            height: 80rpx;
            line-height: 80rpx;
            font-size: 26rpx;
            text-align: center;
            margin: 30rpx auto 0;
            background-color: #1988CC;
            color: #FFFFFF;
            border-radius: 8rpx;
        }

    }

    .swiper-set {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        z-index: 99;

        image {
            width: 50rpx;
            height: 50rpx;
        }
    }

    /* #ifdef MP-TOUTIAO */
    .authorize {
        width: 600rpx;
        background-color: #fff;
    }

    .authorize .text_center {
        width: 100%;
    }

    .authorize .text_center .top {
        padding: 20rpx 40rpx;
    }

    .authorize .text_center .top .title {
        padding: 20rpx 0;
        font-size: 32rpx;
        font-weight: bold;
    }

    .authorize .text_center .top .tip {
        padding: 30rpx 0;
        font-size: 30rpx;
        height: 150rpx;
    }

    .authorize .text_center .bottom {
        bottom: 0;
        width: 100%;
    }

    .authorize .text_center .bottom .btn {
        width: 50%;
        border-radius: 0;
        height: 80rpx;
        line-height: 80rpx;
    }

    .authorize .text_center .bottom .btn:after {
        border: none;
    }

    .authorize .text_center .bottom .btn:last-child {
        background-color: #f85959;
    }

    /* #endif */
    .conter p {
        font-size: 32rpx !important;
    }

    .conter image {
        width: 100% !important;
        display: block;
    }

    .tt_text_left {
        width: 100%;
        height: auto;
        overflow: hidden;
        padding: 0 20rpx;
    }

    .tt_text_left_btn1 {
        float: left;
        width: 150rpx;
        height: 70rpx;
        margin: 0 auto;
        border: 1rpx solid #e6e6e6;
        border-radius: 10rpx;
        text-align: center;
        line-height: 70rpx;
    }

    .tt_text_left_btn2 {
        float: right;
        width: 150rpx;
        height: 70rpx;
        background-color: #e93323;
        color: #fff;
        font-size: 28rpx;
        border-radius: 10rpx;
        text-align: center;
        line-height: 70rpx;
    }


    image {
        width: 100%;
        height: 100%;
    }

    .loadmore {
        padding: 40rpx 0;
    }

    .live-notice {
        width: 100%;
        padding: 0 30rpx;
    }

    .common_wrap {
        font-family: PingFang SC, PingFang SC-Bold;

        .w_top {
            margin-top: 50rpx;

            .t_l {
                width: 500rpx;

                .title {
                    font-size: 32rpx;
                    font-weight: 700;
                    text-align: left;
                    color: #333333;
                    @include show_line(3);
                }

                .span {
                    margin-top: 16rpx;

                    text {
                        @include font_size(18);
                        font-weight: 400;
                        text-align: center;
                        color: #ffffff;
                    }

                    .span1,
                    .span2 {
                        // padding:0 10rpx;
                        height: 32rpx;
                        line-height: 32rpx;
                        border-radius: 8rpx;

                    }

                    .span1 {
                        background: #ff5656;
                    }

                    .span2 {
                        margin-left: 26rpx;
                        background: #6BB4FF;
                    }
                }
            }

            .t_r {
                width: 80rpx;
                text-align: center;

                image {
                    width: 56rpx;
                }

                .txt {
                    @include font_size(20);

                    &.active {
                        color: #ff5656;
                    }
                }
            }
        }

        .join {
            margin: 50rpx 0 36rpx 0;
            padding: 0 24rpx 0 40rpx;
            height: 84rpx;
            line-height: 84rpx;
            background: #e9f1fb;
            border-radius: 16rpx;
            font-weight: 400;
            text-align: center;
            color: #3399ff;

            .txt {
                @include font_size(22, 1);
            }

            .btn {
                width: 152rpx;
                height: 50rpx;
                line-height: 50rpx;
                border: 2rpx solid #3399ff;
                font-size: 24rpx;
                border-radius: 20rpx;
            }
        }

        .info {
            border-radius: 16rpx;
            overflow: hidden;
            margin-bottom: 50rpx;

            .item {
                width: 50%;
                text-align: center;
                padding: 22rpx 0;

                &:first-child {
                    background: #f8e8ea;
                }

                &:last-child {
                    background: #e9f1fb;
                }

                .title {
                    @include font_size(20);
                    font-weight: 400;
                    color: #666666;
                }

                .num {
                    margin-top: 14rpx;
                    font-size: 28rpx;
                    font-weight: 700;
                    text-align: center;
                    color: #333333;
                    line-height: 38rpx;
                }
            }
        }

        .tab_link {
            .tab {
                .item {
                    padding-top: 30rpx;
                    margin-right: 56rpx;

                    &:last-child {
                        margin-right: 0;
                    }

                    .title {
                        height: 44rpx;
                        font-size: 26rpx;
                        font-weight: 500;
                        color: #999999;
                    }

                    .item_line {
                        margin-top: 33rpx;
                        width: 0;
                        height: 2px;
                        background-color: #6BB4FF;
                        transition: width 0.1s ease-in-out;
                    }

                    &.active {
                        .title {
                            font-size: 30rpx;
                            font-weight: 700;
                            color: #333333;
                        }


                        .item_line {
                            width: 100%;
                            left: 0;
                        }

                        // border-bottom: 2px solid #6BB4FF;
                    }
                }
            }

            .line {
                margin-top: -2px;
                height: 2px;
                background-color: #D8D8D8;
            }

            .swiper {
                padding: 20rpx 0;
                height: calc(100vh - 200rpx - 110rpx) !important;
                // height: calc(100vh - 110rpx) !important;
                // border: 1px solid red;
                width: 100%;
            }

            .message_wrap {
                background: none;

                >view.item {
                    padding-bottom: 20rpx;

                    >view:not(.mess_avatar) {
                        padding-left: 52rpx;
                    }

                    &:not(:last-child) {
                        border-bottom: 2rpx solid #e2e6ec;
                    }
                }

                .mess_avatar {
                    margin: 50rpx 0 20rpx 0;
                    padding-bottom: 14rpx;

                    .avatar {
                        image {
                            width: 60rpx;
                            height: 60rpx;
                            border-radius: 50%;
                            margin-right: 32rpx;
                        }

                        text {
                            color: #101010;
                            font-size: 28rpx;
                        }
                    }

                    .btn {
                        width: 108rpx;
                        height: 50rpx;
                        margin-left: 20rpx;
                        border: 1px solid rgba(0, 0, 0, 0.078);
                        border-radius: 32rpx;

                        font-size: 24rpx;

                        color: #666666;
                    }
                }

                .score {
                    margin-bottom: 40rpx;

                    .txt {
                        padding: 8rpx 16rpx;

                        border-radius: 20rpx;
                        border: 2rpx solid #ff5656;
                        color: #ff5656;
                        // letter-spacing: 12rpx;
                        font-weight: 500;
                        font-size: 28rpx;
                        margin-right: 12rpx;
                    }

                    .process {
                        // width: 276rpx;
                        height: 58rpx;
                        padding: 14rpx 20rpx;
                        background: #ffffff;
                        opacity: 1;
                        border-radius: 20rpx;

                        .num {
                            margin-left: 14rpx;
                            color: #ff5656;
                            font-weight: bold;
                            font-size: 40rpx;
                        }
                    }
                }

                .label {
                    ::deep.xlabel {
                        >.wrap {
                            margin-bottom: 36rpx;
                            background: none;

                            .flex {
                                background: none;
                            }

                            .more {
                                width: 60rpx;
                                height: 60rpx;
                                border-radius: 30rpx;

                                text {
                                    width: 60rpx;
                                    height: 60rpx;
                                    border-radius: 30rpx;
                                }
                            }
                        }
                    }
                }

                .mess_des {
                    margin-top: 42rpx;
                    font-size: 24rpx;
                    line-height: 36rpx;
                    color: #101010;
                    margin-bottom: 20rpx;

                    .del {
                        text-align: right;
                        color: #999;
                    }

                    .refining_span {
                        position: absolute;
                        border: 1rpx solid red;
                        top: -40rpx;
                        right: 20rpx;
                        color: red;
                        padding: 2rpx 30rpx;
                        font-weight: bold;
                        font-size: 30rpx;
                        letter-spacing: 6rpx;
                        transform: rotate(-30deg);
                        opacity: 0.6;
                        border-radius: 10rpx;
                    }
                }

                .mess_image {
                    height: 148rpx;
                    margin-bottom: 20rpx;
                    margin-left: -20rpx;

                    .nav {
                        width: 20rpx;
                        line-height: 148rpx;
                        text-align: center;

                        text {
                            font-size: 24rpx;
                            font-weight: bolder;
                        }
                    }

                    .wrap1 {
                        width: calc(100% - 40rpx);

                        .item {
                            // display: inline-block;
                            width: 136rpx;
                            height: 136rpx;

                            &:not(:last-child) {
                                margin-right: 6rpx;
                            }

                            image {
                                width: 136rpx;
                                height: 136rpx;
                            }

                            &.video {
                                position: relative;

                                .cover {
                                    position: absolute;
                                    width: 100%;
                                    height: 100%;
                                    z-index: 10;
                                    top: 0;

                                    image {
                                        width: 120rpx;
                                        height: 120rpx;
                                    }
                                }
                            }

                            &.audio {
                                // .cover {
                                display: flex;
                                align-items: center;
                                justify-content: space-around;

                                image {
                                    width: 120rpx;
                                    height: 120rpx;
                                }

                                // }
                            }
                        }
                    }
                }

                .my_handle {
                    background: #fff;
                    padding: 12rpx 10rpx 12rpx 82rpx;
                    margin-bottom: 30rpx;
                    border-radius: 32rpx;
                    box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);

                    .txt {
                        color: #ff5656;
                    }

                    .btn {
                        width: 152rpx;
                        height: 60rpx;
                        background: #eaeaea;
                        opacity: 1;
                        border-radius: 24rpx;

                        color: #50506f;
                        font-size: 24rpx;
                        margin-left: 8rpx;
                    }
                }

                .mess_handle {
                    font-size: 24rpx;
                    color: #ff5656;
                    margin: 32rpx 0 40rpx 0;

                    .time {
                        color: #999999;
                    }

                    .item {
                        image {
                            width: 34rpx;
                            height: 24rpx;
                        }

                        text {
                            padding: 0 16rpx;
                            font-size: 24rpx;

                            color: #ff5656;
                        }
                    }
                }

                .comment {
                    padding-left: 0rpx !important;
                    margin-left: 52rpx;
                    background: #eaeaea;
                    max-height: 600rpx;
                    overflow-y: scroll;
                }
            }

            .lists {
                .loadmore1 {
                    width: 100%;
                    font-size: 24rpx;
                    font-family: PingFang SC, PingFang SC-Regular;
                    font-weight: 400;
                    text-align: center;
                    color: #999999;

                }

                .titles {
                    width: 100%;
                    margin-bottom: 24rpx;
                    padding-bottom: 24rpx;
                    border-bottom: 2rpx dashed #d0e5fc;

                    &.last {
                        border-bottom: 0;
                    }

                    .title1 {
                        width: 100%;
                        min-height: 84rpx;
                        line-height: 84rpx;
                        background: rgba(107, 180, 255, 0.10);
                        border-radius: 16rpx;
                        font-size: 24rpx;
                        font-family: PingFang SC, PingFang SC-Regular;
                        font-weight: 400;
                        text-align: left;
                        color: #3e3e3e;

                        .title1-left {
                            width: 510rpx;
                            padding: 0 30rpx 0;
                            box-sizing: border-box;
                            line-height: 1;
                        }

                        .title1-right {

                            .title1-right-txt {
                                font-size: 24rpx;
                                font-weight: 400;
                                color: #3e3e3e;
                                font-family: PingFang SC, PingFang SC-Regular;
                            }

                            .title1-right-img {
                                width: 32rpx;
                                height: 32rpx;
                                transform: rotate(-90deg);
                                transition: all .2s ease-in-out;
                                margin: 20rpx;
                                margin-top: 28rpx;

                                &.right {
                                    transform: rotate(0deg);
                                }
                            }
                        }
                    }
                }

                .item {
                    padding: 23rpx 0;
                    border-bottom: 2rpx dashed #d0e5fc;

                    &.last {
                        border-bottom: 0;
                    }

                    .item_l1 {

                        &.alreadySeen {
                            opacity: .4;
                        }

                        .image {
                            width: 64rpx;
                            height: 44rpx;

                            .image-gif {
                                width: 44rpx;
                                height: 44rpx;
                                margin-left: 10rpx;
                            }
                        }

                        .item_l_r {
                            width: 546rpx;
                            margin-left: 20rpx;
                            font-size: 24rpx;
                            font-weight: 400;
                            // text-align: center;
                            color: #666666;

                            &.active {
                                color: #6bb4ff;
                            }

                            &.openlock {
                                width: 522rpx;
                            }

                            &.livelock {
                                width: 500rpx;
                            }

                            &.lastStyle {
                                color: #e93323;
                            }
                        }
                    }

                    .item_l {

                        .image {
                            width: 64rpx;
                            height: 39rpx;

                            .image-gif {
                                width: 44rpx;
                                height: 44rpx;
                                margin-left: 10rpx;
                            }
                        }


                        .item_l_r {
                            width: 546rpx;
                            font-size: 24rpx;
                            font-family: PingFang SC, PingFang SC-Regular;
                            font-weight: 400;
                            color: #6bb4ff;
                            margin-left: 20rpx;

                            &.alreadySeen {
                                color: #666666;
                            }

                            &.active {
                                color: #6bb4ff;
                            }

                            &.openlock {
                                width: 522rpx;
                            }

                            &.livelock {
                                width: 500rpx;
                            }

                            &.lastStyle {
                                color: #e93323;
                            }
                        }
                    }

                    .item_r {
                        width: 30rpx;
                        height: 36rpx;
                        margin-right: 2rpx;
                        &.item_r_shitinh {
                            width: 60rpx;
                        }
                    }
                    .shiting {
                        font-size: 26rpx;
                        background-color: #ffa916;
                        color: #fff;
                        padding: 2rpx 4rpx;
                        border-radius: 4rpx;
                    }

                    .item_live {
                        height: 30rpx;
                        line-height: 28rpx;
                        background: #ff5656;
                        border-radius: 8rpx;

                        &.work {
                            // animation: rainbow 10s infinite;
                            color: #fff;
                            padding: 8rpx;
                            height: auto;
                        }

                        @keyframes rainbow {
                            from {
                                background: #ff0000;
                            }

                            16.7% {
                                background: #ff7f00;
                            }

                            33.3% {
                                background: #ffff00;
                            }

                            50% {
                                background: #00ff00;
                            }

                            66.7% {
                                background: #0000ff;
                            }

                            83.3% {
                                background: #8b00ff;
                            }

                            to {
                                background: #ff0000;
                            }
                        }

                        .text {
                            @include font_size(18);
                            font-weight: 400;
                            text-align: center;
                            color: #ffffff;
                        }
                    }
                }

            }
        }


    }


    .active_footer {
        @include fixed_footer(112rpx);
        box-shadow: 0px 0px 40px 0px rgba(107, 127, 153, 0.20);
        background: #f7f8fa;

        // &.hide {
        //     display: none;
        // }

        .flex_l {
            flex: 1;
            padding: 0 60rpx;

            .item {
                // width: calc((100% - 400rpx) / 2);
                width: 50%;
                text-align: center;
                font-size: 24rpx;

                color: #3e3e3e;

                image {
                    width: 40rpx;
                    height: 40rpx;
                }
            }
        }

        .btn {
            width: 250rpx;
            height: 112rpx;
            line-height: 112rpx;
            text-align: center;

            border-radius: 30rpx 0px 0px 0px;
            font-size: 32rpx;
            background: #ff5656;
            color: #ffffff;
            font-weight: bold;

            &.active {
                background: #6bb4ff;
            }
        }
    }

    .x_modal {
        .title {
            padding: 60rpx 30rpx;
            text-align: center;
            font-size: 32rpx;
            color: #666;
        }

        .btn_l,
        .btn_r {
            width: 50%;
            text-align: center;
            height: 80rpx;
            line-height: 80rpx;
            font-size: 28rpx;
            border-top: 1px solid #e4e7ed;
        }

        .btn_l {
            border-right: 1px solid #e4e7ed;
            color: rgb(96, 98, 102);
        }

        .btn_r {
            border-radius: 0;
            color: rgb(41, 121, 255);

            &::after {
                border-radius: 0;
            }
        }
    }

    .gocommet-btn {
        background-color: #6bb4ff;
        border-radius: 20rpx;
        width: 108rpx;
        height: 50rpx;
        line-height: 50rpx;
        text-align: center;
        font-size: 24rpx;
        font-weight: 400;
        text-align: center;
        color: #ffffff;

        &.work {
            width: 122rpx;
            border-radius: 8rpx;
            background-color: #1b9cec;
        }
    }

    .tab-comment {
        border-top: 2rpx solid #d8d8d8;
        border-bottom: 2rpx solid #d8d8d8;
        margin-top: 16rpx;
        margin-bottom: 40rpx;
    }

    .tab-comment .item.active {
        background: #f4f4f4;
        font-weight: 700;
        color: #333;
    }

    .tab-comment .item:last-child {
        border-right: none;
    }

    .tab-comment .item {
        width: 33%;
        padding-left: 20rpx;
        height: 64rpx;
        line-height: 64rpx;
        font-size: 24rpx;
        background: #ffffff;
        font-weight: 500;
        color: #999999;
        border-right: 2rpx solid #d8d8d8;
    }

    .onlineCls {
        animation: shake 1s 0.15s linear infinite;
    }

    @-webkit-keyframes shake {
        10% {
            transform: rotate(15deg);
        }

        20% {
            transform: rotate(-10deg);
        }

        30% {
            transform: rotate(5deg);
        }

        40% {
            transform: rotate(-5deg);
        }

        50%,
        100% {
            transform: rotate(0deg);
        }
    }

    .iospayPopup {
        position: relative;
        width: 500rpx;
        height: 350rpx;
        background-color: #fff;
        border-radius: 16rpx;
        border: 1rpx solid #999;
        padding: 50rpx 30rpx;
        box-sizing: border-box;
    }

    .cancelPopup {
        position: relative;
        width: 622rpx;
        min-height: 366rpx;
        max-height: 732rpx;
        border: 4rpx solid #50506f;
        border-radius: 30rpx;
        background-color: #fff;
        box-sizing: border-box;
        padding: 92rpx 80rpx 0;
        overflow: auto;

        &.work {
            padding: 92rpx 46rpx 0;
        }
    }

    .iospayPopup-colse {
        position: absolute;
        right: 2rpx;
        top: 2rpx;
        width: 50rpx;
        height: 50rpx;
    }

    .cancel-icon {
        position: absolute;
        right: 12rpx;
        top: 12rpx;
        width: 56rpx;
        height: 56rpx;
    }

    .cancel-title {
        width: 100%;
        font-size: 28rpx;
        font-family: PingFang SC, PingFang SC-Bold;
        font-weight: 700;
        text-align: center;
        color: #50506f;
        line-height: 38rpx;
        min-height: 114rpx;
        max-height: 360rpx;
        word-break: break-all;
        overflow-x: scroll;

        &.work {
            line-height: 38rpx;
            min-height: 0;
            text-align: left;
        }
    }

    .iospayPopup-title {
        width: 100%;
        font-size: 30rpx;
        color: #000;
    }

    .iospayPopup-button {
        width: 220rpx;
        height: 70rpx;
        line-height: 70rpx;
        text-align: center;
        color: #fff;
        font-size: 24rpx;
        background-color: #3399ff;
        border-radius: 10rpx;
        margin: 69rpx auto 0;
    }

    .cancel-button {
        width: 248rpx;
        height: 64rpx;
        line-height: 64rpx;
        text-align: center;
        background: #50506f;
        border-radius: 32rpx;
        font-size: 24rpx;
        font-family: PingFang SC, PingFang SC-Regular;
        font-weight: 400;
        text-align: center;
        color: #ffffff;
        margin: 38rpx auto;

        &.work {
            width: 100%;
            background: none;

            .left-work-button {
                float: left;
                width: 240rpx;
                height: 64rpx;
                line-height: 64rpx;
                text-align: center;
                background-color: #fc5656;
                font-size: 26rpx;
                color: #fff;
                border-radius: 12rpx;
            }

            .right-work-button {
                float: right;
                width: 240rpx;
                height: 64rpx;
                line-height: 64rpx;
                text-align: center;
                background-color: #fc5656;
                font-size: 26rpx;
                color: #fff;
                border-radius: 12rpx;
            }
        }
    }

    .balance-box {
        position: relative;
        width: 600rpx;
        background-color: #fff;
        border-radius: 20rpx;
        padding: 40rpx 30rpx 50rpx;
        box-sizing: border-box;

    }

    .balance-box-close {
        position: absolute;
        top: 2rpx;
        right: 2rpx;
        width: 50rpx !important;
        height: 50rpx !important;
    }

    .balance-box-title {
        width: 100%;
        text-align: center;
        color: #e93323;
        font-size: 28rpx;
        margin-top: 50rpx;

        view {
            margin-bottom: 20rpx;
        }
    }

    .balance-box-btn {
        width: 100%;
        margin-top: 60rpx;
        overflow: auto;

        view {
            width: 240rpx;
            height: 88rpx;
            line-height: 88rpx;
            text-align: center;
            background-color: #fc5656;
            font-size: 26rpx;
            color: #fff;
            border-radius: 12rpx;
        }

        .balance-box-btn-center {
            margin: 0 auto;
        }

    }
</style>