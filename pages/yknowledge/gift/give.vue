<template>
	<view class="common_wrap">
		<listItem :arr="listArr" :rbType="4" :lspan="false" rspan="" :nodata="true" :pagemore="page.more"
			:loadmoreStatus="loadmoreStatus" @listItemClick="listItemClick" @shareClick="shareClick">
			<!-- <template v-slot:header="{ item, index }">
				<view class="flex flex_between gift_top">
					<view class="status">{{item.is_draw?'赠送成功':'未赠送'}}</view>
					<view class="ungo" v-if="item.is_draw">领取人：{{item.gift_user.nickname}}</view>
					<button class="go" v-else open-type="share" @click="shareClick(item)">去赠送</button>
				</view>
			</template> -->
		</listItem>
        <uni-popup ref="sharePopup" :zIndex="999">
            <view style="width: 750rpx;height:100vh;text-align:center">
                <image src="@/static/images/share-f.png" mode="scaleToFill" style="width: 100%;height:100%;"
                    @click="close_share_pop"></image>
            </view>
        </uni-popup>
	</view>
</template>
<script>
    import { SHARE_ID } from '@/config.js';
	import {
		getSpecialMyGive
	} from '@/api/yknowledge.js'
	import listItem from '@/components/zsff/list-item.vue';
	import {
		debounce,
		setClipboardData
	} from '@/utils/common.js'

	// #ifdef H5
	import {
		isWeixin
	} from '@/utils/validate.js';
	import {
		openShareAll
	} from '@/utils/wechat/share.js';
	const _isWeixin = isWeixin();
	// #endif
	export default {
		components: {
			listItem
		},
		data() {
			return {
				pageType: 'course',
				req: getSpecialMyGive,
				listArr: [],
				requestLoading: false,
				loadmoreStatus: 'loadmore',
				page: {
					page: 1,
					limit: 20,
					more: true
				},
				shareConfig: {}
			};
		},
		methods: {
            share_pop(){
                this.$refs.sharePopup.open();
            },
            close_share_pop(){
                this.$refs.sharePopup.close();
            },
			listItemClick(item, index) {
				const {
					pageType
				} = this;
				let id = pageType === 'course' ? 0 : item.id;
				this.$navigator('/pages/yknowledge/course/detail?sid=' + item.special_id + '&id=' + id);
				// this.goPages()
			},
			getList: debounce(function() {
				if (!this.page.more || this.requestLoading) return;
				this.requestLoading = true;
				this.loadmoreStatus = 'loading';
				this.req(this.page).then(res => {
					this.requestLoading = false;
					this.listArr = this.listArr.concat(res.data);
					const isMore = res.data.length === this.page.limit;
					this.page.more = isMore;
					this.page.page++;
					this.loadmoreStatus = isMore ? 'loadmore' : 'nomore';
				})

			}, 500, true),
			shareClick(item) {
				const {
					order_id,
					special_id
				} = item;
				const link =
					`/pages/yknowledge/course/detail?sid=${special_id}&gift_order_id=${order_id}&gift=1&gift_uid=${this.$store.state.userInfo.uid}`;
				this.shareConfig = {
					desc: item.title,
					title: item.title,
					link,
					imgUrl: item.image
				};
				// #ifdef H5
                console.log('this.shareConfig',this.shareConfig)
                console.log('!_isWeixin',!_isWeixin)
				openShareAll(this.shareConfig, !_isWeixin);
                if(_isWeixin){
                    this.share_pop()
                }
				// #endif
			}
		},
		onLoad(option) {
			this.getList();
		},
		onReachBottom() {
			this.getList()
		},
		// #ifdef MP
		onShareAppMessage(options) {
			const {
				from,
				target
			} = options;
			if (from === 'button') {
				return {
					title: this.shareConfig.title || '',
					imageUrl: this.shareConfig.imgUrl,
					path: this.shareConfig.link,
                    templateId: SHARE_ID
				};
			}
		}
		// #endif
	}
</script>
<style lang='scss' scoped>
	.common_wrap {
		margin-top: 40rpx;

		.gift_top {
			padding-bottom: 20rpx;
			font-size: 24rpx;
			border-bottom: 1rpx dashed #ccc;
			margin-bottom: 20rpx;

			.ungo {}

			.go {
				font-size: 24rpx;
				background: #feb720;
				color: #fff;
				padding: 10rpx 20rpx;
			}
		}
	}
</style>
