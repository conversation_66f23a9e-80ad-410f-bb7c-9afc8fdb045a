<template>
	<view class="common_wrap">
		<listItem :arr="listArr" :rbType="1" :types="types" :lspan="true" rspan="rspan1" :nodata="true" :pagemore="page.more" :loadmoreStatus="loadmoreStatus"
			@listItemClick="listItemClick"/>
        <x-authorize @login="getList(true)"></x-authorize>
	</view>
</template>
<script>
	import {
		getSpecialCollection,
		getSpecialMyCourses,
		getSpecialMyLearningRecords
	} from '@/api/yknowledge.js'
	import listItem from '@/components/zsff/list-item.vue';
	import {
		debounce,
        checkLogin,
        autoAuth,
        zxauthNavigator
	} from '@/utils/common.js'
	export default {
		components: {
			listItem
		},
		data() {
			return {
				pageType: 'course',
                pageTitle:'我的课程',
				req: null,
                
				listArr: [],
				requestLoading: false,
				loadmoreStatus:'loadmore',
				page: {
					page: 1,
					limit: 20,
					more: true
				},
                types:false
			};
		},
		methods: {
			listItemClick(item, index) {
				console.log(item);
				const {
					index_type = 1,
					id,
					special_id
				} = item;
				switch (Number(index_type)) {
					case 1:
						this.$navigator(`/pages/yknowledge/course/detail?sid=${special_id}&id=0`);
						break;
					case 2:
						this.$navigator(`/pages/yknowledge/course/detail_list?sid=${special_id}&id=${id}`)
						break;
				}
			},
			getList: debounce(function(type) {
                if(type){
                    this.requestLoading = false;
                }
				if (!this.page.more || this.requestLoading) return;
				this.requestLoading = true;
				this.loadmoreStatus ='loading';
				this.req(this.page).then(res => {
					this.requestLoading = false;
					this.listArr = this.listArr.concat(res.data);
					
					const isMore = res.data.length === this.page.limit;
					
					this.page.more = isMore;
					this.page.page++;
					
					this.loadmoreStatus = isMore ? 'loadmore':'nomore';
				})

			}, 500, true),
		},
        // #ifdef MP
        onShareAppMessage() {
            return {
                title: this.pageTitle,
                path: 'pages/yknowledge/user/course?type=' + this.pageType,
            };
        },
        onShareTimeline() {
            return {
                title: this.pageTitle,
                path: 'pages/yknowledge/user/course?type=' + this.pageType,
            };
        },
        // #endif
		onLoad(option) {
			const {
				type = 'course'
			} = option;

			if (type === 'course') {
				this.pageTitle = '我的课程';
				this.req = getSpecialMyCourses;
                this.types = true;
			} else if (type === 'collect') {
				this.pageTitle = '我的收藏';
				this.req = getSpecialCollection;
			} else if (type === 'record') {
				this.pageTitle = '观看记录';
				this.req = getSpecialMyLearningRecords;
			}
			this.pageType = type;
			
			this.$updateTitle(this.pageTitle);

			this.getList();
		},
		onReachBottom() {
			this.getList()
		}
	}
</script>
<style lang='scss' scoped>
	.common_wrap {
		margin-top: 40rpx;
	}
</style>
