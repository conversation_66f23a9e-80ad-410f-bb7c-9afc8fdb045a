<template>
    <view class="" :style="[{paddingTop:customBarzx + 'rpx'}]">
        <!-- #ifdef MP-WEIXIN -->
        <view class="nav" :style="[{height:customBarzx + 'rpx'}]">
            <image class="nav-img" :style="[{top:menuButtonInfo.top*2 + 'rpx'}]" src="../../../static/images/yuanshi/left-arrow.png" mode="" @click="goback"></image>
            <view class="nav-text" :style="[{top:menuButtonInfo.top*2 + 'rpx'}]">
                我参与的评论
                <view class="nav-text-dots" v-if="commentMessageNumber"></view>
            </view>
        </view>
        <!-- #endif -->
        <xComment ref="xComment" :permissionArr="permissionArr" :userPermission="true" :arr="arr" @showInput="showInput"
            @permissionClick="permissionClick" :isshowdot="true" pageType="list"/>
        <view class="comment-load-more">
            <u-loadmore :status="loadmoreStatus" :loadText="loadText" color="#999999" />
        </view>
        <xChat :adjustPosition="false" :placeholder="placeholder" :inputShow="inputShow" @send="submitComment">
        </xChat>
    </view>
</template>
<script>
    import xComment from '@/components/x-comment/x-comment/x-comment2.vue';
    import xChat from '@/components/x-chat/x-chat';
    import xCommentChat from '@/mixins/xCommentChat.js';
    import {
        systemMessageStatus
    } from '@/api/yuanshi/user.js'
    import {
        getSpecialCommentAdd
    } from '@/api/yknowledge';
    import {
        debounce
    } from '@/utils/common.js'
    import {
        getMyAllMessage,
        handleDelMyMessage,
        handleDelMyComment
    } from '@/api/zsff.js'
    export default {
        components: {
            xChat,
            xComment
        },
        mixins: [xCommentChat],
        data() {
            return {
                customBarzx: 0,
                menuButtonInfo:{},
                bakstatus: false,
                oindex: false,
                message_id: -1,
                permissionArr: [{
                    name: '编辑',
                    type: 1,
                    floorDisabled: true
                }, {
                    name: '删除',
                    type: 2
                }],
                arr: [],
                inputShow: false,
                placeholder: '',
                requestLoading: false,
                loadmoreStatus: 'loading',
                page: {
                    page: 1,
                    limit: 10,
                    more: true
                },
                loadText: {
                    loadmore: '加载更多',
                    loading: '努力加载中',
                    nomore: '已加载全部'
                },
                commentMessageNumber:0
            };
        },
        methods: {
            getSystemMessageStatus() {
                let that = this;
                systemMessageStatus().then(res => {
                    that.commentMessageNumber = res.data.comment_message_number;
                }).catch(err => {
                    console.log('err',err)
                })
            },
            goback(){
                this.$navigator(-1)
            },
            getCustomBarzx() {
                let that = this;
                that.menuButtonInfo = uni.getMenuButtonBoundingClientRect()
                uni.getSystemInfo({
                    success: function(e) {
                        // console.log('that.menuButtonInfo', that.menuButtonInfo)
                        that.customBarzx = (that.menuButtonInfo.bottom + that.menuButtonInfo.top - e.statusBarHeight) * 2
                        // console.log('that.customBarzx', that.customBarzx)
                    }
                });
                that.getSystemMessageStatus()
            },
            permissionClick(item, idx, index1, index2, index3, floor) {
                const {
                    type
                } = this.permissionArr[idx], _this = this;
                console.log(item, floor)
                if (type === 1) {
                    // 编辑
                    const {
                        id,
                        special_id
                    } = item;
                    this.$navigator(`/pages/yknowledge/comment/submit?id=${id}&sid=${special_id}&type=edit`)
                } else {
                    // 删除
                    uni.showModal({
                        content: '确认进行该操作吗',
                        success: function(res) {
                            if (res.confirm) {
                                let req = null;
                                if (floor === 1) {
                                    req = handleDelMyMessage;
                                } else {
                                    req = handleDelMyComment;
                                }
                                req(item.id).then(res => {
                                    _this.$refs.xComment.updateListBySplice(index1, index2, index3,
                                        floor);
                                })
                            } else {}
                        }
                    })

                }
            },
            showInput(info, placeholder) {
                this.chatInfo = info;
                this.inputShow = true;
                this.placeholder = placeholder;
            },
            submitComment(val) {
                let param = {
                    ...this.getSubmitInfo(),
                    comment: val,
                    // #ifdef MP-TOUTIAO
                    from:'bytedance',
                    // #endif
                    // #ifndef MP-TOUTIAO
                    from:'routine',
                    // #endif
                }
                getSpecialCommentAdd(param).then(res => {
                    this.updateAddInfo(res.data)
                }).catch(err => {})
            },
            getMessageList: debounce(function(init, keyword = '') {
                if (!this.page.more || this.requestLoading) return;
                this.requestLoading = true;
                this.loadmoreStatus = 'loading';
                let param = this.page;
                if (this.message_id != -1) {
                    param.message_id = this.message_id;
                }
                getMyAllMessage(param).then(res => {
                    this.requestLoading = false;
                    if (this.oindex) {
                        if (this.bakstatus) {
                            this.arr = this.arr.concat(res.data);
                        } else {
                            this.arr = res.data;
                        }
                    } else {
                        this.arr = this.arr.concat(res.data);
                    }
                    const isMore = res.data.length === this.page.limit;
                    this.page.more = isMore;
                    this.page.page++;
                    this.loadmoreStatus = isMore ? 'loadmore' : 'nomore';
                    console.log('this.arr', this.arr)
                })
            }),
            updateData() {
                this.page = {
                    page: 1,
                    limit: 10,
                    more: true
                };
                this.getMessageList('1111111')
            }
        },
        onLoad(option) {
            if (option.message_id) {
                this.message_id = option.message_id;
            }
            this.getMessageList()
        },
        onShow() {
            if (this.oindex) {
                console.log('重载列表')
                this.updateData()
            }
            // #ifdef MP-WEIXIN
            this.getCustomBarzx()
            // #endif
        },

        onReachBottom() {
            // if (this.tabIdx === -1 || this.tabIdx1 === -1) return;
            this.getMessageList()
            if (this.oindex) {
                this.bakstatus = true;
            }
        },
    }
</script>

<style lang='scss' scoped>
    .comment-load-more {
        padding: 100rpx 0 0 0;
    }

    .nav {
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        background-color: #ffffff;
        z-index: 999;
        box-sizing: border-box;

        .nav-img {
            position: absolute;
            left: 30rpx;
            top: 80rpx;
            width: 34rpx;
            height: 34rpx;
        }
        .nav-text {
            position: absolute;
            left: 50%;
            top: 80rpx;
            transform: translateX(-50%);
            font-size: 30rpx;
            color: #000000;
            font-weight: 500;
            
            .nav-text-dots {
                position: absolute;
                width: 24rpx;
                height: 24rpx;
                background: #ff5656;
                border: 2rpx solid #ffffff;
                border-radius: 50%;
                right: -22rpx;
                    top: 0rpx;
            }
        }
    }
</style>
