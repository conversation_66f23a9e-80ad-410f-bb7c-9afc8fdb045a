<template>
	<view class="product-con" >
		<view class="header acea-row row-center-wrapper" :style="'opacity:' + opacity" ref="header">
			<view class="item" :class="navActive === index ? 'on' : ''" v-for="(item, index) in navList" :key="index" @click="asideTap(index)">{{ item }}</view>
		</view>
		<view id="title0">
			<ProductConSwiper :imgUrls="imgUrls"></ProductConSwiper>
			<view class="wrapper">
				<view class="share acea-row row-between row-bottom">
					<view class="money font-color-red">
						￥
						<span class="num" v-text="storeInfo.price"></span>
						<span class="y-money" v-text="'￥' + storeInfo.product_price"></span>
					</view>
					<!-- #ifdef H5 -->
					<view class="iconfont icon-fenxiang" @click="setPosterImageStatus"></view>
					<!-- #endif -->
					<!-- #ifdef MP-WEIXIN -->
					<button open-type="share"><view class="iconfont icon-fenxiang" @click="setPosterImageStatus"></view></button>
					<!-- #endif -->
				</view>
				<view class="introduce" v-text="storeInfo.title"></view>
				<view class="label acea-row row-between-wrapper">
					<view v-text="'类型：' + storeInfo.people + '人团'"></view>
					<view>累计销量：{{ storeInfo.total ? storeInfo.total : 0 }} 件</view>
					<view>限量: {{ storeInfo.quota_show ? storeInfo.quota_show : 0 }}件</view>
				</view>
			</view>
			<view class="attribute acea-row row-between-wrapper" @click="selecAttrTap" v-if="attr.productAttr.length !== 0">
				<view>
					{{ attrTxt }}：
					<span class="atterTxt">{{ attrValue }}</span>
				</view>
				<view class="iconfont icon-jiantou"></view>
			</view>
			<view class="notice acea-row row-middle">
				<view class="num font-color-red">
					<span class="iconfont icon-laba"></span>
					已拼{{ storeInfo.sales }}{{ storeInfo.unit_name }}
					<span class="line">|</span>
				</view>
				<view class="swiper-no-swiping swiper"><xSwiper :arr="itemNew"></xSwiper></view>
			</view>
			<view class="assemble">
				<view v-if="groupList">
					<view v-for="(item, index) in groupList" :key="index">
						<view class="item acea-row row-between-wrapper" v-if="index < groupListCount">
							<view class="pictxt acea-row row-between-wrapper">
								<view class="pictrue"><image :src="item.avatar" class="image" /></view>
								<view class="text line1" v-text="item.nickname"></view>
							</view>
							<view class="right acea-row row-middle">
								<view>
									<view class="lack">
										还差
										<span class="font-color-red" v-text="item.count"></span>
										人成团
									</view>
									<CountDown :is-day="false" :tip-text="'剩余 '" :day-text="' '" :hour-text="':'" :minute-text="':'" :second-text="' '" :datatime="item.stop_time"></CountDown>
								</view>
								<view class="spellBnt" @click="goPages(`/pages/activity/GroupRule?pink_id=${item.id}`)">
									去拼单
									<span class="iconfont icon-jiantou"></span>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="more" v-if="groupList.length > groupListCount" @click="setGroupListCount">
					查看更多
					<span class="iconfont icon-xiangxia"></span>
				</view>
			</view>
			<view class="playWay">
				<view class="title acea-row row-between-wrapper"><view>拼团玩法</view></view>
				<view class="way acea-row row-middle">
					<view class="item way_font">
						<span class="num">①</span>
						开团/参团
					</view>
					<view class="iconfont icon-arrow"></view>
					<view class="item way_font">
						<span class="num">②</span>
						邀请好友
					</view>
					<view class="iconfont icon-arrow"></view>
					<view class="item way_font">
						<view>
							<span class="num">③</span>
							满员发货
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="userEvaluation" id="title1">
			<view class="title acea-row row-between-wrapper">
				<view v-text="'用户评价(' + replyCount + ')'"></view>
				<view class="praise" @click="goPages(`/pages/shop/EvaluateList?id=${storeInfo.product_id}`)">
					<span class="font-color-red" v-text="replyChance + '%'"></span>
					好评率
					<span class="iconfont icon-jiantou"></span>
				</view>
			</view>
			<UserEvaluation :reply="reply" v-if="reply.length !== 0"></UserEvaluation>
		</view>
		<view class="product-intro" id="title2">
			<view class="title">产品介绍</view>
			<view class="conter">
				<u-parse :html="storeInfo.description" :tag-style="parseStyle" @linkpress="$linkpress"></u-parse></view>
		</view>
		<view style="height:120rpx;"></view>
		<view class="footer acea-row row-between-wrapper">
            <!-- #ifdef MP-WEIXIN -->
            <view @click="openWeChat" class="item">
            	<view class="iconfont icon-kefu"></view>
            	<view>客服</view>
            </view>
            <!-- #endif -->
			<!-- #ifndef MP-WEIXIN -->
			<view @click="goPages(`/pages/user/CustomerList?id=${id}&type=0&scence=pintuan`,true)" class="item">
				<view class="iconfont icon-kefu"></view>
				<view>客服</view>
			</view>
			<!-- #endif -->
			<view class="item acea-row row-center-wrapper row-column" @click="setCollect">
				<view class="iconfont" :class="storeInfo.userCollect ? 'icon-shoucang1' : 'icon-shoucang'"></view>
				<view>收藏</view>
			</view>
			<view class="bnt acea-row" >
				<view class="joinCart" @click="goPages(`/pages/shop/GoodsCon?id=${storeInfo.product_id}`)">单独购买</view>
				<template v-if="attr.productSelect.quota > 0 && attr.productSelect.product_stock > 0">
					<view class="buy" @click="openTeam">立即开团</view>
				</template>
				<template v-if="attr.productSelect.quota <= 0 || attr.productSelect.product_stock <= 0">
					<view class="buy bg-color-hui">已售罄</view>
				</template>
			</view>
		</view>
		<ProductWindow v-on:changeFun="changeFun" :attr="attr"></ProductWindow>
		<!-- #ifdef H5 -->
		<xShare v-model="posterImageStatus" :share="posterData"></xShare>
		<!-- #endif -->
		<x-authorize :isHidden="true" @login="mountedStart"></x-authorize>
		<x-home></x-home>
	</view>
</template>

<script>
import xSwiper from '@/components/x-swiper/x-swiper.vue';
import ProductConSwiper from '@/components/ProductConSwiper';
import CountDown from '@/components/CountDown';
import UserEvaluation from '@/components/UserEvaluation';
import ProductWindow from '@/components/ProductWindow';
import { getCombinationDetail } from '@/api/activity';
import { postCartAdd } from '@/api/store';
import { uniSelectorQueryInfo } from '@/utils/uni_api.js';
import { getCollectAdd, getCollectDel } from '@/api/user';
import { debounce,
authNavigator,
openWeChatCustomerService} from '@/utils/common.js';
import {
        SHARE_ID,
        WX_KEFU_Link,
        WX_ENTERPRISE_Link
    } from '@/config.js';
    
// #ifdef H5
import xShare from '@/components/x-share/x-share.vue';
import { isWeixin } from '@/utils/validate.js';
import { openShareAll } from '@/utils/wechat/share.js';
// #endif

const NAME = 'GroupDetails';

export default {
	name: 'GroupDetails',
	components: {
		ProductConSwiper,
		CountDown,
		UserEvaluation,
		xSwiper,
		// #ifdef H5
		xShare,
		// #endif
		ProductWindow
	},
	props: {},
	data: function() {
		return {
			domStatus: false,
			posterData: {
				image: '',
				title: '',
				price: '',
				code: ''
			},
			posterImageStatus: false,
			reply: [],
			replyCount: 0,
			replyChance: 0,
			imgUrls: [],
			storeInfo: {},
			itemNew: [],
			groupListCount: 2,
			groupList: {},
			attr: {
				cartAttr: false,
				productSelect: {
					image: '',
					store_name: '',
					price: '',
					quota: 0,
					quota_show: 0,
					unique: '',
					cart_num: 1,
					product_stock: 0
				},
				productAttr: []
			},
			attrTxt: '请选择',
			isOpen: false, //是否打开属性组件
			attrValue: '',
			productValue: [],
			navList: [],
			lock: false,
			navActive: 0,
			opacity: 0,
			storeSelfMention: true,
			storeItems: {},
			activity: [],
			id:''
		};
	},
	watch: {
		// #ifdef H5
		$route(n) {
		  if (n.name === NAME) {
		    this.mountedStart();
		  }
		}
		// #endif 
	},
	mounted: function() {},

	onLoad(options) {
		const { id } = options;
		this.id = id;
		this.mountedStart();
	},
	methods: {
        openWeChat(){
            openWeChatCustomerService(WX_KEFU_Link,WX_ENTERPRISE_Link)
        },
		goPages(path, type) {
             if (type) {
                 this.$authNavigator(path);
                } else {
                 this.$navigator(path);
                }

		},
		//收藏商品
		setCollect: function() {
			let that = this,
				id = that.storeInfo.product_id,
				category = 'product';
			if (that.storeInfo.userCollect) {
				getCollectDel(id, category).then(function() {
					that.storeInfo.userCollect = !that.storeInfo.userCollect;
				});
			} else {
				getCollectAdd(id, category).then(function() {
					that.storeInfo.userCollect = !that.storeInfo.userCollect;
				});
			}
		},
		//打开属性插件；
		selecAttrTap: function() {
			this.attr.cartAttr = true;
			this.isOpen = true;
		},
		//商品详情；
		mountedStart: function() {
			var that = this;	
			getCombinationDetail(this.id)
				.then(res => {
					that.$set(that, 'storeInfo', res.data.storeInfo);
					that.$set(that, 'imgUrls', res.data.storeInfo.images);
					that.$set(that, 'itemNew', res.data.pink_ok_list);
					that.$set(that, 'groupList', res.data.pink);
					if (res.data.reply) that.$set(that, 'reply', Array.from(res.data.reply) || []);
					that.$set(that, 'replyCount', res.data.replyCount);
					that.$set(that, 'replyChance', res.data.replyChance);
					that.setProductSelect();

					let title = that.storeInfo.title;
					if (title.length > 30) {
						title = title.substring(0, 30) + '...';
					}

					that.$set(that, 'posterData', {
						title,
						image: that.storeInfo.image_base,
						price: that.storeInfo.price,
						id: that.id,
						code: that.storeInfo.code_base,
						desc: that.storeInfo.info,
						//  #ifdef H5
						href: `/pages/activity/GroupDetails?id=${this.id}`
						// #endif
					});

					that.attr.productAttr = res.data.productAttr;
					that.productValue = res.data.productValue;
					that.domStatus = true;
					let navList = ['商品', '评价', '详情'];
					that.navList = navList;
					// that.getImageBase64();
					// that.setShare();
					that.$updateTitle(that.storeInfo.title);
					that.DefaultSelect();
					that.initScrollInfo();
				})
				.catch(err => {
					console.log(err);
					that.$showToast(err.msg || err);
					that.$navigator(-1);
				});
		},
		//默认选中属性；
		DefaultSelect: function() {
			let productAttr = this.attr.productAttr,
				value = [];
			for (var key in this.productValue) {
				if (this.productValue[key].quota > 0) {
					value = this.attr.productAttr.length ? key.split(',') : [];
					break;
				}
			}
			for (let i = 0; i < productAttr.length; i++) {
				this.$set(productAttr[i], 'index', value[i]);
			}
			this.$set(this.attr.productSelect, 'num', this.storeInfo.num);
			//sort();排序函数:数字-英文-汉字；
			let productSelect = this.productValue[value.sort().join(',')];
			if (productSelect && productAttr.length) {
				this.$set(this.attr.productSelect, 'store_name', this.storeInfo.title);
				this.$set(this.attr.productSelect, 'image', productSelect.image);
				this.$set(this.attr.productSelect, 'price', productSelect.price);
				this.$set(this.attr.productSelect, 'quota', productSelect.quota);
				this.$set(this.attr.productSelect, 'unique', productSelect.unique);
				this.$set(this.attr.productSelect, 'cart_num', 1);
				this.$set(this.attr.productSelect, 'product_stock', productSelect.product_stock);
				this.$set(this.attr.productSelect, 'quota_show', productSelect.quota_show);
				this.$set(this, 'attrValue', value.sort().join(','));
				this.$set(this, 'attrTxt', '已选择');
			} else if (!productSelect && productAttr.length) {
				this.$set(this.attr.productSelect, 'store_name', this.storeInfo.title);
				this.$set(this.attr.productSelect, 'image', this.storeInfo.image);
				this.$set(this.attr.productSelect, 'price', this.storeInfo.price);
				this.$set(this.attr.productSelect, 'quota', this.storeInfo.quota || 0);
				this.$set(this.attr.productSelect, 'unique', '');
				this.$set(this.attr.productSelect, 'cart_num', 0);
				this.$set(this.attr.productSelect, 'quota_show', this.storeInfo.quota_show || 0);
				this.$set(this.attr.productSelect, 'product_stock', this.storeInfo.product_stock || 0);
				this.$set(this, 'attrValue', '');
				this.$set(this, 'attrTxt', '请选择');
			} else if (!productSelect && !productAttr.length) {
				this.$set(this.attr.productSelect, 'store_name', this.storeInfo.title);
				this.$set(this.attr.productSelect, 'image', this.storeInfo.image);
				this.$set(this.attr.productSelect, 'price', this.storeInfo.price);
				this.$set(this.attr.productSelect, 'quota', this.storeInfo.quota || 0);
				this.$set(this.attr.productSelect, 'quota_show', this.storeInfo.quota_show || 0);
				this.$set(this.attr.productSelect, 'product_stock', this.storeInfo.product_stock || 0);
				this.$set(this.attr.productSelect, 'unique', this.storeInfo.unique || '');
				this.$set(this.attr.productSelect, 'cart_num', 1);
				this.$set(this, 'attrValue', '');
				this.$set(this, 'attrTxt', '请选择');
			}
		},

		setPosterImageStatus: function() {
			this.posterImageStatus = !this.posterImageStatus;
		},

		setGroupListCount: function() {
			this.groupListCount = this.groupListCount + 2;
		},
		//将父级向子集多次传送的函数合二为一；
		changeFun: function(opt) {
			if (typeof opt !== 'object') opt = {};
			let action = opt.action || '';
			let value = opt.value === undefined ? '' : opt.value;
			this[action] && this[action](value);
		},
		changeattr: function(res) {
			var that = this;
			that.attr.cartAttr = res;
		},
		//选择属性；
		ChangeAttr: function(res) {
			let productSelect = this.productValue[res];
			this.$set(this.attr.productSelect, 'num', this.storeInfo.num);
			if (productSelect) {
				this.$set(this.attr.productSelect, 'image', productSelect.image);
				this.$set(this.attr.productSelect, 'price', productSelect.price);
				this.$set(this.attr.productSelect, 'quota', productSelect.quota);
				this.$set(this.attr.productSelect, 'unique', productSelect.unique);
				this.$set(this.attr.productSelect, 'cart_num', 1);
				this.$set(this.attr.productSelect, 'product_stock', productSelect.product_stock);
				this.$set(this.attr.productSelect, 'quota_show', productSelect.quota_show);
				this.$set(this, 'attrValue', res);
				this.$set(this, 'attrTxt', '已选择');
			} else {
				this.$set(this.attr.productSelect, 'image', this.storeInfo.image);
				this.$set(this.attr.productSelect, 'price', this.storeInfo.price);
				this.$set(this.attr.productSelect, 'quota', 0);
				this.$set(this.attr.productSelect, 'unique', '');
				this.$set(this.attr.productSelect, 'cart_num', 0);
				this.$set(this.attr.productSelect, 'quota_show', 0);
				this.$set(this.attr.productSelect, 'product_stock', 0);
				this.$set(this, 'attrValue', '');
				this.$set(this, 'attrTxt', '请选择');
			}
		},

		//购物车；
		ChangeCartNum: function(res) {
			//changeValue:是否 加|减
			//获取当前变动属性
			let productSelect = this.productValue[this.attrValue];
			//如果没有属性,赋值给商品默认库存
			if (productSelect === undefined && !this.attr.productAttr.length) productSelect = this.attr.productSelect;
			if (productSelect === undefined) return;
			let quota = productSelect.quota || 0;
			let nums = this.storeInfo.num || 0;
			let num = this.attr.productSelect;
			let productStock = num.product_stock || 0;
			if (res) {
				num.cart_num++;
				let arrMin = [];
				arrMin.push(nums);
				arrMin.push(quota);
				arrMin.push(productStock);
				let minN = Math.min.apply(null, arrMin);
				if (num.cart_num >= minN) {
					this.$set(this.attr.productSelect, 'cart_num', minN ? minN : 1);
					this.$set(this, 'cart_num', minN ? minN : 1);
				}
			} else {
				num.cart_num--;
				if (num.cart_num < 1) {
					this.$set(this.attr.productSelect, 'cart_num', 1);
					this.$set(this, 'cart_num', 1);
				}
			}
		},
		setProductSelect: function() {
			var that = this;
			var attr = that.attr;
			attr.productSelect.image = that.storeInfo.image;
			attr.productSelect.store_name = that.storeInfo.title;
			attr.productSelect.price = that.storeInfo.price;
			attr.productSelect.quota = 0;
			attr.productSelect.quota_show = 0;
			attr.productSelect.product_stock = 0;
			attr.cartAttr = false;
			that.$set(attr.productSelect, 'num', that.storeInfo.num);
			that.$set(that, 'attr', attr);
		},
		openTeam: function() {
			var that = this;
			if (that.attr.cartAttr == false) {
				that.attr.cartAttr = !this.attr.cartAttr;
			} else {
				var data = {};
				data.productId = that.storeInfo.product_id;
				data.cartNum = that.attr.productSelect.cart_num;
				data.uniqueId = that.attr.productSelect.unique;
				data.combinationId = that.storeInfo.id;
				data.new = 1;
				postCartAdd(data)
					.then(res => {
						that.$navigator('/pages/order/OrderSubmission?cartId=' + res.data.cartId);
					})
					.catch(res => {
						return that.$showToast(res.msg || res);
					});
			}
		},
		asideTap(index) {
			this.navActive = index;
			let h = this.$store.state.stytemInfo.statusBarHeight;
			// #ifdef MP
			h = h + this.$store.state.navigationBarHeight;
			// #endif
			uni.pageScrollTo({
				scrollTop: this.navTopArr[index] - h,
				selector: '#title' + index,
				duration: 300
			});
			this.lock = true;
		},
		async initScrollInfo() {
			let topArr = [],
				heightArr = [];
			for (let i = 0; i < this.navList.length; i++) {
				const { top } = await uniSelectorQueryInfo('#title' + i, this);
				topArr.push(top);
			}
			this.navTopArr = topArr;
		}
	},
	onPageScroll(e) {
		var that = this,
			scrollY = e.scrollTop;
		var opacity = scrollY / 350;
		this.opacity = opacity > 1 ? 1 : opacity;
		if (this.lock) {
			this.lock = false;
			return;
		}
	},
	// #ifdef MP
	onShareAppMessage() {
		if (this.posterData) {
			return {
				title: this.posterData.title || '',
				imageUrl: this.posterData.image || '',
				path: `/pages/activity/GroupDetails?id=${this.id}&spid=${this.$store.state.userInfo.uid}`,
                templateId:SHARE_ID
                
			};
		}
	}
	// #endif
};
</script>
<style scoped>
.product-con .wrapper .share .money .y-money {
	color: #82848f;
	margin-left: 13rpx;
	text-decoration: line-through;
	font-weight: normal;
}

.product-con .notice {
	width: 100%;
	height: 62rpx;
	background-color: #ffedeb;
	margin-top: 20rpx;
	padding: 0 30rpx;
}

.product-con .notice .num {
	font-size: 24rpx;
}

.product-con .notice .num .iconfont {
	font-size: 30rpx;
	vertical-align: -3rpx;
	margin-right: 20rpx;
}

.product-con .notice .num .line {
	color: #282828;
	margin-left: 15rpx;
}

.product-con .notice .swiper {
	height: 100%;
	line-height: 62rpx;
	overflow: hidden;
	margin-left: 14rpx;
}

.product-con .notice .swiper .swiper-slide {
	height: 100%;
	width: 100%;
	overflow: hidden;
	font-size: 24rpx;
	color: #282828;
}

.product-con .assemble {
	background-color: #fff;
}

.product-con .assemble .item {
	padding-right: 30rpx;
	margin-left: 30rpx;
	border-bottom: 1px solid #f0f0f0;
	height: 132rpx;
}

.product-con .assemble .item .pictxt {
	width: 295rpx;
}

.product-con .assemble .item .pictxt .text {
	width: 194rpx;
	font-size: 28rpx;
}

.product-con .assemble .item .pictxt .pictrue {
	width: 80rpx;
	height: 80rpx;
}

.product-con .assemble .item .pictxt .pictrue image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.product-con .assemble .item .right .lack {
	font-size: 24rpx;
	color: #333333;
	text-align: right;
}

.product-con .assemble .item .right .time {
	font-size: 22rpx;
	color: #82848f;
	margin-top: 5rpx;
}

.product-con .assemble .item .right .spellBnt {
	font-size: 24rpx;
	color: #fff;
	width: 140rpx;
	height: 50rpx;
	border-radius: 50rpx;
	background-image: linear-gradient(to right, #ff2358 0%, #ff0000 100%);
	background-image: -webkit-linear-gradient(to right, #ff2358 0%, #ff0000 100%);
	background-image: -moz-linear-gradient(to right, #ff2358 0%, #ff0000 100%);
	text-align: center;
	line-height: 50rpx;
	margin-left: 30rpx;
}

.product-con .assemble .item .right .spellBnt .iconfont {
	font-size: 20rpx;
	margin-left: 5rpx;
}

.product-con .assemble .more {
	font-size: 24rpx;
	color: #282828;
	text-align: center;
	height: 90rpx;
	line-height: 90rpx;
}

.product-con .assemble .more .iconfont {
	margin-left: 13rpx;
	font-size: 25rpx;
}

.product-con .playWay {
	background-color: #fff;
	padding: 0 30rpx;
	margin-top: 20rpx;
	font-size: 28rpx;
	color: #282828;
}

.product-con .playWay .title {
	height: 86rpx;
	border-bottom: 1px solid #eee;
}

.product-con .playWay .title .iconfont {
	margin-left: 13rpx;
	font-size: 28rpx;
	color: #717171;
}

.product-con .playWay .way {
	min-height: 110rpx;
	font-size: 26rpx;
	color: #282828;
}

.product-con .playWay .way .iconfont {
	color: #cdcdcd;
	font-size: 40rpx;
	margin: 0 35rpx;
}

.product-con .playWay .way .item .num {
	font-size: 30rpx;
	margin-right: 6rpx;
}

.product-con .playWay .way .item .tip {
	font-size: 22rpx;
	color: #a5a5a5;
}


.way_font {
	font-size: 28rpx !important;
}


.product-con .footer-group .bnt.bg-color-violet {
	background-color: #fa8013;
}
.product-con .wrapper {
	padding-bottom: 26rpx;
}
.product-con .bnt {
	width: 77% !important;
}
.product-con .bnt > view {
	width: 50% !important;
}




.icon-shoucang1 {
	color: #e93323;
}
</style>
