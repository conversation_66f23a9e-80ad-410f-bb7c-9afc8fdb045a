<template>
	<view class="poster-poster" v-if="status === false">
		<view class="tip">
			<span class="iconfont icon-shuoming"></span>
			提示：长按图片保存至手机相册
		</view>
		<view class="pictrue"><image :src="image" mode="widthFix" /></view>
	</view>
</template>
<script>
import { getBargainPoster, getCombinationPoster } from '@/api/activity';

export default {
	name: 'Poster',
	components: {},
	props: {},
	data: function() {
		return {
			status: true,
			id: 0,
			image: '',
			// #ifdef H5
			from: 'wechat',
			// #endif
			// #ifdef MP-WEIXIN
			from: 'routine'
			// #endif
		};
	},
	onLoad(options) {
		const { id, type } = options;
		var that = this;
		that.id = id;
		this.$updateTitle(type == 1 ? '拼团海报 ' : '砍价海报');
		this.$setNavigationBarColor('#e93323');
		if (type == 2) that.getBargainPoster();
		else that.getCombinationPoster();
	},
	methods: {
		getBargainPoster: function() {
			var that = this;
			getBargainPoster({ bargainId: that.id, from: this.from,type:1 })
				.then(res => {
					that.image = res.data.url;
					that.status = false;
				})
				.catch(res => {
					that.$showToast(res.msg || res);
				});
		},
		getCombinationPoster: function() {
			var that = this;
			getCombinationPoster({ id: that.id, from: this.from ,type:1})
				.then(res => {
					that.image = res.data.url;
					that.status = false;
				})
				.catch(res => {
					that.$showToast(res.msg || res);
				});
		}
	}
};
</script>
<style lang="scss">
page {
	background-color: $uni-bg-color-active;
}
</style>
<style scoped>
.poster-poster .tip {
	height: 80rpx;
	font-size: 26rpx;
	color: #e8c787;
	text-align: center;
	line-height: 80rpx;
}
.poster-poster .tip .iconfont {
	font-size: 36rpx;
	vertical-align: -4rpx;
	margin-right: 18rpx;
}
.poster-poster .pictrue {
	width: 690rpx;
	height: 100%;
	margin: 0 auto 50rpx auto;
}
.poster-poster .pictrue image {
	width: 100%;
	height: 100%;
}
</style>
