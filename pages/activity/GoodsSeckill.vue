<template>
	<view class="flash-sale " ref="container">
		<view class="saleBox"></view>
		<view class="header" v-if="timeList[active]"><image :src="timeList[active].slide" /></view>
		<view class="seckillList acea-row row-between-wrapper">
			<view class="priceTag"><image src="@/static/images/baokuan.png" /></view>
			<view class="timeLsit">
				<!-- #ifndef MP-TOUTIAO -->
				<xTab :arr="timeList" lineWidth="0" :active="active" height="100%" title="title" @change="setTime">
					<template v-slot="{ item }">
						<view class="timeItem acea-row row-column-around">
							<span class="time">{{ item.time }}</span>
							<span class="state">{{ item.state }}</span>
						</view>
					</template>
				</xTab>
				<!-- #endif -->
			</view>
		</view>
		<view class="time-tabs">
			<view class="list">
				<view class="item acea-row row-between-wrapper" v-for="(itemSeckill, indexSeckill) in seckillList" :key="indexSeckill" @click="goDetail(itemSeckill.id, itemStatus)">
					<view class="pictrue"><image :src="itemSeckill.image" /></view>
					<view class="text acea-row row-column-around">
						<view class="line1" v-text="itemSeckill.title"></view>
						<view class="money">
							<span class="num font-color-red" v-text="'￥' + itemSeckill.price"></span>
							<span v-text="'￥' + itemSeckill.ot_price" class="ot_price"></span>
						</view>
						<view class="stock">
							限量
							<span v-text="itemSeckill.quota_show + '件'"></span>
						</view>
						<view class="progress cart-color">
							<view class="bg-red" :style="{ width: loading ? itemSeckill.percent + '%' : '' }"></view>
							<view class="piece font-color-red" v-text="'已抢' + itemSeckill.percent + '%'"></view>
						</view>
					</view>
					<view class="grab bg-color-red" v-if="itemStatus === 1 && itemSeckill.quota > 0">马上抢</view>
					<view class="grab bg-color-hui" v-if="itemStatus === 1 && itemSeckill.quota <= 0">已售罄</view>
					<view class="grab bg-color-red" v-if="itemStatus === 2">未开始</view>
					<view class="grab bg-color-hui" v-if="itemStatus === 0">已结束</view>
				</view>
			</view>

			<xNodate :arr="seckillList" :page="page" :isR="false" imgSrc="/wximage/noGood.png"></xNodate>
			<Loading :loaded="status" :loading="loadingList" v-if="seckillList.length > 0"></Loading>
		</view>
		<x-home></x-home>
		<view id="title0"></view>
		<view id="title1"></view>
		<view id="title2"></view>
	</view>
</template>
<script>
import { getSeckillConfig, getSeckillList } from '@/api/activity';
// import CountDown from "@/components/CountDown";
import xTab from '@/components/x-tab/x-tab.vue';
import xNodate from '@/components/x-nodata/x-nodata.vue';
import Loading from '@/components/Loading';
export default {
	name: 'GoodsSeckill',
	components: {
		// CountDown,
		xTab,
		xNodate,
		Loading
	},
	props: {},
	data: function() {
		return {
			timeList: [],
			sticky: false,
			loading: false,
			datatime: 0,
			active: 0,
			seckillList: [],
			status: false, //砍价列表是否获取完成 false 未完成 true 完成
			loadingList: false, //当前接口是否请求完成 false 完成 true 未完成
			page: 1, //页码
			limit: 5 ,//数量
			itemStatus:0
		};
	},
	onLoad() {
		this.$setNavigationBarColor('#e93323')
	},
	mounted: function() {
		this.mountedStart();
	},
	methods: {
		mountedStart: function() {
			var that = this;
			getSeckillConfig().then(res => {
				that.$set(that, 'timeList', res.data.seckillTime);
				that.$set(that, 'active', res.data.seckillTimeIndex);
				that.datatime = that.timeList[that.active].stop;
				that.loadingList = false;
				that.itemStatus = that.timeList[that.active].status;
				that.getSeckillList();
			});
			setTimeout(function() {
				that.loading = true;
			}, 500);
		},
		setTime: function(index,item) {
			console.log(item)
			var that = this;
			that.datatime = that.timeList[index].stop;
			that.itemStatus = item.status;
			that.seckillList = [];
			that.page = 1;
			that.status = false;
			that.loadingList = false;
			that.getSeckillList();
		},
		getSeckillList: function() {
			var that = this;
			if (that.loadingList) return;
			if (that.status) return;
			that.loadingList = true;
			var time = that.timeList[that.active].id;
			getSeckillList(time, { page: that.page, limit: that.limit })
				.then(res => {
					that.status = res.data.length < that.limit;
					that.seckillList.push.apply(that.seckillList, res.data);
					that.page++;
					that.loadingList = false;
				})
				.catch(() => {
					that.loadingList = false;
				});
		},
		goDetail: function(id, status) {
			var that = this;
			var time = that.datatime;
			this.$authNavigator(`/pages/activity/SeckillDetails?id=${id}&time=${time}&status=${status}`);
		}
	},
	onReachBottom() {
		this.getSeckillList();
	}
};
</script>
<style scoped>
page {
	background-color: #f5f5f5 !important;
}
.cart-color {
	border: none !important;
}
.saleBox {
	width: 100%;
	height: 254rpx;
	background: #e93323;
	opacity: 1;
	border-radius: 0 0 50rpx 50rpx;
	padding: 24rpx 20rpx 0 20rpx;
	box-sizing: border-box;
}

.timeItem {
	font-size: 22rpx;
	width: 100%;
	text-align: center;
	background-color: #f5f5f5;
	padding: 21rpx 0 28rpx 0;
}
.flash-sale {
	height: 100%;
	background: #f5f5f5 !important;
}
.time-tabs {
	background: #f5f5f5;
	top: 110rpx;
}

.timeItem .time {
	font-size: 36rpx;
	font-weight: bold;
	height: 50rpx;
	line-height: 50rpx;
}
.timeItem .state {
	height: 30rpx;
	line-height: 30rpx;
	font-size: 20rpx;
	width: auto;
	margin: auto;
}
.activity {
	color: #333;
}
.flash-sale .list .item .grab {
	background-color: #999;
}

.list {
	padding: 0 20rpx;
}
.stock {
	color: #999999;
	font-size: 22rpx;
	margin-bottom: 8rpx;
}
.ot_price {
	text-decoration: line-through;
	color: #999999;
	font-size: 24rpx;
}
.flash-sale .header {
	width: 710rpx;
	height: 300rpx;
	margin: -215rpx auto 0 auto;
	border-radius: 20rpx;
}

.flash-sale .header image {
	width: 100%;
	height: 100%;
}

.flash-sale .whiteFixed {
	position: fixed;
	top: 0;
	background-color: #fff;
	left: 0;
	width: 100%;
	z-index: 5;
}

.flash-sale .seckillList {
	padding: 0 20rpx;
}
.flash-sale .seckillList .priceTag {
	width: 75rpx;
	height: 70rpx;
}
.flash-sale .seckillList .priceTag image {
	width: 100%;
	height: 100%;
}
.flash-sale .timeLsit {
	width: 610rpx;
	white-space: nowrap;
	margin: 10rpx 0;
}
.flash-sale .timeLsit .item {
	display: inline-block;
	font-size: 20rpx;
	color: #666;
	text-align: center;
	padding: 11rpx 0;
	box-sizing: border-box;
	height: 96rpx;
	margin-right: 35rpx;
}
.flash-sale .timeLsit .item .time {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}
.flash-sale .timeLsit .item.on .time {
	color: #e93323;
}
.flash-sale .timeLsit .item.on .state {
	width: 90rpx;
	height: 30rpx;
	border-radius: 15rpx;
	background: linear-gradient(90deg, rgba(252, 25, 75, 1) 0%, rgba(252, 60, 32, 1) 100%);
	color: #fff;
}

.flash-sale .countDown {
	height: 92rpx;
	border-bottom: 1px solid #f0f0f0;
	font-size: 28rpx;
	color: #282828;
}

.flash-sale .countDown .timeTxt {
	color: #fc4141;
}

.flash-sale .countDown .time {
	font-size: 28rpx;
	color: #282828;
}

.flash-sale .countDown .styleAll {
	font-size: 28rpx;
	font-weight: bold;
	background-color: #ffcfcb;
	padding: 4rpx 7rpx;
	border-radius: 3rpx;
	color: #fc4141;
}

.flash-sale .countDown .text {
}

.flash-sale .list.on {
	margin-top: 202rpx;
}

.flash-sale .list .item {
	padding: 25rpx;
	border-bottom: 1px solid #f0f0f0;
	height: auto;
	position: relative;
	background: #fff;
	margin-bottom: 20rpx;
	border-radius: 20rpx;
}

.flash-sale .list .item .pictrue {
	width: 180rpx;
	height: 180rpx;
}

.flash-sale .list .item .pictrue image {
	width: 100%;
	height: 100%;
	border-radius: 20rpx;
}

.flash-sale .list .item .text {
	width: 69%;
	font-size: 30rpx;
	color: #333;
}

.flash-sale .list .item .text .money {
	font-size: 24rpx;
	color: #282828;
}

.flash-sale .list .item .text .money .num {
	font-size: 40rpx;
	font-weight: bold;
	margin-right: 10rpx;
}

.flash-sale .list .item .text .progress {
	overflow: hidden;
	background-color: #ffefef;
	width: 260rpx;
	border-radius: 20rpx;
	height: 18rpx;
	position: relative;
}

.flash-sale .list .item .text .progress .bg-red {
	width: 0;
	height: 100%;
	transition: width 0.6s ease;
	-webkit-transition: width 0.6s ease;
	-moz-transition: width 0.6s ease;
	-o-transition: width 0.6s ease;
	background: linear-gradient(90deg, rgba(233, 51, 35, 1) 0%, rgba(255, 137, 51, 1) 100%);
}

.flash-sale .list .item .text .progress .piece {
	position: absolute;
	left: 22%;
	transform: translate(-50%, -50%);
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	top: 49%;
	font-size: 16rpx;
	color: #ffb9b9 !important;
}

.flash-sale .list .item .grab {
	font-size: 28rpx;
	color: #fff;
	width: 150rpx;
	height: 54rpx;
	border-radius: 27rpx;
	text-align: center;
	line-height: 54rpx;
	position: absolute;
	right: 30rpx;
	bottom: 30rpx;
}
</style>
