<template>
	<view class="group-con">
		<view class="header acea-row row-between-wrapper">
			<view class="pictrue"><image :src="storeCombination.image" /></view>
			<view class="text">
				<view class="line1" v-text="storeCombination.title"></view>
				<view class="money">
					￥
					<span class="num" v-text="storeCombination.price"></span>
					<span class="team cart-color" v-text="storeCombination.people + '人拼'"></span>
				</view>
			</view>
			<view v-if="pinkBool === -1" class="iconfont icon-pintuanshibai"></view>
			<view v-else-if="pinkBool === 1" class="iconfont icon-pintuanchenggong font-color-red"></view>
		</view>
		<view class="wrapper">
			<view class="title acea-row row-center-wrapper">
				<view class="line"></view>
				<view class="name acea-row row-center-wrapper">
					剩余
					<CountDown :is-day="false" :tip-text="' '" :day-text="' '" :hour-text="' : '" :minute-text="' : '" :second-text="' '" :datatime="Number(pinkT.stop_time)" styleAll="groupRule"></CountDown>
					结束
				</view>
				<view class="line"></view>
			</view>
			<view class="tips font-color-red" v-if="pinkBool === 1">恭喜您拼团成功</view>
			<view class="tips" v-else-if="pinkBool === -1">还差{{ count }}人，拼团失败</view>
			<view class="tips font-color-red" v-else-if="pinkBool === 0">拼团中，还差{{ count }}人拼团成功</view>
			<view class="list acea-row row-middle" :class="[pinkBool === 1 || pinkBool === -1 ? 'result' : '', iShidden ? 'on' : '']">
				<view class="pictrue"><image :src="pinkT.avatar" /></view>
				<view class="acea-row row-middle" v-if="pinkAll.length > 0">
					<view class="pictrue" v-for="(item, index) in pinkAll" :key="index"><image :src="item.avatar" /></view>
				</view>
				<view class="pictrue" v-for="index in count" :key="index"><image class="img-none" src="@/static/images/vacancy.png" /></view>
			</view>
			<view v-if="(pinkBool === 1 || pinkBool === -1) && count > 9" class="lookAll acea-row row-center-wrapper" @click="lookAll">
				{{ iShidden ? '收起' : '查看全部' }}
				<span class="iconfont" :class="iShidden ? 'icon-xiangshang' : 'icon-xiangxia'"></span>
			</view>
			<view class="teamBnt bg-color-red" v-if="userBool === 1 && isOk == 0 && pinkBool === 0" @click="goPoster">邀请好友参团</view>
			<view class="teamBnt bg-color-red" v-else-if="userBool === 0 && pinkBool === 0 && count > 0" @click="pay">我要参团</view>
			<view class="teamBnt bg-color-red" v-if="pinkBool === 1 || pinkBool === -1" @click="goDetail(storeCombination.id)">再次开团</view>
			<view class="cancel" @click="getCombinationRemove" v-if="pinkBool === 0 && userBool === 1">
				<span class="iconfont icon-guanbi3"></span>
				取消开团
			</view>
			<view class="lookOrder" v-if="pinkBool === 1" @click="goOrder">
				查看订单信息
				<span class="iconfont icon-xiangyou"></span>
			</view>
		</view>
		<view class="group-recommend">
			<view class="title acea-row row-between-wrapper">
				<view>大家都在拼</view>
				<view class="more" @click="goList">
					更多拼团
					<span class="iconfont icon-jiantou"></span>
				</view>
			</view>
			<view class="list acea-row row-middle">
				<view class="item" v-for="(item, index) in storeCombinationHost" :key="index" @click="goDetail(item.id)">
					<view class="pictrue">
						<image :src="item.image" />
						<view class="team" v-text="item.people + '人团'"></view>
					</view>
					<view class="name line1" v-text="item.title"></view>
					<view class="money font-color-red" v-text="'￥' + item.price"></view>
				</view>
			</view>
		</view>

		<Product-window v-on:changeFun="changeFun" :attr="attr"></Product-window>
	</view>
</template>
<script>
import CountDown from '@/components/CountDown';
import ProductWindow from '@/components/ProductWindow';
import { getCombinationPink, getCombinationRemove } from '@/api/activity';
import { postCartAdd } from '@/api/store';

const NAME = 'GroupRule';
export default {
	name: NAME,
	components: {
		CountDown,
		ProductWindow
	},
	props: {},
	data: function() {
		return {
			currentPinkOrder: '', //当前拼团订单
			isOk: 0, //判断拼团是否完成
			pinkBool: 0, //判断拼团是否成功|0=失败,1=成功
			userBool: 0, //判断当前用户是否在团内|0=未在,1=在
			pinkAll: [], //团员
			pinkT: [], //团长信息
			storeCombination: [], //拼团产品
			storeCombinationHost: [], //拼团推荐
			pinkId: 0,
			count: 0, //拼团剩余人数
			iShidden: false,
			isOpen: false, //是否打开属性组件
			attr: {
				cartAttr: false,
				productSelect: {
					image: '',
					store_name: '',
					price: '',
					quota: 0,
					unique: '',
					cart_num: 1,
					num: 0,
					quota_show: 0,
					product_stock: 0
				},
				attrValue: '',
				productAttr: []
			},
			attrTxt:''
		};
	},
	onLoad(options) {
		const { pink_id ,spid,scene} = options;
		var that = this;
		if (scene) {
			var value = getUrlParams(decodeURIComponent(scene));
			if (typeof value === 'object') {
				if (value.pink_id) pink_id = value.pink_id;
				//记录推广人uid
				if (value.spid)  spid = value.spid;
			}
		}
		if(spid){
			this.$store.commit('UPDATE_SPID', spid)
		}
		that.pinkId = pink_id;
		that.getCombinationPink();
	},
	methods: {
		//将父级向子集多次传送的函数合二为一；
		changeFun: function(opt) {
			if (typeof opt !== 'object') opt = {};
			let action = opt.action || '';
			let value = opt.value === undefined ? '' : opt.value;
			this[action] && this[action](value);
		},
		changeattr: function(res) {
			var that = this;
			that.attr.cartAttr = res;
		},
		//选择属性；
		ChangeAttr: function(res) {
			let productSelect = this.productValue[res];
			this.$set(this.attr.productSelect, 'num', this.storeCombination.num);
			if (productSelect) {
				this.$set(this.attr.productSelect, 'image', productSelect.image);
				this.$set(this.attr.productSelect, 'price', productSelect.price);
				this.$set(this.attr.productSelect, 'quota', productSelect.quota);
				this.$set(this.attr.productSelect, 'unique', productSelect.unique);
				this.$set(this.attr.productSelect, 'cart_num', 1);
				this.$set(this.attr.productSelect, 'product_stock', productSelect.product_stock);
				this.$set(this.attr.productSelect, 'quota_show', productSelect.quota_show);
				this.$set(this, 'attrValue', res);
				this.$set(this, 'attrTxt', '已选择');
			} else {
				this.$set(this.attr.productSelect, 'image', this.storeCombination.image);
				this.$set(this.attr.productSelect, 'price', this.storeCombination.price);
				this.$set(this.attr.productSelect, 'quota', 0);
				this.$set(this.attr.productSelect, 'unique', '');
				this.$set(this.attr.productSelect, 'cart_num', 0);
				this.$set(this.attr.productSelect, 'quota_show', 0);
				this.$set(this.attr.productSelect, 'product_stock', 0);
				this.$set(this, 'attrValue', '');
				this.$set(this, 'attrTxt', '请选择');
			}
		},
		ChangeCartNum: function(res) {
			//changeValue:是否 加|减
			//获取当前变动属性
			let productSelect = this.productValue[this.attrValue];

			//如果没有属性,赋值给商品默认库存
			if (productSelect === undefined && !this.attr.productAttr.length) productSelect = this.attr.productSelect;
			if (productSelect === undefined) return;
			let quota = productSelect.quota || 0;
			let nums = this.storeCombination.num || 0;
			let num = this.attr.productSelect;
			let productStock = num.product_stock || 0;
			if (res) {
				num.cart_num++;
				let arrMin = [];
				arrMin.push(nums);
				arrMin.push(quota);
				arrMin.push(productStock);
				let minN = Math.min.apply(null, arrMin);
				if (num.cart_num >= minN) {
					this.$set(this.attr.productSelect, 'cart_num', minN ? minN : 1);
					this.$set(this, 'cart_num', minN ? minN : 1);
				}
			} else {
				num.cart_num--;
				if (num.cart_num < 1) {
					this.$set(this.attr.productSelect, 'cart_num', 1);
					this.$set(this, 'cart_num', 1);
				}
			}
		},
		//默认选中属性；
		DefaultSelect() {
			let productAttr = this.attr.productAttr,
				value = [];
			for (var key in this.productValue) {
				if (this.productValue[key].quota > 0) {
					value = this.attr.productAttr.length ? key.split(',') : [];
					break;
				}
			}
			for (let i = 0; i < productAttr.length; i++) {
				this.$set(productAttr[i], 'index', value[i]);
			}
			this.$set(this.attr.productSelect, 'num', this.storeCombination.num);
			//sort();排序函数:数字-英文-汉字；
			let productSelect = this.productValue[value.sort().join(',')];
			if (productSelect && productAttr.length) {
				this.$set(this.attr.productSelect, 'store_name', this.storeCombination.title);
				this.$set(this.attr.productSelect, 'image', productSelect.image);
				this.$set(this.attr.productSelect, 'price', productSelect.price);
				this.$set(this.attr.productSelect, 'quota', productSelect.quota);
				this.$set(this.attr.productSelect, 'unique', productSelect.unique);
				this.$set(this.attr.productSelect, 'cart_num', 1);
				this.$set(this.attr.productSelect, 'product_stock', productSelect.product_stock);
				this.$set(this.attr.productSelect, 'quota_show', productSelect.quota_show);
				// this.$set(this, 'attrValue', value.sort().join(','));
				this.attrValue = value.sort().join(',');
				this.$set(this, 'attrTxt', '已选择');
			} else if (!productSelect && productAttr.length) {
				this.$set(this.attr.productSelect, 'store_name', this.storeCombination.title);
				this.$set(this.attr.productSelect, 'image', this.storeCombination.image);
				this.$set(this.attr.productSelect, 'price', this.storeCombination.price);
				this.$set(this.attr.productSelect, 'quota', 0);
				this.$set(this.attr.productSelect, 'unique', '');
				this.$set(this.attr.productSelect, 'cart_num', 0);
				this.$set(this.attr.productSelect, 'product_stock', 0);
				this.$set(this.attr.productSelect, 'quota_show', 0);
				this.$set(this, 'attrValue', '');
				this.$set(this, 'attrTxt', '请选择');
			} else if (!productSelect && !productAttr.length) {
				this.$set(this.attr.productSelect, 'store_name', this.storeCombination.title);
				this.$set(this.attr.productSelect, 'image', this.storeCombination.image);
				this.$set(this.attr.productSelect, 'price', this.storeCombination.price);
				this.$set(this.attr.productSelect, 'quota', 0);
				this.$set(this.attr.productSelect, 'unique', this.storeCombination.unique || '');
				this.$set(this.attr.productSelect, 'cart_num', 1);
				this.$set(this.attr.productSelect, 'quota_show', 0);
				this.$set(this.attr.productSelect, 'product_stock', 0);
				this.$set(this, 'attrValue', '');
				this.$set(this, 'attrTxt', '请选择');
			}
		},
		setProductSelect: function() {
			var that = this;
			var attr = that.attr;
			attr.productSelect.image = that.storeCombination.image;
			attr.productSelect.store_name = that.storeCombination.title;
			attr.productSelect.price = that.storeCombination.price;
			attr.productSelect.quota = 0;
			attr.productSelect.quota_show = 0;
			attr.productSelect.product_stock = 0;
			attr.cartAttr = false;
			that.$set(attr.productSelect, 'num', that.storeCombination.num);
			that.$set(that, 'attr', attr);
		},
		pay: function() {
			var that = this;
			that.attr.cartAttr = true;
			that.isOpen = true;
		},
		goPay(res) {
			var that = this;
			var data = {};
			that.attr.cartAttr = res;
			data.productId = that.storeCombination.product_id;
			data.cartNum = that.attr.productSelect.cart_num;
			data.uniqueId = that.attr.productSelect.unique;
			data.combinationId = that.storeCombination.id;
			data.new = 1;
			postCartAdd(data)
				.then(res => {
					that.$navigator('/pages/order/OrderSubmission?cartId=' + res.data.cartId+'&pinkId='+that.pinkId);
				})
				.catch(res => {
					this.$showToast(res.msg || res);
				});
		},
		goPoster: function() {
			var that = this;
			that.$navigator('/pages/activity/Poster?id=' + that.pinkId + '&type=1');
		},
		goOrder: function() {
			var that = this;		
			that.$navigator(`/pages/order/OrderDetails?order_id=${that.currentPinkOrder}`);
		},
		//拼团列表
		goList: function() {
			this.$navigator(`/pages/activity/GoodsGroup`);
		},
		//拼团详情
		goDetail: function(id) {
			this.$navigator(`/pages/activity/GroupDetails?id=${id}`);
		},
		//拼团信息
		getCombinationPink: function() {
			var that = this;
			getCombinationPink(that.pinkId).then(res => {
				that.$set(that, 'storeCombinationHost', res.data.store_combination_host);
				that.$set(that, 'storeCombination', res.data.store_combination);
				that.$set(that, 'pinkT', res.data.pinkT);
				that.$set(that, 'pinkAll', res.data.pinkAll);
				that.$set(that, 'count', res.data.count);
				that.$set(that, 'userBool', res.data.userBool);
				that.$set(that, 'pinkBool', res.data.pinkBool);
				that.$set(that, 'isOk', res.data.is_ok);
				that.$set(that, 'currentPinkOrder', res.data.current_pink_order);
				that.attr.productAttr = res.data.store_combination.productAttr;
				that.productValue = res.data.store_combination.productValue;
				that.setProductSelect();
				if (that.attr.productAttr.length != 0) that.DefaultSelect();
			});
		},
		//拼团取消
		getCombinationRemove: function() {
			var that = this;
			getCombinationRemove({ id: that.pinkId, cid: that.storeCombination.id })
				.then(res => {
					that.$successToast(res.msg);
					that.$navigator(-1);
				})
				.catch(res => {
					that.$showToast(res.msg || res);
				});
		},
		lookAll: function() {
			this.iShidden = !this.iShidden;
		}
	}
};
</script>
<style scoped>
.group-con .header {
	width: 100%;
	height: 186rpx;
	background-color: #fff;
	border-top: 1px solid #f5f5f5;
	padding: 0 30rpx;
	position: relative;
}

.group-con .header .iconfont {
	font-size: 100rpx;
	position: absolute;
	color: #ccc;
	right: 33rpx;
	bottom: 20rpx;
}

.group-con .header .pictrue {
	width: 140rpx;
	height: 140rpx;
}

.group-con .header .pictrue image {
	width: 100%;
	height: 100%;
	border-radius: 6rpx;
}

.group-con .header .text {
	width: 525rpx;
	font-size: 30rpx;
	color: #222;
}

.group-con .header .text .money {
	font-size: 24rpx;
	font-weight: bold;
	margin-top: 15rpx;
}

.group-con .header .text .money .num {
	font-size: 32rpx;
}

.group-con .header .text .money .team {
	padding: 1rpx 10rpx;
	font-weight: normal;
	border-radius: 50rpx;
	font-size: 20rpx;
	vertical-align: 4rpx;
	margin-left: 15rpx;
}

.group-con .wrapper {
	background-color: #fff;
	margin-top: 20rpx;
	padding: 2rpx 0 35rpx 0;
}

.group-con .wrapper .title {
	margin-top: 30rpx;
}

.group-con .wrapper .title .line {
	width: 136rpx;
	height: 1px;
	background-color: #ddd;
}

.group-con .wrapper .title .name {
	margin: 0 45rpx;
	font-size: 28rpx;
	color: #282828;
}

.group-con .wrapper .title .name .time {
	margin: 0 14rpx;
}

.group-con .wrapper .title .name .timeTxt {
	color: #fc4141;
}

.group-con .wrapper .title .name .time .styleAll {
	background-color: #ffcfcb;
	text-align: center;
	border-radius: 3rpx;
	font-size: 28rpx;
	font-weight: bold;
	display: inline-block;
	vertical-align: middle;
	color: #fc4141;
	padding: 2rpx 5rpx;
}

.group-con .wrapper .tips {
	font-size: 30rpx;
	font-weight: bold;
	text-align: center;
	margin-top: 30rpx;
	color: #999;
}

.group-con .wrapper .list {
	padding: 0 30rpx;
	margin-top: 45rpx;
}

.group-con .wrapper .list.result {
	max-height: 240rpx;
	overflow: hidden;
}

.group-con .wrapper .list.result.on {
	max-height: 2000rpx;
}

.group-con .wrapper .list .pictrue {
	width: 94rpx;
	height: 94rpx;
	margin: 0 0 28rpx 35rpx;
}

.group-con .wrapper .list .pictrue image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	border: 2rpx solid #e93323;
}

.group-con .wrapper .list .pictrue image.img-none {
	border: none;
}

.group-con .wrapper .lookAll {
	font-size: 24rpx;
	color: #282828;
	padding-top: 10rpx;
}

.group-con .wrapper .lookAll .iconfont {
	font-size: 25rpx;
	margin: 2rpx 0 0 10rpx;
}

.group-con .wrapper .teamBnt {
	font-size: 30rpx;
	width: 620rpx;
	height: 86rpx;
	border-radius: 50rpx;
	text-align: center;
	line-height: 86rpx;
	color: #fff;
	margin: 21rpx auto 0 auto;
}

.group-con .wrapper .cancel,
.group-con .wrapper .lookOrder {
	text-align: center;
	font-size: 24rpx;
	color: #282828;
	padding-top: 30rpx;
}

.group-con .wrapper .cancel .iconfont {
	font-size: 35rpx;
	color: #2c2c2c;
	vertical-align: -4rpx;
	margin-right: 9rpx;
}

.group-con .wrapper .lookOrder .iconfont {
	font-size: 25rpx;
	color: #2c2c2c;
	margin-left: 10rpx;
}

.group-con .group-recommend {
	background-color: #fff;
	margin-top: 25rpx;
}

.group-con .group-recommend .title {
	padding-right: 30rpx;
	margin-left: 30rpx;
	height: 85rpx;
	border-bottom: 1px solid #eee;
	font-size: 28rpx;
	color: #282828;
}

.group-con .group-recommend .title .more {
	color: #808080;
}

.group-con .group-recommend .title .more .iconfont {
	margin-left: 13rpx;
	font-size: 28rpx;
}

.group-con .group-recommend .list {
	margin-top: 30rpx;
}

.group-con .group-recommend .list .item {
	width: 210rpx;
	margin: 0 0 25rpx 30rpx;
}

.group-con .group-recommend .list .item .pictrue {
	width: 100%;
	height: 210rpx;
	position: relative;
}

.group-con .group-recommend .list .item .pictrue image {
	width: 100%;
	height: 100%;
	border-radius: 10rpx;
}

.group-con .group-recommend .list .item .pictrue .team {
	position: absolute;
	top: 28rpx;
	left: -5rpx;
	min-width: 100rpx;
	height: 36rpx;
	line-height: 36rpx;
	text-align: center;
	border-radius: 0 18rpx 18rpx 0;
	font-size: 20rpx;
	color: #fff;
	background-image: linear-gradient(to right, #fb5445 0%, #e93323 100%);
	background-image: -webkit-linear-gradient(to right, #fb5445 0%, #e93323 100%);
	background-image: -moz-linear-gradient(to right, #fb5445 0%, #e93323 100%);
}

.group-con .group-recommend .list .item .name {
	font-size: 28rpx;
	color: #333;
	margin-top: 18rpx;
}

.group-con .group-recommend .list .item .money {
	font-weight: bold;
	font-size: 26rpx;
}
</style>
