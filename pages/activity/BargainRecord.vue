<template>
	<view class="bargain-record" ref="container">
		<view class="item" v-for="(item, index) in bargain" :key="index">
			<view class="picTxt acea-row row-between-wrapper">
				<view class="pictrue"><img :src="item.image" /></view>
				<view class="text acea-row row-column-around">
					<view class="line1">{{ item.title }}</view>
					<count-down :is-day="true" :tip-text="'倒计时 '" :day-text="' 天 '" :hour-text="' 时 '" :minute-text="' 分 '" :second-text="' 秒'" :datatime="item.datatime"></count-down>
					<view class="money font-color-red">
						已砍至
						<span class="symbol">￥</span>
						<span class="num">{{ item.residue_price }}</span>
					</view>
				</view>
			</view>
			<view class="bottom acea-row row-between-wrapper">
				<view class="purple" v-if="item.status === 1">活动进行中</view>
				<view class="success" v-else-if="item.status === 3">砍价成功</view>
				<view class="end" v-else>活动已结束</view>
				<view class="acea-row row-middle row-right">
					<view class="bnt cancel" v-if="item.status === 1" @click="getBargainUserCancel(item.bargain_id)">取消活动</view>
					<view class="bnt bg-color-red" v-if="item.status === 1" @click="goDetail(item.bargain_id)">继续砍价</view>
					<view class="bnt bg-color-red" v-else @click="goList">重开一个</view>
				</view>
			</view>
		</view>
		<Loading :loaded="status" :loading="loadingList"></Loading>
		<x-authorize :isHidden="true" @login="getBargainUserList"></x-authorize>
		<x-home></x-home>
	</view>
</template>
<script>
import CountDown from '@/components/CountDown';
import { getBargainUserList, getBargainUserCancel } from '@/api/activity';
import Loading from '@/components/Loading';

export default {
	name: 'BargainRecord',
	components: {
		CountDown,
		Loading
	},
	props: {},
	data: function() {
		return {
			bargain: [],
			status: false, //砍价列表是否获取完成 false 未完成 true 完成
			loadingList: false, //当前接口是否请求完成 false 完成 true 未完成
			page: 1, //页码
			limit: 20 //数量
		};
	},
	onLoad() {
		this.$setNavigationBarColor('#e93323')
	},
	mounted: function() {
		this.getBargainUserList();
	},
	methods: {
		goDetail: function(id) {
			this.$navigator(`/pages/activity/DargainDetails?id=${id}&partake=0`);
		},
		goList: function() {
			this.$navigator(`/pages/activity/GoodsBargain`);
		},
		getBargainUserList: function() {
			var that = this;
			if (that.loadingList) return;
			if (that.status) return;
			getBargainUserList({ page: that.page, limit: that.limit })
				.then(res => {
					that.status = res.data.length < that.limit;
					that.bargain.push.apply(that.bargain, res.data);
					that.page++;
					that.loadingList = false;
				})
				.catch(res => {
					that.$showToast(res.msg || res);
				});
		},
		getBargainUserCancel: function(bargainId) {
			var that = this;
			getBargainUserCancel({ bargainId: bargainId })
				.then(res => {
					that.$showToast(res.msg ,'success',{success(){
						that.status = false;
						that.loadingList = false;
						that.page = 1;
						that.bargain = [];
						that.getBargainUserList();
					}});
				})
				.catch(res => {
					that.$showToast(res.msg || res);
				});
		}
	},
	onReachBottom() {
		this.getBargainUserList();
	}
};
</script>
<style scoped>
	.bargain-record .item {
		background-color: #fff;
		margin-bottom: 12rpx;
	}
	
	.bargain-record .item .picTxt {
		height: 210rpx;
		border-bottom: 1px solid #f0f0f0;
		padding: 0 30rpx;
	}
	
	.bargain-record .item .picTxt .pictrue {
		width: 150rpx;
		height: 150rpx;
	}
	
	.bargain-record .item .picTxt .pictrue img {
		width: 100%;
		height: 100%;
		border-radius: 6rpx;
	}
	
	.bargain-record .item .picTxt .text {
		width: 515rpx;
		font-size: 30rpx;
		color: #282828;
		height: 150rpx;
	}
	
	.bargain-record .item .picTxt .text .time {
		font-size: 24rpx;
		color: #868686;
	}
	
	.bargain-record .item .picTxt .text .time .styleAll {
		color: #fc4141;
	}
	
	.bargain-record .item .picTxt .text .money {
		font-size: 24rpx;
	}
	
	.bargain-record .item .picTxt .text .money .num {
		font-size: 32rpx;
		font-weight: bold;
	}
	
	.bargain-record .item .picTxt .text .money .symbol {
		font-weight: bold;
	}
	
	.bargain-record .item .bottom {
		height: 100rpx;
		padding: 0 30rpx;
		font-size: 27rpx;
	}
	
	.bargain-record .item .bottom .purple {
		color: #f78513;
	}
	
	.bargain-record .item .bottom .end {
		color: #999;
	}
	
	.bargain-record .item .bottom .success {
		color: #e93323;
	}
	
	.bargain-record .item .bottom .bnt {
		font-size: 27rpx;
		color: #fff;
		width: 176rpx;
		height: 60rpx;
		border-radius: 6rpx;
		text-align: center;
		line-height: 60rpx;
	}
	
	.bargain-record .item .bottom .bnt.cancel {
		color: #aaa;
		border: 1px solid #ddd;
	}
	
	.bargain-record .item .bottom .bnt~.bnt {
		margin-left: 18rpx;
	}
</style>