<template>
	<view class="logistics">
		<view class="header acea-row row-between row-top" v-for="cart in cartInfo" :key="cart.id">
			<view class="pictrue"><image :src="cart.productInfo.image" /></view>
			<view class="text acea-row row-between">
				<view class="name line2">{{ cart.productInfo.store_name }}</view>
				<view class="money">
					<view>￥{{ cart.truePrice }}</view>
					<view>x{{ cart.cart_num }}</view>
				</view>
			</view>
		</view>
		<view class="logisticsCon">
			<view class="company acea-row row-between-wrapper">
				<view class="picTxt acea-row row-between-wrapper">
					<view class="iconfont icon-wuliu"></view>
					<view class="text">
						<view>
							<span class="name line1">物流公司：</span>
							{{ orderInfo.delivery_name }}
						</view>
						<view class="express line1">
							<span class="name">快递单号：</span>
							{{ orderInfo.delivery_id }}
						</view>
					</view>
				</view>
				<view class="copy acea-row row-center-wrapper copy-data" @click="copy(orderInfo.delivery_id)">复制单号</view>
			</view>
			<view class="item" v-for="(express, index) in expressList" :key="index">
				<view class="circular" :class="index === 0 ? 'on' : ''"></view>
				<view class="text">
					<view :class="index === 0 ? 'font-color-red' : ''">{{ express.status }}</view>
					<view class="data">{{ express.time }}</view>
				</view>
			</view>
		</view>
		
		<xNodate :arr="expressList" :page="2" :isR="false" imgSrc="/wximage/noExpress.png"></xNodate>
		<x-home></x-home>
	</view>
</template>
<script>
import xNodate from '@/components/x-nodata/x-nodata.vue';
import { express } from '@/api/order';
import {  setClipboardData } from '@/utils/common.js';

const NAME = 'Logistics';

export default {
	name: NAME,
	components: {
		xNodate
	},
	data: function() {
		return {
			id: '',
			cartInfo: [],
			orderInfo: {},
			expressList: [],
			loaded: false
		};
	},
	onLoad(options){
		const {order_id} = options;
		this.id = order_id;
		this.getExpress();
	},
	methods: {
		copy(val) {
			setClipboardData(val);
		},
		getExpress() {
			if (!this.id) return this.$showToast('订单不存在');
			this.loaded = false;
			express(this.id)
				.then(res => {
					const result = res.data.express.result || {};
					this.cartInfo = res.data.order.cartInfo;
					this.orderInfo = res.data.order;
					this.expressList = result.list || [];
					this.loaded = true;

				})
				.catch(e => {
					console.log(e)
					this.$showToast(e.msg || '加载失败');
				});
		}
	}
};
</script>

<style scoped>
.no-express {
	margin: 150rpx 0;
}

.no-express image {
	width: 600rpx;
	margin: 0 auto;
	display: block;
}
.logistics .header {
	padding: 23rpx 30rpx;
	background-color: #fff;
	height: 166rpx;
}

.logistics .header .pictrue {
	width: 120rpx;
	height: 120rpx;
}

.logistics .header .pictrue image {
	width: 100%;
	height: 100%;
	border-radius: 6rpx;
}

.logistics .header .text {
	width: 540rpx;
	font-size: 28rpx;
	color: #999;
	margin-top: 6rpx;
}

.logistics .header .text .name {
	width: 365rpx;
	color: #282828;
}

.logistics .header .text .money {
	text-align: right;
}

.logistics .logisticsCon {
	background-color: #fff;
	margin: 12rpx 0;
}

.logistics .logisticsCon .company {
	height: 120rpx;
	margin: 0 0 45rpx 30rpx;
	padding-right: 30rpx;
	border-bottom: 1px solid #f5f5f5;
}

.logistics .logisticsCon .company .picTxt {
	width: 520rpx;
}

.logistics .logisticsCon .company .picTxt .iconfont {
	width: 50rpx;
	height: 50rpx;
	background-color: #666;
	text-align: center;
	line-height: 50rpx;
	color: #fff;
	font-size: 35rpx;
}

.logistics .logisticsCon .company .picTxt .text {
	width: 450rpx;
	font-size: 26rpx;
	color: #282828;
}

.logistics .logisticsCon .company .picTxt .text .name {
	color: #999;
}

.logistics .logisticsCon .company .picTxt .text .express {
	margin-top: 5rpx;
}

.logistics .logisticsCon .company .copy {
	font-size: 20rpx;
	width: 106rpx;
	height: 40rpx;
	border-radius: 3rpx;
	border: 1px solid #999;
}

.logistics .logisticsCon .item {
	padding: 0 40rpx;
	position: relative;
}

.logistics .logisticsCon .item .circular {
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
	position: absolute;
	top: -1rpx;
	left: 31rpx;
	background-color: #ddd;
}

.logistics .logisticsCon .item .circular.on {
	background-color: #e93323;
}

.logistics .logisticsCon .item .text {
	font-size: 26rpx;
	color: #666;
	width: 615rpx;
	border-left: 1px solid #e6e6e6;
	padding: 0 0 60rpx 38rpx;
}

.logistics .logisticsCon .item .text.on {
	border-left-color: #f8c1bd;
}

.logistics .logisticsCon .item .text .data {
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
}

.logistics .logisticsCon .item .text .data .time {
	margin-left: 15rpx;
}

</style>
