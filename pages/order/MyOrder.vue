<template>
	<view class="my-order">
		<view class="nav acea-row row-around">
			<view class="item" :class="{ on: type === 0 }" @click="changeType(0)">
				<view class="image flex flex_align_center">
					<image src="@/static/images/yuanshi/dfk1.png" mode="widthFix" v-if="type === 0" />
					<image src="@/static/images/yuanshi/dfk.png" mode="widthFix" v-else />
                    <text class="num" v-if="orderData.unpaid_count">{{ orderData.unpaid_count}}</text>
				</view>
				<view class="font_size20">待付款</view>
				
			</view>
			<view class="item" :class="{ on: type === 1 }" @click="changeType(1)">
				<view class="image flex flex_align_center">
					<image src="@/static/images/yuanshi/dfh1.png" mode="widthFix" v-if="type === 1" />
					<image src="@/static/images/yuanshi/dfh.png" mode="widthFix" v-else />
                    <text class="num" v-if="orderData.unshipped_count">{{ orderData.unshipped_count}}</text>
				</view>
				<view class="font_size20">待发货</view>
				
			</view>
			<view class="item" :class="{ on: type === 2 }" @click="changeType(2)">
				<view class="image flex flex_align_center">
					<image src="@/static/images/yuanshi/dsh1.png" mode="widthFix" v-if="type === 2" />
					<image src="@/static/images/yuanshi/dsh.png" mode="widthFix" v-else />
                    <text class="num" v-if="orderData.received_count">{{ orderData.received_count}}</text>
				</view>
				<view class="font_size20">待收货</view>
			</view>
			<view class="item" :class="{ on: type === 3 }" @click="changeType(3)">
				<view class="image flex flex_align_center">
					<image src="@/static/images/yuanshi/dpj1.png" mode="widthFix" v-if="type === 3" />
					<image src="@/static/images/yuanshi/dpj.png" mode="widthFix" v-else />
                    <text class="num" v-if="orderData.evaluated_count">{{ orderData.evaluated_count}}</text>
				</view>
				<view class="font_size20">待评价</view>
			</view>
			<view class="item" :class="{ on: type === 4 }" @click="changeType(4)">
				<view class="image flex flex_align_center">
					<image src="@/static/images/yuanshi/ywc1.png" mode="widthFix" v-if="type === 4" />
					<image src="@/static/images/yuanshi/ywc.png" mode="widthFix" v-else />
                    <!-- <text class="num" v-if="orderData.complete_count">{{ orderData.complete_count}}</text> -->
				</view>
				<view class="font_size20">已完成</view>
			</view>
		</view>
		<view class="list">
			<view class="item" v-for="order in orderList" :key="order.id">
				<view class="title acea-row row-between-wrapper">
					<view class="acea-row row-middle">
						<text class="sign cart-color acea-row row-center-wrapper" v-if="order.combination_id > 0">拼团</text>
						<text class="sign cart-color acea-row row-center-wrapper" v-if="order.seckill_id > 0">活动</text>
						<text class="sign cart-color acea-row row-center-wrapper" v-if="order.bargain_id > 0">砍价</text>
						<text class="time">{{ order._add_time }}</text>
					</view>
					<view class="font-color-red">{{ order._status._title }}</view>
				</view>
				<view class="wrap" @click="goPages(`/pages/order/OrderDetails?order_id=${order.order_id}`)">
					<view class="item-info acea-row r row-top" v-for="cart in order.cartInfo" :key="cart.id">
						<view class="pictrue">
							<image
								:src="cart.productInfo.image"
								@click.stop="goGoodDetail(order,true)"
                                mode="widthFix"
							/>
						<!-- 	<image
								:src="cart.productInfo.image"
								@click.stop="goGoodDetail(cart,order.cartInfo)"
								v-if="cart.combination_id === 0 && cart.bargain_id === 0 && cart.seckill_id === 0"
							/> -->
							<!-- <image :src="cart.productInfo.image" @click.stop="goPages(`/pages/activity/GroupDetails?id=${cart.combination_id}`)" v-else-if="cart.combination_id > 0" /> -->
							<!-- <image :src="cart.productInfo.image" @click.stop="goPages(`/pages/activity/DargainDetails?id=${cart.bargain_id}`)" v-else-if="cart.bargain_id > 0" /> -->
							<!-- <image :src="cart.productInfo.image" @click.stop="goPages(`/pages/activity/SeckillDetails?id=${cart.seckill_id}`)" v-else-if="cart.seckill_id > 0" /> -->
						</view>
						<view class="text ">
							<view class="name ">{{ cart.productInfo.store_name }}</view>
							<view class="desc">{{ cart.productInfo.attrInfo.suk }}</view>
							<view class="money flex flex_between">
								<view>￥ {{ cart.productInfo.attrInfo ? cart.productInfo.attrInfo.price : cart.productInfo.price }}</view>
								<view>x {{ cart.cart_num }}</view>
							</view>
						</view>
					</view>
					<view class="totalPrice flex flex_align_center flex_between">
						<view>
							共{{ order.cartInfo.length || 0 }}件商品，总金额
							<text class="money ">￥{{ order.pay_price }}</text>
						</view>
						<view class="btn flex_line_height" @click.stop="goOrderDetail(`/pages/order/OrderDetails?order_id=${order.order_id}`)"><text class="font_size20">查看详情</text></view>
					</view>

					<view class="bottom acea-row row-right row-middle">
						<template v-if="order._status._type === 0 || order._status._type == 9">
							<view class="bnt default flex_line_height" @click.stop="cancelOrder(order)"><text class="">取消订单</text></view>
						</template>
						<template v-if="order._status._type === 0">
							<view class="bnt  flex_line_height" @click.stop="paymentTap(order)"><text class="">立即付款</text></view>
						</template>
						<template v-if="order._status._type > 1 && order.delivery_type === 'express'">
							<view class="bnt default flex_line_height" @click.stop="goPages(`/pages/order/Logistics?order_id=${order.order_id}`)" v-if="order.delivery_type === 'express'">
								<text class="">查看物流</text>
							</view>
						</template>
						<template v-if="order._status._type === 2">
							<view class="bnt flex_line_height" @click.stop="takeOrder(order)"><text class="">确认收货</text></view>
						</template>
						<template v-if="order._status._type === 3">
							<!-- <view class="bnt  flex_line_height" @click.stop="goPages(`/pages/order/OrderDetails?order_id=${order.order_id}`)"><text class="">去评价</text></view> -->
							<view class="bnt  flex_line_height" 	@click.stop="goGoodDetail(order,false)" v-if="order.shipping_type === 2 && order.paid === 1"><text class="">去评价</text></view>
							<view class="bnt  flex_line_height" 	@click.stop="goEvaluate(order)" v-else><text class="">去评价</text></view>
						</template>
					</view>
				</view>
			</view>
		</view>
		<xNodate :arr="orderList" :page="page" :isR="false" imgSrc="/wximage/noOrder.png"></xNodate>
		<Loading :loaded="loaded" :loading="loading"></Loading>
		<Payment v-model="pay" :types="payType" @checked="toPay" :balance="userInfo.now_money"></Payment>
		<x-home></x-home>
		<GeneralWindow :generalActive="generalActive" @closeGeneralWindow="closeGeneralWindow" :generalContent="generalContent"></GeneralWindow>
	</view>
</template>
<script>
import { getOrderData, getOrderList } from '@/api/order';
import { cancelOrderHandle, payOrderHandle, takeOrderHandle } from '@/utils/order.js';
import Loading from '@/components/Loading';
import Payment from '@/components/Payment';
import { mapGetters } from 'vuex';
import GeneralWindow from '@/components/GeneralWindow';
import xNodate from '@/components/x-nodata/x-nodata.vue';
import { openOrderSubscribe } from '@/utils/SubscribeMessage.js';
// #ifdef H5
import { isWeixin } from '@/utils/validate.js';
const _isWeixin = isWeixin();
// #endif
// #ifdef MP
const _isWeixin = true;
// #endif
const STATUS = ['待付款', '待发货', '待收货', '待评价', '已完成', '', '', '', '', '待付款'];
const NAME = 'MyOrder';

export default {
	name: NAME,
	data() {
		return {
			offlinePayStatus: 2,
			orderData: {},
			orderInfo: {}, //当前要支付订单信息
			type: 0,
			page: 1,
			limit: 20,
			loaded: false,
			loading: false,
			orderList: [],
			pay: false,
			payType: ['yue', 'weixin'],
			// #ifdef MP-WEIXIN
			from: 'routine',
			// #endif
			// #ifdef MP-TOUTIAO
			from: 'bytedance',
			// #endif
			// #ifdef H5
			from: _isWeixin ? 'weixin' : 'weixinh5',
			// #endif
			generalActive: false,
			generalContent: {
				promoterNum: '',
				title: ''
			},
			paymentType: true //true时，测评版本直接微信支付
		};
	},
	components: {
		Loading,
		xNodate,
		Payment,
		GeneralWindow
	},
	computed: {
		...mapGetters(['userInfo'])
	},
	methods: {
        goEvaluate(order){
            if(order.cartInfo.length == 1){
                this.goPages('/pages/shop/GoodsEvaluate?unique=' + order.cartInfo[0].unique)
            }else {
                this.goOrderDetail(`/pages/order/OrderDetails?order_id=${order.order_id}`)
            }
        },
		goPages(path) {
			this.$navigator(path);
		},
		goGoodDetail(order,type){
			let cartInfo = order.cartInfo;
			let cart = cartInfo[0];
			let path =`/pages/order/OrderDetails?order_id=${order.order_id}`,typ = true;
			cartInfo.forEach((item, index) => {
				if (item.type === 'evaluate_product') {
					typ = false;
				}
			});			
			if(!typ){
				path = '/pages/yuanshi/evaluate/detail?wid=' + cart.wish_id + '&pid=' + cart.product_id;
				if(!type){
					path = path +'&type=pj'
				}
			}else{
				path = `/pages/shop/GoodsCon?id=${cart.product_id}`;
			}
			this.$navigator(path);
		},
		goOrderDetail(path) {
			openOrderSubscribe().then(() => {
				this.$navigator(path);
			});
		},
		setOfflinePayStatus: function(status) {
			var that = this;
			that.offlinePayStatus = status;
			if (status === 1) {
				if (that.payType.indexOf('offline') < 0) {
					that.payType.push('offline');
				}
			}
		},
		getOrderData() {
			getOrderData().then(res => {
				this.orderData = res.data;
			});
		},
		takeOrder(order) {
			this.$loadingToast('正在加载中');
			takeOrderHandle(order.order_id)
				.then(res => {
					if ((res.data.gain_integral != '0.00' && res.data.gain_coupon != '0.00') || (res.data.gain_integral > 0 && res.data.gain_coupon > 0)) {
						this.$hideLoading();
						this.generalActive = true;
						this.generalContent = {
							promoterNum: `恭喜您获得${res.data.gain_coupon}元优惠券以及${res.data.gain_integral}积分，购买商品时可抵现哦～`,
							title: '恭喜您获得优惠礼包'
						};
						return;
					} else if (res.data.gain_integral != '0.00' || res.data.gain_integral > 0) {
						this.$hideLoading();
						this.generalActive = true;
						this.generalContent = {
							promoterNum: `恭喜您获得${res.data.gain_integral}积分，购买商品时可抵现哦～`,
							title: '赠送积分'
						};
						return;
					} else if (res.data.gain_coupon != '0.00' || res.data.gain_coupon > 0) {
						this.$hideLoading();
						this.generalActive = true;
						this.generalContent = {
							promoterNum: `恭喜您获得${res.data.gain_coupon}元优惠券，购买商品时可抵现哦～`,
							title: '恭喜您获得优惠券'
						};
						return;
					} else {
						this.$hideLoading();
						this.$successToast('收货成功');
					}
					this.getOrderData();
					this.orderList = [];
					this.page = 1;
					this.loaded = false;
					this.loading = false;
					this.getOrderList();
				})
				.catch(err => {
					this.$hideLoading();
					this.$showToast(err.msg || err);
				});
		},
		closeGeneralWindow(msg) {
			this.generalActive = msg;
			this.reload();
			this.getOrderData();
		},
		reload() {
			this.changeType(this.type);
		},
		changeType(type) {
			this.type = type;
			this.orderList = [];
			this.page = 1;
			this.loaded = false;
			this.loading = false;
			this.getOrderList();
		},
		getOrderList() {
			if (this.loading || this.loaded) return;
			this.loading = true;
			const { page, limit, type } = this;
			getOrderList({
				page,
				limit,
				type
			}).then(res => {
				this.orderList = this.orderList.concat(res.data);
				this.page++;
				this.loaded = res.data.length < this.limit;
				this.loading = false;
			});
		},
		getStatus(order) {
			return STATUS[order._status._type];
		},
		cancelOrder(order) {
			cancelOrderHandle(order.order_id)
				.then(() => {
					this.getOrderData();
					this.orderList.splice(this.orderList.indexOf(order), 1);
				})
				.catch(() => {
					this.reload();
				});
		},
		paymentTap: function(order) {
			var that = this;
			if (!(order.combination_id > 0 || order.bargain_id > 0 || order.seckill_id > 0)) {
				that.setOfflinePayStatus(order.offlinePayStatus);
			}
			this.orderInfo = order;
			if (this.paymentType && _isWeixin) {
                // #ifndef MP-TOUTIAO
                this.toPay('weixin');
                // #endif
                // #ifdef MP-TOUTIAO
                this.toPay('bytedance');
                // #endif
			} else {
				this.pay = true;
			}
		},
		toPay(type) {
			payOrderHandle(this.orderInfo.order_id, type, this.from)
				.then(res => {
					const { status, result } = res;
					if (status === 'WECHAT_H5_PAY') {
						// #ifndef MP-TOUTIAO
						return that.$navigator('/pages/order/PaymentStatus?orderId=' + order.order_id + '&status=5');
						// #endif
					}
					const type = parseInt(this.type) || 0;
					this.changeType(type);
					this.getOrderData();
				})
				.catch(err => {
					this.$showToast(err.msg || err);
				});
		}
	},
	onLoad(options) {
		const { type = 0 } = options;
		this.type = Number(type);
	},
	onShow() {
		this.getOrderData();
		this.changeType(this.type);
	},
	onReachBottom() {
		this.getOrderList();
	}
};
</script>

<style scoped lang="scss">
.my-order {
	.nav {
		background-color: #fff;

		border-radius: 0px 0px 70rpx 70rpx;
		box-shadow: 0px 0px 30rpx rgba(107, 127, 153, 0.3);
		padding: 52rpx 0 60rpx 0;
		.item {
			text-align: center;
			font-size: 26rpx;
			color: #282828;
			padding: 28rpx 0 20rpx 0;

			.image {
                position: relative;
				width: 112rpx;
				height: 112rpx;
				background: #ffffff;
				box-shadow: 0px 0px 20rpx rgba(107, 127, 153, 0.2);
				border-radius: 40rpx;
				margin-bottom: 4rpx;
				image {
					width: 52rpx;
					height: 52rpx;
					margin: 0 auto;
				}
			}
			&.on {
				color: #ff5656;
				font-weight: 500;
			}
			.num {
				min-width: 35rpx;
				height: 35rpx;
				text-align: center;
				line-height: 35rpx;
				border-radius: 24rpx;
				color: #fff;
				background: #ff3232;
				font-weight: 500;
				position: absolute;
				right: -8rpx;
				top: -8rpx;
				font-size: 24rpx;
				padding: 0 12rpx;
			}
		}
	}
	.list {
		padding: 0 20rpx;
		margin: 40rpx auto 0 auto;
		.item {
			background-color: #fff;
			border-radius: 30rpx;
			box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
			margin-bottom: 30rpx;
			.title {
				height: 84rpx;
				padding: 0 30rpx;
				border-bottom: 1rpx solid #eee;
				font-size: 28rpx;
				color: #282828;

				font-weight: 500;
				border: 4rpx solid #ffffff;
				border-radius: 30rpx 30rpx 0px 0px;
				background: #f2f5f8;
				.sign {
					font-size: 24rpx;
					padding: 0 7rpx;
					height: 36rpx;
					margin-right: 15rpx;
				}
				.time {
					font-size: 24rpx;

					font-weight: 500;
				}
			}
			.wrap {
				padding: 30rpx 40rpx 50rpx 40rpx;
				.item-info {
					margin-top: 22rpx;
					padding-bottom: 32rpx;
					border-bottom: 1rpx solid #eee;
					.pictrue {
						width: 134rpx;
						// height: 113rpx;
                        // max-height: 178rpx;
                        
						image {
							width: 100%;
							height: 100%;
							border-radius: 30rpx;
						}
					}
					.text {
						width: calc(100% - 134rpx);
						padding-left: 20rpx;
						font-size: 24rpx;
						color: #333333;
						.name {
							font-size: 32rpx;
							font-weight: bold;
							color: #333333;
						}
						.desc {
							margin-top: 14rpx;
							font-size: 24rpx;
							height: 72rpx;

							color: #666666;
						}
						.money {
							font-weight: 500;
						}
					}
				}
				.totalPrice {
					font-size: 28rpx;
					color: #282828;
					text-align: right;
					padding: 44rpx 0rpx 40rpx 0;
					border-bottom: 1rpx solid #eee;
					.money {
						font-size: 24rpx;
					}
					.btn {
						width: 120rpx;
						height: 38rpx;
						text-align: center;
						line-height: 38rpx;
						color: #fff;
						border-radius: 14rpx;
						border: 2rpx solid #ff5656;
						color: #ff5656;
					}
				}
				.bottom {
					margin-top: 32rpx;
					.bnt {
						width: 152rpx;

						height: 60rpx;
						text-align: center;
						line-height: 60rpx;
						color: #fff;
						border-radius: 24rpx;
						font-size: 24rpx;

						background: #ff5656;
						&.default {
							color: #50506f;
							background: #eaeaea;
						}
					}
					& .bnt ~ .bnt {
						margin-left: 17rpx;
					}
				}
			}
		}
	}
}

// .my-order .list .item .bottom .bnt.cancelBnt {
// 	border: 1px solid #ddd;
// 	color: #aaa;
// }

// .my-order .list .item .bottom .bnt.default {
// 	color: #444;
// 	border: 1px solid #444;
// }

// .my-order .list .item .bottom .bnt ~ .bnt {
// 	margin-left: 17rpx;
// }
</style>
