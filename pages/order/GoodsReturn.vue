<template>
	<view class="apply-return">
		<view class="goodsStyle acea-row row-between" v-for="cart in orderInfo.cartInfo" :key="cart.id">
			<template v-if="cart.activityInfo">
				<!-- <view class="pictrue">
					<image :src="cart.activityInfo.image" class="image" />
				</view> -->
                <view class="pictrues">
                    <image :src="cart.activityInfo.image" class="image" mode="widthFix" />
                </view>
				<view class="text acea-row row-between">
					<view class="name line2">{{ cart.activityInfo.store_name }}</view>
					<view class="money">
						<view>
							￥{{ cart.activityInfo.attrInfo ? cart.activityInfo.attrInfo.price : cart.activityInfo.price }}
						</view>
						<view class="num">x{{ cart.cart_num }}</view>
					</view>
				</view>
			</template>
			<template v-else>
				<!-- <view class="pictrue">
					<image :src="cart.productInfo.image" class="image" />
				</view> -->
                <view class="pictrues">
                    <image :src="cart.productInfo.image" class="image" mode="widthFix" />
                </view>
				<view class="text acea-row row-between">
					<view class="name line2">{{ cart.productInfo.store_name }}</view>
					<view class="money">
						<view>
							￥{{ cart.productInfo.attrInfo ? cart.productInfo.attrInfo.price : cart.productInfo.price }}
						</view>
						<view class="num">x{{ cart.cart_num }}</view>
					</view>
				</view>
			</template>
		</view>
		<view class="list">
			<view class="item acea-row row-between-wrapper">
				<view>退货件数</view>
				<view class="num">{{ orderInfo.total_num }}</view>
			</view>
			<view class="item acea-row row-between-wrapper">
				<view>退款金额</view>
				<view class="num">￥{{ orderInfo.total_price }}</view>
			</view>
			<view class="item acea-row row-between-wrapper">
				<view>退款原因</view>
				<picker class="num" @change="bindPickerChange" :value="index" :range="reasonList">
					<view class="picker acea-row row-between-wrapper">
						<view class="reason">{{ reasonList[index] }}</view>
						<text class="iconfont icon-jiantou"></text>
					</view>
				</picker>
			</view>
			<view class="item textarea acea-row row-between">
				<view>备注说明</view>
				<textarea placeholder="填写备注信息，100字以内" class="num" v-model="refund_reason_wap_explain"></textarea>
			</view>
			<view class="item acea-row row-between">
				<view class="title acea-row row-between-wrapper">
					<view>上传凭证</view>
					<view class="tip">( 最多可上传3张 )</view>
				</view>
				<view class="upload acea-row row-middle">
					<view class="btn btn-primary">
						<xImageUpload :num="3" @chooseImage="chooseImage">
							<view class="pictrue acea-row row-center-wrapper row-column" style="margin:0">
								<span class="iconfont icon-icon25201"></span>
								<view>上传凭证</view>
							</view>
						</xImageUpload>
					</view>
				</view>
			</view>
		</view>
		<view class="returnBnt bg-color-red" @click="submit">申请退款</view>
		<x-home></x-home>
	</view>
</template>

<script>
	import {
		orderDetail,
		getRefundReason,
		postOrderRefund
	} from '@/api/order';
	import {
		activityOrderDetail,
		activityOrderRefund
	} from '@/api/community.js';
	import xImageUpload from '@/components/x-image-upload/x-image-upload';
	import {
		uploadImg
	} from '@/utils/upload.js';
	export default {
		name: 'goodsReturn',
		components: {
			xImageUpload
		},
		data() {
			return {
				index: 0,
				id: 0,
				orderInfo: {
					cartInfo: [],
					activityInfo: {}
				},
				reasonList: [],
				reason: '',
				refund_reason_wap_explain: '',
				refund_reason_wap_img: [],
				source: ''
			};
		},
		methods: {
			bindPickerChange(e) {
				this.index = e.detail.value;
				this.reason = this.reasonList[this.index];
			},
			chooseImage(val) {
				this.refund_reason_wap_img = val;
			},
			getOrderDetail() {
				let req = '';
				if (this.source === 'community') {
					req = activityOrderDetail
				} else {
					req = orderDetail
				}
				req(this.id)
					.then(res => {
						this.orderInfo = res.data;
					})
					.catch(err => {
						this.$showToast(err.msg || err || '获取订单失败');
					});
			},
			getRefundReason() {
				getRefundReason().then(res => {
					this.reasonList = res.data;
					this.reason = res.data[this.index]
				});
			},
			async submit() {
				const refund_reason_wap_explain = this.refund_reason_wap_explain.trim(),
					text = this.reason;
				if (!text) return this.$showToast('请选择退款原因');
				let refund_reason_wap_img = await uploadImg(this.refund_reason_wap_img);
				let req = '';
				if (this.source === 'community') {
					req = activityOrderRefund
				} else {
					req = postOrderRefund
				}
				req({
						text,
						uni: this.orderInfo.order_id,
						refund_reason_wap_img: refund_reason_wap_img.join(","),
						refund_reason_wap_explain
					})
					.then(res => {
						this.$showToast(res.msg);
						this.$navigator(-1);
					})
					.catch(res => {
						this.$showToast(res.msg || res);
						this.$navigator(-1);
					});
			}
		},
		onLoad(options) {
			const {
				order_id,
				source
			} = options;
			this.id = order_id;
			this.source = source;
			this.getOrderDetail();
			this.getRefundReason();
		}
	};
</script>
<style scoped>
    .goodsStyle .pictrues {
        width: 120rpx;
        /* max-height: 168rpx; */
        /* height: 120rpx; */
    }
    .goodsStyle .pictrues .image{
            border-radius: 6rpx;
    }
	.line2 {
		word-break: break-all;
		display: -webkit-box;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.apply-return .list {
		background-color: #fff;
		margin-top: 18rpx;
	}

	.apply-return .list .item {
		margin-left: 30rpx;
		padding-right: 30rpx;
		min-height: 90rpx;
		border-bottom: 1px solid #eee;
		font-size: 30rpx;
		color: #333;
	}

	.apply-return .list .item .num {
		color: #282828;
		width: 426rpx;
		text-align: right;
		position: relative;
	}

	.apply-return .list .item .num .picker .reason {
		width: 385rpx;
	}

	.apply-return .list .item .num .picker .iconfont {
		color: #666;
		font-size: 30rpx;
		margin-top: 2rpx;
	}

	.apply-return .list .item.textarea {
		padding: 30rpx 30rpx 30rpx 0;
	}

	.apply-return .list .item textarea {
		height: 100rpx;
		font-size: 30rpx;
	}

	.apply-return .list .item textarea::placeholder {
		color: #bbb;
	}

	.apply-return .list .item .title {
		height: 95rpx;
		width: 100%;
	}

	.apply-return .list .item .title .tip {
		font-size: 30rpx;
		color: #bbb;
	}

	.apply-return .list .item .upload {
		padding-bottom: 36rpx;
	}

	.apply-return .list .item .upload .pictrue {
		margin: 22rpx 23rpx 0 0;
		width: 156rpx;
		height: 156rpx;
		position: relative;
		font-size: 24rpx;
		color: #bbb;
		border: 1px solid #bbb;
	}

	.apply-return .list .item .upload .pictrue:nth-of-type(4n) {
		margin-right: 0;
	}

	.apply-return .list .item .upload .pictrue image {
		width: 100%;
		height: 100%;
		border-radius: 6rpx;
	}

	.apply-return .list .item .upload .pictrue .icon-guanbi1 {
		position: absolute;
		font-size: 45rpx;
		top: -10rpx;
		right: -10rpx;
		width: 45rpx;
		height: 45rpx;
		line-height: 45rpx;
	}

	.apply-return .list .item .upload .pictrue .icon-icon25201 {
		color: #bfbfbf;
		font-size: 50rpx;
		width: 50rpx;
		height: 60rpx;
		line-height: 60rpx;
	}

	.apply-return .list .item .upload .pictrue:nth-last-child(1) {
		border: 1px solid #ddd;
	}

	.apply-return .returnBnt {
		font-size: 32rpx;
		color: #fff;
		width: 690rpx;
		height: 86rpx;
		border-radius: 50rpx;
		text-align: center;
		line-height: 86rpx;
		margin: 43rpx auto;
	}
</style>
