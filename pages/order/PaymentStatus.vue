<template>
    <view class="payment-status" v-cloak>
        <view class="status_icon">
            <image src="@/static/images/yuanshi/check.png" mode="widthFix"
                v-if="orderInfo.paid ||orderInfo.deposit_paid || orderInfo.pay_type == 'offline'"></image>
            <image src="@/static/images/yuanshi/fail.png" mode="widthFix" v-else></image>
        </view>
        <!-- 失败时：订单支付失败 -->
        <view class="status" v-if="(!isWeixin && orderInfo.pay_type == 'weixin') || orderInfo.pay_type == 'offline'">
            订单创建成功</view>
        <view class="status" v-else-if="orderInfo.paid || orderInfo.deposit_paid">
            <!-- #ifndef MP-TOUTIAO -->
            订单支付成功
            <!-- #endif -->
            <!-- #ifdef MP-TOUTIAO -->
            活动报名成功
            <!-- #endif -->
        </view>
        <view class="status fail" v-else>
            <!-- #ifndef MP-TOUTIAO -->
            订单支付失败
            <!-- #endif -->
            <!-- #ifdef MP-TOUTIAO -->
            活动报名失败
            <!-- #endif -->
        </view>
        <view class="wrapper">
            <view class="item acea-row row-between-wrapper">
                <view>订单编号</view>
                <view class="itemCom">{{orderInfo.order_id}}</view>
            </view>
            <view class="item acea-row row-between-wrapper">
                <view>下单时间</view>
                <view class="itemCom">{{orderInfo._add_time}}</view>
            </view>
            <!-- #ifndef MP-TOUTIAO -->
            <view class="item acea-row row-between-wrapper">
                <view>支付方式</view>
                <view class="itemCom">{{ orderInfo._status ? orderInfo._status._payType : '' }}</view>
            </view>
            <view class="item acea-row row-between-wrapper">
                <view>支付金额</view>
                <view class="itemCom" v-if="source==='community'">
                    {{(orderInfo.order_type===2) ? ((orderInfo.deposit_paid===1&&orderInfo.paid===1)?orderInfo.pay_price: orderInfo.deposit_pay_price) : orderInfo.pay_price}}
                </view>
                <view class="itemCom" v-else>{{orderInfo.pay_price}}</view>
            </view>
            <!-- #endif -->
            <!--失败时加上这个  -->

            <!-- #ifndef MP-TOUTIAO -->
            <view class="item fail acea-row row-between-wrapper"
                v-if="source==='community'&&orderInfo.paid === 0&&orderInfo.deposit_paid === 0">
                <view>失败原因</view>
                <view class="itemCom">{{status==2 ? '取消支付':msgContent}}</view>
            </view>
            <view class="" v-else-if="source==='normal'">
                <view class="item fail acea-row row-between-wrapper"
                    v-if="orderInfo.paid == 0 && orderInfo.pay_type != 'offline' && isWeixin && msgContent">
                    <view>失败原因</view>
                    <view class="itemCom">{{status==2 ? '取消支付':msgContent}}</view>
                </view>
            </view>
            <!-- #endif -->
        </view>
        
        <view class="wechatGroup" v-if="(orderInfo.paid ||orderInfo.deposit_paid || orderInfo.pay_type == 'offline') && orderInfo.join_group_image">
            <view class="wechatGroup-title">
                重要！扫描二维码加入
                <!-- #ifndef MP-TOUTIAO -->
                微信
                <!-- #endif -->
                <!-- #ifdef MP-TOUTIAO -->
                抖音
                <!-- #endif -->
                活动消息通知群
            </view>
            <view class="wechatGroup-img">
                <image show-menu-by-longpress="true" :src="orderInfo.join_group_image" mode="widthFix"></image>
                <view class="wechatGroup-sumbit" @click="saveimg(orderInfo.join_group_image)">
                    保存
                </view>
            </view>
            
        </view>
        
        <!--失败时： 重新购买 -->
        <view v-if="orderInfo.pay_type == 'weixin' && orderInfo.is_channel == 2">
            <view class="btn success" @click="goPages()">查看支付结果</view>
            <view class="btn fail" @click="goPages()">支付失败重新支付</view>
        </view>
        <view v-else>
            <view class="btn success" @click="goPages()">查看订单</view>
            <view class="btn fail flex flex_around" @click="goPages('/pages/tabBar/index/index', 'switchTab')">
                <view class="flex flex_align_center">
                    <image src="@/static/images/1-001.png" mode=""></image>
                    返回首页
                </view>
            </view>
        </view>
    </view>
</template>
<script>
    import {
        mapGetters
    } from 'vuex';
    import {
        orderDetail
    } from '@/api/order';
    import {
        activityOrderDetail,
        
    } from '@/api/community.js';
    import {
        openOrderSubscribe
    } from '@/utils/SubscribeMessage.js';
    // #ifdef H5
    import {
        isWeixin
    } from '@/utils/validate.js';
    const _isWeixin = isWeixin();
    // #endif

    // #ifdef MP
    const _isWeixin = true;
    // #endif

    const NAME = 'PayMentStatus';

    export default {
        name: NAME,
        props: {},
        data: function() {
            return {
                id: '',
                status: 0,
                msgContent: '',
                orderInfo: {
                    paid: 1
                },
                isWeixin: _isWeixin,
                source: 'normal',
                inviterId:0
            };
        },
        watch: {},
        computed: {
            ...mapGetters(['userInfo'])
        },
        onLoad(options) {
            console.log('options--',options)
            let that = this;
            this.id = options.orderId;
            this.inviterId = options.inviterId;
            
            this.source = options.source || 'normal';
            this.msgContent = options.msg;
            this.status = parseInt(options.status);
            if(this.id){
                this.getOrderInfo(this.id,this.inviterId);
            }
        },
        methods: {
            saveimg(url) {
                let that = this;
                uni.downloadFile({
                    url: url,
                    header: {
                        "content-type": "application/json",
                    },
                    success: (res) => {
                        console.log('res', res)
                        if (res.statusCode === 200) {
                            uni.saveImageToPhotosAlbum({
                                filePath: res.tempFilePath,
                                success(res) {
                                    return that.$showToast('已保存相册')
                                },
                                fail(res) {
                                    console.log(res);
                                    return that.$showToast('无相册权限')
                                }
                            });
                        }
                    }
                })
            },
            goPages(path, type) {
                if (!path) {
                    console.log('')
                    const {
                        source
                    } = this;
                    if (this.status === 5) {
                        path = source === 'community' ? `/pages/ycommunity/order/list?type=0` :
                            `/pages/order/MyOrder?type=0`;
                    } else {
                        path = source === 'community' ?
                            `/pages/ycommunity/order/detail?order_id=${this.orderInfo.order_id}` :
                            `/pages/order/OrderDetails?order_id=${this.orderInfo.order_id}`;

                        openOrderSubscribe().then(() => {
                            this.$navigator(path, type);
                        });

                        return;
                    }
                }
                this.$navigator(path, type);
            },
            getOrderInfo(id,inviterId) {
                let req = null;
                if (this.source === 'community') {
                    req = activityOrderDetail(id,inviterId)
                } else {
                    req = orderDetail(id)
                }
                req.then(res => {
                    this.orderInfo = res.data;
                    console.log('orderInfo',this.orderInfo)
                    if (this.isWeixin) {
                        uni.setNavigationBarTitle({
                            // #ifndef MP-TOUTIAO
                            title: (this.orderInfo.paid || this.orderInfo.deposit_paid) ? '支付成功' :
                                '支付失败'
                            // #endif
                            // #ifdef MP-TOUTIAO
                            title: (this.orderInfo.paid || this.orderInfo.deposit_paid) ? '报名成功' :
                                '报名失败'
                            // #endif
                        });
                        // document.title = this.orderInfo.paid ? '支付成功' : '支付失败';
                    } else {
                        uni.setNavigationBarTitle({
                            title: '订单创建成功'
                        });
                        // document.title = '订单创建成功';
                    }
                });
            }
        }
    };
</script>

<style scoped lang="scss">
    .payment-status {
        margin: 120rpx auto 0 auto;

        .status_icon {
            text-align: center;

            image {
                width: 120rpx;
                height: 120rpx;
            }
        }

        .status {
            text-align: center;
            color: #50506f;
            font-weight: bold;
            font-size: 28rpx;
            margin: 30rpx 0 37rpx 0;

            &.fail {
                color: #999999;
            }
        }

        .wrapper {
            width: 710rpx;
            margin: 0 auto 50rpx auto;
            padding: 46rpx 28rpx;
            background: #ffffff;
            box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
            border-radius: 30rpx;

            .item {
                font-size: 24rpx;
                color: #333333;

                &:not(:last-child) {
                    margin-bottom: 30rpx;
                }

                &.fail {
                    font-weight: 500;
                    color: #50506f;
                }

                .itemCom {
                    // color: #666;
                }
            }
        }

        .btn {
            width: 670rpx;
            height: 100rpx;
            border-radius: 50rpx;
            color: #fff;
            font-size: 30rpx;
            text-align: center;
            line-height: 100rpx;
            margin: 0 auto 30rpx auto;

            font-size: 24rpx;

            &.success {
                background: #ff5656;
            }

            &.fail {
                color: #666666;
                border: 2rpx solid #d2d2d2;

                image {
                    width: 28rpx;
                    height: 28rpx;
                    margin-right: 20rpx;
                }
            }
        }
        .wechatGroup {
            width: 100%;
            height: auto;
            margin: 0 auto;
            
            .wechatGroup-title {
                width: 100%;
                color: #50506f;
                text-align: center;
                font-weight: bold;
                font-size: 28rpx;
                margin-bottom: 30rpx;
            }
            .wechatGroup-img {
                background-color: #fff;
                width: 340rpx;
                height: auto;
                overflow: hidden;
                margin: 0 auto 30rpx;
                padding: 20rpx;
                box-sizing: border-box;
                
                image {
                    display: block;
                    width: 300rpx;
                    height: 300rpx;
                    margin: 0 auto;
                }
                .wechatGroup-sumbit {
                    width: 150rpx;
                    height: 70rpx;
                    line-height: 70rpx;
                    text-align: center;
                    color: #fff;
                    border-radius: 10rpx;
                    background-color: #E93323;
                    margin: 20rpx auto 0;
                }
            }
            
            
        }
    }
</style>
