<template>
    <view class="order-submission">
        <view class="allAddress">
            <view class="address acea-row row-between-wrapper" @click="addressTap"
                v-if="default_express_delivery === 1">
                <view class="addressCon" v-if="addressInfo.real_name">
                    <view class="flex flex_align_center flex_between">
                        <view class="flex flex_align_center">
                            <view class="block flex_line_height">
                                <image src="@/static/images/yuanshi/address.png" mode="widthFix"></image>
                            </view>
                            <view class="">
                                <text class="default font-color-red" v-if="addressInfo.is_default">[默认]</text>
                                <text
                                    class="addre">{{ addressInfo.province }}{{ addressInfo.city }}{{ addressInfo.district }}{{ addressInfo.detail }}</text>
                            </view>
                        </view>
                        <view class="iconfont icon-jiantou"></view>
                    </view>
                    <view class="name flex flex_align_center">
                        <view class="block"></view>
                        {{ addressInfo.real_name }}
                    </view>
                    <view class="name flex flex_align_center">
                        <view class="block"></view>
                        <text class="phone">{{ addressInfo.phone }}</text>
                    </view>
                </view>
                <view class="addressCon flex flex_align_center flex_between" v-else>
                    <view class="setaddress">设置收货地址</view>
                    <view class="iconfont icon-jiantou"></view>
                </view>
            </view>
            <view class="address shop" @click="showStoreList" v-if="store_self_mention === 1">
                <view class="addressCon acea-row row-between-wrapper">
                    <view style="max-width: 80%;">
                        <view class="name" v-if="storeItem">
                            {{ storeItem.name }}
                            <span class="phone" v-text="storeItem.phone"></span>
                        </view>
                        <view v-text="storeItem.address + ',' + storeItem.detailed_address"
                            v-if="storeItem && storeItem.address && storeItem.detailed_address"></view>
                    </view>
                    <view class="iconfont icon-jiantou"></view>
                </view>
            </view>
        </view>
        <OrderGoods :evaluate="0" :cartInfo="orderGroupInfo.cartInfo" :price="orderPrice.total_price" :isNew="isNew">
        </OrderGoods>
        <template v-if="isNew">
            <view class="newWrapper">
                <view class="item acea-row row-between-wrapper" v-if="
						orderGroupInfo.priceGroup.vipPrice > 0 &&
							userInfo.vip &&
							pinkId == 0 &&
							orderGroupInfo.bargain_id == 0 &&
							orderGroupInfo.combination_id == 0 &&
							orderGroupInfo.seckill_id == 0
					">
                    会员优惠
                    <view class="discount">-￥{{ orderGroupInfo.priceGroup.vipPrice }}</view>
                </view>
                <view style="margin-top: 24rpx;">
                    <wrapVip></wrapVip>
                </view>
                <view class="wrap">
                    <block v-if="shipping_type === 1&&default_express_delivery===0">
                        <view class="item acea-row row-between-wrapper">
                            <view>联系人</view>
                            <view class="discount"><input type="text" placeholder="请填写您的联系姓名" v-model="contacts" />
                            </view>
                        </view>
                        <view class="item acea-row row-between-wrapper">
                            <view>联系电话</view>
                            <view class="discount"><input type="text" placeholder="请填写您的联系电话" v-model="contactsTel" />
                            </view>
                        </view>
                    </block>
                    <view class="item">
                        <view>订单备注</view>
                        <textarea placeholder="请添加备注（150字以内）" v-model="mark"></textarea>
                    </view>
                    <view class="item">
                        <view>支付方式</view>
                        <view class="methods flex flex_align_center flex_between" @click="payItem('weixin')">
                            <image src="@/static/images/yuanshi/wxpay.png" mode=""></image>
                            <image src="@/static/images/yuanshi/check.png" mode="" v-if="active === 'weixin'"></image>
                            <image src="@/static/images/yuanshi/uncheck.png" mode="" v-else></image>
                        </view>
                        <view class="methods flex flex_align_center flex_between" @click="payItem('yue')"
                            v-if="!isWeixin">
                            <view class="tip">
                                余额支付
                                <text>可用余额：{{ userInfo.now_money || 0 }}</text>
                            </view>
                            <image src="@/static/images/yuanshi/check.png" mode="" v-if="active === 'yue'"></image>
                            <image src="@/static/images/yuanshi/uncheck.png" mode="" v-else></image>
                        </view>
                        <view class="methods flex flex_align_center flex_between"
                            v-if="offlinePayStatus === 1 && deduction === false && shipping_type === 0"
                            @click="payItem('offline')">
                            <view class="tip">线下支付</view>
                            <image src="@/static/images/yuanshi/check.png" mode="" v-if="active === 'offline'"></image>
                            <image src="@/static/images/yuanshi/uncheck.png" mode="" v-else></image>
                        </view>
                    </view>
                </view>
            </view>
        </template>
        <template v-else>
            <view class="wrapper">
                <view class="item acea-row row-between-wrapper" @click="couponTap" v-if="deduction === false">
                    <view>优惠券</view>
                    <view class="discount">
                        {{ usableCoupon.coupon_title || '请选择' }}
                        <span class="iconfont icon-jiantou"></span>
                    </view>
                </view>
                <view class="item acea-row row-between-wrapper" v-if="deduction === false">
                    <view>积分抵扣</view>
                    <view class="discount acea-row row-middle">
                        <view>
                            当前积分
                            <span class="num font-color-red">{{ userInfo.integral || 0 }}</span>
                        </view>
                        <checkbox-group @change="checkIntegral">
                            <checkbox :checked="useIntegral" />
                        </checkbox-group>
                    </view>
                </view>
                <view class="item acea-row row-between-wrapper" v-if="
						orderGroupInfo.priceGroup.vipPrice > 0 &&
							userInfo.vip &&
							pinkId == 0 &&
							orderGroupInfo.bargain_id == 0 &&
							orderGroupInfo.combination_id == 0 &&
							orderGroupInfo.seckill_id == 0
					">
                    会员优惠
                    <view class="discount">-￥{{ orderGroupInfo.priceGroup.vipPrice }}</view>
                </view>
                <view class="item acea-row row-between-wrapper" v-if="shipping_type === 0">
                    <view>快递费用</view>
                    <view class="discount">{{ orderPrice.pay_postage > 0 ? '￥' + orderPrice.pay_postage : '免运费' }}
                    </view>
                </view>
                <view v-else>
                    <view class="item acea-row row-between-wrapper">
                        <view>联系人</view>
                        <view class="discount"><input type="text" placeholder="请填写您的联系姓名" v-model="contacts" /></view>
                    </view>
                    <view class="item acea-row row-between-wrapper">
                        <view>联系电话</view>
                        <view class="discount"><input type="text" placeholder="请填写您的联系电话" v-model="contactsTel" />
                        </view>
                    </view>
                </view>
                <view class="item">
                    <view>备注信息</view>
                    <textarea placeholder="请添加备注（150字以内）" v-model="mark"></textarea>
                </view>
            </view>
            <view class="wrapper">
                <view class="item">
                    <view>支付方式</view>
                    <view class="list">
                        <!-- #ifndef MP-TOUTIAO -->
                        <view class="payItem acea-row row-middle" :class="active === 'weixin' ? 'on' : ''"
                            @click="payItem('weixin')">
                            <view class="name acea-row row-center-wrapper">
                                <view class="iconfont icon-weixin2" :class="active === 'weixin' ? 'bounceIn' : ''">
                                </view>
                                微信支付
                            </view>
                            <view class="tip">微信快捷支付</view>
                        </view>
                        <!-- #endif -->
                        <!-- #ifdef MP-TOUTIAO -->
                        <view class="payItem acea-row row-middle" :class="active === 'bytedance' ? 'on' : ''"
                            @click="payItem('bytedance')">
                            <view class="name acea-row row-center-wrapper">
                                <image style="width: 46rpx;height: 46rpx;margin-right: 10rpx;"
                                    :class="active === 'weixin' ? 'bounceIn' : ''"
                                    src="@/static/images/yuanshi/douyin.png" mode=""></image>
                                抖音支付
                            </view>
                            <view class="tip">抖音快捷支付</view>
                        </view>
                        <!-- #endif -->
                        <view class="payItem acea-row row-middle" :class="active === 'yue' ? 'on' : ''"
                            @click="payItem('yue')">
                            <view class="name acea-row row-center-wrapper">
                                <view class="iconfont icon-icon-test" :class="active === 'yue' ? 'bounceIn' : ''">
                                </view>
                                余额支付
                            </view>
                            <view class="tip">可用余额：{{ userInfo.now_money || 0 }}</view>
                        </view>
                        <view class="payItem acea-row row-middle" :class="active === 'offline' ? 'on' : ''"
                            @click="payItem('offline')"
                            v-if="offlinePayStatus === 1 && deduction === false && shipping_type === 0">
                            <view class="name acea-row row-center-wrapper">
                                <view class="iconfont icon-yinhangqia" :class="active === 'offline' ? 'bounceIn' : ''">
                                </view>
                                线下支付
                            </view>
                            <view class="tip">线下方便支付</view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="moneyList">
                <view class="item acea-row row-between-wrapper" v-if="orderPrice.total_price !== undefined">
                    <view>商品总价：</view>
                    <view class="money">￥{{ orderPrice.total_price }}</view>
                </view>
                <view class="item acea-row row-between-wrapper" v-if="orderPrice.pay_postage > 0">
                    <view>运费：</view>
                    <view class="money">+￥{{ orderPrice.pay_postage }}</view>
                </view>
                <view class="item acea-row row-between-wrapper" v-if="orderPrice.coupon_price > 0">
                    <view>优惠券抵扣：</view>
                    <view class="money">-￥{{ orderPrice.coupon_price }}</view>
                </view>
                <view class="item acea-row row-between-wrapper" v-if="orderPrice.deduction_price > 0">
                    <view>积分抵扣：</view>
                    <view class="money">-￥{{ orderPrice.deduction_price }}</view>
                </view>
            </view>
        </template>
        <view style="height:120rpx"></view>
        <view class="footer acea-row row-between-wrapper">
            <view class="footer_l">
                合计
                <text>￥{{ orderPrice.pay_price }}</text>
            </view>
            <view class="footer_r" @click="createOrder">立即结算</view>
        </view>
        <CouponListWindow v-on:couponchange="changecoupon($event)" v-model="showCoupon" :price="orderPrice.total_price"
            :checked="usableCoupon.id" :cartid="cartId" @checked="changeCoupon"></CouponListWindow>
        <x-home></x-home>
        <AddressWindow @checked="changeAddress" @redirect="addressRedirect" v-model="showAddress"
            :checked="addressInfo.id" ref="mychild"></AddressWindow>
        <x-authorize @login="updateData"></x-authorize>
    </view>
</template>

<script>
    import OrderGoods from '@/components/OrderGoods';
    import CouponListWindow from '@/components/CouponListWindow';
    import AddressWindow from '@/components/AddressWindow';
    import wrapVip from '@/components/yuanshi/wrap-vip';
    import {
        postOrderConfirm,
        postOrderComputed,
        createOrder
    } from '@/api/order';
    import {
        initTime,
        authNavigator,
        toLogin,
        checkLogin,
        autoAuth,
        zxauthNavigator
    } from '@/utils/common.js';
    import {
        storeListApi,
        postCartAdd,
        getProductDetail
    } from '@/api/store';
    import {
        RegPhone,
        RegFixedPhone,
        isWeixin
    } from '@/utils/validate';
    import {
        mapGetters
    } from 'vuex';
    import {
        openPaySubscribe
    } from '@/utils/SubscribeMessage.js';
    // #ifdef H5
    import {
        pay
    } from '@/utils/wechat/pay.js';
    const _isWeixin = isWeixin();
    // #endif

    // #ifdef MP
    const _isWeixin = true;
    // #endif

    import {
        LONGITUDE,
        LATITUDE
    } from '@/config.js';
    const NAME = 'OrderSubmission';
    export default {
        name: NAME,
        components: {
            OrderGoods,
            CouponListWindow,
            AddressWindow,
            wrapVip
        },
        props: {},
        computed: {
            ...mapGetters(['storeItems']),
            storeItem: function() {
                if (JSON.stringify(this.storeItems) == '{}') {
                    return this.storeList;
                } else {
                    return this.storeItems;
                }
            }
        },
        data: function() {
            return {
                tmplIds: [
                    // 发货通知
                    'pGyJf4feuZDn6-A_WeAsRpXaWdW9N4u1akU_WRjfWQg',
                    // 收货结果通知
                    'd35y39CStSP9gMxWNzCKh3lgJgxu9HXdAGRR6l5K_XQ',
                    // 订单评价提醒
                    'i9GEZNtoqXQHbPJowHfiFrLZkDCEFBaDvD9JQxoO7EQ'
                ],
                cartId: '',
                offlinePayStatus: 2,
                // #ifdef MP-WEIXIN
                from: 'routine',
                // #endif
                // #ifdef MP-TOUTIAO
                from: 'bytedance',
                active: 'bytedance',
                // #endif
                // #ifdef H5
                from: _isWeixin ? 'weixin' : 'weixinh5',
                // #endif
                deduction: true,
                isWeixin: process.env.NODE_ENV === 'development' ? false : _isWeixin,
                pinkId: 0,

                // #ifndef MP-TOUTIAO
                active: _isWeixin ? 'weixin' : 'yue',
                // #endif

                showCoupon: false,
                showAddress: false,
                addressInfo: {},
                couponId: 0,
                orderGroupInfo: {
                    priceGroup: {}
                },
                usableCoupon: {},
                addressLoaded: false,
                useIntegral: false,
                orderPrice: {
                    pay_price: '计算中'
                },
                mark: '',
                system_store: {},
                shipping_type: 1, //0快递 1门店
                contacts: '',
                contactsTel: '',
                store_self_mention: 0,
                default_express_delivery: 0,
                userInfo: {},
                storeList: [],
                isNew: false,
                productID: 0,
                scene: 0, // 默认微信场景值
                unique: '',
                traceId:'', // 分享id
            };
        },
        watch: {},
        onLoad(options) {
            let that = this;
            // console.log(options);
            this.productID = options.productID || 0;

            this.couponId = options.couponId || 0;
            this.pinkId = options.pinkId ? parseInt(options.pinkId) : 0;
            this.addressId = options.addressId || 0;
            this.cartId = options.cartId;
            this.is_address = options.is_address ? true : false;
            this.isNew = !!Number(options.isNew);
            console.log('productID', this.productID)
            
            // if (this.productID == 0) {
            //     // 原普通场景
            //     this.getCartInfo();
            // } else {
            //     // 视频号场景
            //     this.getProDetail(this.productID)
            // }
            this.getCartInfo();
        },
        onShow() {
            // #ifdef MP-WEIXIN
            this.getScene()
            // #endif
        },
        mounted: function() {},
        methods: {
            getScene() {
                let that = this;
                let opt = wx.getEnterOptionsSync();
                this.scene = opt.scene;
            },
            getTraceId(){
                let that = this;
                let traceId = '';
                return new Promise((resolve, reject) => {
                    wx.checkBeforeAddOrder({
                      success (res) { 
                          traceId = res.data.traceId;
                          // console.log('获取分享id', traceId)
                          resolve(traceId);
                      },
                      fail (err) { 
                          reject(err);
                          // console.log('获取分享id失败', err)
                      }
                    })
                });
            },
            updateData() {
                this.getProDetail(this.productID)
            },
            getProDetail(id) {
                let _this = this;
                getProductDetail(id)
                    .then(res => {
                        _this.unique = res.data.productValue['默认'].unique ? res.data.productValue['默认'].unique : '';
                        _this.directBuy(id, _this.unique)

                    })
                    .catch(err => {

                    });
            },
            // 视频号直接购买调试
            directBuy(id, unique) {
                let that = this;
                let q = {
                    productId: id,
                    cartNum: 1,
                    new: 1,
                    uniqueId: unique,
                };
                postCartAdd(q).then(function(res) {
                        that.cartId = res.data.cartId.toString();
                        that.getCartInfo();
                    })
                    .catch(res => {
                        return that.$showToast(res.msg || res);
                    });
            },
            checkIntegral() {
                this.useIntegral = !this.useIntegral;
                this.computedPrice();
            },
            // 获取门店列表数据
            getList: function() {
                let data = {
                    latitude: this.$storage.get(LATITUDE) || '', //纬度
                    longitude: this.$storage.get(LONGITUDE) || '', //经度
                    page: 1,
                    limit: 10,
                    product_id: this.orderGroupInfo.cartInfo[0].product_id
                };
                storeListApi(data)
                    .then(res => {
                        this.storeList = res.data.list[0];
                        this.$store.commit('GET_STORE', res.data.list[0]);
                    })
                    .catch(err => {
                        this.$showToast(err.msg || err, 'error');
                    });
            },
            // 跳转到门店列表
            showStoreList() {
                this.$store.commit('GET_TO', 'orders');
                this.$navigator('/pages/shop/StoreList?id=' + this.orderGroupInfo.cartInfo[0].product_id);
                // this.$router.push('/shop/storeList/orders');
            },
            addressType: function(index) {
                if (index && !this.system_store.id) return this.$showToast('暂无门店信息，您无法选择到店自提！', 'error');
                this.shipping_type = index;
            },
            computedPrice() {
                let shipping_type = this.shipping_type;
                let obj = {
                    addressId: this.addressInfo.id,
                    useIntegral: this.useIntegral ? 1 : 0,
                    couponId: this.usableCoupon.id || 0,
                    shipping_type: parseInt(shipping_type) + 1,
                    payType: this.active,
                    secKillId: this.orderGroupInfo.seckill_id
                };
                postOrderComputed(this.orderGroupInfo.orderKey, obj)
                    .then(res => {
                        const data = res.data;
                        if (data.status === 'EXTEND_ORDER') {
                            this.$navigator(`/pages/shop/GoodsCon?id=${data.result.orderId}`, 'redirectTo');
                        } else {
                            this.orderPrice = data.result;
                            console.log('计算订单金额', this.orderPrice)
                        }
                    })
                    .catch(err => {
                        console.log('error', err);
                        this.$showToast(err.msg || err, 'error');
                    });
            },
            getCartInfo() {
                const cartIds = this.cartId;
                if (!cartIds) {
                    this.$showToast('参数有误', 'error');
                    return this.$navigator(-1);
                }
                postOrderConfirm({
                    cartId:cartIds,
                    scene: this.scene,//场景值
                })
                    .then(res => {
                        this.offlinePayStatus = res.data.offline_pay_status;
                        this.orderGroupInfo = res.data;
                        this.deduction = res.data.deduction;
                        this.usableCoupon = res.data.usableCoupon || {};
                        this.addressInfo = res.data.addressInfo || {};
                        this.system_store = res.data.system_store || {};
                        this.store_self_mention = res.data.store_self_mention;
                        this.userInfo = res.data.userInfo;
                        this.default_express_delivery = res.data.default_express_delivery;
                        this.shipping_type = res.data.store_self_mention;

                        this.getList();
                        this.computedPrice();
                    })
                    .catch(err => {
                        this.$showToast('加载订单数据失败', 'error');
                        return this.$navigator(-1);
                    });
            },
            addressTap: function() {
                this.showAddress = true;
                if (!this.addressLoaded) {
                    this.addressLoaded = true;
                    this.$refs.mychild.getAddressList();
                }
            },
            addressRedirect() {
                this.addressLoaded = false;
                this.showAddress = false;
            },
            couponTap: function() {
                this.showCoupon = true;
            },
            changeCoupon: function(coupon) {
                if (!coupon) {
                    this.usableCoupon = {
                        coupon_title: '不使用优惠券',
                        id: 0
                    };
                } else {
                    this.usableCoupon = coupon;
                }
                console.log('选择优惠券', this.usableCoupon)
                this.computedPrice();
            },
            payItem: function(index) {
                this.active = index;
                this.computedPrice();
            },
            changeAddress(addressInfo) {
                this.addressInfo = addressInfo;
                this.computedPrice();
            },
            async createOrder() {
                let shipping_type = this.shipping_type,
                    that = this;
                zxauthNavigator()
                if (!this.active) return this.$showToast('请选择支付方式');
                if (!this.addressInfo.id && this.default_express_delivery === 1) return this.$showToast('请选择收货地址');
                if (this.active == 'yue' && parseFloat(that.userInfo.now_money) < parseFloat(that.orderPrice.pay_price))
                    return that.$showToast('余额不足！');
                if (this.shipping_type) {
                    this.contacts = this.shipping_type === 1 && !this.isNew ? this.contacts : this.addressInfo
                        .real_name;
                    this.contactsTel = this.shipping_type === 1 && !this.isNew ? this.contactsTel : this.addressInfo
                        .phone;
                    if ((this.contacts === '' || this.contactsTel === '') && this.shipping_type) return this.$showToast(
                        '请填写联系人或联系人电话');
                    if (!RegPhone(this.contactsTel) && !RegFixedPhone(this.contactsTel)) {
                        return this.$showToast('请填写正确的手机号');
                    }
                    if (!this.contacts) {
                        return this.$showToast('请填写您的真实姓名');
                    }
                }
                // this.traceId = await this.getTraceId();
                this.$loadingToast('生成订单中');
                openPaySubscribe()
                    .then(() => {
                        createOrder(this.orderGroupInfo.orderKey, {
                                real_name: this.contacts,
                                phone: this.contactsTel,
                                addressId: this.addressInfo.id,
                                useIntegral: this.useIntegral ? 1 : 0,
                                couponId: this.usableCoupon.id || 0,
                                payType: this.active,
                                pinkId: this.pinkId,
                                seckill_id: this.orderGroupInfo.seckill_id,
                                combinationId: this.orderGroupInfo.combination_id,
                                bargainId: this.orderGroupInfo.bargain_id,
                                from: this.from,
                                mark: this.mark || '',
                                shipping_type: parseInt(shipping_type) + 1,
                                store_id: this.storeItem ? this.storeItem.id : 0,
                                // scene: this.scene,//场景值
                                // trace_id: this.traceId, 
                            })
                            .then(res => {
                                this.$hideLoading();
                                const data = res.data;
                                // #ifndef MP-TOUTIAO
                                let url = '/pages/order/PaymentStatus?orderId=' + data.result.orderId +
                                    '&msg=' + res.msg;
                                // #endif
                                // #ifdef MP-TOUTIAO
                                let url = '/pages/order/PaymentStatus?orderId=' + data.result.key + '&msg=' +
                                    res.msg;
                                // #endif
                                switch (data.status) {
                                    case 'ORDER_EXIST':
                                    case 'EXTEND_ORDER':
                                    case 'PAY_DEFICIENCY':
                                    case 'PAY_ERROR':
                                        that.$showToast(res.msg);
                                        if (data.result.orderId) {
                                            that.$navigator(url + '&status=0', 'redirectTo');
                                        }
                                        break;
                                    case 'SUCCESS':
                                        that.$showToast(res.msg);
                                        // #ifdef H5
                                        that.$navigator(url + '&status=1', 'redirectTo');
                                        // #endif
                                        // #ifdef MP
                                        
                                        uni.getSetting({
                                            withSubscriptions: true,
                                            success(res) {
                                                console.log('获取设置res++', res.subscriptionsSetting.mainSwitch)
                                                if (res.subscriptionsSetting.mainSwitch) {
                                                    uni.requestSubscribeMessage({
                                                        tmplIds: that.tmplIds,
                                                        success(res) {},
                                                        fail(err) {
                                                        },
                                                        complete: function(res) {
                                                            console.log('获取设置res--', res[
                                                                'pGyJf4feuZDn6-A_WeAsRpXaWdW9N4u1akU_WRjfWQg'
                                                                ])
                                                            if (res[
                                                                'pGyJf4feuZDn6-A_WeAsRpXaWdW9N4u1akU_WRjfWQg'] ==
                                                                'accept') {
                                                                // reject
                                                                if (that.BargainId || that.combinationId || that.pinkId || that
                                                                    .seckillId) {
                                                                    console.log('11')
                                                                    return that.$navigator(url, 'reLaunch');
                                                                    
                                                                }
                                                                console.log('22')
                                                                return that.$navigator(url, 'redirectTo');
                                                                
                                                            } else {
                                                                uni.showModal({
                                                                    title: '您已经拒绝商品收货通知',
                                                                    content: '请先打开消息通知设置',
                                                                    confirmText: '去允许',
                                                                    success: function(res1) {
                                                                        if (res1.cancel) {
                                                                            uni.showToast({
                                                                                title: '已取消',
                                                                                icon: 'none',
                                                                                duration: 1000
                                                                            })
                                                                            return
                                                                        } else if (res1.confirm) {
                                                                            uni.openSetting({
                                                                                success: function(res2) {
                                                                                    console.log('res2',res2)
                                                                                },
                                                                                fail: function(err) {
                                                                                    console.log('xxx',err)
                                                                                }
                                                                            })
                                                                        }
                                                                    },
                                                                })
                                                            }
                                                        },
                                                    })
                                                } else { // 用户没有点击“总是保持以上，不再询问”则每次都会调起订阅消息
                                                    uni.openSetting({ // 打开设置页
                                                        success(res) {
                                                            // console.log(res.authSetting) 
                                                        }
                                                    });
                                                }
                                            },
                                            fail(err) {
                                                console.log('获取设置err', err)
                                            },
                                        })
                                        // #endif
                                        break;
                                    case 'BYTEDANCE_PAY':
                                    case 'BYTEDANCE_ORDER':
                                        // #ifndef MP-TOUTIAO
                                        that.$navigator(url + '&status=2', 'redirectTo');
                                        setTimeout(() => {
                                            location.href = data.result.jsConfig.mweb_url;
                                        }, 100);
                                        // #endif
                                        // #ifdef MP-TOUTIAO
                                        let tt_res = res.data.result;
                                        // console.log('字节支付----', tt_res)
                                        tt.pay({
                                            orderInfo: {
                                                order_id: tt_res.orderId,
                                                order_token: tt_res.orderToken,
                                            },
                                            service: 5,
                                            _debug: 1,
                                            success(res) {
                                                if (res.code == 0) {
                                                    // console.log('字节支付成功', res)
                                                    that.$showToast('支付成功');
                                                    if (that.BargainId || that.combinationId || that
                                                        .pinkId || that.seckillId) {
                                                        return that.$navigator(url, 'reLaunch');
                                                    }
                                                    return that.$navigator(url, 'redirectTo');
                                                }
                                            },
                                            fail(res) {
                                                // console.log('字节支付失败', res)
                                                that.$showToast('取消支付');
                                                return that.$navigator(url + '&status=2', 'redirectTo');
                                            },
                                        });
                                        // #endif
                                        break;
                                    case 'WECHAT_PAY':
                                        // #ifdef H5
                                        pay(data.result.jsConfig).finally(() => {
                                            that.$navigator(url + '&status=4', 'redirectTo');
                                        });
                                        // #endif

                                        // #ifdef MP-WEIXIN
                                        let jsConfig = res.data.result.jsConfig;
                                        console.log('生成支付信息',jsConfig)
                                        //  原支付订单jsapi
                                        // requestOrderPayment
                                        // 视频号参数 timestamp 大写
                                        wx.requestPayment({
                                            timeStamp: jsConfig.timestamp,
                                            nonceStr: jsConfig.nonceStr,
                                            package: jsConfig.package,
                                            signType: jsConfig.signType,
                                            paySign: jsConfig.paySign,
                                            success: function(res) {
                                                // console.log('成功',res)
                                                uni.getSetting({
                                                    withSubscriptions: true,
                                                    success(res) {
                                                        console.log('获取设置res++', res.subscriptionsSetting.mainSwitch)
                                                        if (res.subscriptionsSetting.mainSwitch) {
                                                            uni.requestSubscribeMessage({
                                                                tmplIds: that.tmplIds,
                                                                success(res) {},
                                                                fail(err) {
                                                                },
                                                                complete: function(res) {
                                                                    console.log('获取设置res--', res[
                                                                        'pGyJf4feuZDn6-A_WeAsRpXaWdW9N4u1akU_WRjfWQg'
                                                                        ])
                                                                    if (res[
                                                                        'pGyJf4feuZDn6-A_WeAsRpXaWdW9N4u1akU_WRjfWQg'] ==
                                                                        'accept') {
                                                                        // reject
                                                                       
                                                                       that.$showToast('支付成功');
                                                                       if (that.BargainId || that.combinationId || that
                                                                           .pinkId || that.seckillId) {
                                                                           return that.$navigator(url, 'reLaunch');
                                                                       }
                                                                       return that.$navigator(url, 'redirectTo');
                                                                        
                                                                    } else {
                                                                        uni.showModal({
                                                                            title: '您已经拒绝商品收货通知',
                                                                            content: '请先打开消息通知设置',
                                                                            confirmText: '去允许',
                                                                            success: function(res1) {
                                                                                if (res1.cancel) {
                                                                                    uni.showToast({
                                                                                        title: '已取消',
                                                                                        icon: 'none',
                                                                                        duration: 1000
                                                                                    })
                                                                                    return
                                                                                } else if (res1.confirm) {
                                                                                    uni.openSetting({
                                                                                        success: function(res2) {
                                                                                            console.log('res2',res2)
                                                                                        },
                                                                                        fail: function(err) {
                                                                                            console.log('xxx',err)
                                                                                        }
                                                                                    })
                                                                                }
                                                                            },
                                                                        })
                                                                    }
                                                                },
                                                            })
                                                        } else { // 用户没有点击“总是保持以上，不再询问”则每次都会调起订阅消息
                                                            uni.openSetting({ // 打开设置页
                                                                success(res) {
                                                                    // console.log(res.authSetting) 
                                                                }
                                                            });
                                                        }
                                                    },
                                                    fail(err) {
                                                        console.log('获取设置err', err)
                                                    },
                                                })
                                                
                                                
                                            },
                                            fail: function(e) {
                                                that.$showToast('取消支付');
                                                console.log('失败',e)
                                                return that.$navigator(url + '&status=2',
                                                    'redirectTo');
                                            },
                                            complete: function(e) {
                                                //关闭当前页面跳转至订单状态
                                                if (res.errMsg == 'requestPayment:cancel')
                                                    return that.$navigator(url + '&status=2',
                                                        'redirectTo');
                                            }
                                        });
                                        // #endif
                                }
                            })
                            .catch(err => {
                                console.log('err', err);
                                this.$showToast(err.msg || err || '创建订单失败', 'error');
                                this.$router.go(-1);
                            });
                    })
                    .catch(err => {
                        console.log('err', err);
                    });
            }
        }
    };
</script>
<style lang="scss">
    page {
        background: $uni-bg-color;
    }
</style>
<style scoped lang="scss">
    .allAddress {
        .address {
            padding: 46rpx 28rpx 26rpx 28rpx;
            background-color: #fff;
            margin: 20rpx;
            box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
            border-radius: 30rpx;

            &.shop {
                padding: 26rpx 28rpx;
            }

            .addressCon {
                width: 100%;
                font-size: 24rpx;
                color: #666;

                .addre {
                    color: #333333;
                    font-weight: bold;
                    font-size: 32rpx;
                }

                .block {
                    width: 58rpx;
                    text-align: center;

                    image {
                        width: 27rpx;
                        height: 27rpx;
                    }
                }

                .name {
                    font-size: 24rpx;

                    color: #666666;
                    margin-bottom: 4rpx;

                    .phone {}
                }

                .default {
                    margin-right: 12rpx;
                }

                .setaddress {
                    color: #333;
                    font-size: 28rpx;
                }
            }

            .iconfont {
                font-size: 30rpx;
                color: #a3a3a3;
                margin-left: 10rpx;
            }
        }
    }

    .newWrapper {
        .item {
            width: 710rpx;
            margin: 0 auto;
            padding: 27rpx 30rpx;
            font-size: 30rpx;

            color: #333333;
            background: $uni-bg-color;
            border-radius: 30rpx;
            box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);

            // margin-bottom: 24rpx;
            .info {
                color: #999999;

                text {
                    padding: 0 10rpx;
                }
            }

            .discount {
                font-size: 30rpx;
                color: #999;
                /* width: 500rpx; */
                text-align: right;

                .money {
                    color: #3e3e3e;
                    font-size: 24rpx;
                }
            }
        }

        .wrap {
            background: $uni-bg-color;
            width: 710rpx;
            margin: 34rpx auto;
            box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
            padding: 50rpx 28rpx;
            border-radius: 30rpx;

            .item {
                padding: 20rpx 0;
                width: 100%;
                box-shadow: none;
                font-size: 24rpx;
                border-radius: 0;

                color: #333333;

                &:not(:last-child) {
                    border-bottom: 2rpx solid #e2e6ec;
                }

                input {
                    font-size: 24rpx;
                }
            }

            textarea {
                background-color: #f9f9f9;
                box-sizing: border-box;
                width: 100%;
                height: 150rpx;
                border-radius: 3rpx;
                padding: 20rpx;
                margin: 20rpx 0;
                font-size: 24rpx;
            }

            .methods {
                margin-top: 30rpx;

                image {
                    width: 44rpx;
                    height: 44rpx;
                }

                .tip {
                    text {
                        padding: 0 10rpx;
                        color: #999999;
                    }
                }
            }
        }
    }

    .order-submission .wrapper {
        background-color: #fff;
        margin-top: 13rpx;
    }

    .order-submission .wrapper .item {
        padding: 27rpx 30rpx;
        font-size: 30rpx;
        color: #282828;
        border-bottom: 1px solid #f0f0f0;
    }

    .order-submission .wrapper .item .discount {
        font-size: 30rpx;
        color: #999;
        /* width: 500rpx; */
        text-align: right;
    }

    .order-submission .wrapper .item .discount .integral {
        margin-right: 40rpx;
    }

    .order-submission .wrapper .item .discount .checkbox-wrapper .icon {
        right: 0;
        left: unset;
    }

    .order-submission .wrapper .item .discount .iconfont {
        color: #515151;
        font-size: 30rpx;
        margin-left: 15rpx;
    }

    .order-submission .wrapper .item .discount .num {
        font-size: 32rpx;
        margin-right: 20rpx;
    }

    .order-submission .wrapper .item textarea {
        background-color: #f9f9f9;
        width: 640rpx;
        height: 140rpx;
        border-radius: 3rpx;
        margin-top: 30rpx;
        padding: 25rpx;
    }

    .order-submission .wrapper .item textarea::placeholder {
        color: #ccc;
    }

    .order-submission .wrapper .item .list {
        margin-top: 35rpx;
    }

    .order-submission .wrapper .item .list .payItem {
        border: 1px solid #eee;
        border-radius: 6rpx;
        height: 86rpx;
        width: 100%;
        margin-top: 20rpx;
        font-size: 28rpx;
        color: #282828;
    }

    .order-submission .wrapper .item .list .payItem.on {
        border-color: #fc5445;
        color: #e93323;
    }

    .order-submission .wrapper .item .list .payItem .name {
        width: 50%;
        text-align: center;
        border-right: 1px solid #eee;
    }

    .order-submission .wrapper .item .list .payItem .name .iconfont {
        width: 44rpx;
        height: 44rpx;
        border-radius: 50%;
        text-align: center;
        line-height: 44rpx;
        background-color: #fe960f;
        color: #fff;
        font-size: 30rpx;
        margin-right: 15rpx;
    }

    .order-submission .wrapper .item .list .payItem .name .iconfont.icon-weixin2 {
        background-color: #41b035;
    }

    .order-submission .wrapper .item .list .payItem .name .iconfont.icon-yinhangqia {
        background-color: #eb6623;
    }

    .order-submission .wrapper .item .list .payItem .tip {
        width: 49%;
        text-align: center;
        font-size: 26rpx;
        color: #aaa;
    }

    .order-submission .moneyList {
        margin-top: 12rpx;
        background-color: #fff;
        padding: 30rpx;
    }

    .order-submission .moneyList .item {
        font-size: 28rpx;
        color: #282828;
    }

    .order-submission .moneyList .item~.item {
        margin-top: 20rpx;
    }

    .order-submission .moneyList .item .money {
        color: #868686;
    }

    .order-submission .footer {
        @include fixed_footer(110rpx);
        background: $uni-bg-color;

        .footer_l {
            width: calc(100% - 400rpx);
            text-align: center;

            font-size: 32rpx;
            font-weight: bold;

            text {
                color: #ff5656;
            }
        }

        .footer_r {
            width: 400rpx;
            height: 112rpx;
            background: #ff5656;
            border-radius: 30rpx 0px 0px 0px;
            color: #ffffff;
            font-weight: bold;
            font-size: 32rpx;
            text-align: center;
            line-height: 112rpx;
        }
    }

    .order-submission .wrapper .shipping select {
        color: #999;
        padding-right: 15rpx;
    }

    .order-submission .wrapper .shipping .iconfont {
        font-size: 30rpx;
        color: #515151;
    }

    .order-submission .wrapper .item .discount input::placeholder {
        color: #ccc;
    }
</style>
