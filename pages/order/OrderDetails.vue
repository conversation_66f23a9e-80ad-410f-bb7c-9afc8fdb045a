<template>
	<view class="order_details">
		<!-- 给header上与data上加on为退款订单-->
		<template v-if="!refundOrder && orderInfo.shipping_type === 2 && orderInfo.paid === 1">
			<view class="writeOff">
				<view class="btn_hx" v-if="status.type === 3">已核销。待评价</view>
				<view class="wrap">
					<view class="title flex flex_align_center flex_between" :class="{ dpj: status.type > 2 }">
						<template v-if="status.type === 1">
							<view class="txt">
								<view>待核销，请到指定地点核销</view>
								<view>待核销</view>
							</view>
							<view class="image"><image src="@/static/images/yuanshi/scan.png" mode=""></image></view>
						</template>
						<template v-if="status.type === 3">
							<view class="txt">
								<view>已核销，发布你的测评体验</view>
								<view>{{ orderInfo._add_time }}</view>
							</view>
							<view class="image"><image src="@/static/images/yuanshi/check.png" mode=""></image></view>
						</template>
						<template v-if="status.type === 4">
							<view class="txt">
								<view>已核销，已发布测评体验</view>
								<view>{{ orderInfo._add_time }}</view>
							</view>
							<view class="image"><image src="@/static/images/yuanshi/check.png" mode=""></image></view>
						</template>
					</view>
					<view class="grayBg">
						<view class="pictrue"><image :src="orderInfo.code" /></view>
					</view>
					<view class="num">{{ orderInfo._verify_code }}</view>
				</view>

				<view class="rules">
					<view class="item">
						<view class="title ">核销时间</view>
						<view class="info">
							每日：
							<text class="time">{{ system_store.day_time }}</text>
						</view>
					</view>
					<view class="item">
						<view class="title">使用说明</view>
						<view class="info">{{ orderInfo.info ? orderInfo.info : '可将二维码出示给店员扫描或提供数字核销码' }}</view>
					</view>
				</view>
			</view>

			<view class="map acea-row row-between-wrapper">
				<view>核销地址</view>
				<view class="place " @click="showChang"><!-- <image src="@/static/images/yuanshi/address.png" mode="widthFix"></image> --></view>
			</view>
		</template>
		<view class="content_wrap" :class="{ radius: orderInfo.shipping_type === 1 }">
			<template v-if="!refundOrder">
				<view class="address">
					<template v-if="orderInfo.shipping_type === 1">
						<view class="name">{{ orderInfo.real_name }}-<text class="phone">{{ orderInfo.user_phone }}</text></view>
						<view >{{ orderInfo.user_address }}</view>
						
					</template>
					<template v-else>
						<view class="name flex flex_align_center flex_between">
							<view>{{ system_store.name }}</view>
							<view class="image" @click="showChang"><image src="@/static/images/yuanshi/address.png" mode="widthFix"></image></view>
						</view>
						<view class="desc">{{ system_store._detailed_address }}</view>
						<view class="phone flex flex_align_center" @click="makePhoneCall(system_store.phone)">
							<image src="@/static/images/yuanshi/phone.png" mode="widthFix"></image>
							<text class="phone">{{ system_store.phone }}</text>
						</view>
						
					</template>
				</view>
			</template>

			<view class="order_goods">
				<view class="wrap">
					<view class="item flex" v-for="cart in orderInfo.cartInfo" :key="cart.id">
						<view class="pictrue" @click="goGoodDetail(cart, orderInfo.cartInfo)"><image :src="cart.productInfo.image" alt="img" class="image" mode="widthFix"/></view>
						<view class="text">
							<view class="name ">{{ cart.productInfo.store_name }}</view>
							<view class="desc">{{ cart.productInfo.attrInfo.suk }}</view>
							<view class="money flex flex_between">
								<view>￥ {{ cart.productInfo.attrInfo ? cart.productInfo.attrInfo.price : cart.productInfo.price }}</view>
								<view>x {{ cart.cart_num }}</view>
							</view>
							<view class="flex flex_end">
								<view class="evaluate " v-if="status.type === 3 && orderInfo.shipping_type !== 2" @click="goPages('/pages/shop/GoodsEvaluate?unique=' + cart.unique)">评价</view>
							</view>
						</view>
					</view>
				</view>
				<view class="detail">
					<view class="item acea-row row-between">
						<view>订单编号：</view>
						<view class="conter acea-row row-middle row-right">
							{{ orderInfo.order_id || '' }}
							<!-- <text class="copy copy-data" @click="copy(orderInfo.order_id)">复制</text> -->
						</view>
					</view>
					<view class="item acea-row row-between">
						<view>下单时间：</view>
						<view class="conter">{{ (orderInfo.add_time_y || '') + ' ' + (orderInfo.add_time_h || '') }}</view>
					</view>
					<view class="item acea-row row-between">
						<view>订单类型：</view>
						<view class="conter">{{ orderTypeName || '' }}</view>
					</view>
					<view class="item acea-row row-between">
						<view>支付状态：</view>
						<view class="conter">{{ orderInfo.paid ? '已支付' : '未支付' }}</view>
					</view>
					<view class="item acea-row row-between">
						<view>支付方式：</view>
						<view class="conter">{{ orderInfo._status._payType }}</view>
					</view>
					<view class="item acea-row row-between" v-if="orderInfo.mark">
						<view>买家留言：</view>
						<view class="conter">{{ orderInfo.mark }}</view>
					</view>
				</view>
				<view v-if="orderInfo.status != 0">
					<view class="detail" v-if="orderInfo.delivery_type === 'express'">
						<view class="item acea-row row-between">
							<view>配送方式：</view>
							<view class="conter">发货</view>
						</view>
						<view class="item acea-row row-between">
							<view>快递公司：</view>
							<view class="conter">{{ orderInfo.delivery_name || '' }}</view>
						</view>
						<view class="item acea-row row-between">
							<view>快递号：</view>
							<view class="conter">{{ orderInfo.delivery_id || '' }}</view>
						</view>
					</view>

					<view class="detail" v-if="orderInfo.delivery_type === 'send'">
						<view class="item acea-row row-between">
							<view>配送方式：</view>
							<view class="conter">送货</view>
						</view>
						<view class="item acea-row row-between">
							<view>配送人：</view>
							<view class="conter">{{ orderInfo.delivery_name || '' }}</view>
						</view>
						<view class="item acea-row row-between">
							<view>配送电话：</view>
							<view class="conter acea-row row-middle row-right">
								{{ orderInfo.delivery_id || '' }}
								<a :href="'tel:' + orderInfo.delivery_id"><text class="copy">拨打</text></a>
							</view>
						</view>
					</view>
				</view>
				<!--     退款订单详情 -->
				<view class="detail" v-if="refundOrder">
					<view class="item acea-row row-between">
						<view>收货人：</view>
						<view class="conter">{{ orderInfo.real_name || '' }}</view>
					</view>
					<view class="item acea-row row-between">
						<view>联系电话：</view>
						<view class="conter">{{ orderInfo.user_phone || '' }}</view>
					</view>
					<view class="item acea-row row-between">
						<view>收货地址：</view>
						<view class="conter">{{ orderInfo.user_address || '' }}</view>
					</view>
				</view>
			</view>

			<view class="wrapper">
				<template v-if="orderType">
					<view class="item acea-row row-between">
						<view>支付金额：</view>
						<view class="conter">￥{{ orderInfo.total_price || '' }}</view>
					</view>
					<view class="item acea-row row-between" v-if="orderInfo.coupon_price > 0">
						<view>优惠券抵扣：</view>
						<view class="conter">-￥{{ orderInfo.coupon_price || '' }}</view>
					</view>
					<view class="item acea-row row-between" v-if="orderInfo.use_integral > 0">
						<view>积分抵扣：</view>
						<view class="conter">-￥{{ orderInfo.deduction_price || '' }}</view>
					</view>
					<view class="item acea-row row-between" v-if="orderInfo.pay_postage > 0">
						<view>运费：</view>
						<view class="conter">￥{{ orderInfo.pay_postage || '' }}</view>
					</view>
				</template>
				<view class="item actualPay flex flex_between">
					实付款：
					<text class="money">￥{{ orderInfo.pay_price }}</text>
				</view>
			</view>
			<view class="footer acea-row row-right row-middle" v-if="!refundOrder && offlineStatus">
				<template v-if="status.type === 0">
					<view class="btn default" @click="cancelOrder">取消订单</view>
					<view class="btn " @click="paymentTap">立即付款</view>
				</template>
				<template v-if="status.type > 0 && orderInfo.paid === 1 && orderInfo.refund_status === 0">
					<view class="btn default" @click="goOrderRefund(`/pages/order/GoodsReturn?order_id=${orderInfo.order_id}`)">申请退款</view>
				</template>
				<template v-if="status.type === 4">
					<view class="btn default" @click="delOrder">删除订单</view>
				</template>
				<template v-if="status.type > 1 && orderInfo.delivery_type === 'express'">
					<view class="btn default" @click="goPages(`/pages/order/Logistics?order_id=${orderInfo.order_id}`)">查看物流</view>
				</template>
				<template v-if="status.type === 2">
					<view class="btn " @click="takeOrder">确认收货</view>
				</template>
				<template v-if="status.type === 6">
					<view class="btn " @click="goPages(`/pages/activity/GroupRule?pink_id=${orderInfo.pink_id}`)">查看拼团</view>
				</template>
				<view class="btn " @click="goOrderConfirm(orderInfo)" v-if="orderInfo.paid === 1 && orderInfo.refund_status === 0 && orderInfo.status >= 2">再次购买</view>
			</view>
		</view>
		<Payment v-model="pay" :types="payType" @checked="toPay" :balance="userInfo.now_money"></Payment>
		<!-- #ifdef H5 -->
		<view class="geoPage" v-if="mapShow">
			<iframe
				width="100%"
				height="100%"
				frameborder="0"
				scrolling="no"
				:src="'https://apis.map.qq.com/uri/v1/geocoder?coord=' + system_store.latitude + ',' + system_store.longitude + '&referer=' + mapKey"
			></iframe>
		</view>
		<!-- #endif -->
		<x-home></x-home>
		<GeneralWindow :generalActive="generalActive" @closeGeneralWindow="closeGeneralWindow" :generalContent="generalContent"></GeneralWindow>
	</view>
</template>

<script>
import OrderGoods from '@/components/OrderGoods';
import { orderDetail, orderAgain } from '@/api/order';
import Payment from '@/components/Payment';
import { mapGetters } from 'vuex';
import { cancelOrderHandle, takeOrderHandle, delOrderHandle, payOrderHandle } from '@/utils/order.js';

import GeneralWindow from '@/components/GeneralWindow';
import { authOpenLocation, setClipboardData } from '@/utils/common.js';
import { openOrderRefundSubscribe } from '@/utils/SubscribeMessage.js';
// #ifdef H5
import { isWeixin } from '@/utils/validate.js';

const _isWeixin = isWeixin();
// #endif

// #ifdef MP
const _isWeixin = true;
// #endif

const NAME = 'OrderDetails';
export default {
	name: NAME,
	components: {
		OrderGoods,
		Payment,
		GeneralWindow
	},
	props: {},
	data: function() {
		return {
			offlinePayStatus: 2,
			orderTypeName: '普通订单',
			orderTypeNameStatus: true,
			offlineStatus: true,
			id: '',
			orderInfo: {
				_status: {},
				cartInfo: []
			},
			status: {},
			pay: false,
			payType: ['yue', 'weixin'],
			// #ifdef MP-WEIXIN
			from: 'routine',
			// #endif
			// #ifdef MP-TOUTIAO
			from: 'bytedance',
			// #endif
			// #ifdef H5
			from: _isWeixin ? 'weixin' : 'weixinh5',
			// #endif
			system_store: {},
			mapKay: '',
			mapShow: false,
			generalActive: false,
			generalContent: {
				promoterNum: '',
				title: ''
			},
			paymentType: true //true时，测评版本直接微信支付
		};
	},
	computed: {
		refundOrder() {
			return this.orderInfo.refund_status > 0;
		},
		orderType() {
			let typ = true;
			this.orderInfo.cartInfo.forEach((item, index) => {
				if (item.type === 'evaluate_product') {
					typ = false;
				}
			});
			return typ;
		},
		...mapGetters(['userInfo'])
	},
	mounted: function() {},
	onLoad(options) {
		const { order_id } = options;
		this.id = order_id;
	},
	onShow() {
		this.getDetail();
	},
	methods: {
		goPages(path) {
			this.$navigator(path);
		},
		goGoodDetail(cart, cartInfo) {
			let path = `/pages/shop/GoodsCon?id=${cart.productInfo.id}`,
				typ = true;
			cartInfo.forEach((item, index) => {
				if (item.type === 'evaluate_product') {
					typ = false;
				}
			});
			if (!typ) {
				path = '/pages/yuanshi/evaluate/detail?wid=' + cart.wish_id + '&pid=' + cart.product_id;
			}
			this.$navigator(path);
		},
		makePhoneCall(phone) {
			uni.makePhoneCall({
				phoneNumber: phone //仅为示例
			});
		},

		goOrderRefund(path) {
			openOrderRefundSubscribe().then(() => {
				this.$navigator(path);
			});
		},
		copy(val) {
			setClipboardData(val);
		},
		paymentTap() {
			if (this.paymentType && _isWeixin) {
				// #ifndef MP-TOUTIAO
				this.toPay('weixin');
				// #endif
				// #ifdef MP-TOUTIAO
				this.toPay('bytedance');
				// #endif
			} else {
				this.pay = true;
			}
		},
		// 再次购买
		goOrderConfirm(e) {
			orderAgain(e.order_id)
				.then(res => {
					this.$navigator('/pages/order/OrderSubmission?cartId=' + res.data.cateId);
				})
				.catch(res => {
					console.log(res);
					this.$showToast(res.msg || res);
				});
		},
		closeGeneralWindow(msg) {
			this.generalActive = msg;
			this.getDetail();
		},
		showChang: function() {
			if (_isWeixin) {
				let config = {
					latitude: parseFloat(this.system_store.latitude),
					longitude: parseFloat(this.system_store.longitude),
					name: this.system_store.name,
					address: this.system_store._detailed_address
				};
				authOpenLocation();
			} else {
				if (!this.mapKey) return this.$showToast('暂无法使用查看地图，请配置您的腾讯地图key');
				this.mapShow = true;
			}
		},
		goBack() {
			const history = getCurrentPages();
			if (history.length > 1) return this.$navigator(-1);
			else return this.$navigator('/pages/order/MyOrder', 'redirectTo');
		},
		cancelOrder() {
			cancelOrderHandle(this.orderInfo.order_id)
				.then(() => {
					setTimeout(() => this.goBack(), 300);
				})
				.catch(() => {
					this.getDetail();
				});
		},
		takeOrder() {
			this.$loadingToast('正在加载中');
			takeOrderHandle(this.orderInfo.order_id)
				.then(res => {
					if ((res.data.gain_integral != '0.00' && res.data.gain_coupon != '0.00') || (res.data.gain_integral > 0 && res.data.gain_coupon > 0)) {
						this.$hideLoading();
						this.generalActive = true;
						this.generalContent = {
							promoterNum: `恭喜您获得${res.data.gain_coupon}元优惠券以及${res.data.gain_integral}积分，购买商品时可抵现哦～`,
							title: '恭喜您获得优惠礼包'
						};
						return;
					} else if (res.data.gain_integral != '0.00' || res.data.gain_integral > 0) {
						this.$hideLoading();
						this.generalActive = true;
						this.generalContent = {
							promoterNum: `恭喜您获得${res.data.gain_integral}积分，购买商品时可抵现哦～`,
							title: '赠送积分'
						};
						return;
					} else if (res.data.gain_coupon != '0.00' || res.data.gain_coupon > 0) {
						this.$hideLoading();
						this.generalActive = true;
						this.generalContent = {
							promoterNum: `恭喜您获得${res.data.gain_coupon}元优惠券，购买商品时可抵现哦～`,
							title: '恭喜您获得优惠券'
						};
						return;
					} else {
						this.$hideLoading();
						this.$successToast('收货成功');
					}
					this.getDetail();
				})
				.catch(res => {
					this.$hideLoading();
					this.$showToast(res.msg || res);
				});
		},
		delOrder() {
			delOrderHandle(this.orderInfo.order_id).then(() => {
				setTimeout(() => this.goBack(), 300);
			});
		},
		setOfflinePayStatus: function(status) {
			var that = this;
			that.offlinePayStatus = status;
			if (status === 1 && that.orderTypeNameStatus === true) {
				that.payType.push('offline');
			}
		},
		getOrderStatus: function() {
			let orderInfo = this.orderInfo || {},
				_status = orderInfo._status || { _type: 0 },
				status = {};
			let type = parseInt(_status._type),
				delivery_type = orderInfo.delivery_type,
				seckill_id = orderInfo.seckill_id ? parseInt(orderInfo.seckill_id) : 0,
				bargain_id = orderInfo.bargain_id ? parseInt(orderInfo.bargain_id) : 0,
				combination_id = orderInfo.combination_id ? parseInt(orderInfo.combination_id) : 0;
			status = {
				type: type,
				class_status: 0
			};
			if (type === 1 && combination_id > 0) {
				status.type = 6;
				status.class_status = 1;
			} //查看拼团
			if (type === 2 && delivery_type === 'express') status.class_status = 2; //查看物流
			if (type === 2) status.class_status = 3; //确认收货
			if (type === 4 || type === 0) status.class_status = 4; //删除订单
			if (!seckill_id && !bargain_id && !combination_id && (type === 3 || type === 4)) status.class_status = 5; //再次购买
			if (type == 9) {
				//线下付款
				status.class_status = 0;
				this.offlineStatus = false;
			}
			this.status = status;
		},
		getDetail() {
			const id = this.id;
			if (!id) return this.$showToast('订单不存在');
			orderDetail(id)
				.then(res => {
					this.orderInfo = res.data;
					this.getOrderStatus();
					if (this.orderInfo.combination_id > 0) {
						this.orderTypeName = '拼团订单';
						this.orderTypeNameStatus = false;
					} else if (this.orderInfo.bargain_id > 0) {
						this.orderTypeName = '砍价订单';
						this.orderTypeNameStatus = false;
					} else if (this.orderInfo.seckill_id > 0) {
						this.orderTypeName = '秒杀订单';
						this.orderTypeNameStatus = false;
					}
					this.system_store = res.data.system_store || {};
					this.mapKey = res.data.mapKey;
					this.setOfflinePayStatus(this.orderInfo.offlinePayStatus);
				})
				.catch(err => {
					this.$showToast(err.msg || err);
					this.$router.go(-1);
				});
		},
		async toPay(type) {
			var that = this;
			payOrderHandle(this.orderInfo.order_id, type, that.from)
				.then(res => {
					const { status, result } = res;
					if (status === 'WECHAT_H5_PAY') {
						return that.$navigator('/pages/order/PaymentStatus?orderId=' + this.orderInfo.order_id + '&status=0');
					}
					that.getDetail();
				})
				.catch(err => {
					this.$showToast(err.msg || err);
				});
		}
	}
};
</script>
<style scoped lang="scss">
.goodCall {
	color: #e93323;
	text-align: center;
	width: 100%;
	height: 86rpx;
	padding: 0 30rpx;
	border-bottom: 1rpx solid #eee;
	font-size: 30rpx;
	line-height: 86rpx;
	background: #fff;
}

.geoPage {
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
	z-index: 10000;
}
.order_details {
	min-height: 100vh;
	padding: 30rpx 22rpx;
	.writeOff {
		.btn_hx {
			width: 670rpx;
			height: 100rpx;
			line-height: 100rpx;
			background: #ff5656;
			border-radius: 40rpx;
			text-align: center;
			margin: 0 auto 40rpx auto;

			font-size: 28rpx;
			color: #ffffff;
		}
		.wrap {
			background-color: #fff;
			border: 6rpx solid #ffffff;
			border-radius: 30rpx;
			overflow: hidden;
			min-height: 800rpx;
			.title {
				font-size: 28rpx;
				color: #ffffff;
				height: 170rpx;
				padding: 44rpx 60rpx;
				background: #fc5656;
				&.dpj {
					background: #f2f5f8;

					color: #50506f;
				}
				.txt {
					padding: 2rpx 0;
				}
				.image {
					image {
						width: 72rpx;
						height: 72rpx;
					}
				}
			}
			.grayBg {
				margin: 30rpx 0;
				.pictrue {
					width: 462rpx;
					height: 462rpx;
					margin: 0 auto;
					image {
						width: 100%;
						height: 100%;
						display: block;
					}
				}
			}
			.num {
				background: #f2f5f8;
				height: 170rpx;
				line-height: 170rpx;

				font-weight: 500;

				color: #666666;
				font-size: 52rpx;
				text-align: center;
			}
		}
		.rules {
			box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
			border-radius: 30rpx;
			background: #ffffff;
			margin: 40rpx 0 30rpx 0rpx;
			padding: 50rpx 40rpx;
			.item {
				// margin-top: 15rpx;
				&:not(:last-child) {
					margin-bottom: 30rpx;
				}
				.title {
					font-size: 28rpx;
					font-weight: bold;
					color: #333333;
				}
				.info {
					margin-top: 10rpx;
					font-size: 24rpx;
					color: #666666;
					font-weight: 400;
				}
			}
		}
	}
	.map {
		height: 98rpx;
		font-size: 28rpx;

		font-weight: bold;

		color: #333333;
		line-height: 98rpx;
		background: #f2f5f8;
		border: 4rpx solid #ffffff;
		margin-top: 13rpx;
		border-radius: 30rpx 30rpx 0px 0px;
		padding: 0 40rpx;

		.place {
			image {
				width: 27rpx;
			}
		}
	}
	.content_wrap {
		background: #fff;
		padding: 0 40rpx 50rpx 40rpx;
		border-radius: 0 0 30rpx 30rpx;
		box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
		&.radius {
			border-radius: 30rpx;
		}
		.address {
			color: #333333;
			font-weight: 400;
			font-size: 24rpx;
			padding: 30rpx 0rpx 40rpx 0rpx;
			.name {
				color: #333333;
				font-weight: bold;
				font-size: 32rpx;
				.image {
					image {
						width: 27rpx;
					}
				}
			}
			.desc {
				margin: 14rpx 0 30rpx 0;
			}
			.phone {
				image {
					width: 28rpx;
				}
			}
		}
		.order_goods {
			.wrap {
				padding: 40rpx 0 32rpx 0;
				border-top: 2rpx solid #eaeaea;
				border-bottom: 2rpx solid #eaeaea;
				.item {
					.pictrue {
						width: 134rpx;
						// height: 113rpx;
                        // max-height: 178rpx;
                        
						image {
							width: 100%;
							height: 100%;
							border-radius: 30rpx;
						}
					}
					.text {
						width: calc(100% - 134rpx);
						padding-left: 20rpx;
						font-size: 24rpx;
						color: #333333;
						.name {
							font-size: 32rpx;
							font-weight: bold;
							color: #333333;
						}
						.desc {
							margin-top: 14rpx;
							font-size: 24rpx;
							height: 72rpx;

							color: #666666;
						}
						.money {
							font-weight: 500;
						}
					}
				}
			}
			.detail {
				padding: 40rpx 0;
				border-bottom: 2rpx solid #eaeaea;
				.item {
					color: #333333;
					font-size: 24rpx;
					&.item ~ .item {
						margin-top: 30rpx;
					}
				}
			}
		}
		.wrapper {
			padding: 40rpx 0;
			// border-bottom: 2rpx solid #eaeaea;
			.item {
				color: #333333;
				font-size: 24rpx;
				&.item ~ .item {
					margin-top: 30rpx;
				}
				&.actualPay {
					color: #333333;
					font-weight: 500;
					font-size: 28rpx;
				}
			}
		}
		.footer {
			padding: 0 58rpx;
			margin-top: 32rpx;

			border-radius: 8rpx 8rpx 0px 0px;
			box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
			background: #ffffff;
			@include fixed_footer(128rpx);
			.btn {
				width: 152rpx;
				height: 60rpx;
				line-height: 60rpx;
				text-align: center;
				background: #ff5656;
				font-size: 24rpx;
				color: #ffffff;
				border-radius: 24rpx;
				&.default {
					color: #50506f;
					background: #eaeaea;
				}
			}
			& .btn ~ .btn {
				margin-left: 8rpx;
			}
		}
	}
}
.evaluate {
	width: 110rpx;
	height: 40rpx;
	text-align: center;
	line-height: 40rpx;
	color: #fff;
	border-radius: 24rpx;
	font-size: 24rpx;
	background: #ff5656;
	margin-top: 10rpx;
}
</style>
