<template>
	<view class="return-list">
		<view class="goodWrapper" v-for="order in orderList" :key="order.order_id">
			<view class="iconfont icon-tuikuanzhong powder" v-if="order._status._type === -1"></view>
			<view class="iconfont icon-yituikuan" v-if="order._status._type === -2"></view>
			<view class="orderNum">订单号：{{ order.order_id }}</view>
			<view class="item acea-row row-between-wrapper" v-for="cart in order.cartInfo" :key="cart.id" @click="goPages(order)">
				<view class="pictrue"><image :src="cart.productInfo.image" class="image" @click.stop="goGoodDetail(cart,order)" /></view>
				<view class="text">
					<view class="acea-row row-between-wrapper">
						<view class="name line1">{{ cart.productInfo.store_name }}</view>
						<view class="num">x {{ cart.cart_num }}</view>
					</view>
					<view class="attr line1" v-if="cart.productInfo.attrInfo">{{ cart.productInfo.attrInfo.suk }}</view>
					<view class="attr line1" v-else>{{ cart.productInfo.store_name }}</view>
					<view class="money">￥{{ cart.productInfo.price }}</view>
				</view>
			</view>
			<view class="totalSum">
				共{{ order.cartInfo.length || 0 }}件商品，总金额
				<span class="font-color-red price">￥{{ order.pay_price }}</span>
			</view>
		</view>
		<xNodate :arr="orderList" :page="page" :isR="false" imgSrc="/wximage/noOrder.png"></xNodate>
		<x-home></x-home>
		<Loading :loaded="loaded" :loading="loading"></Loading>
	</view>
</template>

<script>
	import OrderGoods from '@/components/OrderGoods';
import { getOrderList } from '@/api/order';
import Loading from '@/components/Loading';
import xNodate from '@/components/x-nodata/x-nodata.vue';
export default {
	name: 'ReturnList',
	components: {
		Loading,
		OrderGoods,
		xNodate
	},
	data() {
		return {
			orderList: [],
			page: 1,
			limit: 20,
			loading: false,
			loaded: false
		};
	},
	methods: {
		goGoodDetail(cart,order){
			const {cartInfo} = order;
			let path =`/pages/shop/GoodsCon?id=${cart.productInfo.id}`,typ = true;
			cartInfo.forEach((item, index) => {
				if (item.type === 'evaluate_product') {
					typ = false;
				}
			});			
			if(!typ){
				path = '/pages/yuanshi/evaluate/detail?wid=' + cart.wish_id + '&pid=' + cart.productInfo.id;
			}
			if(order.order_type){
				path = '/pages/ycommunity/shop/detail?id=' + order.activityInfo.id;
			}
			this.$navigator(path);
		},
		goPages(order) {
			
			let path = `/pages/order/OrderDetails?order_id=${order.order_id}`;
			if(order.order_type){
				path = `/pages/ycommunity/order/detail?order_id=${order.order_id}`
			}
			this.$navigator(path);
		},
		getOrderList() {
			const { page, limit } = this;
			if (this.loading || this.loaded) return;
			this.loading = true;
			getOrderList({
				page,
				limit,
				type: -3
			}).then(res => {
				this.orderList = this.orderList.concat(res.data);
				this.loading = false;
				this.loaded = res.data.length < limit;
				this.page++;
			});
		}
	},
	onLoad(options) {
		this.getOrderList();
	},
	onReachBottom() {
		this.getOrderList();
	}
};
</script>

<style scoped>
.return-list .goodWrapper {
	background-color: #fff;
	margin-bottom: 13rpx;
	position: relative;
}

.return-list .goodWrapper .orderNum {
	padding: 0 30rpx;
	border-bottom: 1px solid #eee;
	height: 87rpx;
	line-height: 87rpx;
	font-size: 30rpx;
	color: #282828;
}

.return-list .goodWrapper .item {
	border-bottom: 0;
}

.return-list .goodWrapper .totalSum {
	padding: 0 30rpx 32rpx 30rpx;
	text-align: right;
	font-size: 26rpx;
	color: #282828;
}

.return-list .goodWrapper .totalSum .price {
	font-size: 28rpx;
	font-weight: bold;
}

.return-list .goodWrapper .iconfont {
	position: absolute;
	font-size: 109rpx;
	top: 7rpx;
	right: 30rpx;
	color: #ccc;
	width: 109rpx;
	height: 109rpx;
	line-height: 109rpx;
}

.return-list .goodWrapper .iconfont.powder {
	color: #f8c1bd;
}

.goodWrapper .item {
	margin-left: 30rpx;
	padding-right: 30rpx;
	border-bottom: 2rpx solid #f0f0f0;
	height: 180rpx;
}

.goodWrapper .item .pictrue {
	width: 130rpx;
	height: 130rpx;
}

.goodWrapper .item .pictrue .image {
	width: 100%;
	height: 100%;
	border-radius: 6rpx;
}

.goodWrapper .item .text {
	width: 537rpx;
	position: relative;
}

.goodWrapper .item .text .name {
	font-size: 28rpx;
	color: #282828;
	width: 453rpx;
}

.goodWrapper .item .text .num {
	font-size: 26rpx;
	color: #868686;
}

.goodWrapper .item .text .attr {
	font-size: 20rpx;
	color: #868686;
	margin-top: 7rpx;
}

.goodWrapper .item .text .money {
	font-size: 26rpx;
	margin-top: 17rpx;
}

.goodWrapper .item .text .evaluate {
	position: absolute;
	width: 113rpx;
	height: 46rpx;
	border: 1px solid #e93323;
	color: #e93323;
	border-radius: 4rpx;
	text-align: center;
	line-height: 46rpx;
	right: 0;
	bottom: -10rpx;
}

.goodWrapper .item .text .evaluate.userEvaluated {
	font-size: 26rpx;
	color: #aaa;
	background-color: #f7f7f7;
	border-color: #f7f7f7;
}
</style>
