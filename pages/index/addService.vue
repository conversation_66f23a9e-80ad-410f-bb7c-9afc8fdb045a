<template >
	<view class="content">
	    <view class="content-box">
	        <view class="content-top">
	            <image class="content-top-logo" :src="pageData.logoUrl" mode=""></image>
	            <view class="content-top-title">
	                <view class="title" v-if="pageData.site_wechat">
	                    {{pageData.site_wechat?pageData.site_wechat:''}}
	                </view>
	                <view class="details" :class="!pageData.site_wechat?'addheight':''">
	                    {{pageData.add_kf_explain?pageData.add_kf_explain:''}}
	                </view>
	            </view>
	        </view>
	        <image v-if="pageData.site_wechat_qrcode" class="content-cen" :src="pageData.site_wechat_qrcode" mode="widthFix" show-menu-by-longpress="true"></image>
	    </view>
	</view>
</template>

<script>
import {
        yIndex,
    } from '@/api/yuanshi/public';


export default {
	
	data() {
		return {
			pageData:{}
		};
	},
    onLoad() {
        this.initData()
    },
	methods: {
      initData() {
          let that = this;
          let from = '';
          // #ifndef MP-TOUTIAO
          from = 'routine';
          // #endif
          // #ifdef MP-TOUTIAO
          from = 'bytedance';
          // #endif
          yIndex(from).then(res => {
              that.pageData = res.data;
              console.log('请求信息',this.pageData)
              uni.setNavigationBarTitle({
              	title: that.pageData.site_top_title
              });
          });
          
      },  
	}
};
</script>
<style >
    page{
        padding-bottom: 0 !important;
    }
    .content {
      width: 100%;
      min-height: 100vh;
      padding: 30rpx 30rpx 0;
      background: #f2f3f8;
      box-sizing: border-box;
    }
    .content .content-box {
      width: 100%;
      padding: 0;
      margin: 0;
      overflow: auto;
    }
    .content .content-box .add-button {
      width: 280rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      background-color: #0091CC;
      border-radius: 10rpx;
      margin: 100rpx auto;
      color: #ffffff;
      font-size: 28rpx;
    }
    .content .content-box .content-cen {
      width: 100%;
      display: inline-block;
      margin: 0 auto;
    }
    .content .content-box .content-top {
      width: 100%;
      overflow: auto;
      min-height: 118rpx;
      padding: 30rpx;
      box-sizing: border-box;
      background: #f6f7fb;
    }
    .content .content-box .content-top .content-top-logo {
      float: left;
      width: 242rpx;
      height: 80rpx;
    }
    .content .content-box .content-top .content-top-title {
      float: left;
      width: 388rpx;
      padding-left: 26rpx;
      box-sizing: border-box;
    }
    .content .content-box .content-top .content-top-title .title {
      width: 100%;
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
    }
    .content .content-box .content-top .content-top-title .details {
      width: 100%;
      font-size: 26rpx;
      color: #666666;
      margin-top: 10rpx;
    }
    .content .content-box .content-top .content-top-title .details.addheight {
      margin-top: 40rpx;
    }
    
</style>
