<template>
	<view class="user">
		<view class="header bg-color-red acea-row row-between-wrapper">
			<view class="picTxt acea-row row-between-wrapper">
				<view class="pictrue"><image :src="userInfo.avatar" /></view>
				<view class="text">
					<view class="acea-row row-middle">
						<view class="name line1">{{ userInfo.nickname }}</view>
						<view class="member acea-row row-middle" v-if="userInfo.vip">
							<image v-if="userInfo.vip_icon" :src="userInfo.vip_icon" />
							{{ userInfo.vip_name }}
						</view>
					</view>
					<view @click="goPages('/pages/user/PersonalData')" class="id" v-if="userInfo.phone">
						ID：{{ userInfo.uid || 0 }}
						<text class="iconfont icon-bianji1"></text>
					</view>
					<view @click="goPages('/pages/user/BindingPhone')" class="binding" v-else><text>绑定手机号</text></view>
				</view>
			</view>
			<text class="iconfont icon-shezhi" @click="goSetting"></text>
		</view>
		<view class="wrapper">
			<view class="nav acea-row row-middle">
				<view @click="goPages('/pages/user/UserAccount')" class="item">
					<view>我的余额</view>
					<view class="num">{{ userInfo.now_money || 0 }}</view>
				</view>
				<view @click="goPages('/pages/user/promotion/UserPromotion')" class="item" v-if="userInfo.is_promoter === 1 || userInfo.statu === 2">
					<view>当前佣金</view>
					<view class="num">{{ userInfo.brokerage_price || 0 }}</view>
				</view>

				<view @click="goPages('/pages/user/signIn/Integral')" class="item" v-else>
					<view>当前积分</view>
					<view class="num">{{ userInfo.integral || 0 }}</view>
				</view>
				<view @click="goPages('/pages/user/coupon/UserCoupon')" class="item">
					<view>优惠券</view>
					<view class="num">{{ userInfo.couponCount || 0 }}</view>
				</view>
			</view>
			<view class="myOrder">
				<view class="title acea-row row-between-wrapper">
					<view>我的订单</view>
					<view @click="goPages('/pages/order/MyOrder')" class="allOrder">
						全部订单
						<text class="iconfont icon-jiantou"></text>
					</view>
				</view>
				<view class="orderState acea-row row-middle">
					<view @click="goPages('/pages/order/MyOrder?type=0')" class="item">
						<view class="pictrue">
							<image src="@/static/images/dfk.png" />
							<text class="order-status-num" v-if="orderStatusNum.unpaid_count > 0">{{ orderStatusNum.unpaid_count }}</text>
						</view>
						<view>待付款</view>
					</view>
					<view @click="goPages('/pages/order/MyOrder?type=1')" class="item">
						<view class="pictrue">
							<image src="@/static/images/dfh.png" />
							<text class="order-status-num" v-if="orderStatusNum.unshipped_count > 0">{{ orderStatusNum.unshipped_count }}</text>
						</view>
						<view>待发货</view>
					</view>
					<view @click="goPages('/pages/order/MyOrder?type=2')" class="item">
						<view class="pictrue">
							<image src="@/static/images/dsh.png" />
							<text class="order-status-num" v-if="orderStatusNum.received_count > 0">{{ orderStatusNum.received_count }}</text>
						</view>
						<view>待收货</view>
					</view>
					<view @click="goPages('/pages/order/MyOrder?type=3')" class="item">
						<view class="pictrue">
							<image src="@/static/images/dpj.png" />
							<text class="order-status-num" v-if="orderStatusNum.evaluated_count > 0">{{ orderStatusNum.evaluated_count }}</text>
						</view>
						<view>待评价</view>
					</view>
					<view @click="goPages('/pages/order/ReturnList')" class="item">
						<view class="pictrue">
							<image src="@/static/images/sh.png" />
							<text class="order-status-num" v-if="orderStatusNum.refund_count > 0">{{ orderStatusNum.refund_count }}</text>
						</view>
						<view>售后/退款</view>
					</view>
				</view>
			</view>
	<!-- 		<view class="myYuanshi">
				<view class="title acea-row row-middle">我的着调儿</view>
				<view class="yuanshi">
					<view class="acea-row">
						<view class="item" @click="goPages('/pages/user/chat/ChatList')">
							<view class="image"></view>
							<view>聊天</view>
						</view>
						<view class="item" @click="goPages('/pages/user/yuanshi/evaluate')">
							<view class="image"></view>
							<view>我的测评</view>
						</view>
						<view class="item" @click="goPages('/pages/user/yuanshi/kanban')">
							<view class="image"></view>
							<view>我的看板</view>
						</view>
						<view class="item" @click="goPages('/pages/user/yuanshi/active')"> 
							<view class="image"></view>
							<view>我的活动</view>
						</view>
						<view class="item" @click="goPages('/pages/user/yuanshi/welfare')">
							<view class="image"></view>
							<view>我的福利</view>
						</view>
					</view>
				</view>
				<view class="show">测评完成：3次 参与活动：3次 测评跳票：0次 福利领取：1次</view>
			</view> -->
			<view class="myService">
				<view class="title acea-row row-middle">我的服务</view>
				<view class="serviceList acea-row row-middle">
					<template v-for="(item, index) in MyMenus">
						<view class="item" :key="index" @click="goPages(item.wap_url)" v-if="item.wap_url">
							<view class="pictrue"><image :src="item.pic" /></view>
							<view>{{ item.name }}</view>
						</view>
					</template>
				</view>
			</view>
		</view>
		<view class="support" style="width:100%">
			<view style="text-align:center;color:#3B66A2;font-weight:bold;font-size:26rpx;">北京源未文化发展有限公司</view>
			<view style="text-align:center;color:#b1b1b1;font-size:22rpx;padding:10px 0;">版权所有 © 2019 https://www.arthorize.com</view>
		</view>
		<view class="footer-line-height"></view>
		<!-- <SwitchWindow v-on:changeswitch="changeswitch" :switchActive="switchActive" :login_type="userInfo.login_type"></SwitchWindow> -->
		<GeneralWindow :generalActive="generalActive" @closeGeneralWindow="closeGeneralWindow" :generalContent="generalContent"></GeneralWindow>
		<x-authorize :isHidden="true" @login="updateData"></x-authorize>
	</view>
</template>

<script>
import { getUser, getMenuUser } from '@/api/user';

import { uniGetLocation } from '@/utils/uni_api.js';
import { checkLogin } from '@/utils/common.js';
// import { isWeixin } from '@/utils/validate.js';
// import SwitchWindow from '@/components/SwitchWindow';
import GeneralWindow from '@/components/GeneralWindow';
const NAME = 'User';

export default {
	name: NAME,
	components: {
		// SwitchWindow,
		GeneralWindow
	},
	props: {},
	data: function() {
		return {
			userInfo: {},
			MyMenus: [],
			orderStatusNum: {},
			switchActive: false,
			generalActive: false,
			generalContent: {
				promoterNum: '',
				title: '您未获得推广权限'
			}
		};
	},
	watch: {
		// #ifdef H5
		$route(n) {
			if (n.name === NAME) {
				this.updateData();
			}
		},
		// #endif
		'$store.getters.userInfo': function(newVal, oldVal) {
			this.userInfo = newVal;
		}
	},
	mounted: function() {},
	onLoad() {
		this.$setNavigationBarColor('#e93323');
	},
	onShow() {
		// #ifdef H5
		this.updateData();
		// #endif
		// #ifdef MP-WEIXIN
		// 微信不允许用户不操作就进行授权操作
		if (checkLogin()) {
			this.updateData();
		}
		// #endif
	},
	methods: {
		updateData() {
			this.User();
			this.MenuUser();
		},
		goPages(path, type) {
			if (path === '/user/user_promotion' || path === '/pages/user/promotion/UserPromotion') {
				if (!this.userInfo.is_promoter && this.userInfo.statu == 1) return this.$showToast('您还没有推广权限！！');
				if (!this.userInfo.is_promoter && this.userInfo.statu == 2) {
					return (this.generalActive = true);
				}
			}
			if (path === '/customer/index' && !this.userInfo.adminid) {
				return this.$showToast('您还不是客服！！');
			}
			this.$authNavigator(path, type);
		},
		goSetting() {
			// #ifdef H5
			this.$authNavigator('/pages/user/PersonalData');
			// #endif
			// #ifdef MP
			uni.openSetting({
				success: function(res) {
					console.log(res.authSetting);
					uniGetLocation();
				}
			});
			// #endif
		},
		changeswitch: function(data) {
			this.switchActive = data;
		},
		User: function() {
			let that = this;
			getUser().then(res => {
				that.userInfo = res.data;
				that.$store.commit('UPDATE_USERINFO', res.data);
				that.orderStatusNum = res.data.orderStatusNum;
				this.generalContent = {
					promoterNum: `您在商城累计消费金额仅差 <text style="color: #ff8500;">${res.data.promoter_price || 0}元</text>即可开通推广权限`,
					title: '您未获得推广权限'
				};
			});
		},
		MenuUser: function() {
			let that = this;
			getMenuUser().then(res => {
				that.MyMenus = res.data.routine_my_menus;
			});
		},
		closeGeneralWindow(msg) {
			this.generalActive = msg;
		}
	}
};
</script>
<style lang="scss">
page {
	background-color: $uni-bg-color-page;
}
</style>
<style scoped lang="scss">
.footer-line-height {
	height: 100rpx;
}
.order-status-num {
	width: 35rpx;
	height: 35rpx;
	text-align: center;
	line-height: 35rpx;
	background-color: #fff;
	color: #ee5a52;
	border-radius: 15px;
	position: absolute;
	right: -14rpx;
	top: -15rpx;
	font-size: 20rpx;
	border: 1px solid #ee5a52;
}

.pictrue {
	position: relative;
}
.switch-h5 {
	margin-left: 20rpx;
}
.binding {
	padding: 5rpx 20rpx;
	background-color: #ca1f10;
	border-radius: 50px;
	font-size: 14rpx;
	border: 1px solid #e8695e;
	color: #ffffff;
}
.user .header {
	padding: 0 30rpx;
	height: 190rpx;
	position: relative;
}

.user .header:after {
	position: absolute;
	left: 0;
	right: 0;
	bottom: -98rpx;
	z-index: -1;
	content: '';
	height: 100rpx;
	width: 100%;
	border-radius: 0 0 50% 50%;
	background-color: #e93323;
}

.user .header .picTxt .pictrue {
	width: 120rpx;
	height: 120rpx;
}

.user .header .picTxt .pictrue image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	border: 3rpx solid #f5f5f5;
}

.user .header .picTxt .text {
	width: 434rpx;
	color: rgba(255, 255, 255, 1);
	margin-left: 35rpx;
	text-align: left;
}

.user .header .picTxt .text .name {
	font-size: 32rpx;
	max-width: 260rpx;
	width: unset;
}

.user .header .picTxt .text .member {
	padding: 0 10rpx;
	height: 36rpx;
	background-color: rgba(0, 0, 0, 0.2);
	font-size: 20rpx;
	border-radius: 30rpx;
	margin-left: 17rpx;
}

.user .header .picTxt .text .member image {
	width: 28rpx;
	height: 28rpx;
	font-size: 20rpx;
	margin-right: 8rpx;
}

.user .header .picTxt .text .id {
	color: rgba(255, 255, 255, 0.6);
	font-size: 26rpx;
	margin-top: 15rpx;
}

.user .header .picTxt .text .id .iconfont {
	font-size: 30rpx;
	margin-left: 12rpx;
}

.user .header .icon-shezhi {
	font-size: 36rpx;
	color: #fff;
	margin-top: -52rpx;
}

.user .wrapper {
	padding: 0 20rpx;
}

.user .wrapper .nav {
	background-color: #fff;
	border-radius: 6rpx;
	height: 140rpx;
}

.user .wrapper .nav .item {
	flex: 1;
	-webkit-flex: 1;
	-o-flex: 1;
	-ms-flex: 1;
	text-align: center;
	font-size: 26rpx;
	color: #aaa;
	position: relative;
}

.user .wrapper .nav .item ~ .item:after {
	position: absolute;
	content: '';
	left: 0;
	width: 1px;
	height: 70rpx;
	background-color: #eee;
	top: 50%;
	margin-top: -35rpx;
}

.user .wrapper .nav .item .num {
	margin-top: 10rpx;
	font-size: 36rpx;
	color: #282828;
}

.user .wrapper .myOrder {
	background-color: #fff;
	border-radius: 10rpx;
	margin-top: 15rpx;
}

.user .wrapper .myOrder .title,
.user .wrapper .myService .title {
	height: 88rpx;
	padding: 0 30rpx;
	border-bottom: 1px dashed #eee;
	font-size: 30rpx;
	color: #282828;
}

.user .wrapper .myOrder .title .allOrder {
	font-size: 26rpx;
	color: #666;
}

.user .wrapper .myOrder .title .allOrder .iconfont {
	font-size: 25rpx;
	margin-left: 7rpx;
}

.user .wrapper .myOrder .orderState {
	height: 160rpx;
}

.user .wrapper .myOrder .orderState .item {
	font-size: 26rpx;
	color: #454545;
	flex: 1;
	-webkit-flex: 1;
	-o-flex: 1;
	-ms-flex: 1;
	text-align: center;
}

.user .wrapper .myOrder .orderState .item .pictrue {
	width: 49rpx;
	height: 42rpx;
	margin: 0 auto 18rpx auto;
}

.user .wrapper .myOrder .orderState .item .pictrue image {
	width: 100%;
	height: 100%;
}

.user .wrapper .myService {
	background-color: #fff;
	margin-top: 15rpx;
	border-radius: 10rpx;
}

.user .wrapper .myService .serviceList {
	padding: 8rpx 0 27rpx 0;
}

.user .wrapper .myService .serviceList .item {
	width: 25%;
	text-align: center;
	font-size: 26rpx;
	color: #333;
	margin-top: 30rpx;
}

.user .wrapper .myService .serviceList .item .pictrue {
	width: 52rpx;
	height: 52rpx;
	margin: 0 auto 16rpx auto;
}

.user .wrapper .myService .serviceList .item .pictrue image {
	width: 100%;
	height: 100%;
}

.user .support {
	width: 219rpx;
	height: 74rpx;
	margin: 54rpx auto;
	display: block;
}


.myYuanshi{
	background-color: #fff;
	margin-top: 15rpx;
	border-radius: 10rpx;
	.title {
		height: 88rpx;
		padding: 0 30rpx;
		border-bottom: 1px dashed #eee;
		font-size: 30rpx;
		color: #282828;
	}
	.item {
		width: 25%;
		text-align: center;
		font-size: 26rpx;
		color: #333;
		margin: 30rpx 0 16rpx 0;
		.image{
			width:150rpx;
			height: 150rpx;
			margin: 0 auto 16rpx auto;
			border:1px solid red;
		}
	}
	.show{
			padding: 0 30rpx;
			font-size: 24rpx;
	}
}
</style>
