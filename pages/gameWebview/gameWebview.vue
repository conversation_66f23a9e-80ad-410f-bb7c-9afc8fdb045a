<template>
    <view>
        <view class="mp-btn" v-if="urlType =='mp'">
            <!-- #ifdef MP -->
            <view class="txt">
                小程序端不支持URL Scheme
            </view>
            <!-- #endif -->
            <!-- #ifdef H5 -->
            <view class="" v-if="isWx">
                <wx-open-launch-weapp id="launch-btn" username="gh_e17469a9d205"
                    path="/pages/webview/webview?type=weapp">
                    <script type="text/wxtag-template">
                        <style>.btn { padding: 12px 24px; font-size: 17px;}</style>
    				    <button class="btn">打开小程序</button>
    				  </script>
                </wx-open-launch-weapp>
            </view>
            <view class="" v-else>
                <view class="txt">正在跳转小程序</view>
                <button type="primary" @click="openMp" class="btn">打开小程序</button>
            </view>
            <!-- #endif -->
        </view>
        <view class="" v-else>
            <web-view :webview-styles="webviewStyles" :src="url" @message="message" @load="load"
                @onPostMessage="onPostMessage"></web-view>
        </view>
    </view>
</template>

<script>
    import {
        regHref,
        isWeixin
    } from '@/utils/validate.js'; 
    import {
        reportWebView
    } from '@/utils/ReportAnalytics.js';
    import {
        getWechatConfig
    } from '@/api/public';
    import {
        SHARE_ID
    } from '@/config.js';
    export default {
        data() {
            return {
                url: '',
                urlType: '',
                // #ifdef H5
                isWx: isWeixin(),
                // #endif
                webviewStyles: {
                    progress: {
                        color: '#FF3333'
                    }
                },
                // #ifdef H5
                isWeixin: isWeixin(),
                // #endif
            }
        },
        onLoad(option) {
            // #ifdef H5
             this.isWeixin = isWeixin();
             // #endif
             
            // #ifdef MP
             this.isWeixin = true;
             // #endif
             console.log('this。isWeixin', this.isWeixin);
             
            console.log('option', option)
            const {
                type = 'h5',
                token = '',
                    url
            } = option

            this.urlType = type;

            // #ifdef H5
            if (url.indexOf('weixin://') > -1 && this.isWx) {
                getWechatConfig()
                console.log('微信浏览器', url)
            } else {
                location.replace(url);
            }
            return;
            // #endif

            // #ifdef MP
            if (type === 'weapp') {
                this.$navigator('/pages/tabBar/index/index', 'switchTab');
                return;
            }
            // #endif


            if (type === 'mp') {
                // #ifdef MP
                setTimeout(() => {
                    this.$showToast('小程序端不支持URL Scheme');
                }, 500)
                console.log('仅h5生效')
                setTimeout(() => {
                    this.$navigator(1)
                }, 2000)
                // #endif
            } else {
                console.log('regHref(option.url)', regHref(option.url))
                if (regHref(option.url)) {
                    reportWebView({
                        link: option.url
                    });
                    this.url = option.url + '?token=' +option.token;
                    console.log('this.url--',this.url)
                } else {
                    let setTimer = setTimeout(() => {
                        this.$showToast('地址不存在');
                        this.$navigator(-1);
                        clearTimeout(setTimer);
                    }, 1000);
                }
            }
        },
        // #ifdef MP
        onShareAppMessage() {
            return {
                title: '着调儿',
                path: `/pages/gameWebview/gameWebview?url=${this.url}&type=${this.urlType}`,
                templateId: SHARE_ID,
            };
        },
        // #endif
        methods:{
            load(e) {
                console.log('load', e)
            },
            message(e) {
                console.log('message', e)
            },
            onPostMessage(e) {
                console.log('onPostMessage', e)
            },
            goPages(path, type) {
                this.$navigator(path, type);
            },
            openMp() {
                location.href = this.url
            }
        }
    }
</script>

<style scoped lang="scss">
    .mp-btn {
        width: 360rpx;
        padding-top: 60%;
        margin: 0 auto;
        text-align: center;

        .txt {
            padding: 60rpx 0;
            font-size: 32rpx;
        }

        .btn {
            font-size: 34rpx;
            padding: 10rpx 0;
        }

    }
</style>