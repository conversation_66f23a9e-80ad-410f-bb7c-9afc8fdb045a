<template>
	<view class="bill-details" >
		<view class="nav acea-row">
			<view class="item" :class="types == 0 ? 'on' : ''" @click="changeTypes(0)">全部</view>
			<view class="item" :class="types == 1 ? 'on' : ''" @click="changeTypes(1)">消费</view>
			<view class="item" :class="types == 2 ? 'on' : ''" @click="changeTypes(2)">充值</view>
		</view>
		<view class="sign-record">
			<view class="list">
				<view class="item" v-for="(item, index) in list" :key="index">
					<view class="data">{{ item.time }}</view>
					<view class="listn" v-for="(val, key) in item.list" :key="key">
						<view class="itemn acea-row row-between-wrapper">
							<view>
								<view class="name line1">{{ val.title }}</view>
								<view>{{ val.add_time }}</view>
							</view>
							<view class="num" :class="val.pm == 0 ? 'font-color-red' : ''">{{ val.pm == 0 ? '-' : '+' }}{{ val.number }}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<Loading :loaded="loaded" :loading="loading"></Loading>
		<x-home></x-home>
	</view>
</template>
<script>
import { getCommissionInfo } from '@/api/user';
import Loading from '@/components/Loading';
export default {
	name: 'UserBill',
	components: {
		Loading
	},
	props: {},
	data: function() {
		return {
			types: '',
			where: {
				page: 1,
				limit: 5
			},
			list: [],
			loaded: false,
			loading: false
		};
	},
	onLoad(options){
		const {type} = options;
		this.types = type;
		this.getIndex();
	},
	methods: {
		goPages(path, type) {
			this.$authNavigator(path, type);
		},
		code: function() {
			this.sendCode();
		},
		changeTypes: function(val) {
			if (val != this.types) {
				this.types = val;
				this.list = [];
				this.where.page = 1;
				this.loaded = false;
				this.loading = false;
				
				this.getIndex();
			}
		},
		getIndex: function() {
			let that = this;
			if (that.loaded == true || that.loading == true) return;
			that.loading = true;
			getCommissionInfo(that.where, that.types).then(
				res => {
					that.loading = false;
					that.loaded = res.data.length < that.where.limit;
					that.where.page = that.where.page + 1;
					that.list.push.apply(that.list, res.data);
				},
				err => {
					that.$showToast(err.msg || err);
				}
			);
		}
	},
	onReachBottom(){
		this.getIndex()
	}
};
</script>
<style scoped lang="scss">
	page{
		background-color:$uni-bg-color-page;
	}
	.bill-details .nav {
		background-color: #fff;
		height: 90rpx;
		width: 100%;
		line-height: 90rpx;
	}
	
	.bill-details .nav .item {
		flex: 1;
		-webkit-flex: 1;
		-o-flex: 1;
		-ms-flex: 1;
		text-align: center;
		font-size: 30rpx;
		color: #282828;
		height: 100%;
	}
	
	.bill-details .nav .item.on {
		color: #e93323;
		border-bottom: 4rpx solid #e93323;
	}
</style>
