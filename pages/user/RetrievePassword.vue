<template>
  <view class="register absolute">
    <view class="shading">
      <view class="pictrue acea-row row-center-wrapper">
        <image src="@/static/images/logo2.png" />
      </view>
    </view>
    <view class="whiteBg">
      <view class="title">找回密码</view>
      <view class="list">
        <view class="item">
          <view>
           <!-- <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-phone_"></use>
            </svg> -->
            <input type="text" placeholder="输入手机号码" v-model="account" />
          </view>
        </view>
        <view class="item">
          <view class="align-left">
           <!-- <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-code_1"></use>
            </svg> -->
            <input
              type="text"
              placeholder="填写验证码"
              class="codeIput"
              v-model="captcha"
            />
            <button
              class="code"
              :disabled="disabled"
              :class="disabled === true ? 'on' : ''"
              @click="code"
            >
              {{ text }}
            </button>
          </view>
        </view>
        <view class="item">
          <view>
           <!-- <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-code_"></use>
            </svg> -->
            <input
              type="password"
              placeholder="填写您的登录密码"
              v-model="password"
            />
          </view>
        </view>
        <view class="item" v-if="isShowCode">
          <view class="align-left">
         <!--   <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-code_"></use>
            </svg> -->
            <input
              type="text"
              placeholder="填写验证码"
              class="codeIput"
              v-model="codeVal"
            />
            <view class="code" @click="again"><image :src="codeUrl" /></view>
          </view>
        </view>
      </view>
      <view class="logon" @click="registerReset">确认</view>
      <view class="tip">
        <span @click="goLoginPages()" class="font-color-red"
          >立即登录</span
        >
      </view>
    </view>
    <view class="bottom"></view>
  </view>
</template>

<script>
import sendVerifyCode from "@/mixins/SendVerifyCode";
import { registerVerify, registerReset, getCodeApi } from "@/api/user";
import { RegPhone } from '@/utils/validate.js';
import { VUE_APP_API_URL } from '@/config.js';
export default {
  name: "RetrievePassword",
  data: function() {
    return {
      account: "",
      password: "",
      captcha: "",
      keyCode: "",
      codeUrl: "",
      codeVal: "",
      isShowCode: false
    };
  },
  mixins: [sendVerifyCode],
  mounted: function() {
    this.getCode();
  },
  methods: {
		goLoginPages(path,type){
			this.$navigator('/pages/login/login','redirectTo')
		},
    again() {
      this.codeUrl =
        VUE_APP_API_URL + "/captcha?" + this.keyCode + Date.parse(new Date());
    },
    getCode() {
      getCodeApi()
        .then(res => {
          this.keyCode = res.data.key;
        })
        .catch(res => {
          this.$showToast(res.msg || res);
        });
    },
    async registerReset() {
      var that = this;
      const { account, captcha, password, codeVal } = that;
			if (!RegPhone(account)) {
				return that.$showToast(account.length ? '请填写正确手机号':'请填写手机号');
			}
			if (!captcha.trim().length) {
				return that.$showToast('请输入验证码');
			}
			if (!password.trim().length) {
				return that.$showToast('请填写密码');
			}
			if (this.isShowCode && !codeVal.trim().length) {
				return that.$showToast('请填写验证码');
			}
      registerReset({
        account: that.account,
        captcha: that.captcha,
        password: that.password,
        code: that.codeVal
      })
        .then(res => {
					that.$successToast(res.msg);
					that.goLoginPages()
        })
        .catch(res => {
					console.log(res)
          that.$showToast(res.msg || res);
        });
    },
    async code() {
      var that = this;
      const { account } = that;
			if (!RegPhone(account)) {
				return that.$showToast(account.length ? '请填写正确手机号':'请填写手机号');
			}
      registerVerify({
        phone: that.account,
        key: that.keyCode,
        code: that.codeVal
      })
        .then(res => {
          that.$successToast(res.msg);
          that.sendCode();
        })
        .catch(res => {
          if (res.data.status === 402) {
            that.codeUrl = `${VUE_APP_API_URL}/sms_captcha?key=${that.keyCode}`;
            that.isShowCode = true;
          }
          that.$showToast(res.msg || res);
        });
    }
  }
};
</script>
<style scoped>
.code image {
  width: 100%;
  height: 100%;
}
</style>
