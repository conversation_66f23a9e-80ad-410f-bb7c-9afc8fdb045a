<template>
	<view class="register absolute">
		<view class="shading">
			<view class="pictrue acea-row row-center-wrapper">
				<image :src="logoUrl" mode="widthFix" v-if="logoUrl" />
				<image src="@/static/images/logo2.png" mode="widthFix" v-else/>
			</view>
		</view>
		<view class="whiteBg" v-if="formItem === 1">
			<view class="title acea-row row-center-wrapper">
				<view class="item" :class="current === index ? 'on' : ''" v-for="(item, index) in navList" @click="navTap(index)" :key="index">{{ item }}</view>
			</view>
			<view class="list" :hidden="current !== 0">
				<form @submit.prevent="submit">
					<view class="item">
						<view class="acea-row row-between-wrapper">
							<!-- <svg class="icon" aria-hidden="true"><use xlink:href="#icon-phone_"></use></svg> -->
							<input type="text" placeholder="输入手机号码" v-model="account" required />
						</view>
					</view>
					<view class="item">
						<view class="acea-row row-between-wrapper">
							<!-- <svg class="icon" aria-hidden="true"><use xlink:href="#icon-code_"></use></svg> -->
							<input type="password" placeholder="填写登录密码" v-model="password" required />
						</view>
					</view>
				</form>
				<view class="forgetPwd" @click="goPages('/pages/user/RetrievePassword', 'redirectTo')">
					<span class="iconfont icon-wenti"></span>
					忘记密码
				</view>
			</view>
			<view class="list" :hidden="current !== 1">
				<view class="item">
					<view class="acea-row row-between-wrapper">
						<!-- <svg class="icon" aria-hidden="true"><use xlink:href="#icon-phone_"></use></svg> -->
						<input type="text" placeholder="输入手机号码" v-model="account" />
					</view>
				</view>
				<view class="item">
					<view class="align-left">
						<!-- <svg class="icon" aria-hidden="true"><use xlink:href="#icon-code_1"></use></svg> -->
						<input type="text" placeholder="填写验证码" class="codeIput" v-model="captcha" />
						<button class="code" :disabled="disabled" :class="disabled === true ? 'on' : ''" @click="code">{{ text }}</button>
					</view>
				</view>
				<view class="item" v-if="isShowCode">
					<view class="align-left">
						<!-- <svg class="icon" aria-hidden="true"><use xlink:href="#icon-code_"></use></svg> -->
						<input type="text" placeholder="填写验证码" class="codeIput" v-model="codeVal" />
						<view class="code" @click="again"><image :src="codeUrl" /></view>
					</view>
				</view>
				<view class="forgetPwd"><span class="iconfont "></span></view>
			</view>
			<view class="logon" @click="loginMobile" :hidden="current !== 1">登录</view>
			<view class="logon" @click="submit" :hidden="current === 1">登录</view>
			<view class="tip">
				没有账号?
				<span @click="formItem = 2" class="font-color-red">立即注册</span>
			</view>
		</view>
		<view class="whiteBg" v-else>
			<view class="title">注册账号</view>
			<view class="list">
				<view class="item">
					<view>
						<!-- <svg class="icon" aria-hidden="true"><use xlink:href="#icon-phone_"></use></svg> -->
						<input type="text" placeholder="输入手机号码" v-model="account" />
					</view>
				</view>
				<view class="item">
					<view class="align-left">
						<!-- <svg class="icon" aria-hidden="true"><use xlink:href="#icon-code_1"></use></svg> -->
						<input type="text" placeholder="填写验证码" class="codeIput" v-model="captcha" />
						<button class="code" :disabled="disabled" :class="disabled === true ? 'on' : ''" @click="code">{{ text }}</button>
					</view>
				</view>
				<view class="item">
					<view>
						<!-- <svg class="icon" aria-hidden="true"><use xlink:href="#icon-code_"></use></svg> -->
						<input type="password" placeholder="填写您的登录密码" v-model="password" />
					</view>
				</view>
				<view class="item" v-if="isShowCode">
					<view class="align-left">
						<!-- <svg class="icon" aria-hidden="true"><use xlink:href="#icon-code_"></use></svg> -->
						<input type="text" placeholder="填写验证码" class="codeIput" v-model="codeVal" />
						<view class="code" @click="again"><image :src="codeUrl" /></view>
					</view>
				</view>
			</view>
			<view class="logon" @click="register">注册</view>
			<view class="tip">
				已有账号?
				<span @click="formItem = 1" class="font-color-red">立即登录</span>
			</view>
		</view>
		<view class="bottom"></view>
	</view>
</template>
<script>
import sendVerifyCode from '@/mixins/SendVerifyCode';
import { login, loginMobile, registerVerify, register, getCodeApi } from '@/api/user';
import { getLogo } from '@/api/public';

import { VUE_APP_API_URL } from '@/config.js';
import { RegPhone } from '@/utils/validate.js';
import { toLogin, updateToken } from '@/utils/common.js';

// #ifdef H5
import { isWeixin } from '@/utils/validate.js';
const _isWeixin = isWeixin();
// #endif
// #ifdef MP
const _isWeixin = true;
// #endif
const BACK_URL = 'login_back_url';

export default {
	name: 'Login',
	mixins: [sendVerifyCode],
	data: function() {
		return {
			navList: ['账号登录', '快速登录'],
			current: 0,
			account:process.env.NODE_ENV === 'development' ? '***********':'',
			password: process.env.NODE_ENV === 'development' ? '*********':'',
			captcha: '',
			formItem: 1,
			type: 'login',
			logoUrl: '',
			keyCode: '',
			codeUrl: '',
			codeVal: '',
			isShowCode: false,
			isWeixin: _isWeixin
		};
	},
	mounted: function() {
		this.getCode();
		this.getLogoImage();
	},
	onUnload() {
		this.$store.commit("SET_GO_AUTH", false);
	},
	methods: {
		goPages(path, type) {
			this.$navigator(path, type);
		},
		again() {
			this.codeUrl = VUE_APP_API_URL + '/sms_captcha?' + 'key=' + this.keyCode + Date.parse(new Date());
		},
		getCode() {
			getCodeApi()
				.then(res => {
					this.keyCode = res.data.key;
				})
				.catch(res => {
					this.$showToast(res.msg || res);
				});
		},
		async getLogoImage() {
			let that = this;
			getLogo(2).then(res => {
				that.logoUrl = res.data.logo_url;
			});
		},
		async loginMobile() {
			var that = this;
			const { account, captcha } = that;
			if (!RegPhone(account)) {
				return that.$showToast(account.length ? '请填写正确手机号' : '请填写手机号');
			}
			if (!captcha.trim().length) {
				return that.$showToast('验证码');
			}
			loginMobile({
				phone: that.account,
				captcha: that.captcha,
				spread: this.$storage.get('spid')
			})
				.then(res => {
					let data = res.data;
					let expires_time = data.expires_time.substring(0, 19);
					expires_time = expires_time.replace(/-/g, '/');
					expires_time = new Date(expires_time).getTime() - ********;
					updateToken(data.token, expires_time, data);
					if(this.isWeixin){
						that.$navigator('/pages/tabBar/index/index','switchTab');
					}else{
						that.$navigator(1, 'navigateBack');
					}
				})
				.catch(res => {
					that.$showToast(res.msg || res);
				});
		},
		async register() {
			var that = this;
			const { account, captcha, password } = that;
			if (!RegPhone(account)) {
				return that.$showToast(account.length ? '请填写正确手机号' : '请填写手机号');
			}
			if (!captcha.trim().length) {
				return that.$showToast('请输入验证码');
			}
			if (!password.trim().length) {
				return that.$showToast('请填写密码');
			}
			register({
				account: that.account,
				captcha: that.captcha,
				password: that.password,
				spread: that.$storage.get('spread')
			})
				.then(res => {
					that.$successToast(res.msg);
					that.formItem = 1;
				})
				.catch(res => {
					that.$showToast(res.msg || res);
				});
		},
		async code() {
			var that = this;
			const { account } = that;
			if (!RegPhone(account)) {
				return that.$showToast(account.length ? '请填写正确手机号' : '请填写手机号');
			}
			if (that.formItem == 2) that.type = 'register';
			await registerVerify({
				phone: that.account,
				type: that.type,
				key: that.keyCode,
				code: that.codeVal
			})
				.then(res => {
					that.$successToast(res.msg);
					that.sendCode();
				})
				.catch(res => {
					if (res.data.status === 402) {
						that.codeUrl = `${VUE_APP_API_URL}/sms_captcha?key=${that.keyCode}`;
						that.isShowCode = true;
					}
					that.$showToast(res.msg || res);
				});
		},
		navTap: function(index) {
			this.current = index;
		},
		async submit() {
			const { account, password, codeVal } = this;
			if (!RegPhone(account)) {
				return that.$showToast(account.length ? '请填写正确手机号' : '请填写手机号');
			}

			if (!password.trim().length) {
				return that.$showToast('请填写密码');
			}

			if (this.isShowCode && !codeVal.trim().length) {
				return that.$showToast('请填写验证码');
			}
			toLogin({ account, password, code: codeVal });
		}
	}
};
</script>
<style scoped>
.code image {
	width: 100%;
	height: 100%;
}
</style>
