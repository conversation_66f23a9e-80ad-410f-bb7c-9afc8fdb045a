<template>
    <view>
        <view class="student-box">
            <view class="concat-item" v-for="(item,index) in config.content" :key="index">
                <view class="concat-item-left">{{item.name}}</view>
                <view class="introduce-textarea" v-if="item.check_type == 0 && !item.answer_options">
                    <textarea class="introduce-textarea-box"
                        :placeholder="item.desc" placeholder-style="font-size: 32rpx;color:#999999;" maxlength="100"
                        v-model="item.content"></textarea>
                        <text class="questionnaire-wrap_pay-text">{{item.content?item.content.length:'0'}}/100字</text>
                </view>
                <view class="channel-box">
                    <view>
                        <block v-for="(item1, index1) in item.answer_options" :key="index1">
                            <view class="sex-radios-box call" @click="addRadios(item1,index,index1,item)"
                                :class="[item1.check_type == 3 || item1.check_type == 0 || (index == 0 && item.answer_options.length == index1 + 1)?'qita':'']">
                                <view v-if="item.check_type != 2">
                                    <image v-if="!config.content[index].value_group[0]" class="sex-radios-box-img" src="@/static/images/yuanshi/uncheck2.png" mode="">
                                    </image>
                                    <view v-else>
                                        <image v-if="config.content[index].value_group[0].value == item1.value" class="sex-radios-box-img" src="@/static/images/yuanshi/check1-r.png" mode=""></image>
                                        <image v-else class="sex-radios-box-img" src="@/static/images/yuanshi/uncheck2.png" mode="">
                                        </image>
                                    </view>
                                    <view class="sex-radios-box-title" :class="(index == 0 && item.answer_options.length == index1 + 1)?'float0':''" v-if="!config.content[index].value_group[0]">
                                        {{item1.name}}
                                    </view>
                                    
                                    <view v-else>
                                        <view :class="[config.content[index].value_group[0].value == item1.value?'active':'',(index == 0 && item.answer_options.length == index1 + 1)?'float0':'']" class="sex-radios-box-title">
                                            {{item1.name}}
                                        </view>
                                    </view>
                                </view>
                                <!-- config.content[index].value_group[0].value == item1.value -->
                                <view v-if="item.check_type == 2" style="overflow: auto;float: left;">
                                    <image class="sex-radios-box-img" src="@/static/images/yuanshi/check1-r.png" mode=""
                                        v-if="item1.isCheck"></image>
                                    <image class="sex-radios-box-img" src="@/static/images/yuanshi/uncheck2.png" mode=""
                                        v-else>
                                    </image>
                                    
                                    <view class="sex-radios-box-title" :class="item1.isCheck?'active':''">
                                        {{item1.name}}
                                    </view>
                                </view>
                                <picker v-if="item1.check_type == 3" mode="multiSelector" @change="bindRegionChange"
                                    @columnchange="bindMultiPickerColumnChange" :value="valueRegion"
                                    :range="multiArray">
                                    <view class="selex">
                                        <view class="selex-box">
                                            <view class="selex-picker-box">
                                                <view class="selex-picker-item">
                                                    <view class="title">{{ region[0] }}{{ region[1] }}{{ region[2] }}
                                                    </view>
                                                    <image class="img" src="../../static/images/arrow-bottom.png"
                                                        mode="">
                                                    </image>
                                                </view>
                                            </view>
                                        </view>
                                    </view>
                                </picker>
                                <input v-if="item1.check_type == 0" @click.stop class="sex-radios-box call input"
                                    placeholder-style="font-size: 14px;color:#999999" v-model="item1.content"
                                    type="text">
                            </view>
                        </block>
                    </view>
                </view>
            </view>

        </view>
        <view class="submit">
            <view class="submit-but" @click="supplement">
                提交
            </view>
        </view>
        <x-authorize @login="getPollster( ids.type?ids.type:'1', ids.id?ids.id:'0')">
        </x-authorize>
    </view>
</template>
<script>
    import {
        checkLogin,
        autoAuth,
    } from '@/utils/common.js'
    import {
        getCity
    } from '@/api/public';
    import {
        getPollster,
        pollsterCreate
    } from '@/api/yknowledge.js'

    export default {
        data() {
            return {
                setnum: 0,
                config: {},
                ids: {},
                // #ifdef MP-TOUTIAO
                from: 'bytedance',
                // #endif
                // #ifndef MP-TOUTIAO
                from: 'routine',
                // #endif
                finishStudent: false,
                district: [],
                address: {
                    city_id: 0
                },
                region: ['省', '市', '区'],
                multiArray: [],
                valueRegion: [0, 0, 0],
                multiIndex: [0, 0, 0],
                noClick: true // 防止重复提交
            };
        },
        onLoad(option) {
            const {
                id = 0,
                    type = 0
            } = option;
            this.ids = option;
            this.getPollster(type, id)
        },
        onShow() {

        },
        methods: {
            // 开始
            addRadios(item1, index, index1, item) {
                let that = this;
                if (!item.is_child_question) {
                    // 除身份其他一层选择
                    if (item.check_type == 0 || item.check_type == 1 || item.check_type == 3) {
                        that.config.content[index].value_group = [];
                        that.config.content[index].value_group[0] = item1;
                    }
                    if (item.check_type == 2) {
                        let arr = [...that.config.content[index].answer_options];
                        let selarr = [...that.config.content[index].value_group];
                        if (arr[index1].isCheck == false) {
                            arr[index1].isCheck = true;
                            selarr.push(item1)
                        } else {
                            arr[index1].isCheck = false;
                            for (let i = 0; i < selarr.length; i++) {
                                if (selarr[i].value == item1.value) {
                                    selarr.splice(i, 1)
                                }
                            }
                        }
                        that.config.content[index].answer_options = arr;
                        that.config.content[index].value_group = selarr;
                    }
                } else {
                    // console.log('复选---')
                    if (item.check_type == 1) {
                        this.config.content[index].value_group = [];
                        this.config.content[index].value_group[0] = item1;
                        if (this.setnum) {
                            this.config.content.splice(1, this.setnum)
                        }
                        for (let i = item1.answer_options.length - 1; i >= 0; i--) {
                            this.config.content.splice(1, 0, item1.answer_options[i])
                        }
                        for (let i = 0; i < this.config.content.length; i++) {
                            if (!this.config.content[i].value_group) {
                                this.config.content[i].value_group = [];
                            }
                            if (this.config.content[i].check_type == 2) {
                                for (let j = 0; j < that.config.content[i].answer_options.length; j++) {
                                    that.config.content[i].answer_options[j].isCheck = false
                                }
                            }
                        }
                        this.setnum = item1.answer_options.length;
                    }
                }
                this.$forceUpdate();
            },
            // 结束
            getPollster(type = 1, id) {
                let that = this;
                let param = {
                    type: type,
                    id: id,
                    from: that.from,
                }
                getPollster(param).then(res => {
                        this.config = res.data;
                        for (let i = 0; i < this.config.content.length; i++) {
                            if (!this.config.content[i].value_group) {
                                this.config.content[i].value_group = [];
                            }
                        }
                        console.log('res--', this.config)
                        this.getCityList() // 获取国内城市
                    })
                    .catch(err => {
                        that.$showToast(err.msg || err);
                    });
            },
            shallowCopy(arr) {
                var newArr = Array.isArray(arr) ? [] : {}
                for (let key in arr) {
                    newArr[key] = arr[key]
                }
                return newArr
            },
            supplement() {
                let that = this;
                if (checkLogin()) {
                    for (let i = 0; i < that.config.content.length; i++) {
                        if (that.config.content[i].check_type == 0) {
                            if (!that.config.content[i].content) {
                                return that.$showToast('请填写' + that.config.content[i].name);
                            }
                        }
                        if (that.config.content[i].check_type == 1 || that.config.content[i].check_type == 2) {
                            if (that.config.content[i].value_group.length == 0) {
                                return that.$showToast(that.config.content[i].name);
                            }
                            if (that.config.content[i].check_type == 2) {
                                for (let j = 0; j < that.config.content[i].value_group.length; j++) {
                                    if (that.config.content[i].value_group[j].check_type == 0 && !that.config.content[i]
                                        .value_group[j].content) {
                                        return that.$showToast('请填写' + that.config.content[i].name + '所选其他选项');
                                    }
                                }
                            }
                        }
                        if (that.config.content[i].check_type == 3) {
                            if (that.config.content[i].value_group.length == 0) {
                                return that.$showToast(that.config.content[i].name);
                            }
                            for (let j = 0; j < that.config.content[i].value_group.length; j++) {
                                if (that.config.content[i].value_group[j].check_type == 3 && that.address.city_id) {
                                    that.config.content[i].value_group[j].content = that.address.province + that.address
                                        .city + that.address.district
                                }
                                if ((that.config.content[i].value_group[j].check_type == 0 || that.config.content[i]
                                        .value_group[j].check_type == 3) && !that.config.content[i].value_group[j]
                                    .content) {
                                    return that.$showToast(that.config.content[i].name);
                                }
                            }
                        }
                    }
                    let array = JSON.parse(JSON.stringify(that.config.content));
                    array.splice(1, this.setnum)
                    for (let i = 0; i < array.length; i++) {
                        delete array[i].answer_options;
                        delete array[i].check_type;
                        delete array[i].desc;
                        delete array[i].is_child_question;
                        delete array[i].required;
                        delete array[i].sort;
                        for (let j = 0; j < array[i].value_group.length; j++) {
                            if (array[i].value_group[j].content) {
                                array[i].value_group[j].value = array[i].value_group[j].content
                                delete array[i].value_group[j].content;
                            }
                            delete array[i].value_group[j].check_type;
                            if (!array[i].value_group[j].is_child_question) {
                                delete array[i].value_group[j].isCheck;
                            } else {
                                delete array[i].value_group[j].required;
                                for (let n = 0; n < array[i].value_group[j].answer_options.length; n++) {
                                    delete array[i].value_group[j].answer_options[n].answer_options;
                                    delete array[i].value_group[j].answer_options[n].check_type;
                                    delete array[i].value_group[j].answer_options[n].desc;
                                    delete array[i].value_group[j].answer_options[n].is_child_question;
                                    delete array[i].value_group[j].answer_options[n].required;
                                    delete array[i].value_group[j].answer_options[n].isCheck;
                                    if (array[i].value_group[j].answer_options[n].content) {
                                        array[i].value_group[j].answer_options[n].value = array[i].value_group[j]
                                            .answer_options[n].content
                                        delete array[i].value_group[j].answer_options[n].content;
                                    }
                                    for (let m = 0; m < array[i].value_group[j].answer_options[n].value_group
                                        .length; m++) {
                                        delete array[i].value_group[j].answer_options[n].value_group[m].check_type;
                                        delete array[i].value_group[j].answer_options[n].value_group[m].isCheck;
                                        if (array[i].value_group[j].answer_options[n].value_group[m].content) {
                                            array[i].value_group[j].answer_options[n].value_group[m].value = array[i]
                                                .value_group[j].answer_options[n].value_group[m].content
                                            delete array[i].value_group[j].answer_options[n].value_group[m].content;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    let param = {
                        type: this.ids.type,
                        id: this.config.id,
                        interlink_id: this.ids.id,
                        answer_sheet: array
                    }
                    console.log('提交问卷参数', param)
                    if(that.noClick){
                        that.noClick = false;
                        pollsterCreate(param).then(res => {
                                console.log('提交问卷返回', res)
                                if (res.status == 200) {
                                    that.noClick = true;
                                    that.$showToast('保存成功');
                                    var pages = getCurrentPages();
                                    var prevPage = pages[pages.length - 2];
                                    that.finishStudent = true;
                                    prevPage.$vm.finishStudent = that.finishStudent;
                                    setTimeout(function() {
                                        uni.navigateBack({ //返回
                                            delta: 1
                                        })
                                    }, 1500);
                                }
                            })
                            .catch(err => {
                                that.noClick = true;
                                that.$showToast(err.msg || err);
                            });
                    }

                } else {
                    // #ifndef MP-TOUTIAO
                    autoAuth();
                    // #endif
                }
            },
            getCityList() {
                let that = this;
                getCity().then(res => {
                        that.district = res.data;
                        that.initCityArr();
                    })
                    .catch(err => {
                        that.$showToast(err.msg || err);
                    });
            },
            initCityArr() {
                let that = this,
                    province = [],
                    city = [],
                    area = [];
                if (that.district.length) {
                    let cityChildren = that.district[0].c || [];
                    let areaChildren = cityChildren.length ? cityChildren[0].c || [] : [];
                    that.district.forEach(function(item) {
                        province.push(item.n);
                    });
                    cityChildren.forEach(function(item) {
                        city.push(item.n);
                    });
                    areaChildren.forEach(function(item) {
                        area.push(item.n);
                    });
                    that.multiArray = [province, city, area];
                }
            },
            bindMultiPickerColumnChange(e) {
                let that = this,
                    column = e.detail.column,
                    value = e.detail.value,
                    currentCity = this.district[value] || {
                        c: []
                    },
                    multiArray = that.multiArray,
                    multiIndex = that.multiIndex;
                multiIndex[column] = value;
                switch (column) {
                    case 0:
                        let areaList = currentCity.c[0] || {
                            c: []
                        };
                        multiArray[1] = currentCity.c.map(item => {
                            return item.n;
                        });
                        multiArray[2] = areaList.c.map(item => {
                            return item.n;
                        });
                        this.multiIndex.splice(1, 1, 0)
                        this.multiIndex.splice(2, 1, 0)
                        break;
                    case 1:
                        let cityList = that.district[multiIndex[0]].c[multiIndex[1]].c || [];
                        multiArray[2] = cityList.map(item => {
                            return item.n;
                        });
                        this.multiIndex.splice(2, 1, 0)
                        break;
                    case 2:
                        break;
                }
                this.multiArray = multiArray;
                this.multiIndex = multiIndex;
            },
            bindRegionChange(e) {
                let multiIndex = this.multiIndex,
                    province = this.district[multiIndex[0]] || {
                        c: []
                    },
                    city = province.c[multiIndex[1]] || {
                        v: 0
                    },
                    multiArray = this.multiArray,
                    value = e.detail.value;
                this.region = [multiArray[0][value[0]], multiArray[1][value[1]], multiArray[2][value[2]]];
                this.cityId = city.v;
                this.valueRegion = [0, 0, 0];

                this.address = {
                    province: this.region[0],
                    city: this.region[1],
                    district: this.region[2],
                    city_id: this.cityId
                };
                console.log('this.address', this.address)
            },
        }
    };
</script>
<style lang="scss" scoped>
    .submit {
        width: 100%;
        padding: 0 40rpx;
        box-sizing: border-box;

        .submit-but {
            width: 100%;
            height: 100rpx;
            line-height: 100rpx;
            text-align: center;
            font-size: 24rpx;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            color: #ffffff;
            background: #50506f;
            border-radius: 40rpx;
            margin-top: 66rpx;
        }
    }

    .student-box {
        width: 100%;
        height: auto;
        background: #ffffff;
        box-shadow: 0px 0px 30rpx 30rpx rgba(107, 127, 153, 0.30);
        padding: 0 40rpx 60rpx;
        border-radius: 0 0 60rpx 60rpx;
        box-sizing: border-box;

        .concat-item {
            width: 100%;
            padding-top: 34rpx;
            overflow: auto;
            border-bottom: 2rpx solid #7dbffa;
            padding-bottom: 24rpx;
            box-sizing: border-box;

            .introduce-textarea {
                position: relative;
                width: 100%;

                .introduce-textarea-box {
                    width: 100%;
                    height: 200rpx;
                    font-size: 32rpx;
                    font-family: SF Pro Text, SF Pro Text-Regular;
                    font-weight: 400;
                    text-align: left;
                    border-radius: 12rpx;
                    box-sizing: border-box;
                    padding: 20rpx;
                }
                .questionnaire-wrap_pay-text {
                    position: absolute !important;
                    right: 0rpx !important;
                    bottom: -20rpx !important;
                    font-size: 32rpx;
                    font-family: SF Pro Text, SF Pro Text-Regular;
                    font-weight: 400;
                    text-align: left;
                    color: #d2d2d2;
                    .red {
                        color: #ff5656;
                    }
                }
            }

            .concat-item-left {
                width: 100%;
                font-size: 40rpx;
                font-family: PingFang SC, PingFang SC-Regular;
                font-weight: 400;
                text-align: left;
                color: #3399ff;

                .title {
                    color: #999999;
                    font-size: 26rpx;
                    margin-left: 8rpx;
                }
            }

            .introduce-textarea-box {
                width: 100%;
                height: 144rpx;
                box-sizing: border-box;
                padding: 20rpx 0;
            }

            .channel-box {
                width: 100%;
                overflow: auto;

                .sex-radios-box {
                    float: left;
                    width: 172rpx;
                    margin-top: 20rpx;

                    &.call {
                        width: 42%;
                    }

                    &.w50 {
                        width: 50%;
                    }

                    &.qita {
                        width: 100%;
                    }

                    &.input {
                        width: 360rpx;
                        margin-top: -4rpx;
                        margin-left: 8rpx;
                        border-bottom: 2rpx solid #7dbffa;
                        font-size: 28rpx;
                    }

                    .sex-radios-box-img {
                        float: left;
                        width: 44rpx;
                        height: 44rpx;
                        margin-right: 16rpx;
                    }

                    .sex-radios-box-title {
                        float: left;
                        font-size: 30rpx;
                        font-family: PingFang SC, PingFang SC-Regular;
                        font-weight: 400;
                        text-align: left;
                        color: #3d4971;

                        &.active {
                            color: #fc5656;
                        }

                        &.float0 {
                            float: none;
                        }
                    }

                    .selex {
                        float: left;
                        overflow: auto;
                        padding: 4rpx 20rpx;

                        .selex-box {
                            float: left;
                            min-width: 128rpx;
                            height: 42rpx;
                            line-height: 40rpx;
                            background: #ffffff;
                            border: 2rpx solid #7dbffa;
                            border-radius: 4rpx;
                            padding: 0 16rpx;

                            .selex-picker-box {
                                width: 100%;

                                .selex-picker-item {
                                    width: 100%;

                                    .title {
                                        float: left;
                                        font-size: 30rpx;
                                        font-family: PingFang SC, PingFang SC-Regular;
                                        font-weight: 400;
                                        text-align: center;
                                        color: #3399ff;
                                        margin-right: 26rpx;
                                        padding: 0;
                                    }

                                    .img {
                                        float: right;
                                        width: 18rpx;
                                        height: 18rpx;
                                        margin-top: 6rpx;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
</style>