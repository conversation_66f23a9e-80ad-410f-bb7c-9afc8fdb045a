<template>
	<view class="personal-data">
		<view class="wrapper">
			<view class="title">管理我的账号</view>
			<view class="wrapList">
				<view v-for="(item, index) in switchUserInfo" :key="index">
					<template v-if="isWeixin">
						<view class="item acea-row row-between-wrapper" :class="item.uid === userInfo.uid ? 'on' : ''">
							<view class="picTxt acea-row row-between-wrapper">
								<view class="pictrue">
									<template v-if="item.uid === userInfo.uid">
                                        <!-- #ifdef MP-WEIXIN -->
                                        <button style="width: 96rpx;height: 96rpx;border-radius: 50%;" class="" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
                                            <view class="pictrue " style="margin:0;border-radius:50%"><image :src="avatar?avatar:item.avatar" /></view>
                                        </button>
                                        <!-- #endif -->
                                        <!-- #ifndef MP-WEIXIN -->
                                        <xImageUpload :num="1" @chooseImage="chooseImage" @avatarSize="avatarSize" :close="false" width="96rpx" height="96rpx" borderRadius="50%">
                                        	<view class="pictrue " style="margin:0;border-radius:50%"><image :src="item.avatar" /></view>
                                        </xImageUpload>
                                        <!-- #endif -->
                                        
									</template>
									<view class="pictrue" v-else><image :src="item.avatar" /></view>
									<image src="@/static/images/alter.png" class="alter" v-if="item.uid === userInfo.uid" />
								</view>
								<view class="text">
									<view class="name line1">{{ item.nickname }}</view>
									<!-- <view class="phone">绑定手机号：{{ item.phone ? item.phone : '' }}</view> -->
                                    <view class="phone" v-if="item.uid">ID号：{{item.uid}} <text class="phoneuid">成长积分：{{item.growing_integral}}</text></view>
								</view>
							</view>
							<view class="currentBnt acea-row row-center-wrapper font-color-red" v-if="item.uid === userInfo.uid">当前账号</view>
							<view class="bnt font-color-red acea-row row-center-wrapper" v-else @click="switchAccounts(index)">使用账号</view>
						</view>
					</template>
					<template v-else>
						<view v-if="item.uid === userInfo.uid" class="item acea-row row-between-wrapper" :class="item.uid === userInfo.uid ? 'on' : ''">
							<view class="picTxt acea-row row-between-wrapper">
								<view class="pictrue">
									<xImageUpload :num="1" @chooseImage="chooseImage" @avatarSize="avatarSize" :close="false" width="96rpx" height="96rpx" borderRadius="50%">
										<view class="pictrue " style="margin:0;border-radius:50%"><image :src="item.avatar" /></view>
									</xImageUpload>
									<image src="@/static/images/alter.png" class="alter" />
								</view>
								<view class="text">
									<view class="name line1">{{ item.nickname }}</view>
									<view class="phone">绑定手机号：{{ item.phone ? item.phone : ''}}</view>
								</view>
							</view>
							<view class="currentBnt acea-row row-center-wrapper font-color-red">当前账号</view>
						</view>
					</template>
				</view>
			</view>
		</view>
		<view class="list">
			<view class="item acea-row row-between-wrapper">
				<view>昵称</view>
				<view class="input"><input type="nickname" v-model="userInfo.nickname" /></view>
			</view>
			<!-- <view class="item acea-row row-between-wrapper">
				<view>ID号</view>
				<view class="input acea-row row-between-wrapper">
					<input type="text" :value="userInfo.uid" disabled class="id" />
					<span class="iconfont icon-suozi"></span>
				</view>
			</view> -->

			<!-- #ifdef MP -->
			<view class="item acea-row row-between-wrapper">
				<view>权限设置</view>
				<view class="input" @click="openSetting">
					点击管理
					<text class="iconfont icon-xiangyou"></text>
				</view>
			</view>
			<!-- #endif -->

			<view v-if="!userInfo.phone">
				<view @click="goPages('/pages/user/BindingPhone')" class="item acea-row row-between-wrapper">
					<view>绑定手机号</view>
					<view class="input">
						点击绑定手机号
						<span class="iconfont icon-xiangyou"></span>
					</view>
				</view>
			</view>
			<view class="item acea-row row-between-wrapper" v-else-if="userInfo.phone">
				<view>手机号码</view>
				<view class="input acea-row row-between-wrapper">
					<view class="input acea-row row-between-wrapper" @click="goPages('/pages/user/BindingPhone')">
						<input type="text" :value="userInfo.phone" disabled class="id" />
						<!-- <span class="iconfont icon-suozi"></span> -->
					</view>
				</view>
			</view>
			<view v-if="userInfo.phone && userInfo.user_type === 'h5'">
				<view @click="goPages('/pages/user/ChangePassword')" class="item acea-row row-between-wrapper">
					<view>密码</view>
					<view class="input">
						点击修改密码
						<!-- <span class="iconfont icon-xiangyou"></span> -->
					</view>
				</view>
			</view>
		</view>
		<view class="modifyBnt bg-color-red" @click="submit">保存修改</view>
		<view class="logOut cart-color acea-row row-center-wrapper" @click="logout" v-if="!isWeixin">退出登录</view>
		<x-home></x-home>
        <!-- #ifdef MP-WEIXIN -->
        <x-agreement ref="agreement"></x-agreement>
        <!-- #endif -->
	</view>
</template>
<script>
import xImageUpload from '@/components/x-image-upload/x-image-upload';
import { postUserEdit, getLogout, switchH5Login, getUser } from '@/api/user';
import { uploadImg } from '@/utils/upload.js';
import { toLogin, updateToken } from '@/utils/common.js';

// #ifdef H5
import { isWeixin } from '@/utils/validate.js';
const _isWeixin = isWeixin();
// #endif
// #ifdef MP

const _isWeixin = true;

// #endif
export default {
	name: 'PersonalData',
	components: {
		xImageUpload
	},
	data: function() {
		return {
			avatar: '',
			isWeixin: _isWeixin,
			currentAccounts: 0,
			switchUserInfo: [],
			userIndex: 0,
			isEdit: false,
			userInfo: '',
            avatarSizes:0
		};
	},
	mounted: function() {
		this.getUserInfo();
	},
	methods: {
        // 头像填写能力
        onChooseAvatar(e){
            this.isEdit = true;
            this.avatar = e.detail.avatarUrl;
        },
		// #ifdef MP
		openSetting() {
			uni.openSetting({
				success: function(res) {
					console.log(res.authSetting);
				}
			});
		},
		// #endif
		goPages(path, type) {
			this.$authNavigator(path, type);
		},
		chooseImage(val) {
			this.isEdit = true;
			this.avatar = val;
            console.log('this.avatar',this.avatar)
		},
        avatarSize(e){
            this.avatarSizes = e;
            console.log('头像上传大小-字节',this.avatarSizes)
        },
		switchAccounts: function(index) {
			console.log(index)
			let that = this;
			this.userIndex = index;
			let userInfo = this.switchUserInfo[this.userIndex];
			if (this.switchUserInfo.length <= 1) return true;
			if (userInfo === undefined) return this.$showToast('切换的账号不存在');
			if (userInfo.user_type === 'h5') {
				switchH5Login()
					.then(({ data }) => {
						let expires_time = data.expires_time.substring(0, 19);
						expires_time = expires_time.replace(/-/g, '/');
						expires_time = new Date(expires_time).getTime() - ********;
						const datas = {
							token: data.token,
							expires_time: expires_time
						};
						updateToken(data.token, expires_time, data);
						that.$emit('changeswitch', false);
						this.$navigator('/pages/tabBar/index/index', 'switchTab');
					})
					.catch(err => {
						return that.$showToast(err.msg || err);
					});
			} else {
				// #ifdef H5
				this.$store.commit('LOGOUT');
				this.$emit('changeswitch', false);
				this.$navigator('/pages/tabBar/index/index', 'switchTab');
				// #endif
				// #ifdef MP-WEIXIN
				uni.login({
					success(res) {
						uni.getUserInfo({
							// #ifdef MP-WEIXIN
							lang: 'zh_CN', //头条不支持该字段
							// #endif
							// #ifdef MP-TOUTIAO
							withCredentials: true,
							// #endif
							success(userInfo) {
								userInfo.code = res.code;
								toLogin(userInfo, function(res) {
									console.log(res);
									that.getUserInfo();
									that.$navigator('/pages/tabBar/index/index', 'switchTab');
								});
							},
							fail(err) {
								// 用户未曾授权
								// uni.hideLoading();
							}
						});
					},
					fail(res) {
						// uni.hideLoading();
					}
				});
				// #endif
			}
		},
		getUserInfo: function() {
			let that = this;
			getUser().then(res => {
				let switchUserInfo = res.data.switchUserInfo;
				this.userInfo = res.data;
				this.avatar = this.userInfo.avatar;
				for (let i = 0; i < switchUserInfo.length; i++) {
					if (switchUserInfo[i].uid == that.userInfo.uid) {
						that.userIndex = i
						this.userInfo.phone = switchUserInfo[i].phone;
						};
					if (!that.isWeixin && switchUserInfo[i].user_type != 'h5' && switchUserInfo[i].phone === '') switchUserInfo.splice(i, 1);
				}
				that.$set(this, 'switchUserInfo', switchUserInfo);
			});
		},
		imageuploaded(res) {
			if (res.status !== 200) return this.$showToast(res.msg || '上传图片失败');
			if (this.switchUserInfo[this.userIndex] === undefined) return;
			this.$set(this.switchUserInfo[this.userIndex], 'avatar', res.data.url);
		},

		async submit() {
			let userInfo = this.switchUserInfo[this.userIndex],
				avatarInfo = userInfo.avatar;
            if(this.avatarSizes >= 2097152){
                return this.$showToast('头像上传文件大小超过2M');
            }
            if(this.userInfo.nickname == ''){
                return this.$showToast('昵称不能为空');
            }
			if (this.isEdit) {
				let res = await uploadImg(this.avatar);
                console.log('res',res)
				avatarInfo = res[0];
			}
			postUserEdit({
				nickname: this.userInfo.nickname.trim(),
				avatar: avatarInfo
			}).then(
				res => {
                    console.log('res---',res)
                    this.isEdit = false;
					this.$store.dispatch('USERINFO', true);
					this.$successToast(res.msg);
					this.$navigator(-1);
				},
				error => {
                    console.log('error---',error)
					this.$showToast(error);
				}
			);
		},
		logout: function() {
			let that = this;
			that.$showModal('提示', '确认退出登录?', {
				success: function(res) {
					if (res.confirm) {
						that.$store.commit('LOGOUT');
						that.$navigator('/pages/tabBar/index/index', 'switchTab');
					} else if (res.cancel) {
					}
				}
			});
		}
	}
};
</script>
<style scoped>
.personal-data .wrapper {
	margin: 10rpx 0;
	background-color: #fff;
	padding: 36rpx 30rpx 13rpx 30rpx;
}

.personal-data .wrapper .title {
	margin-bottom: 30rpx;
	font-size: 32rpx;
	color: #282828;
}

.personal-data .wrapper .wrapList .item {
	width: 690rpx;
	height: 160rpx;
	background-color: #f8f8f8;
	border-radius: 20rpx;
	margin-bottom: 22rpx;
	padding: 0 30rpx;
	position: relative;
	border: 2rpx solid #f8f8f8;
}

.personal-data .wrapper .wrapList .item.on {
	border-color: #e93323;
	border-radius: 20rpx;
	background-image: url('data:image/png;base64,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')
		no-repeat;
	background-size: 100% 100%;
	background-color: #fff9f9;
}

.personal-data .wrapper .wrapList .item .picTxt {
	width: 445rpx;
}

.personal-data .wrapper .wrapList .item .picTxt .pictrue {
	width: 96rpx;
	height: 96rpx;
	position: relative;
}

.personal-data .wrapper .wrapList .item .picTxt .pictrue image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.personal-data .wrapper .wrapList .item .picTxt .pictrue .alter {
	width: 30rpx;
	height: 30rpx;
	border-radius: 50%;
	position: absolute;
	bottom: 0;
	right: 0;
}

.personal-data .wrapper .wrapList .item .picTxt .text {
	width: 325rpx;
}

.personal-data .wrapper .wrapList .item .picTxt .text .name {
	width: 100%;
	font-size: 30rpx;
	color: #282828;
}

.personal-data .wrapper .wrapList .item .picTxt .text .phone ,
.personal-data .wrapper .wrapList .item .picTxt .text .phone .phoneuid{
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
}
.personal-data .wrapper .wrapList .item .picTxt .text .phone .phoneuid {
    margin-left: 40rpx;
}

.personal-data .wrapper .wrapList .item .bnt {
	font-size: 24rpx;
	background-color: #fff;
	border-radius: 27rpx;
	width: 140rpx;
	height: 54rpx;
	border: 2rpx solid #e93323;
}

.personal-data .wrapper .wrapList .item .currentBnt {
	position: absolute;
	right: 0;
	top: 0;
	font-size: 26rpx;
	background-color: rgba(233, 51, 35, 0.1);
	width: 140rpx;
	height: 48rpx;
	border-radius: 0 20rpx 0 20rpx;
}

.personal-data .list {
	background-color: #fff;
}

.personal-data .list .item {
	padding: 30rpx 30rpx 30rpx 0;
	border-bottom: 1px solid #f2f2f2;
	margin-left: 30rpx;
	font-size: 32rpx;
	color: #282828;
}

.personal-data .list .item .pictrue {
	width: 88rpx;
	height: 88rpx;
}

.personal-data .list .item .pictrue image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.personal-data .list .item .input {
	width: 415rpx;
	text-align: right;
	color: #868686;
	font-size: 24rpx;
}

.personal-data .list .item .input input {
	color: #868686;
	text-align: right;
	width: 100%;
	font-size: 24rpx;
}

.personal-data .list .item .input .id {
	/* width: 365rpx; */
}

.personal-data .list .item .input .iconfont {
	font-size: 35rpx;
}

.personal-data .list .item .input .iconfont.icon-xiangyou {
	font-size: 30rpx;
	margin-left: 27rpx;
}

.personal-data .modifyBnt {
	font-size: 24rpx;
	color: #fff;
	width: 690rpx;
	height: 90rpx;
	border-radius: 40rpx;
	text-align: center;
	line-height: 90rpx;
	margin: 76rpx auto 0 auto;
}

.personal-data .logOut {
	font-size:24rpx;
	text-align: center;
	width: 690rpx;
	height: 90rpx;
	border-radius: 40rpx;
	margin: 30rpx auto 70rpx auto;
}

/*拼团海报*/
.poster-poster .tip {
	height: 80rpx;
	font-size: 26rpx;
	color: #e8c787;
	text-align: center;
	line-height: 80rpx;
}

.poster-poster .tip .iconfont {
	font-size: 36rpx;
	vertical-align: -4rpx;
	margin-right: 18rpx;
}

.poster-poster .poster {
	width: 690rpx;
	height: 100%;
	margin: 0 auto;
}

.poster-poster .poster image {
	width: 100%;
	height: 100%;
	display: block;
}
</style>
