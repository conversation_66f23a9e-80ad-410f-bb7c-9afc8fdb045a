<template>
	<view class="integral-details" >
		<view class="header">
			<view class="currentScore">当前积分</view>
			<view>{{ info.integral }}</view>
			<view class="line"></view>
			<view class="nav acea-row">
				<view class="item">
					<view class="num">{{ info.sum_integral }}</view>
					<view>累计积分</view>
				</view>
				<view class="item">
					<view class="num">{{ info.deduction_integral }}</view>
					<view>累计消费</view>
				</view>
				<view class="item">
					<view class="num">{{ info.today_integral }}</view>
					<view>今日获得</view>
				</view>
			</view>
		</view>
		<view class="wrapper">
			<view class="nav acea-row">
				<view class="item acea-row row-center-wrapper" :class="current === index ? 'on' : ''" v-for="(item, index) in navList" :key="index" @click="nav(index)">
					<span class="iconfont" :class="item.icon"></span>
					{{ item.name }}
				</view>
			</view>
			<view class="list" v-if="current === 0" >
				<view class="tip acea-row row-middle">
					<span class="iconfont icon-shuoming"></span>
					提示：积分数值的高低会直接影响您的会员等级
				</view>
				<view class="item acea-row row-between-wrapper" v-for="(item, index) in list" :key="index">
					<view>
						<view class="state">{{ item.mark }}</view>
						<view>{{ item.add_time }}</view>
					</view>
					<view class="num" v-if="item.pm == 1">+{{ item.number }}</view>
					<view class="num font-color-red" v-if="item.pm == 0">-{{ item.number }}</view>
				</view>
			</view>
			<view class="list2"  v-if="current === 1">
				<view @click="goPages('/','switchTab')" class="item acea-row row-between-wrapper" >
					<view class="pictrue"><image src="@/static/images/score.png" /></view>
					<view class="name">购买商品可获得积分奖励</view>
					<view class="earn">赚积分</view>
				</view>
				<view @click="goPages('/pages/user/signIn/Sign')" class="item acea-row row-between-wrapper">
					<view class="pictrue"><image src="@/static/images/score.png" /></view>
					<view class="name">每日签到可获得积分奖励</view>
					<view class="earn">赚积分</view>
				</view>
			</view>
		</view>
		<Loading :loaded="loaded" :loading="loading"></Loading>
		<x-home></x-home>
	</view>
</template>
<script>
import { getIntegralList, postSignUser } from '@/api/user';
import Loading from '@/components/Loading';
export default {
	name: 'Integral',
	components: {
		Loading
	},
	props: {},
	data: function() {
		return {
			navList: [{ name: '分值明细', icon: 'icon-mingxi' }, { name: '分值提升', icon: 'icon-tishengfenzhi' }],
			current: 0,
			where: {
				page: 1,
				limit: 15
			},
			data: {
				sign: 1,
				integral: 1,
				all: 1
			},
			list: [],
			info: [],
			loaded: false,
			loading: false
		};
	},
	mounted: function() {
		this.getIntegral();
		this.getInfo();

	},
	methods: {
		goPages(path, type) {
			this.$authNavigator(path, type);
		},
		nav: function(index) {
			this.current = index;
		},
		getInfo: function() {
			let that = this;
			if (that.loaded == true || that.loading == true) return;
			that.loading = true;
			getIntegralList(that.where).then(
				res => {
					that.loading = false;
					that.loaded = res.data.length < that.where.limit;
					that.loadTitle = that.loaded ? '人家是有底线的' : '上拉加载更多';
					that.where.page = that.where.page + 1;
					that.list.push.apply(that.list, res.data);
				},
				err => {
					that.$showToast(err.msg || err);
				}
			);
		},
		getIntegral: function() {
			let that = this;
			postSignUser(that.data).then(
				res => {
					that.info = res.data;
				},
				err => {
						that.$showToast(err.msg || err);
				}
			);
		}
	},
	onReachBottom(){
		 this.getInfo();
	}
};
</script>
<style scoped>
	.integral-details .header {
		background-image: url('@/static/images/integralbg.jpg');
		background-repeat: no-repeat;
		background-size: 100% 100%;
		width: 100%;
		height: 459rpx;
		font-size: 72rpx;
		color: #fff;
		padding: 31rpx 0 45rpx 0;
		text-align: center;
		font-family: 'GuildfordProBook 5';
	}
	
	.integral-details .header .currentScore {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.8);
		text-align: center;
		margin-bottom: 5rpx;
	}
	
	.integral-details .header .line {
		width: 60rpx;
		height: 3rpx;
		background-color: #fff;
		margin: 20rpx auto 0 auto;
	}
	
	.integral-details .header .nav {
		font-size: 22rpx;
		color: rgba(255, 255, 255, 0.8);
		flex: 1;
		-webkit-flex: 1;
		-o-flex: 1;
		-ms-flex: 1;
		margin-top: 35rpx;
	}
	
	.integral-details .header .nav .item {
		width: 33.33%;
		text-align: center;
	}
	
	.integral-details .header .nav .item .num {
		color: #fff;
		font-size: 40rpx;
	}
	
	.integral-details .wrapper .nav {
		flex: 1;
		-webkit-flex: 1;
		-o-flex: 1;
		-ms-flex: 1;
		width: 690rpx;
		border-radius: 20rpx 20rpx 0 0;
		margin: -96rpx auto 0 auto;
		background-color: #f7f7f7;
		height: 96rpx;
		font-size: 30rpx;
		color: #bbb;
	}
	
	.integral-details .wrapper .nav .item {
		text-align: center;
		width: 50%;
	}
	
	.integral-details .wrapper .nav .item.on {
		background-color: #fff;
		color: #e93323;
		font-weight: bold;
		border-radius: 20rpx 0 0 0;
	}
	
	.integral-details .wrapper .nav .item .iconfont {
		font-size: 38rpx;
		margin-right: 10rpx;
	}
	
	.integral-details .wrapper .nav .item:nth-of-type(2) {
		border-radius: 0 20rpx 0 0;
	}
	
	.integral-details .wrapper .list {
		background-color: #fff;
		padding: 24rpx 30rpx;
	}
	
	.integral-details .wrapper .list .tip {
		font-size: 25rpx;
		width: 690rpx;
		height: 60rpx;
		border-radius: 50rpx;
		background-color: #fff5e2;
		border: 1px solid #ffeac1;
		color: #c8a86b;
		padding: 0 20rpx;
		margin-bottom: 24rpx;
	}
	
	.integral-details .wrapper .list .tip .iconfont {
		font-size: 35rpx;
		margin-right: 15rpx;
	}
	
	.integral-details .wrapper .list .item {
		height: 124rpx;
		border-bottom: 1px solid #eee;
		font-size: 24rpx;
		color: #999;
	}
	
	.integral-details .wrapper .list .item .state {
		font-size: 28rpx;
		color: #282828;
		margin-bottom: 8rpx;
	}
	
	.integral-details .wrapper .list .item .num {
		font-size: 36rpx;
		font-family: 'GuildfordProBook 5';
		color: #16ac57;
	}
	
	.integral-details .wrapper .list2 {
		background-color: #fff;
		padding: 24rpx 0;
	}
	
	.integral-details .wrapper .list2 .item {
		background-image: linear-gradient(to right, #fff7e7 0%, #fffdf9 100%);
		background-image: -webkit-linear-gradient(to right, #fff7e7 0%, #fffdf9 100%);
		background-image: -moz-linear-gradient(to right, #fff7e7 0%, #fffdf9 100%);
		width: 690rpx;
		height: 180rpx;
		position: relative;
		border-radius: 10rpx;
		margin: 0 auto 20rpx auto;
		padding: 0 25rpx 0 180rpx;
	}
	
	.integral-details .wrapper .list2 .item .pictrue {
		width: 90rpx;
		height: 150rpx;
		position: absolute;
		bottom: 0;
		left: 45rpx;
	}
	
	.integral-details .wrapper .list2 .item .pictrue image {
		width: 100%;
		height: 100%;
	}
	
	.integral-details .wrapper .list2 .item .name {
		width: 285rpx;
		font-size: 30rpx;
		font-weight: bold;
		color: #c8a86b;
	}
	
	.integral-details .wrapper .list2 .item .earn {
		font-size: 26rpx;
		color: #c8a86b;
		border: 2rpx solid #c8a86b;
		text-align: center;
		line-height: 52rpx;
		height: 52rpx;
		width: 160rpx;
		border-radius: 50rpx;
	}
</style>
