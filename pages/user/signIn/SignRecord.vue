<template>
	<view class="sign-record" >
		<view class="list">
			<view class="item" v-for="(item, index) in signList" :key="index">
				<view class="data">{{ item.month }}</view>
				<view class="listn">
					<view class="itemn acea-row row-between-wrapper" v-for="(itemn, indexn) in item.list" :key="indexn">
						<view>
							<view class="name line1">{{ itemn.title }}</view>
							<view>{{ itemn.add_time }}</view>
						</view>
						<view class="num font-color-red">+{{ itemn.number }}</view>
					</view>
				</view>
			</view>
		</view>
		<Loading :loaded="loadend" :loading="loading"></Loading>
		<x-home></x-home>
	</view>
</template>
<script>
import { getSignMonth } from '@/api/user';
import Loading from '@/components/Loading';
export default {
	name: 'SignRecord',
	components: {
		Loading
	},
	props: {},
	data: function() {
		return {
			page: 1,
			limit: 5,
			signList: [],
			loading: false,
			loadend: false,
			active: false
		};
	},
	mounted: function() {
		this.signListTap();
	},
	methods: {
		signListTap: function() {
			let that = this;
			if (that.loading || that.loaded) return;
			that.loading = true;
			getSignMonth(that.page, that.limit).then(res => {
					that.loading = false;
					that.loaded = res.data.length < that.limit;
					that.page = that.page + 1;
					that.signList.push.apply(that.signList, res.data);
				}).catch(	err => {
					 that.$showToast( err.msg || err)
				});
		}
	},
	onReachBottom() {
		this.signListTap()
	}
};
</script>

<style scoped lang="scss">
	page{
		background-color:$uni-bg-color-page;
	}
	
</style>
