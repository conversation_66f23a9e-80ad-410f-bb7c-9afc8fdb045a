<template>
    <view v-if="serviceOnline == true">
        <view class="chat_room" id="chatRroom">
            <view class="nodata" v-if="isLoad && serviceOnline && chatServerData.serviceList.length > 0">
                <u-loadmore :status="loadmoreStatus" :loadText="loadText" color="#999999" />
            </view>
            <view class="chart_list" id="contentMessage">
                <view class="chart_list_item" v-for="(item, index) in records" :key="index">
                    <view class="chart_list_item_time" v-show="item.show">{{item.time}}</view>
                    <view class="chart_list_item_content" :class="{'right-box': item.user_id == chatServerData.user_id}">
                        <view class="chart_list_item_avatar">
                            <image :src="item.avatar" mode=""></image>
                        </view>
                        <!-- 文字及表情信息 -->
                        <view class="chart_list_item_text" v-if="item.msn_type <= 2" v-html="replace_em(item.msn)">
                        </view>
                        <!-- 图片信息 -->
                        <view class="chart_list_item_img" v-if="item.msn_type == 3" >
                            <image :src="item.msn" mode="widthFix" @click="prviewImage(item.msn)" show-menu-by-longpress="true"></image>
                        </view>
                        <!-- 图文信息 -->
                        <view class="chart_list_item_imgOrText" v-if="item.msn_type == 5">
                            <view class="order-wrapper">
                                <view class="img-box">
                                    <image :src="item.other.image" mode=""></image>
                                </view>
                                <view class="order-info">
                                    <view class="name">{{item.other.store_name}}</view>
                                    <view class="price-box">
                                        <view class="num">¥ {{item.other.price}}</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="productMessage_container" v-if="isShowProductModel">
                    <view class="productMessage_container_image">
                        <image :src="ProductDetail.image"></image>
                    </view>
                    <view class="productMessage_container_content">
                        <view class="productMessage_container_content_title">{{ProductDetail.store_name}}</view>
                        <view class="productMessage_container_content_priceOrHandle">
                            <view>￥{{ProductDetail.price}}</view>
                            <view @click="sendProduct">发送客服</view>
                        </view>
                    </view>
                </view>
                <view id="bottom" style="width: 100%; height: 210rpx;"></view>
            </view>
            <xChat @send="sendMsg" inputType="textarea" :isReplace="true" :adjustPosition="true" :tool="true" :emoji="true" :inputShow="true" desc="chat" ref="xChat"></xChat>
        </view>
    </view>
    <view class="not-online" v-else>
        <view class="not-online-title">
            {{feedback}}
        </view>
        <view class="user-name">
            <input class="user-name-input" type="text" v-model="userName" placeholder="请输入您的姓名"/>
        </view>
        <view class="user-name">
            <input class="user-name-input" type="text" v-model="userPhone" placeholder="请输入您的联系电话"/>
        </view>
        <view class="user-content">
            <textarea class="user-content-text" v-model="userContent" placeholder="请填写留言内容" />
        </view>
        <view class="save-message" @click="savemessage">
            提交留言
        </view>
    </view>
</template>
<script>
    import {
        RegPhone
    } from '@/utils/validate.js';
    import {
        orderDetail
    } from '@/api/order';
    import {
        getProductDetail
    } from '@/api/store';
    import {
        getSeckillDetail,
        getCombinationDetail
    } from '@/api/activity';
    import {
        getSpecialDetail,
    } from '@/api/yknowledge.js'
    import {
        evaluationDetail
    } from '@/api/yuanshi/evaluate.js';
    import {
        activityDetail,
    } from '@/api/community';
    import {
        VUE_APP_URL,
        KEFUTOKEN,
        KEFU_URL,
        CHAT_WS_URL
    } from '@/config.js';
    import storage from '@/utils/storage.js';
    import xChat from '@/components/x-chat/x-chat';
    import {
        checkLogin,
        initTime
    } from '@/utils/common.js';
    export default {
        components: {
            xChat
        },
        computed: {
            records() {
                if (this.chatServerData.to_user_id) {
                    return this.chatServerData.serviceList.map((item, index) => {
                        item.time = this.format(item.add_time * 1000);
                        if (index) {
                            if (
                                item.add_time - this.chatServerData.serviceList[index - 1].add_time >= 300
                            ) {
                                item.show = true;
                            } else {
                                item.show = false;
                            }
                        } else {
                            item.show = true;
                        }
                        return item;
                    });
                }
            },
        },
        data: function() {
            return {
                upperData: {}, // 外部链接携带进来的参数
                userName:'',
                userPhone:'',
                userContent:'',
                feedback:'',
                chatServerData: {
                    avatar: '',
                    nickname: '',
                    site_name: '',
                    user_id: '',
                    to_user_avatar: '',
                    to_user_id: '',
                    to_user_nickname: '',
                    uid: '',
                    serviceList: [] // 聊天记录
                },
                serviceOnline: true, // 客服是否在线
                isLoad: true,
                loadmoreStatus: 'loadmore',
                loadText: {
                    loadmore: '下拉加载记录',
                    loading: '努力加载中',
                    nomore: '已加载全部'
                },
                chatStatus: false, // 聊天状态

                scrollTop: 0,
                wScr: 0,
                isShowProductModel: false, // 是否携带商品
                ProductDetail: {},
                time: initTime(),

                history: [],
                scrollY: 0,
                socket: null,
                socketOpen: false,
                socketLimit: 5,
                socketTj: 0,
                networkStatus: true, //网络状态
                to_transferdata:{}
            };
        },
        onLoad(options) {
            let _this = this;
            _this.upperData = options;
            const {
                type,
                id,
                wid,
                scence,
                uid
            } = options;
            console.log('options', options)
            if (type === '0') {
                if (scence === 'pintuan') {
                    this.getCombinationDetail(id);
                } else if (scence === 'miaosha') {
                    this.getSeckillDetail(id);

                } else if (scence === 'community') {
                    this.getActivityDetail(id);
                } else if (scence === 'zsff') {
                    this.getSpecialDetail(id);
                } else {
                    this.getProduct(id, wid);
                }
            } else if (type === '1') {
                // this.getOrder(id);
            } else {

            }
            this.initial();
        },
        onShow() {},
        mounted() {
            let _this = this;
            // #ifdef MP-TOUTIAO
            uni.getNetworkType({
                success: function(res) {
                    // console.log('networkType', res.networkType);
                    _this.networkStatus = res.networkType === 'none' ? false : true;
                }
            });
            // #endif
            // #ifndef MP-TOUTIAO
            uni.onNetworkStatusChange(function(res) {
                // console.log('onNetworkStatusChange', res);
                // console.log(res.networkType);
                _this.networkStatus = res.isConnected;
            });
            // #endif
        },
        onHide() {
            //     监听页面隐藏
            // this.onCloseSocket()
            // let socket = this.socket,
            //     _this = this;
            // let obj = {
            //     id: 0
            // };
            // if(this.serviceOnline){
            //     _this.socket.send({
            //         data: JSON.stringify({
            //             data: obj,
            //             type: 'to_chat'
            //         }),
            //         success(res) {
            //             // console.log('客户离开通知成功', res);
            //         },
            //         fail(err) {
            //             // console.log('客户离开通知失败', err);
            //         }
            //     });
            // }
        },
        onUnload() {
            if(this.serviceOnline){
                // 页面卸载
                this.onCloseSocket()
            }
        },
        methods: {
            // 各平台关闭socket连接
            onCloseSocket() {
                let socket = this.socket,
                    _this = this;
                let obj = {
                    id: 0
                };
                _this.socket.send({
                    data: JSON.stringify({
                        data: obj,
                        type: 'to_chat'
                    }),
                    success(res) {
                        // console.log('successres', res);
                    },
                    fail(err) {
                        // console.log('failerr', err);
                    }
                });
                // #ifndef MP-TOUTIAO
                uni.closeSocket({
                    code: 1000,
                    success: res => {
                        // console.log('关闭Socket成功');
                        _this.socketOpen = false;
                    },
                    fail: err => {
                        // console.log('关闭Socket失败');
                    }
                })
                // #endif
                // #ifdef MP-TOUTIAO
                _this.socket.close({
                    code: 1000,
                    reason: "close",
                    success: (res) => {
                        // 关闭成功的回调
                        // console.log("头条关闭连接", res);
                        _this.socketOpen = false;
                    },
                    fail: (res) => {
                        // 关闭失败的回调
                        // console.log("close fail", res);
                    },
                });
                // #endif
            },
            goBottom() {
                this.$nextTick(() => {
                    const query = uni.createSelectorQuery().in(this);
                    query
                        .select('#bottom')
                        .boundingClientRect(data => {
                            // console.log('data', data)
                            this.scrollTop = data.top + this.wScr + 100;
                            // console.log('this.scrollTopthis.scrollTop', this.scrollTop)
                        })
                        .exec();
                });
            },
            initData() {
                if(!this.serviceOnline){
                    return
                };
                let _this = this;
                _this.loadmoreStatus = 'loading';
                uni.request({
                    url: KEFU_URL + '/api/mobile/user/record',
                    data: {
                        limit: 20,
                        uid: _this.chatServerData.uid,
                        idTo: _this.chatServerData.serviceList ? _this.chatServerData.serviceList[0]
                            .id : '',
                        toUserId: _this.chatServerData.to_user_id
                    },
                    header: {
                        'authori-zation': 'Bearer ' + _this.$storage.get(KEFUTOKEN)
                    },
                    success: (res) => {
                        _this.loadmoreStatus = res.data.data.serviceList.length == 0 ? 'nomore' : 'loadmore';
                        if (res.data.status == 200) {
                            res.data.data.serviceList.reverse().forEach(item => {
                                _this.chatServerData.serviceList.unshift(item);
                            })
                        }
                    }
                });
            },
            getProduct(id, wid) {
                let req = getProductDetail(id),
                    _this = this;
                if (wid) {
                    req = evaluationDetail({
                        wish_id: wid,
                        product_id: id
                    })
                }
                req.then(async (res) => {
                    this.ProductDetail = res.data.storeInfo;
                    this.isShowProductModel = true;
                    // console.log('商品详情', this.ProductDetail)
                });
            },
            getSeckillDetail(id) {
                getSeckillDetail(id).then(res => {
                    let data = res.data.storeInfo;
                    let obj = {
                        c5: '秒杀',
                        d1: data.title,
                        d2: data.price,
                        d3: data.ot_price,
                        d4: data.image,
                        d5: _this.detailLink(id, wid),
                        d6: id
                    };
                    // this.goService(obj);
                });
            },
            getCombinationDetail(id) {
                getCombinationDetail(id).then(res => {
                    let data = res.data.storeInfo;
                    let obj = {
                        c5: '拼团',
                        d1: data.title,
                        d2: data.price,
                        d3: data.product_price,
                        d4: data.image,
                        d5: _this.detailLink(id, wid),
                        d6: id
                    };
                    // this.goService(obj);
                });
            },
            getActivityDetail(id) {
                activityDetail(id).then(res => {
                    this.ProductDetail = res.data.activityInfo;
                    this.ProductDetail.store_name = res.data.activityInfo.name;
                    this.isShowProductModel = true;
                    // console.log('活动详情', this.ProductDetail)
                });
            },
            getSpecialDetail(id) {
                getSpecialDetail({
                    id
                }).then(res => {
                    // console.log('知识付费res.data',res.data)
                    this.ProductDetail = res.data;
                    this.ProductDetail.store_name = res.data.title;
                    this.ProductDetail.price = res.data.money;
                    this.isShowProductModel = true;
                })
            },
            
            // 聊天表情转换
            replace_em(str) {
                str = str.replace(/\[em-([a-z_]*)\]/g, "<span class='em em-$1'/>");
                return str;
            },
            add0(m) {
                return m < 10 ? '0' + m : m
            },
            format(times) {
                var time = new Date(times);
                var y = time.getFullYear();
                var m = time.getMonth() + 1;
                var d = time.getDate();
                var h = time.getHours();
                var mm = time.getMinutes();
                var s = time.getSeconds();
                return m + '月' + this.add0(d) + '日 ' + this.add0(h) + ':' + this.add0(mm);
            },
            initial() {
                let _this = this;
                if (!_this.networkStatus) {
                    return _this.$showToast('网络异常');
                }
                let userInfo = _this.$store.state.userInfo;
                uni.request({
                    url: KEFU_URL + '/api/mobile/user/record',
                    method: 'GET',
                    data: {
                        token: _this.$storage.get(KEFUTOKEN),
                        uid: userInfo.uid,
                        limit: 20,
                        nickname: userInfo.nickname,
                        phone: _this.$storage.get('userInfo_phone'),
                        sex: '',
                        avatar: userInfo.avatar,
                        openid: '',
                        kefu_id: _this.upperData.kefu_id || 0,
                        toUserId: 0,
                        // #ifdef MP-WEIXIN
                        type: 2,
                        // #endif
                        // #ifdef H5
                        type: 3,
                        // #endif
                        // #ifdef APP
                        type: 4,
                        // #endif
                        // #ifdef MP-TOUTIAO
                        type: 5,
                        // #endif
                    },
                    header: {
                        'authori-zation': 'Bearer ' + _this.$storage.get(KEFUTOKEN)
                    },
                    success: (res) => {
                        _this.chatServerData = res.data.data;
                        console.log('获取客服信息', this.chatServerData);
                        // console.log('获取客服信息', res.data.status);
                        if (res.data.status == 200) {
                            if (_this.socket) {
                                _this.initWebsocket();
                            } else {
                                _this.socket = _this.linkSocket();
                                _this.initWebsocket();
                            }
                            if (res.data.data.welcome) {
                                _this.pushMessageToList(res.data.data.welcome)
                            }
                            _this.serviceOnline = true;
                            uni.setNavigationBarTitle({
                                title: '正在与' + _this.chatServerData.to_user_nickname + '对话中'
                            });
                            _this.$nextTick(() => {
                                _this.goBottom();
                            });
                        } else if (res.data.status == 400) {
                            _this.getmessage()
                            _this.serviceOnline = false;
                            uni.setNavigationBarTitle({
                                title: '客服已离线'
                            });
                            if(res.data.data.message == '无效TOKEN'){
                                uni.request({
                                    url: KEFU_URL + '/api/mobile/service/kf',
                                    method:'GET',
                                    success: (res) => {
                                        if(res.data.status == 200){
                                            _this.$storage.set(KEFUTOKEN, res.data.data.kefu_token);
                                        }else {
                                            _this.$showToast('请检查后台客服token');
                                        }
                                    }
                                });
                            }
                        }
                    }
                });
            },
            // type:  1 文本   2 图片   5 商品  
            // 发送商品给客服
            sendProduct(chat = true) {
                let _this = this;
                if (!_this.chatStatus) {
                    return _this.$showToast('正在连接中');
                }
                let userInfo = _this.$store.state.userInfo;
                let productMessage = {};
                productMessage.store_name = _this.ProductDetail.store_name;
                productMessage.stock = _this.ProductDetail.stock ? _this.ProductDetail.stock : 0;
                productMessage.sales = _this.ProductDetail.sales ? _this.ProductDetail.sales : 0;
                productMessage.price = _this.ProductDetail.price;
                productMessage.image = _this.ProductDetail.image;
                productMessage.proUrl = ''; // 商品链接
                let obj = {
                    to_user_id: _this.chatServerData.to_user_id,
                    uid: userInfo.uid,
                    type: 5,
                    other: productMessage
                };
                if (chat) {
                    this.$refs.xChat.clear()
                }
                if (!this.networkStatus) {
                    this.scroll(80);
                    return this.$showToast('网络异常');
                }
                _this.socket.send({
                    data: JSON.stringify({
                        type: 'chat',
                        data: obj
                    }),
                    // type: 'chat',
                    success(res) {
                        // console.log('successres', res);
                    },
                    fail(err) {
                        // console.log('failerr', err);
                    }
                });
                this.isShowProductModel = false;
            },
            initWebsocket() {
                if (!checkLogin()) return null;
                let socket = this.socket,
                    _this = this;
                socket.onOpen(res => {
                    console.log('WebSocket连接已打开！');
                    _this.socketOpen = true;
                    _this.socketTj = _this.socketTj || 0;
                    _this.timer = setInterval(function() {
                        _this.socket.send({
                            data: JSON.stringify({
                                type: 'ping'
                            })
                        });
                    }, 10000);
                });
                socket.onClose(res => {
                    console.log('WebSocket 已关闭！', res);
                    _this.socketOpen = false;
                    clearInterval(_this.timer);
                    if (res.code != 1000) {
                        // 重新连接
                        _this.againSocket();
                    }
                    // console.log('监听 WebSocket 连接关闭事件。', onClose)
                });
                socket.onError(res => {
                    // console.log('WebSocket连接打开失败!', res);
                    clearInterval(_this.timer);
                    _this.socketOpen = false;
                });
                socket.onMessage(res => {
                    let data = JSON.parse(res.data);
                    // console.log('收到服务器内容', data);
                    _this.to_transferdata = data.data;
                    if (data.type === 'close') {
                        _this.chatStatus = false;
                    }
                    if (data.type === 'reply' || data.type === 'chat') {
                        _this.pushMessageToList(data.data)
                        _this.goBottom()
                        // console.log('this.scrollTop-=', this.scrollTop)
                        _this.scroll(this.scrollTop)
                    }
                    if (data.type === 'success') {
                        _this.chatStatus = true;
                        // console.log('获取客服id----',_this.chatServerData.to_user_id)
                        let socket = _this.socket;
                        let userInfo = _this.$store.state.userInfo;
                        let to_user_id = _this.chatServerData.to_user_id ? _this.chatServerData.to_user_id : 0;
                        let obj = {
                            to_user_id: to_user_id,
                            uid: userInfo.uid,
                            nickname: userInfo.nickname,
                            avatar: userInfo.avatar,
                            phone: _this.$storage.get('userInfo_phone'),
                            openid: '',
                            // 说明  0 = pc , 1 = 微信 ，2 = 小程序 ，3 = H5, 4 = APP 5 字节小程序
                            // #ifdef MP-WEIXIN
                            type: 2,
                            // #endif
                            // #ifdef H5
                            type: 3,
                            // #endif
                            // #ifdef APP
                            type: 4,
                            // #endif
                            // #ifdef MP-TOUTIAO
                            type: 5,
                            // #endif
                        }
                        socket.send({
                            data: JSON.stringify({
                                data: obj,
                                type: 'user',
                            }),
                            success(res) {
                                // console.log('确认用户信息---', res);
                            },
                            fail(err) {
                                // console.log('failerr', err);
                            }
                        });
                        if (!to_user_id) {
                            if (_this.chatServerData.to_user_id) {
                                let obj1 = {
                                    id: _this.chatServerData.to_user_id,
                                    test: 1,
                                }
                                socket.send({
                                    data: JSON.stringify({
                                        data: obj1,
                                        type: 'to_chat',
                                    }),
                                });
                            }
                        }
                    }
                    if (data.type === 'to_transfer') {
                        let data = _this.to_transferdata;
                        let to_user_id = _this.chatServerData.to_user_id;
                        _this.chatServerData.to_user_id = data.toUid
                        _this.chatServerData.to_user_nickname = data.nickname
                        _this.chatServerData.to_user_avatar = data.avatar
                        _this.chatServerData.serviceList.map(item => {
                            if (data.toUid == item.user_id) {
                                item.avatar = data.avatar
                            }
                        })
                        let obj2 = {
                            id: data.toUid,
                        }
                        socket.send({
                            data: JSON.stringify({
                                data: obj2,
                                type: 'to_chat',
                            }),
                        });
                        uni.setNavigationBarTitle({
                            title: '正在与' + data.nickname + '对话中'
                        });
                        _this.serviceOnline = true;
                    }
                });
            },
            linkSocket() {
                let _this = this;
                return uni.connectSocket({
                    url: CHAT_WS_URL + '?type=user&form=pc&token=' + this.$storage.get(KEFUTOKEN),
                    header: {
                        'Cookie': "1"
                    },
                    success: res => {
                        console.log('连接成功', res);
                    },
                    fail: err => {
                        console.log('连接失败', err);
                    }
                });
            },
            againSocket() {
                let _this = this;
                if (!this.networkStatus) return;
                let timer = setTimeout(() => {
                    console.log('重连', _this.socketTj);
                    if (_this.socketTj < _this.socketLimit) {
                        _this.socketTj++;
                        _this.socket = _this.linkSocket();
                        _this.initWebsocket();
                    } else {
                        _this.socketOpen = false;
                        clearTimeout(timer);
                    }
                }, 1000);
            },
            sendMsg(msn, type, chat = true) {
                let _this = this;
                if (_this.serviceOnline == false) {
                    _this.$showToast('暂无客服人员在线', 'none', showToast);
                    return;
                }
                let obj = {
                    msn,
                    type,
                    to_user_id: _this.chatServerData.to_user_id,
                    is_tourist: 0
                };
                if (chat) {
                    _this.$refs.xChat.clear()
                }
                if (!_this.networkStatus) {
                    _this.scroll(80);
                    return _this.$showToast('网络异常');
                }
                let guid = _this.getGuid();
                let chats = _this.chatOptinos(guid, msn, type);
                _this.sendtxt(chats)
                // _this.socket.send({
                //     data: JSON.stringify({
                //         type: 'chat',
                //         data: obj
                //     }),
                //     type: 'chat',
                //     success(res) {
                //         // console.log('successres', res);
                //     },
                //     fail(err) {
                //         // console.log('failerr', err);
                //     }
                // });
            },
            getGuid() {
                return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                    var r = Math.random() * 16 | 0,
                        v = c == 'x' ? r : (r & 0x3 | 0x8);
                    return v.toString(16);
                });
            },
            scroll(n = 999, t = 500) {
                uni.pageScrollTo({
                    scrollTop: this.scrollY + n,
                    duration: t
                });
            },
            // 预览图片
            prviewImage(prviewImage) {
                // 传入图片url
                let urls = [];
                this.records.forEach(i => {
                    if (i.msn_type == 3) {
                        urls.push(i.msn);
                    }
                });
                uni.previewImage({
                    urls: urls,
                    current: urls.indexOf(prviewImage)
                });
            },
            getmessage(){
                let _this = this;
                uni.request({
                    url: KEFU_URL + '/api/mobile/service/feedback',
                    method:'GET',
                    header: {
                        'authori-zation': 'Bearer ' + _this.$storage.get(KEFUTOKEN)
                    },
                    success: (res) => {
                        if(res.data.status == 200){
                            _this.feedback = res.data.data.feedback;
                        }else {
                            _this.feedback = '客服不在线，请留言！';
                        }
                    }
                });
            },
            // 提交留言
            savemessage(){
                let _this = this;
                if(_this.userName == ''){
                    return _this.$showToast('请输入您的姓名');
                }
                if (!RegPhone(_this.userPhone)) {
                    return _this.$showToast(_this.userPhone.length ? '请填写正确手机号' : '请填写手机号');
                }
                if(_this.userContent == ''){
                    return _this.$showToast('请输入您的留言信息');
                }
                uni.request({
                    url: KEFU_URL + '/api/mobile/service/feedback',
                    method:'POST',
                    header: {
                        'authori-zation': 'Bearer ' + this.$storage.get(KEFUTOKEN)
                    },
                    data: {
                        content: _this.userContent,
                        phone: _this.userPhone,
                        rela_name: _this.userName,
                    },
                    success: (res) => {
                        // console.log('res1111',res.data)
                        let showToast = {};
                        showToast.duration = 2000;
                        if(res.data.status == 200){
                            _this.$showToast(res.data.msg, 'none', showToast);
                            setTimeout(() => {
                                _this.$navigator(-1)
                            }, 2000);
                        }else {
                            _this.$showToast('提交失败，请稍后再试','none', showToast);
                            setTimeout(() => {
                                _this.$navigator(-1)
                            }, 2000);
                        }
                    }
                });
            },
            sendtxt(data){
                let _this = this;
                uni.request({
                    url: KEFU_URL + '/api/mobile/service/send_message',
                    method:'POST',
                    header: {
                        'authori-zation': 'Bearer ' + _this.$storage.get(KEFUTOKEN)
                    },
                    mobile: true,
                    data,
                    success: (res) => {
                        if(res.data.status == 200){
                            data.add_time = Date.parse(new Date()) / 1000;
                            _this.pushMessageToList(data);
                            if (res.data.data.autoReply === true) {
                                _this.pushMessageToList(res.data.data.autoReplyData);
                            }
                            _this.goBottom()
                            _this.scroll(_this.scrollTop)
                        }
                    }
                });
            },
            pushMessageToList(data) {
                this.chatServerData.serviceList.push(data);
            },
            chatOptinos(guid, msn, type, other) {
                return {
                    msn,
                    msn_type: type,
                    to_user_id: this.chatServerData.to_user_id,
                    is_send: 0,
                    is_tourist: 0,
                    avatar: this.chatServerData.avatar,
                    user_id: this.chatServerData.user_id,
                    appid: this.chatServerData.appid,
                    other: other || {},
                    type: 0,
                    guid: guid
                };
            },
            
        },
        onPageScroll(e) {
            this.wScr = e.scrollTop;
        },
        onPullDownRefresh() {
            this.initData(); // 获取聊天记录
            uni.stopPullDownRefresh();
        },
    };
</script>

<style>
    page {
        /* background-color: #FFFFFF !important; */
        padding: 0 !important;
    }
    .chat_room {
        padding-top: 20rpx;
        box-sizing: border-box;
    }

    .chart_list {
        position: relative;
        z-index: 2;
    }

    .chart_list_item_content {
        display: flex;
        align-items: center;
        padding: 16rpx;
    }

    .chart_list_item_avatar {
        width: 66rpx;
        height: 66rpx;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 20rpx;
        align-self: flex-start;
    }

    .chart_list_item_avatar image {
        width: 100%;
        height: 100%;
        border-radius: 50%;
    }

    .chart_list_item_text {
        min-width: 60rpx;
        // height: 60rpx;
        max-width: 60%;
        word-wrap: break-word;
        background: #fff;
        padding: 9rpx 11rpx 9rpx 12rpx;
        font-size: 30rpx;
        border-radius: 12rpx;
        text-align: left;
        background: #cde0ff;
        color: #000;
        overflow: auto;
    }

    .chart_list_item_text text {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    .chart_list_item_img {
        max-width: 60%;
        width: 400rpx;
        height: auto;
    }

    .chart_list_item_img image {
        width: 100%;
        border-radius: 12rpx;
    }

    .chart_list_item_imgOrText {
        max-width: 90%;
        background: #fff;
        padding: 20rpx;
        border-radius: 16rpx;
        box-sizing: border-box;
    }

    .chart_list_item_imgOrText .order-wrapper .img-box {
        float: left;
        width: 140rpx;
        height: 140rpx;
        border-radius: 10rpx;
    }

    .chart_list_item_imgOrText .order-wrapper .img-box image {
        width: 100%;
        border-radius: 10rpx;
    }

    .chart_list_item_imgOrText .order-wrapper .order-info {
        float: left;
        width: 336rpx;
        padding-left: 28rpx;
        box-sizing: border-box;
    }

    .chart_list_item_imgOrText .order-wrapper .order-info .price-box {
        color: #ff0000;
        font-size: 18px;
    }

    .chart_list_item .chart_list_item_imgOrText .order-wrapper .order-info .name {
        font-size: 14px;
    }

    .chart_list_item_time {
        text-align: center;
        margin: 20rpx auto;
    }

    .chart_list_item .right-box {
        flex-direction: row-reverse;
    }

    .chart_list_item_avatar {
        margin-left: 20rpx;
    }

    .chart_list_item .right-box .chart_list_item_img {
        text-align: right;
    }

    .chart_list_item .right-box .chart_list_item_img image {
        width: 100%;
    }


    .chart_list_item_imgOrText .order-wrapper .order-info .price-box {
        color: #ff0000;
        font-size: 18px;
    }

    .chart_list_item_imgOrText .order-wrapper .order-info .name {
        font-size: 14px;
    }


    .productMessage_container {
        width: 90%;
        padding: 24rpx;
        box-sizing: border-box;
        background: #fff;
        display: flex;
        margin: 30rpx auto 0;
        border-radius: 10rpx;
    }

    .productMessage_container_image {
        margin-right: 24rpx;
        border-radius: 6rpx;
    }

    .productMessage_container_image image {
        display: block;
        width: 154rpx;
        height: 154rpx;
        border-radius: 6rpx;
        margin: 0 auto;
    }

    .productMessage_container_content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .productMessage_container_content_title {
        font-size: 28rpx;
        color: #333;
        height: 84rpx;
        font-weight: 800;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        text-align: left !important;
    }

    .productMessage_container_content_priceOrHandle {
        display: flex;
        justify-content: space-between;
    }

    .productMessage_container_content_priceOrHandle>view:nth-child(1) {
        font-size: 36rpx;
        color: #e93323;
        text-align: left;
    }

    .productMessage_container_content_priceOrHandle>view:nth-child(2) {
        width: 130rpx;
        height: 50rpx;
        background: #e83323;
        opacity: 1;
        border-radius: 124rpx;
        color: #fff;
        font-size: 24rpx;
        text-align: center;
        line-height: 50rpx;
        cursor: pointer;
    }
    .not-online-title {
        width: 100%;
        font-size: 26rpx;
            color: #333;
            margin-bottom: 52rpx;
    }
    .not-online {
        width: 100%;
        padding: 0 30rpx;
        box-sizing: border-box;
        background: #fff;
            height: 100vh;
    }
    .user-name {
        width: 100%;
        border-radius: 6rpx;
        border: 2rpx solid #ececec;
        outline: none;
        font-size: 26rpx;
        padding-left: 28rpx;
        padding-top: 14rpx;
        height: 80rpx;
        line-height: 80rpx;
        margin-bottom: 36rpx;
        box-sizing: border-box;
    }
    .user-content {
        width: 100%;
        border-radius: 6rpx;
        border: 2rpx solid #ececec;
        outline: none;
        font-size: 26rpx;
        box-sizing: border-box;
        padding: 28rpx;
        overflow: auto;
        height: auto;
        margin-bottom: 36rpx;
    }
    .user-content-text {
        width: 100%;
        min-height: 450rpx;
    }
    .save-message {
        width: 100%;
        height: 80rpx;
        line-height: 80rpx;
            background: #3875ea;
            border-radius: 6rpx;
            color: #fff;
            font-size: 26rpx;
            text-align: center;
    }
</style>
