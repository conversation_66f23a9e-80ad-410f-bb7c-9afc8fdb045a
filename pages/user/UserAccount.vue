<template>
	<view class="my-account">
		<view class="wrapper">
			<view class="header">
				<view class="headerCon">
					<view class="account acea-row row-top row-between">
						<view class="assets">
							<view>总资产(元)</view>
							<view class="money">{{ now_money }}</view>
						</view>
						<view @click="goRecharge('/pages/user/Recharge')" class="recharge font-color-red">充值</view>
					</view>
					<view class="cumulative acea-row row-top">
						<view class="item">
							<view>累计充值(元)</view>
							<view class="money">{{ recharge }}</view>
						</view>
						<view class="item">
							<view>累计消费(元)</view>
							<view class="money">{{ orderStatusSum }}</view>
						</view>
					</view>
				</view>
			</view>
			<view class="nav acea-row row-middle">
				<view class="item" @click="goPages('/pages/user/UserBill?type=0')">
					<view class="pictrue"><image src="@/static/images/record1.png" /></view>
					<view>账单记录</view>
				</view>
				<view class="item" @click="goPages('/pages/user/UserBill?type=1')">
					<view class="pictrue"><image src="@/static/images/record2.png" /></view>
					<view>消费记录</view>
				</view>
				<view class="item" @click="goPages('/pages/user/UserBill?type=2')">
					<view class="pictrue"><image src="@/static/images/record3.png" /></view>
					<view>充值记录</view>
				</view>
				<view class="item" @click="goPages('/pages/user/signIn/Integral')">
					<view class="pictrue"><image src="@/static/images/record4.png" /></view>
					<view>积分中心</view>
				</view>
			</view>
			<view class="advert acea-row row-between-wrapper">
				<view class="item acea-row row-between-wrapper" @click="goPages('/pages/user/signIn/Sign')">
					<view class="text">
						<view class="name">签到领积分</view>
						<view>赚积分抵现金</view>
					</view>
					<view class="pictrue"><image src="@/static/images/gift.png" /></view>
				</view>
				<view class="item on acea-row row-between-wrapper" @click="goPages('/pages/user/coupon/GetCoupon')">
					<view class="text">
						<view class="name">领取优惠券</view>
						<view>满减享优惠</view>
					</view>
					<view class="pictrue"><image src="@/static/images/money.png" /></view>
				</view>
			</view>
			<view class="list">
				<view class="item acea-row row-between-wrapper">
					<view class="picTxt acea-row row-between-wrapper">
						<view class="iconfont icon-hebingxingzhuang"></view>
						<view class="text">
							<view class="line1">最新拼团活动</view>
							<view class="infor line1">最新的优惠商品上架拼团</view>
						</view>
					</view>
					<view class="bnt" @click="goPages('/pages/activity/GoodsGroup')" v-if="activity.is_pink">立即参与</view>
					<view class="bnt end" v-else>已结束</view>
				</view>
				<view class="item acea-row row-between-wrapper">
					<view class="picTxt acea-row row-between-wrapper">
						<view class="iconfont icon-miaosha yellow"></view>
						<view class="text">
							<view class="line1">当前限时秒杀</view>
							<view class="infor line1">最新商品秒杀进行中</view>
						</view>
					</view>
					<view class="bnt" @click="goPages('/pages/activity/GoodsSeckill')" v-if="activity.is_seckill">立即参与</view>
					<view class="bnt end" v-else>已结束</view>
				</view>
				<view class="item acea-row row-between-wrapper">
					<view class="picTxt acea-row row-between-wrapper">
						<view class="iconfont icon-kanjia1 green"></view>
						<view class="text">
							<view class="line1">砍价活动</view>
							<view class="infor line1">呼朋唤友来砍价</view>
						</view>
					</view>
					<view class="bnt" @click="goPages('/pages/activity/GoodsBargain')" v-if="activity.is_bargin">立即参与</view>
					<view class="bnt end" v-else>已结束</view>
				</view>
			</view>
		</view>
		<Recommend></Recommend>
		<x-home></x-home>
	</view>
</template>
<script>
import Recommend from '@/components/Recommend';
import { getActivityStatus, getBalance } from '@/api/user';
import { openRechargeSubscribe } from '@/utils/SubscribeMessage.js';
export default {
	name: 'UserAccount',
	components: {
		Recommend
	},
	props: {},
	data: function() {
		return {
			now_money: 0,
			orderStatusSum: 0,
			recharge: 0,
			activity: {
				is_bargin: false,
				is_pink: false,
				is_seckill: false
			}
		};
	},
	mounted: function() {
		this.getIndex();
		this.getActivity();
	},
	methods: {
		goPages(path, type) {
			this.$authNavigator(path, type);
		},
		goRecharge(path, type){
			openRechargeSubscribe().then(()=>{
				this.$authNavigator(path, type);
			})
		},
		getIndex: function() {
			let that = this;
			getBalance().then(
				res => {
					that.now_money = res.data.now_money;
					that.orderStatusSum = res.data.orderStatusSum;
					that.recharge = res.data.recharge;
				},
				err => {
					that.$dialog.message(err.msg || err);
				}
			);
		},
		getActivity: function() {
			let that = this;
			getActivityStatus().then(
				res => {
					that.activity.is_bargin = res.data.is_bargin;
					that.activity.is_pink = res.data.is_pink;
					that.activity.is_seckill = res.data.is_seckill;
				},
				error => {
					that.$dialog.message(error.msg);
				}
			);
		}
	}
};
</script>
<style scoped>
.my-account .wrapper {
	background-color: #fff;
	padding: 32rpx 0 34rpx 0;
	margin-bottom: 14rpx;
}

.my-account .wrapper .header {
	width: 690rpx;
	height: 330rpx;
	background-image: linear-gradient(to right, #f33b2b 0%, #f36053 100%);
	background-image: -webkit-linear-gradient(to right, #f33b2b 0%, #f36053 100%);
	background-image: -moz-linear-gradient(to right, #f33b2b 0%, #f36053 100%);
	border-radius: 16rpx;
	margin: 0 auto;
	color: rgba(255, 255, 255, 0.6);
	font-size: 24rpx;
}

.my-account .wrapper .header .headerCon {
	background-image: url('@/static/images/accountBg.png');
	background-repeat: no-repeat;
	background-size: 100%;
	height: 100%;
	width: 100%;
	padding: 36rpx 0 28rpx 0;
}

.my-account .wrapper .header .headerCon .account {
	padding: 0 35rpx;
}

.my-account .wrapper .header .headerCon .account .assets .money {
	font-size: 72rpx;
	color: #fff;
	font-family: 'GuildfordProBook 5';
	margin-top: 10rpx;
	height: 75rpx;
	line-height: 75rpx;
}

.my-account .wrapper .header .headerCon .account .recharge {
	font-size: 28rpx;
	width: 150rpx;
	height: 54rpx;
	border-radius: 27rpx;
	background-color: #fff9f8;
	text-align: center;
	line-height: 54rpx;
}

.my-account .wrapper .header .headerCon .cumulative {
	margin-top: 46rpx;
}

.my-account .wrapper .header .headerCon .cumulative .item {
	flex: 1;
	-webkit-flex: 1;
	-o-flex: 1;
	-ms-flex: 1;
	padding-left: 35rpx;
}

.my-account .wrapper .header .headerCon .cumulative .item .money {
	font-size: 48rpx;
	font-family: 'GuildfordProBook 5';
	color: #fff;
	margin-top: 6rpx;
}

.my-account .wrapper .nav {
	height: 155rpx;
	border-bottom: 1px solid #f5f5f5;
}

.my-account .wrapper .nav .item {
	flex: 1;
	-webkit-flex: 1;
	-o-flex: 1;
	-ms-flex: 1;
	text-align: center;
	font-size: 26rpx;
	color: #999;
}

.my-account .wrapper .nav .item .pictrue {
	width: 44rpx;
	height: 44rpx;
	margin: 0 auto;
	margin-bottom: 20rpx;
}

.my-account .wrapper .nav .item .pictrue image {
	width: 100%;
	height: 100%;
}

.my-account .wrapper .advert {
	padding: 0 30rpx;
	margin-top: 30rpx;
}

.my-account .wrapper .advert .item {
	background-color: #fff6d1;
	width: 332rpx;
	height: 118rpx;
	border-radius: 10rpx;
	padding: 0 27rpx 0 25rpx;
	font-size: 24rpx;
	color: #e44609;
}

.my-account .wrapper .advert .item.on {
	background-color: #fff3f3;
	color: #e96868;
}

.my-account .wrapper .advert .item .pictrue {
	width: 78rpx;
	height: 78rpx;
}

.my-account .wrapper .advert .item .pictrue image {
	width: 100%;
	height: 100%;
}

.my-account .wrapper .advert .item .text .name {
	font-size: 30rpx;
	font-weight: bold;
	color: #f33c2b;
	margin-bottom: 7rpx;
}

.my-account .wrapper .advert .item.on .text .name {
	color: #f64051;
}

.my-account .wrapper .list {
	padding: 0 30rpx;
}

.my-account .wrapper .list .item {
	margin-top: 44rpx;
}

.my-account .wrapper .list .item .picTxt .iconfont {
	width: 82rpx;
	height: 82rpx;
	border-radius: 50%;
	background-image: linear-gradient(to right, #ff9389 0%, #f9776b 100%);
	background-image: -webkit-linear-gradient(to right, #ff9389 0%, #f9776b 100%);
	background-image: -moz-linear-gradient(to right, #ff9389 0%, #f9776b 100%);
	text-align: center;
	line-height: 82rpx;
	color: #fff;
	font-size: 40rpx;
}

.my-account .wrapper .list .item .picTxt .iconfont.yellow {
	background-image: linear-gradient(to right, #ffccaa 0%, #fea060 100%);
	background-image: -webkit-linear-gradient(to right, #ffccaa 0%, #fea060 100%);
	background-image: -moz-linear-gradient(to right, #ffccaa 0%, #fea060 100%);
}

.my-account .wrapper .list .item .picTxt .iconfont.green {
	background-image: linear-gradient(to right, #a1d67c 0%, #9dd074 100%);
	background-image: -webkit-linear-gradient(to right, #a1d67c 0%, #9dd074 100%);
	background-image: -moz-linear-gradient(to right, #a1d67c 0%, #9dd074 100%);
}

.my-account .wrapper .list .item .picTxt {
	width: 428rpx;
	font-size: 30rpx;
	color: #282828;
}

.my-account .wrapper .list .item .picTxt .text {
	width: 317rpx;
}

.my-account .wrapper .list .item .picTxt .text .infor {
	font-size: 24rpx;
	color: #999;
	margin-top: 5rpx;
}

.my-account .wrapper .list .item .bnt {
	font-size: 26rpx;
	color: #282828;
	width: 156rpx;
	height: 52rpx;
	border: 1px solid #ddd;
	border-radius: 26rpx;
	text-align: center;
	line-height: 50rpx;
}

.my-account .wrapper .list .item .bnt.end {
	font-size: 26rpx;
	color: #aaa;
	background-color: #f2f2f2;
	border-color: #f2f2f2;
}
</style>
