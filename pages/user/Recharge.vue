<template>
	<view class="rechargeBox">
		<view class="payment-top acea-row row-column row-center-wrapper">
			<span class="name">我的余额</span>
			<view class="pic">
				￥
				<span class="pic-font">{{ now_money || 0 }}</span>
			</view>
		</view>
		<view class="recharge">
			<view class="nav acea-row row-around row-middle">
				<view class="item" :class="active === index ? 'on' : ''" v-for="(item, index) in navRecharge" :key="index" @click="navRecharges(index)">{{ item }}</view>
			</view>
			<view class="info-wrapper">
				<view v-if="active">
					<view class="money">
						<span>￥</span>
						<input type="number" placeholder="0.00" v-model="money" />
					</view>
					<view class="tip-box">
						<span class="tip">提示：</span>
						<view class="tip-samll">
							当前可转入佣金：
							<span class="font-color">￥{{ userInfo.commissionCount || 0 }}</span>
						</view>
					</view>
				</view>
				<view class="picList acea-row row-between mt-20" v-if="!active">
					<view
						class="pic-box pic-box-color acea-row row-center-wrapper row-column"
						:class="activePic == index ? 'pic-box-color-active' : ''"
						v-for="(item, index) in picList"
						:key="index"
						@click="picCharge(index, item)"
					>
						<view class="pic-number-pic">
							{{ item.price }}
							<span class="pic-number">元</span>
						</view>
						<view class="pic-number">赠送：{{ item.give_money }} 元</view>
					</view>
					<view class="pic-box pic-box-color acea-row row-center-wrapper" :class="activePic == picList.length ? 'pic-box-color-active' : ''" @click="picCharge(picList.length)">
						<input type="number" placeholder="其他" v-model="money" class="pic-box-money pic-number-pic" :class="activePic == picList.length ? 'pic-box-color-active' : ''" />
					</view>
				</view>
				<view class="acea-row row-column">
					<view class="tip mt-30">注意事项：</view>
					<view class="tip-samll" v-for="item in rechargeAttention" :key="item">{{ item }}</view>
				</view>
				<view class="pay-btn bg-color-red" @click="recharge">{{ active ? '立即转入' : '立即充值' }}</view>
			</view>
		</view>
		<x-home></x-home>
	</view>
</template>
<script>
import { mapGetters } from 'vuex';

import { rechargeWechat, getRechargeApi } from '@/api/user';
import { add, sub } from '@/utils/bc';

// #ifdef H5 
import { isWeixin } from '@/utils/validate.js';
import { pay } from '@/utils/wechat/pay.js';
const _isWeixin = isWeixin();
// #endif

// #ifdef MP
const _isWeixin = true;
// #endif
export default {
	name: 'Recharge',
	props: {},
	data: function() {
		return {
			navRecharge: ['账户充值', '佣金导入'],
			active: 0,
			payType: ['weixin'],
			// #ifdef MP
			from: 'routine',
			// #endif
			// #ifdef H5 
			from: _isWeixin ? 'weixin' : 'weixinh5',
			// #endif
			money: '',
			now_money: '',
			picList: [],
			activePic: 0,
			numberPic: '',
			rechar_id: 0,
			rechargeAttention: []
		};
	},
	computed: mapGetters(['userInfo']),
	mounted: function() {
		this.now_money = this.userInfo.now_money;
		this.getRecharge();
	},
	methods: {
		/**
		 * 充值额度选择
		 */
		getRecharge() {
			getRechargeApi()
				.then(res => {
					this.picList = res.data.recharge_quota;
					if (this.picList[0]) {
						this.rechar_id = this.picList[0].id;
						this.numberPic = this.picList[0].price;
					}
					this.rechargeAttention = res.data.recharge_attention || [];
				})
				.catch(res => {
					that.$showToast(res.msg || res)
				});
		},
		/**
		 * 选择金额
		 */
		picCharge(idx, item) {
			this.activePic = idx;
			if (item === undefined) {
				this.rechar_id = 0;
				this.numberPic = '';
			} else {
				this.money = '';
				this.rechar_id = item.id;
				this.numberPic = item.price;
			}
		},
		navRecharges: function(index) {
			this.active = index;
			this.rechar_id = this.picList[0].id;
			this.numberPic = this.picList[0].price;
			this.activePic = 0;
			this.money = '';
		},
		recharge: function() {
			let that = this,
				price = Number(this.money);
			if (that.active) {
				if (price === 0) {
					return that.$showToast('请输入您要转入的金额');
				} else if (price < 0.01) {
					return that.$showToast('转入金额不能低于0.01');
				} else if (price > this.userInfo.commissionCount) {
					return that.$showToast('转入金额不能大于可提现佣金');
				}
				that.$showModal('转入余额', '转入余额无法在转出，请确认转入', {
					confirmText: '确认',
					cancelText: '取消',
					success() {
						rechargeWechat({ price: price, from: that.from, type: 1 })
							.then(res => {
								that.now_money = add(price, parseInt(that.now_money));
								that.userInfo.commissionCount = sub(that.userInfo.commissionCount, price);
								that.money = '';
								return that.$showToast(res.msg);
							})
							.catch(res => {
								return that.$showToast(res.msg);
							});
					},
					fail() {
						return that.$showToast('已取消');
					}
				});
			} else {
				if (this.picList.length == this.activePic && price === 0) {
					return that.$showToast('请输入您要充值的金额');
				} else if (this.picList.length == this.activePic && price < 0.01) {
					return that.$showToast('充值金额不能低于0.01');
				}
				rechargeWechat({
					price: that.rechar_id == 0 ? that.money : that.numberPic,
					from: that.from,
					type: 0,
					rechar_id: that.rechar_id
				})
					.then(res => {
						let data = res.data;
						//  #ifdef H5
						if (data.type == 'weixinh5') {
							location.replace(data.data.mweb_url);
							setTimeout(() => {
								that.$showModal('提示', '充值余额', {
									confirmText: '已充值',
									cancelText: '查看余额',
									success() {
										that.$navigator('/pages/user/UserAccount', 'redirectTo');
									},
									fail() {
										that.$navigator('/pages/user/UserAccount', 'redirectTo');
									}
								});
							}, 500);							
						} else {
							pay(data.data)
								.finally(() => {
									that.now_money = add(price, parseInt(that.userInfo.now_money));
									that.$showToast('支付成功');
								})
								.catch(function() {
									that.$showToast('支付失败');
								});
						}
						//  #endif
						//  #ifdef MP
						uni.requestPayment({
							timeStamp: data.timestamp,
							nonceStr: data.nonceStr,
							package: data.package,
							signType: data.signType,
							paySign: data.paySign,
							success: function(res) {
								that.$showToast('支付成功');
							},
							fail: function(e) {
								that.$showToast('支付失败');
							},
							complete: function(e) {
								//关闭当前页面跳转至订单状态
							}
						});
						//  #endif
					})
					.catch(res => {
						that.$showToast(res.msg || res);
					});
			}
		}
	}
};
</script>
<style scoped>
.rechargeBox {
	height: 100%;
	background: #fff;
}

.pic-box-color-active {
	background-color: #ec3323 !important;
	color: #fff !important;
}
.pic-box-active {
	width: 216rpx;
	height: 120rpx;
	background-color: #ec3323;
	border-radius: 20rpx;
}
.picList {
	margin-bottom: 30rpx;
	margin-top: 30rpx;
}
.font-color {
	color: #e83323;
}
.recharge {
	border-radius: 10rpx;
	width: 100%;
	background-color: #fff;
	margin: 20rpx auto 0 auto;
	padding: 30rpx;
	border-top-right-radius: 39rpx;
	border-top-left-radius: 39rpx;
	margin-top: -45rpx;
	box-sizing: border-box;
}
.recharge .nav {
	height: 75rpx;
	line-height: 75rpx;
	padding: 0 100rpx;
}
.recharge .nav .item {
	font-size: 30rpx;
	color: #333;
}
.recharge .nav .item.on {
	font-weight: bold;
	border-bottom: 4rpx solid #e83323;
}
.recharge .info-wrapper {
}
.recharge .info-wrapper .money {
	margin-top: 60rpx;
	padding-bottom: 20rpx;
	border-bottom: 1px dashed #ddd;
	text-align: center;
}
.recharge .info-wrapper .money span {
	font-size: 56rpx;
	color: #333;
	font-weight: bold;
}
.recharge .info-wrapper .money input {
	display: inline-block;
	width: 300rpx;
	font-size: 84rpx;
	text-align: center;
	color: #282828;
	font-weight: bold;
	padding-right: 70rpx;
	height: 96rpx;
	line-height: 96rpx;
	min-height: auto;
}
.recharge .info-wrapper .money input::placeholder {
	color: #ddd;
}
.tip {
	font-size: 28rpx;
	color: #333333;
	font-weight: 800;
	margin-bottom: 14rpx;
}
.tip-samll {
	font-size: 24rpx;
	color: #333333;
	margin-bottom: 14rpx;
}
.tip-box {
	margin-top: 30rpx;
}
.recharge .info-wrapper .tips span {
	color: #ef4a49;
}
.recharge .info-wrapper .pay-btn {
	display: block;
	width: 100%;
	height: 86rpx;
	margin: 50rpx auto 0 auto;
	line-height: 86rpx;
	text-align: center;
	color: #fff;
	border-radius: 50rpx;
	font-size: 30rpx;
	font-weight: bold;
}
.payment-top {
	width: 100%;
	height: 350rpx;
	background-color: #e83323;
}
.payment-top .name {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
	margin-top: -38rpx;
	margin-bottom: 30rpx;
}
.payment-top .pic {
	font-size: 32rpx;
	color: #fff;
}
.payment-top .pic-font {
	font-size: 78rpx;
	color: #fff;
}
.picList .pic-box {
	width: 32%;
	height: auto;
	border-radius: 20rpx;
	margin-top: 21rpx;
	padding: 20rpx 0;
}
.pic-box-color {
	background-color: #f4f4f4;
	color: #656565;
}
.pic-number {
	font-size: 22rpx;
}
.pic-number-pic {
	font-size: 38rpx;
	margin-right: 10rpx;
	text-align: center;
}
.pic-box-money {
	width: 100%;
	display: block;
}
</style>
