<!-- <template>
	<view class="member-center">
		<view class="header">
			<view class="slider-banner banner">
				<xSwiper :arr="vipList" srcName="image">
					<template v-slot="{ item }">
						<view class="swiper-slide memberBg" :class="item.class" :style="{ backgroundImage: 'url(' + item.image + ')' }">
							<view class="name">{{ item.name }}</view>
							<view class="discount">
								可享受商品折扣: {{ item.discount / 10 }}折
								<span class="iconfont icon-zhekou"></span>
							</view>
							<view class="nav acea-row" v-if="item.grade == grade">
								<view class="item" v-for="(val, indexn) in vipComplete" :key="indexn">
									<view class="num">{{ val.number }}</view>
									<view>{{ val.real_name }}</view>
								</view>
							</view>
							<view class="lock" v-if="item.grade > grade">
								<span class="iconfont icon-quanxianguanlisuozi"></span>
								该会员等级尚未解锁
							</view>
							<view class="lock" v-if="item.grade < grade">
								<span class="iconfont icon-xuanzhong1"></span>
								已解锁更高等级
							</view>
						</view>
					</template>
				</xSwiper>
			</view>
		</view>
		<view class="wrapper">
			<view class="title acea-row row-between-wrapper">
				<view>
					<span class="iconfont icon-jingyanzhi"></span>
					会员升级要求
				</view>
				<view class="num">
					<span class="current">{{ taskCount }}</span>
					/{{ vipRequire.length }}
				</view>
			</view>
			<view class="list">
				<view class="item" v-for="(item, index) in vipComplete" :key="index">
					<view class="top acea-row row-between-wrapper">
						<view class="name">
							{{ item.name }}
							<span class="iconfont icon-wenti" v-if="item.illustrate" @click="showGrow(item)"></span>
						</view>
						<view>{{ item.finish ? '已满足条件' : '未满足条件' }}</view>
					</view>
					<view class="cu-progress"><view class="bg-red" :style="{ width: item.speed + '%' }"></view></view>
					<view class="experience acea-row row-between-wrapper">
						<view>{{ item.task_type_title }}</view>
						<view>
							<span class="num">{{ item.new_number }}</span>
							/{{ item.number }}
						</view>
					</view>
				</view>
			</view>
		</view>
		<Recommend></Recommend>
		<x-home></x-home>
		<view class="growthValue" :class="growthValue === false ? 'on' : ''">
			<view class="pictrue">
				<image :src="imagePath + '/wximage/value.jpg'" />
				<span class="iconfont icon-guanbi3" @click="growthTap"></span>
			</view>
			<view class="conter">{{ illustrate }}</view>
		</view>
		<view class="mask" :hidden="growthValue" @click="growthTap"></view>
	</view>
</template>
<script>
import xSwiper from '@/components/x-swiper/x-swiper.vue';
import Recommend from '@/components/Recommend';
import { getVipInfo, getVipTask, setDetection } from '../../api/user';
import { VUE_APP_URL } from '@/config.js';
export default {
	name: 'Poster',
	components: {
		xSwiper,
		Recommend
	},
	props: {},
	data: function() {
		return {
			imagePath: VUE_APP_URL,
			vipList: [], //等级列表
			vipRequire: [], //等级要求
			vipComplete: [], //完成情况
			taskCount: 0, //任务数
			grade: 0, //当前会员等级
			swiperVip: {
				speed: 1000,
				effect: 'coverflow',
				slidesPerView: 'auto',
				centeredSlides: true,
				// loop: true,
				coverflowEffect: {
					rotate: 0, // 旋转的角度
					stretch: -20, // 拉伸   图片间左右的间距和密集度
					depth: 100, // 深度   切换图片间上下的间距和密集度
					modifier: 2, // 修正值 该值越大前面的效果越明显
					slideShadows: false // 页面阴影效果
				},
				observer: true,
				observeParents: true
			},
			loading: false,
			growthValue: true,
			illustrate: '',
			activeIndex: 0
		};
	},
	watch: {
		vipList: function() {
			let that = this,
				gradeArray = [];
			if (that.vipList.length > 0) {
				that.vipList.forEach(function(item, index) {
					if (item.is_clear === true) {
						that.activeIndex = index;
						// that.grade = item.grade;
						gradeArray.push(item.grade);
					}
				});
			}
			if (gradeArray.length) {
				that.grade = gradeArray[0];
			}
		}
	},
	computed: {},
	mounted: function() {
		let that = this;
		setDetection();
		that.getInfo();
	},
	methods: {
		growthTap: function() {
			this.growthValue = true;
		},
		getInfo: function() {
			let that = this;
			getVipInfo().then(
				res => {
					that.vipList = res.data.list;
					that.vipRequire = res.data.task.list;
					that.vipComplete = res.data.task.task;
					that.taskCount = res.data.task.reach_count;
					that.getTask()
				},
				err => {
					that.$dialog.message(err.msg || err);
				}
			);
		},
		getTask: function() {
			let that = this;
			getVipTask(that.vipList[that.activeIndex].id).then(
				res => {
					that.vipRequire = res.data.list;
					that.vipComplete = res.data.task;
					that.taskCount = res.data.reach_count;
				},
				err => {
					that.$dialog.message(err.msg || err);
				}
			);
		},
		showGrow: function(item) {
			if (this.illustrate != item.illustrate) this.illustrate = item.illustrate;
			this.growthValue = false;
		}
	}
};
</script>
<style lang="scss" scoped>
.slider-banner {
	height: 330rpx;
}
.growthValue {
	background-color: #fff;
	border-radius: 16rpx;
	position: fixed;
	top: 266rpx;
	left: 50%;
	width: 560rpx;
	height: 740rpx;
	margin-left: -280rpx;
	z-index: 99;
	transform: translate3d(0, -200%, 0);
	transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
	&.on {
		transform: translate3d(0, 0, 0);
	}
	.pictrue {
		width: 100%;
		height: 257rpx;
		position: relative;
		image {
			width: 100%;
			height: 100%;
			border-radius: 16rpx 16rpx 0 0;
		}
		.iconfont {
			position: absolute;
			font-size: 65rpx;
			color: #fff;
			top: 775rpx;
			left: 50%;
			transform: translateX(-50%);
		}
	}
	.conter {
		padding: 0 35rpx;
		font-size: 30rpx;
		color: #333;
		margin-top: 58rpx;
		line-height: 1.5;
		height: 350rpx;
		overflow: auto;
	}
}
</style>
 -->