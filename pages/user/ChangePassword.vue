<template>
	<view class="ChangePassword">
		<view class="phone">
			<view>
				当前手机号:
				<text style="font-weight:normal;padding-left:10rpx">{{ phone }}</text>
			</view>
		</view>
		<view class="list">
			<view class="item"><input type="password" placeholder="设置新密码" v-model="password" /></view>
			<view class="item"><input type="password" placeholder="确认新密码" v-model="password2" /></view>
			<view class="item acea-row row-between-wrapper">
				<input type="text" placeholder="填写验证码" class="codeIput" v-model="captcha" />
				<button class="code font-color-red" :disabled="disabled" :class="disabled === true ? 'on' : ''" @click="code">{{ text }}</button>
			</view>
			<view class="item acea-row row-between-wrapper" v-if="isShowCode">
				<input type="text" placeholder="填写验证码" class="codeIput" v-model="codeVal" />
				<view class="codeVal" @click="again"><image :src="codeUrl" /></view>
			</view>
		</view>
		<view class="confirmBnt bg-color-red" @click="confirm">确认修改</view>
	</view>
</template>

<script>
import { mapGetters } from 'vuex';
import { registerReset, registerVerify, getUserInfo, getCodeApi } from '@/api/user';
import { VUE_APP_API_URL } from '@/config.js';
import { RegPhone } from '@/utils/validate.js';
// #ifdef H5
import sendVerifyCode from '@/mixins/SendVerifyCode';
//  #endif
export default {
	name: 'ChangePassword',
	components: {},
	props: {},
	data: function() {
		return {
			password: '',
			password2: '',
			captcha: '',
			phone: '', //隐藏几位数的手机号；
			yphone: '', //手机号；
			keyCode: '',
			codeUrl: '',
			codeVal: '',
			isShowCode: false
		};
	},
	// #ifdef H5
	mixins: [sendVerifyCode],
	//  #endif
	computed: mapGetters(['userInfo']),
	mounted: function() {
		this.getCode();
		this.getUserInfo();
	},
	methods: {
		again() {
			this.codeUrl = VUE_APP_API_URL + '/sms_captcha?' + 'key=' + this.keyCode + Date.parse(new Date());
			console.log(this.codeUrl);
		},
		getCode() {
			getCodeApi()
				.then(res => {
					this.keyCode = res.data.key;
				})
				.catch(res => {
					this.$showToast(res.msg || res);
				});
		},
		getUserInfo: function() {
			let that = this;
			getUserInfo().then(res => {
				that.yphone = res.data.phone;
				let reg = /^(\d{3})\d*(\d{4})$/;
				that.phone = that.yphone.replace(reg, '$1****$2');
			});
		},
		async confirm() {
			let that = this;
			const { password, password2, captcha, codeVal } = that;
			if (!password.trim().length) return that.$showToast('请设置新密码');
			if (password !== password2) return that.$showToast('两次密码不一致');
			if (password.trim().length < 6) return that.$showToast('密码至少6位');
			if (!captcha.trim().length) {
				return that.$showToast('请输入验证码');
			}
			if (this.isShowCode && !codeVal.trim().length) {
				return that.$showToast('请输入图形码');
			}
			registerReset({
				account: that.yphone,
				captcha: that.captcha,
				password: that.password,
				code: that.codeVal
			})
				.then(res => {
					that.$successToast(res.msg);
					that.$navigator('/pages/login/login', 'redirectTo');
				})
				.catch(res => {
					that.$showToast(res.msg || res);
				});
		},
		async code() {
			let that = this;
			const { yphone, codeVal } = that;
			registerVerify({ phone: yphone, key: that.keyCode, code: that.codeVal })
				.then(res => {
					that.$successToast(res.msg);
					that.sendCode();
				})
				.catch(res => {
					if (res.data.status === 402) {
						that.codeUrl = `${VUE_APP_API_URL}/sms_captcha?key=${that.keyCode}`;
						that.isShowCode = true;
					}
					that.$showToast(res.msg || res);
				});
		}
	}
};
</script>
<style lang="scss">
page {
	background: $uni-bg-color;
}
</style>
<style scoped>
.ChangePassword .phone input {
	width: 200rpx;
	text-align: center;
}
.codeVal {
	width: 150rpx;
	height: 50rpx;
}
.codeVal image {
	width: 100%;
	height: 100%;
}

.ChangePassword .phone {
	font-size: 32rpx;
	font-weight: bold;
	text-align: center;
	margin-top: 55rpx;
}

.ChangePassword .list {
	width: 580rpx;
	margin: 53rpx auto 0 auto;
}

.ChangePassword .list .item {
	width: 100%;
	height: 110rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.ChangePassword .list .item input {
	width: 100%;
	height: 100%;
	font-size: 32rpx;
}

.ChangePassword .list .item input::placeholder {
	color: #b9b9bc;
}

.ChangePassword .list .item input.codeIput {
	width: 340rpx;
}

.ChangePassword .list .item .code {
	font-size: 32rpx;
}

.ChangePassword .list .item .code.on {
	color: #b9b9bc !important;
}

.ChangePassword .confirmBnt {
	font-size: 32rpx;
	width: 580rpx;
	height: 90rpx;
	border-radius: 45rpx;
	color: #fff;
	margin: 92rpx auto 0 auto;
	text-align: center;
	line-height: 90rpx;
}
</style>
