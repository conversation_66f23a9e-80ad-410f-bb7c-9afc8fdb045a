<template>
	<view class="address-management" :class="addressList.length < 1 && page > 1 ? 'on' : ''">
		<template v-if="addressList.length">
			<view class="item" v-for="(item, index) in addressList" :key="index">
				<view class="wrap">
					<view class="wrap_t flex ">
						<view class="image flex_line_height"><image src="@/static/images/yuanshi/address.png" mode="widthFix"></image></view>

						收货地址：{{ item.province }}{{ item.city }}{{ item.district }}{{ item.detail }}
					</view>
					<view class="wrap_b">
						<view>收货人：{{ item.real_name }}</view>
						<view>电话： {{ item.phone }}</view>
					</view>
				</view>
				<view class="operation flex flex_align_center flex_between">
					<view class="radio flex flex_align_center" @click="radioChange(index)">
						<view class="image">
							<image src="@/static/images/yuanshi/check.png" mode="widthFix" v-if="item.is_default"></image>
							<image src="@/static/images/yuanshi/uncheck.png" mode="widthFix" v-else></image>
						</view>
						<view class="txt">设置为默认地址</view>
					</view>
					<view class="handle flex flex_align_center">
						<view @click="editAddress(index)" class="flex flex_align_center ">
							<image src="@/static/images/yuanshi/edit.png" mode="widthFix"></image>
							编辑
						</view>
						<view @click="delAddress(index)" class="flex flex_align_center ">
							<image src="@/static/images/yuanshi/del.png" mode="widthFix"></image>
							删除
						</view>
					</view>
				</view>
			</view>
		</template>
		<view :class="{ no_address: !hasAddress }" class="absolute">
			<view class="image" v-if="!addressList.length">
				<image :src="baseUrl + '/wximage/noaddress.png'" mode="widthFix"></image>
				<view class="txt font_size20">-暂无添加地址-</view>
			</view>
			<view :class="{ footer: hasAddress }" class="c_btn acea-row row-between-wrapper">
				<view class="btn  flex flex_around" :class="{ on: !isWechat }" @click="addAddress">
					<view class="flex flex_align_center">
						<image src="@/static/images/yuanshi/address1.png" mode="widthFix"></image>
						添加新地址
					</view>
				</view>
				<view class="btn wxbtn flex flex_around" v-if="isWechat" @click="importAddress">
					<view class="flex flex_align_center">
						<!-- #ifndef MP-TOUTIAO -->
						<image src="@/static/images/yuanshi/wxicon.png" mode="widthFix"></image>
						导入微信地址
						<!-- #endif -->
						<!-- #ifdef MP-TOUTIAO -->
						导入收货地址
						<!-- #endif -->
					</view>
				</view>
			</view>
		</view>

		<Loading :loaded="loadend" :loading="loading"></Loading>
        <!-- #ifdef MP-WEIXIN -->
        <x-agreement ref="agreement"></x-agreement>
        <!-- #endif -->
		<x-home></x-home>
	</view>
</template>
<script>
import { getAddressList, getAddressRemove, getAddressDefaultSet, editAddress } from '@/api/user';
import Loading from '@/components/Loading';
import xNodate from '@/components/x-nodata/x-nodata.vue';
import { authGetAddress } from '@/utils/common.js';
// #ifdef H5
import { isWeixin } from '@/utils/validate.js';
const _isWeixin = isWeixin();
// #endif

//  #ifdef MP-WEIXIN
const _isWeixin = true;
// #endif
//  #ifdef MP-TOUTIAO
const _isWeixin = false;
// #endif

import { VUE_APP_URL } from '@/config.js';
export default {
	components: {
		Loading,
		xNodate
	},
	data() {
		return {
			baseUrl: VUE_APP_URL,
			page: 1,
			limit: 20,
			addressList: [],
			loadTitle: '',
			loading: false,
			loadend: false,
			isWechat: _isWeixin
		};
	},
	computed: {
		hasAddress() {
			return this.addressList.length ? true : false;
		}
	},
	onShow() {
		this.page = 1;
		this.addressList = [];
		this.loading = false;
		this.loadend = false;
		this.AddressList();
	},
	methods: {
		/**
		 * 获取地址列表
		 *
		 */
		AddressList: function() {
			let that = this;
			if (that.loading) return; //阻止下次请求（false可以进行请求）；
			if (that.loadend) return; //阻止结束当前请求（false可以进行请求）；
			that.loading = true;
			getAddressList({ page: that.page, limit: that.limit }).then(res => {
				that.loading = false;
				//apply();js将一个数组插入另一个数组;
				that.addressList = that.addressList.concat(res.data);
				that.loadend = res.data.length < that.limit; //判断所有数据是否加载完成；
				that.page = that.page + 1;
			});
		},
		/**
		 * 编辑地址
		 */
		editAddress: function(index) {
			this.$navigator('/pages/user/address/AddAddress?id=' + this.addressList[index].id);
		},
		/**
		 * 删除地址
		 */
		delAddress: function(index) {
			let that = this;
			let address = this.addressList[index];
			let id = address.id;
			getAddressRemove(id).then(function() {
				that.$showToast('删除成功', 'success', {
					success() {
						that.addressList.splice(index, 1);
						that.$set(that, 'addressList', that.addressList);
					}
				});
			});
		},
		/**
		 * 设置默认地址
		 */
		radioChange: function(index) {
			let that = this,
				address = this.addressList[index],
				id = address.id;
			getAddressDefaultSet(id).then(function() {
				for (var i = 0, len = that.addressList.length; i < len; i++) {
					if (i === index) that.addressList[i].is_default = 1;
					else that.addressList[i].is_default = 0;
				}
				that.$set(that, 'addressList', that.addressList);
			});
		},
		/**
		 * 新增地址
		 */
		addAddress: function() {
			this.$navigator('/pages/user/address/AddAddress');
		},
		importAddress() {
			let that = this;
			authGetAddress().then(res => {
				let obj = {
						address: {
							province: res.provinceName,
							city: res.cityName,
							district: res.countyName
						},
						is_default: 1,
						real_name: res.userName,
						post_code: res.postalCode,
						phone: res.telNumber,
						detail: res.detailInfo,
						type: 1
					},
					that = this;
				// #ifdef H5
				obj.wx_export = 1;
				// #endif
				// #ifdef MP
				obj.id = 0;
				// #endif
				editAddress(obj)
					.then(res => {
						that.$showToast('添加成功', 'success', {
							success() {
								// that.page = 1;
								// that.loading = false;
								// that.loadend = false;
								// that.addressList = [];
								// that.AddressList();
							}
						});
					})
					.catch(err => {
						return that.$showToast(err || err.msg);
					});
			});
		}
	},
	onReachBottom() {
		this.AddressList();
	}
};
</script>

<style scoped lang="scss">
.address-management {
	padding: 60rpx 20rpx;
	.item {
		background-color: #fff;
		padding: 46rpx 36rpx 36rpx 36rpx;
		margin-bottom: 30rpx;
		border-radius: 30rpx;
		box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
		.wrap {
			.wrap_t {
				font-weight: bold;
				font-size: 32rpx;

				color: #333333;
				.image {
					height: 42rpx;
					line-height: 42rpx;
					image {
						width: 27rpx;
						height: 27rpx;
						margin-right: 10rpx;
					}
				}
			}
			.wrap_b {
				margin: 12rpx 0 20rpx 37rpx;
				color: #666666;
				font-size: 24rpx;
			}
		}
		.operation {
			height: 83rpx;
			font-size: 28rpx;
			color: #282828;
			border-top: 2rpx solid #eaeaea;
			.radio {
				text {
					margin-left: 14rpx;
				}
				.image {
					margin-right: 16rpx;
					height: 40rpx;
					image {
						width: 43rpx;
						height: 40rpx;
					}
				}
			}
			.handle {
				font-size: 24rpx;
				color: #999999;
				image {
					width: 44rpx;
					height: 44rpx;
					margin: 0 4rpx 0 20rpx;
				}
			}
		}
	}
	.c_btn {
		// background: $uni-bg-color;
		padding: 0 60rpx 60rpx !important;
		.btn {
			width: 300rpx;
			height: 100rpx;
			line-height: 100rpx;
			background: #ff5656;
			border-radius: 40rpx;
			color: #ffffff;
			&.wxbtn {
				background: #28d669;
			}
			&.on {
				width: 690rpx;
				margin: 0 auto;
			}
			image {
				width: 32rpx;
				height: 32rpx;
				margin-right: 10rpx;
			}
		}
	}
	.no_address {
		background: $uni-bg-color;
		top: 0;
		left: 0;
		overflow: hidden;
		padding: 50rpx 0rpx;
		border-radius: 0px 0px 70rpx 70rpx;
		box-shadow: 0px 0px 30rpx rgba(107, 127, 153, 0.3);
		.image {
			width: 398rpx;
			text-align: center;
			margin: 100rpx auto 0rpx auto;
			.txt {
				margin: 60rpx 0 100rpx 0;

				color: #999999;
			}
			image {
				width: 398rpx;
				height: 398rpx;
			}
		}
	}
	.footer {
		@include fixed_footer(120rpx);
		padding: 0rpx 20rpx;
	}
}
</style>
