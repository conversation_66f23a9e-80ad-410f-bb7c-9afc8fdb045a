<template>
	<view class="addAddress absolute">
		<view class="wrap">
			<view class="list">
				<view class="item ">
					<view class="name">姓名</view>
					<input type="text" placeholder="请输入姓名" v-model="userAddress.real_name" placeholder-style="color:#D2D2D2" required />
				</view>
				<view class="item ">
					<view class="name">联系电话</view>
					<input type="text" placeholder="请输入联系电话" v-model="userAddress.phone" placeholder-style="color:#D2D2D2" required />
				</view>
				<view class="item ">
					<view class="name">所在地区</view>
					<view class="picker flex flex_align_center flex_between">
						<view class="address">
							<picker mode="multiSelector" @change="bindRegionChange" @columnchange="bindMultiPickerColumnChange" :value="valueRegion" :range="multiArray">
								<view class="picker" :style="{color:region[0] === '省' ? '#D2D2D2' : '#333333'}">{{ region[0] }}，{{ region[1] }}，{{ region[2] }}</view>
							</picker>
						</view>
						<view class="iconfont "><image src="@/static/images/yuanshi/address.png" mode="widthFix"></image></view>
					</view>
				</view>
				<view class="item ">
					<view class="name">详细地址</view>
					<input type="text" placeholder="请填写具体地址" placeholder-style="color:#D2D2D2" v-model="userAddress.detail" required />
				</view>
			</view>
			<view class="default flex flex_align_center" @click="ChangeIsDefault">
				<view class="image">
					<image src="@/static/images/yuanshi/check.png" mode="widthFix" v-if="userAddress.is_default"></image>
					<image src="@/static/images/yuanshi/uncheck.png" mode="widthFix" v-else></image>
				</view>
				<view class="txt">设置为默认地址</view>
			</view>
		</view>
		<view class="btn keepBnt " @click="submit">立即保存</view>
		<view class="btn wechatAddress flex flex_around" v-if="isWechat && !id" @click="importAddress">
			<!-- #ifndef MP-TOUTIAO -->
			<view class="flex flex_align_center">
				<view class="iconfont icon-weixin2"></view>
				导入微信地址
			</view>
			<!-- #endif -->
			<!-- #ifdef MP-TOUTIAO -->
			导入收货地址
			<!-- #endif -->
		</view>
		<x-home></x-home>
        <!-- #ifdef MP-WEIXIN -->
        <x-agreement ref="agreement"></x-agreement>
        <!-- #endif -->
	</view>
</template>
<script >
// import { CitySelect } from 'vue-ydui/dist/lib.rem/cityselect';
import { getAddress, editAddress } from '@/api/user';
import { getCity } from '@/api/public';
import { RegPhone,RegFixedPhone } from '@/utils/validate';
// import { validatorDefaultCatch } from '@utils/dialog';
import { authGetAddress } from '@/utils/common.js';
// #ifdef H5
import { isWeixin } from '@/utils/validate.js';
const _isWeixin = isWeixin();
// #endif

//  #ifdef MP-WEIXIN
const _isWeixin = true;
// #endif
//  #ifdef MP-TOUTIAO
const _isWeixin = false;
// #endif
export default {
	components: {
		// CitySelect
	},
	data() {
		return {
			model2: '',
			district: [],
			id: 0,
			userAddress: { is_default: 0 },
			address: {},
			isWechat: _isWeixin,
			ready: false,
			region: ['省', '市', '区'],
			multiArray: [],
			valueRegion: [0, 0, 0],
			multiIndex: [0, 0, 0]
		};
	},
	mounted: function() {},
	onLoad(options) {
		const { id } = options;
		this.id = id;
		this.$updateTitle(!id ? '添加地址' : '修改地址');
		this.getUserAddress();
		this.getCityList();
	},
	methods: {
		getCityList: function() {
			let that = this;
			getCity().then(res => {
					that.district = res.data;
					that.initCityArr();
				})
				.catch(err => {
					that.$showToast(err.msg || err);
				});
		},
		initCityArr() {
			let that = this,
				province = [],
				city = [],
				area = [];
			if (that.district.length) {
				let cityChildren = that.district[0].c || [];
				let areaChildren = cityChildren.length ? cityChildren[0].c || [] : [];
				that.district.forEach(function(item) {
					province.push(item.n);
				});
				cityChildren.forEach(function(item) {
					city.push(item.n);
				});
				areaChildren.forEach(function(item) {
					area.push(item.n);
				});
				that.multiArray = [province, city, area];
			}
		},
		bindRegionChange(e) {
			let multiIndex = this.multiIndex,
				province = this.district[multiIndex[0]] || { c: [] },
				city = province.c[multiIndex[1]] || { v: 0 },
				multiArray = this.multiArray,
				value = e.detail.value;
			this.region = [multiArray[0][value[0]], multiArray[1][value[1]], multiArray[2][value[2]]];
			this.cityId = city.v;
			this.valueRegion = [0, 0, 0];

			this.address = {
				province: this.region[0],
				city: this.region[1],
				district: this.region[2],
				city_id: this.cityId
			};
		},
		bindMultiPickerColumnChange(e) {
			let that = this,
				column = e.detail.column,
				value = e.detail.value,
				currentCity = this.district[value] || { c: [] },
				multiArray = that.multiArray,
				multiIndex = that.multiIndex;
			multiIndex[column] = value;
			switch (column) {
				case 0:
					let areaList = currentCity.c[0] || { c: [] };
					multiArray[1] = currentCity.c.map(item => {
						return item.n;
					});
					multiArray[2] = areaList.c.map(item => {
						return item.n;
					});
					this.multiIndex.splice(1, 1, 0)
					this.multiIndex.splice(2, 1, 0)
					break;
				case 1:
					let cityList = that.district[multiIndex[0]].c[multiIndex[1]].c || [];
					multiArray[2] = cityList.map(item => {
						return item.n;
					});
					this.multiIndex.splice(2, 1, 0)
					break;
				case 2:
					break;
			}
			this.multiArray = multiArray;
			this.multiIndex = multiIndex;
		},
		getUserAddress: function() {
			if (!this.id) return false;
			let that = this;
			getAddress(that.id)
				.then(res => {
					that.userAddress = res.data;
					that.region = [res.data.province, res.data.city, res.data.district];
					that.address = {
						province: that.region[0],
						city: that.region[1],
						district: that.region[2],
						city_id: res.data.city_id
					};
				})
				.catch(err => {
					that.$showToast(err.msg || err);
				});
		},
		// 导入微信地址
		importAddress() {
			authGetAddress().then(res => {
				let obj = {
						address: {
							province: res.provinceName,
							city: res.cityName,
							district: res.countyName
						},
						is_default: 1,
						real_name: res.userName,
						post_code: res.postalCode,
						phone: res.telNumber,
						detail: res.detailInfo,
						type: 1
					},
					that = this;
				// #ifdef H5
				obj.wx_export = 1;
				// #endif
				// #ifdef MP
				obj.id = 0;
				// #endif
				editAddress(obj)
					.then(res => {
						that.$showToast('添加成功', 'success', {
							success() {
								setTimeout(() => {
									that.$navigator(-1);
								}, 500);
							}
						});
					})
					.catch(err => {
						return that.$showToast(err.msg || err || '添加失败');
					});
			});
		},
		async submit() {
			let name = this.userAddress.real_name,
				phone = this.userAddress.phone,
				detail = this.userAddress.detail,
				isDefault = this.userAddress.is_default;

			if (!name) return this.$showToast('请填写收货人姓名');
			if (!phone) return this.$showToast('请填写联系电话');
			if (!RegPhone(phone)&&!RegFixedPhone(phone)) return this.$showToast('请输入正确的手机号码');
			if (this.region[0] == '省') return this.$showToast('请选择所在地区');
			if (!detail.trim().length) return this.$showToast('请填写详细地址');
			let that = this,
				data = {
					id: that.id,
					real_name: name,
					phone: phone,
					address: this.address,
					detail: detail,
					is_default: isDefault,
					post_code: ''
				};
			editAddress(data)
				.then(function() {
					if (that.id) that.$showToast('修改成功');
					else that.$showToast('添加成功');
					that.$navigator(-1);
				})
				.catch(err => {
					that.$showToast(err.msg || err);
				});
		},
		ChangeIsDefault: function() {
			this.userAddress.is_default = !this.userAddress.is_default;
		}
	}
};
</script>
<style scoped lang="scss">
.addAddress {
	.wrap {
		padding: 60rpx 42rpx;
		background-color: #fff;

		border-radius: 0px 0px 70rpx 70rpx;
		box-shadow: 0px 0px 30rpx rgba(107, 127, 153, 0.3);
		.list {
			.item {
				padding: 34rpx 30rpx 24rpx 0;
				border-bottom: 2rpx solid #d2d2d2;
				min-height: 90rpx;
				color: #333333;
				.name {
					font-size: 24rpx;
					color: #666666;
					margin-bottom: 14rpx;
				}
				input {
					font-size: 32rpx;
				}
				.picker {
					.address {
						// width: 409rpx;
						width: 100%;
					}
					.iconfont {
						image {
							width: 27rpx;
						}
					}
				}
			}
		}
		.default {
			margin-top: 32rpx;

			font-size: 24rpx;
			color: #333333;
			.image {
				margin-right: 16rpx;
				height: 40rpx;
				image {
					width: 43rpx;
				}
			}
		}
	}
	.btn {
		width: 670rpx;
		height: 100rpx;
		border-radius: 50rpx;
		text-align: center;
		line-height: 100rpx;
		font-size: 24rpx;
		font-weight: 400;
		&.keepBnt {
			margin: 50rpx auto 30rpx auto;
			background: #ff5656;

			color: #fff;
		}
		&.wechatAddress {
			margin: 0 auto;
			color: #28d669;
			border: 1px solid #28d669;
			.iconfont {
				margin-right: 10rpx;
			}
		}
	}
}
</style>
