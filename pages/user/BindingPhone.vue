<template>
    <view class="ChangePassword">
        <view class="list">
            <view class="item"><input type="number" placeholder="填写手机号码" v-model="phone" /></view>
            <view class="item acea-row row-between-wrapper">
                <input type="text" placeholder="填写验证码" class="codeIput" v-model="captcha" />
                <button class="code font-color-red" :disabled="disabled" :class="disabled === true ? 'on' : ''"
                    @click="code">{{ text }}</button>
            </view>
            <view class="item acea-row row-between-wrapper" v-if="isShowCode">
                <input type="text" placeholder="填写验证码" class="codeIput" v-model="codeVal" />
                <view class="codeVal" @click="again">
                    <image :src="codeUrl" />
                </view>
            </view>
        </view>
        <view class="confirmBnt bg-color-red" @click="confirm">确认绑定</view>
    </view>
</template>
<script>
    import {
        mapGetters
    } from 'vuex';
    import sendVerifyCode from '@/mixins/SendVerifyCode';
    import {
        registerVerify,
        bindingPhone,
        getCodeApi
    } from '@/api/user';
    import storage from '@/utils/storage.js'
    import {
        VUE_APP_API_URL
    } from '@/config.js';
    import {
        RegPhone
    } from '@/utils/validate.js';
    export default {
        name: 'BindingPhone',
        components: {},
        props: {},
        data: function() {
            return {
                captcha: '',
                phone: '', //手机号
                keyCode: '',
                codeUrl: '',
                codeVal: '',
                isShowCode: false
            };
        },
        mixins: [sendVerifyCode],
        computed: mapGetters(['userInfo']),
        mounted: function() {
            this.getCode();
        },
        methods: {
            goPersonCenter() {
                this.$navigator('/pages/user/PersonalData', 'redirectTo')
            },
            again() {
                this.codeUrl = VUE_APP_API_URL + '/captcha?' + this.keyCode + Date.parse(new Date());
            },
            getCode() {
                getCodeApi()
                    .then(res => {
                        this.keyCode = res.data.key;
                    })
                    .catch(res => {
                        this.$showToast(res.msg || res);
                    });
            },
            async confirm() {
                let that = this;
                const {
                    phone,
                    captcha,
                    codeVal
                } = that;
                if (!RegPhone(phone)) {
                    return that.$showToast(phone.length ? '请填写正确手机号' : '请填写手机号');
                }
                if (!captcha.trim().length) {
                    return that.$showToast('请输入验证码');
                }
                if (this.isShowCode && !codeVal.trim().length) {
                    return that.$showToast('请填写验证码');
                }
                bindingPhone({
                        phone: this.phone,
                        captcha: this.captcha,
                        code: this.codeVal
                    }).then(res => {
                        console.log('res.data',res)
                        if (res.data !== undefined && res.data.is_bind) {
                            that.$showModal('绑定提醒', res.msg, {
                                confirmText: '确认绑定',
                                success: function(res) {
                                    if (res.confirm) {
                                        bindingPhone({
                                                phone: that.phone,
                                                captcha: that.captcha,
                                                step: 1
                                            }).then(res => {
                                                if (res.msg == '绑定成功') {
                                                    storage.set('userInfo_phone',this.phone)
                                                    that.$successToast(res.msg);
                                                    that.goPersonCenter()
                                                    
                                                } else {
                                                    uni.showToast({
                                                        title: res.msg,
                                                        icon: 'none',
                                                        duration: 5000
                                                    })
                                                }
                                            })
                                            .catch(res => {
                                                that.$showToast(res.msg || res);
                                                that.goPersonCenter()
                                                
                                            });
                                    } else if (res.cancel) {
                                        that.$showToast('已取消绑定');
                                        that.goPersonCenter()
                                    }
                                }
                            });
                        } else {
                            if (res.msg == '绑定成功') {
                                storage.set('userInfo_phone',this.phone)
                                that.$successToast(res.msg);
                                that.goPersonCenter()
                            } else {
                                uni.showToast({
                                    title: res.msg,
                                    icon: 'none',
                                    duration: 5000
                                })
                            }
                        }
                    })
                    .catch(res => {
                        that.$showToast(res.msg || res);
                    });
            },
            async code() {
                let that = this;
                const {
                    phone
                } = that;
                if (!RegPhone(phone)) {
                    return that.$showToast(phone.length ? '请填写正确手机号' : '请填写手机号');
                }

                registerVerify({
                        phone: phone,
                        key: that.keyCode,
                        code: that.codeVal
                    })
                    .then(res => {
                        that.$successToast(res.msg);
                        that.sendCode();
                    })
                    .catch(res => {
                        if (res.data.status === 402) {
                            that.codeUrl = `${VUE_APP_API_URL}/sms_captcha?key=${that.keyCode}`;
                            that.isShowCode = true;
                        }
                        that.$showToast(res.msg || res);
                    });
            }
        }
    };
</script>
<style lang="scss">
    page {
        background: $uni-bg-color;
    }
</style>
<style scoped>
    .codeVal {
        width: 150rpx;
        height: 050rpx;
    }

    .codeVal image {
        width: 100%;
        height: 100%;
    }

    .ChangePassword .phone input {
        width: 200rpx;
        text-align: center;
    }

    .codeVal {
        width: 150rpx;
        height: 50rpx;
    }

    .codeVal image {
        width: 100%;
        height: 100%;
    }

    .ChangePassword .phone {
        font-size: 32rpx;
        font-weight: bold;
        text-align: center;
        margin-top: 55rpx;
    }

    .ChangePassword .list {
        width: 580rpx;
        margin: 53rpx auto 0 auto;
    }

    .ChangePassword .list .item {
        width: 100%;
        height: 110rpx;
        border-bottom: 2rpx solid #f0f0f0;
    }

    .ChangePassword .list .item input {
        width: 100%;
        height: 100%;
        font-size: 32rpx;
    }

    .ChangePassword .list .item input::placeholder {
        color: #b9b9bc;
    }

    .ChangePassword .list .item input.codeIput {
        width: 340rpx;
    }

    .ChangePassword .list .item .code {
        font-size: 32rpx;
    }

    .ChangePassword .list .item .code.on {
        color: #b9b9bc !important;
    }

    .ChangePassword .confirmBnt {
        font-size: 32rpx;
        width: 580rpx;
        height: 90rpx;
        border-radius: 45rpx;
        color: #fff;
        margin: 92rpx auto 0 auto;
        text-align: center;
        line-height: 90rpx;
    }
</style>
