<template>
	<view >
		<view class="coupon-list" v-if="couponsList.length > 0">
			<view class="item acea-row row-center-wrapper" v-for="(item, index) in couponsList" :key="index">
				<view class="money" :class="item.is_use ? 'moneyGray' : ''">
					<view>
						￥
						<span class="num">{{ item.coupon_price }}</span>
					</view>
					<view class="pic-num">满{{ item.use_min_price }}元可用</view>
				</view>
				<view class="text">
					<view class="condition line1">
						<span class="line-title" :class="item.is_use === true || item.is_use === 2 ? 'bg-color-huic' : 'bg-color-check'" v-if="item.type === 0">通用劵</span>
						<span class="line-title" :class="item.is_use === true || item.is_use === 2 ? 'bg-color-huic' : 'bg-color-check'" v-else-if="item.type === 1">品类券</span>
						<span class="line-title" :class="item.is_use === true || item.is_use === 2 ? 'bg-color-huic' : 'bg-color-check'" v-else>商品券</span>
						<span>{{ item.title }}</span>
					</view>
					<view class="data acea-row row-between-wrapper">
						<view v-if="item.end_time !== 0">{{ item.start_time ? item.start_time + '-' : '' }}{{ item.end_time }}</view>
						<view v-else>不限时</view>
						<view class="bnt gray" v-if="item.is_use === true">已领取</view>
						<view class="bnt gray" v-else-if="item.is_use === 2">已领完</view>
						<view class="bnt bg-color-red" v-else @click="getCoupon(item.id, index)">立即领取</view>
					</view>
				</view>
			</view>
		</view>
		<Loading :loaded="loadend" :loading="loading"></Loading>
		<!--暂无优惠券-->
		<xNodate :arr="couponsList" :page="page" imgSrc="/wximage/noCoupon.png"></xNodate>
		<x-home></x-home>
	</view>
</template>
<script>
import { getCoupon, getCouponReceive } from '@/api/user';
import Loading from '@/components/Loading';
import xNodate from '@/components/x-nodata/x-nodata.vue';
export default {
	name: 'getCoupon',
	components: {
		Loading,
		xNodate
	},
	props: {},
	data: function() {
		return {
			page: 1,
			limit: 10,
			couponsList: [],
			loading: false,
			loadend: false
		};
	},
	mounted: function() {
		this.getUseCoupons();
	},
	methods: {
		getCoupon: function(id, index) {
			let that = this;
			let list = that.couponsList;
			getCouponReceive(id)
				.then(function() {
					list[index].is_use = true;
					that.$showToast( '领取成功')
				})
				.catch(function(res) {
					that.$showToast(  res.msg || res)
				});
		},
		getUseCoupons: function() {
			let that = this;
			if (that.loading) return; //阻止下次请求（false可以进行请求）；
			if (that.loadend) return; //阻止结束当前请求（false可以进行请求）；
			that.loading = true;
			let q = { page: that.page, limit: that.limit };
			getCoupon(q).then(res => {
				that.loading = false;
				that.couponsList.push.apply(that.couponsList, res.data);
				that.loadend = res.data.length < that.limit; //判断所有数据是否加载完成；
				that.page = that.page + 1;
			});
		}
	},
	onReachBottom() {
		this.getUseCoupons()
	}
};
</script>
<style scoped>
.bg-color-check {
	color: #e83323;
	background: #fff7f7;
	border: 1px solid #e83323;
}
</style>
