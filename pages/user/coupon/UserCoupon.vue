<template>
    <view>
        <view class="coupon-list" v-if="couponsList.length > 0">
            <view class="item acea-row row-center-wrapper" v-cloak v-for="(item, index) in couponsList" :key="index">
                <view class="money" :class="item._type === 0 ? 'moneyGray' : ''">
                    <view>
                        ￥<span class="num">{{ item.coupon_price }}</span>
                    </view>
                    <view class="pic-num">满{{ item.use_min_price }}元可用</view>
                </view>
                <view class="text">
                    <view class="condition line1">
                        <span class="line-title" :class="item._type === 0 ? 'bg-color-huic' : 'bg-color-check'"
                            v-if="item.applicable_type === 0">通用劵</span>
                        <span class="line-title" :class="item._type === 0 ? 'bg-color-huic' : 'bg-color-check'"
                            v-else-if="item.applicable_type === 1">品类券</span>
                        <span class="line-title" :class="item._type === 0 ? 'bg-color-huic' : 'bg-color-check'"
                            v-else>商品券</span>
                        <span>{{ item.coupon_title }}</span>
                    </view>
                    <view class="data acea-row row-between-wrapper">
                        <view v-if="item._end_time === 0">不限时</view>
                        <view v-else>{{ item._add_time }}-{{ item._end_time }}</view>
                        <view class="bnt gray" v-if="item._type === 0">{{ item._msg }}</view>
                        <view class="bnt bg-color-red" v-else>{{ item._msg }}</view>
                    </view>
                </view>
            </view>
        </view>
        <!--暂无优惠券-->
        <xNodate :arr="couponsList" :page="2" :isR="false" imgSrc="/wximage/noCoupon.png"></xNodate>
        <x-authorize :isHidden="true" @login="getUseCoupons"></x-authorize>
        <x-home></x-home>
    </view>
</template>
<script>
    import {
        getCouponsUser
    } from "@/api/user";
    import xNodate from '@/components/x-nodata/x-nodata.vue';
    const NAME = "UserCoupon";

    export default {
        name: "UserCoupon",
        components: {
            xNodate
        },
        props: {},
        data: function() {
            return {
                couponsList: [],
                loading: false
            };
        },
        onShow() {
            this.getUseCoupons();
        },
        methods: {
            getUseCoupons: function() {
                let that = this,
                    type = 0;
                getCouponsUser(type).then(res => {
                    that.couponsList = res.data;
                    that.loading = true;
                });
            }
        }
    };
</script>
<style lang="scss">
    page {
        background-color: $uni-bg-color-page;
    }
</style>
