<template>
  <view class="promoter-order" ref="container">
    <view class="promoterHeader bg-color-red">
      <view class="headerCon acea-row row-between-wrapper">
        <view>
          <view class="name">累计推广订单</view>
          <view>
            <span class="num">{{ count }}</span
            >单
          </view>
        </view>
        <view class="iconfont icon-2"></view>
      </view>
    </view>
    <view class="list">
      <view class="item" v-for="(item, index) in list" :key="index">
        <view class="title acea-row row-column row-center">
          <view class="data">{{ item.time }}</view>
          <view>本月累计推广订单：{{ item.count ? item.count : 0 }}单</view>
        </view>
        <view class="listn">
          <view class="itenm" v-for="(val, indexn) in item.child" :key="indexn">
            <view class="top acea-row row-between-wrapper">
              <view class="pictxt acea-row row-between-wrapper">
                <view class="pictrue">
                  <image :src="val.avatar" />
                </view>
                <view class="text line1">{{ val.nickname }}</view>
              </view>
              <view class="money">
                <view v-if="val.type === 'pay_money'">
                  暂未返佣
                </view>
                <view v-else>
                  返佣：<span class="font-color-red"
                    >￥{{ val.number ? val.number : 0 }}</span
                  >
                </view>
              </view>
            </view>
            <view class="bottom">
              <view><span class="name">订单号：</span>{{ val.order_id }}</view>
              <view v-if="val.type === 'pay_money'">
                <span class="name">下单时间：</span>{{ val.time }}
              </view>
              <view v-else>
                <span class="name">返佣时间：</span>{{ val.time }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <Loading :loaded="loaded" :loading="loading"></Loading>
		<x-home></x-home>
  </view>
</template>
<script>
import { getSpreadOrder } from "../../../api/user";
import Loading from "@/components/Loading";
export default {
  name: "PromoterOrder",
  components: {
    Loading
  },
  props: {},
  data: function() {
    return {
      list: [],
      where: {
        page: 1,
        limit: 15
      },
      loaded: false,
      loading: false,
      loadTitle: "",
      count: ""
    };
  },
  mounted: function() {
    this.getIndex();
  },
	onLoad() {
		this.$setNavigationBarColor('#e93323')
	},
  methods: {
    getIndex: function() {
      let that = this;
      if (that.loaded == true || that.loading == true) return;
      that.loading = true;
      getSpreadOrder(that.where).then(
        res => {
          that.loading = false;
          that.loaded = res.data.list.length < that.where.limit;
          that.loadTitle = that.loaded ? "人家是有底线的" : "上拉加载更多";
          that.where.page = that.where.page + 1;
          that.list.push.apply(that.list, res.data.list);
          that.count = res.data.count;
        },
        err => {
          that.$showToast(err.msg || err);
        }
      );
    }
  },
	onReachBottom() {
		this.getIndex();
	}
};
</script>
<style lang="scss" scoped>
	.promoter-order .list .item .title {
		height: 133rpx;
		padding: 0 30rpx;
		font-size: 26rpx;
		color: #999;
	}
	
	.promoter-order .list .item .title .data {
		font-size: 28rpx;
		color: #282828;
		margin-bottom: 5rpx;
	}
	
	.promoter-order .list .item .listn .itenm {
		background-color: #fff;
	}
	
	.promoter-order .list .item .listn .itenm~.itenm {
		margin-top: 12rpx;
	}
	
	.promoter-order .list .item .listn .itenm .top {
		margin-left: 30rpx;
		padding-right: 30rpx;
		border-bottom: 1px solid #eee;
		height: 100rpx;
	}
	
	.promoter-order .list .item .listn .itenm .top .pictxt {
		width: 320rpx;
	}
	
	.promoter-order .list .item .listn .itenm .top .pictxt .text {
		width: 229rpx;
		font-size: 30rpx;
		color: #282828;
	}
	
	.promoter-order .list .item .listn .itenm .top .pictxt .pictrue {
		width: 66rpx;
		height: 66rpx;
	}
	
	.promoter-order .list .item .listn .itenm .top .pictxt .pictrue image {
		width: 100%;
		height: 100%;
		border-radius: 50%;
		border: 3rpx solid #fff;
		box-shadow: 0 0 10rpx #aaa;
		-webkit-box-shadow: 0 0 10rpx #aaa;
		-moz-box-shadow: 0 0 10rpx #aaa;
		-o-box-shadow: 0 0 10rpx #aaa;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
	}
	
	.promoter-order .list .item .listn .itenm .top .money {
		font-size: 28rpx;
	}
	
	.promoter-order .list .item .listn .itenm .bottom {
		padding: 20rpx 30rpx;
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
	}
	
	.promoter-order .list .item .listn .itenm .bottom .name {
		color: #999;
	}
</style>
