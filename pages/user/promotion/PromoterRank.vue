<template>
	<view class="PromoterRank">
		<view class="redBg bg-color-red">
			<view class="header">
				<view class="nav acea-row row-center-wrapper">
					<view class="item" :class="active === index ? 'font-color-red' : ''" v-for="(item, index) in navList" :key="index" @click="switchTap(index)">{{ item }}</view>
				</view>
				<view class="rank acea-row row-bottom row-around">
					<view class="item" v-show="Two.uid">
						<view class="pictrue"><image :src="Two.avatar" /></view>
						<view class="name line1">{{ Two.nickname }}</view>
						<view class="num">{{ Two.count }}人</view>
					</view>
					<view class="item" v-if="One.uid">
						<view class="pictrue"><image :src="One.avatar" /></view>
						<view class="name line1">{{ One.nickname }}</view>
						<view class="num">{{ One.count }}人</view>
					</view>
					<view class="item" v-if="Three.uid">
						<view class="pictrue"><image :src="Three.avatar" /></view>
						<view class="name line1">{{ Three.nickname }}</view>
						<view class="num">{{ Three.count }}人</view>
					</view>
				</view>
			</view>
		</view>
		<view class="list" v-if="rankList.length">
			<view class="item acea-row row-between-wrapper" v-for="(item, index) in rankList" :key="item.nickname">
				<view class="num">{{ index + 4 }}</view>
				<view class="picTxt acea-row row-between-wrapper">
					<view class="pictrue"><image :src="item.avatar" /></view>
					<view class="text line1">{{ item.nickname }}</view>
				</view>
				<view class="people font-color-red">{{ item.count }}人</view>
			</view>
		</view>
		<x-home></x-home>
	</view>
</template>

<script>
import { getRankList } from '@/api/user';
const NAME = 'PromoterRank';
export default {
	name: NAME,
	props: {},
	data: function() {
		return {
			navList: ['周榜', '月榜'],
			active: 0,
			page: 1,
			limit: 10,
			loading: false,
			loadend: false,
			rankList: [],
			One: {},
			Two: {},
			Three: {},
			type: 'week'
		};
	},
	onLoad() {
		this.$setNavigationBarColor('#e93323')
	},
	mounted: function() {
		this.getRankList();
	},
	methods: {
		switchTap(idx){
			console.log('xxx',idx)
			if(this.active===idx) return;
			this.active=idx;
			this.type = idx ? 'month' : 'week';
			this.page = 1;
			this.loaded = false;
			this.$set(this, 'rankList', []);
			this.getRankList();
		},
		getRankList: function() {
			getRankList({
				page: this.page,
				limit: this.limit,
				type: this.type
			})
				.then(res => {
					let list = res.data;
					this.rankList.push.apply(this.rankList, list);
					if (this.page == 1) {
						this.One = this.rankList.shift() || {};
						this.Two = this.rankList.shift() || {};
						this.Three = this.rankList.shift() || {};
					}
					this.page++;
					this.loading = false;
					this.loaded = list.length < this.limit;
					this.$set(this, 'rankList', this.rankList);
				})
				.catch(() => {
					this.loading = false;
				});
		}
	},
	onReachBottom() {
		this.getRankList();
	}
};
</script>
<style scoped>
.PromoterRank .redBg {
	padding: 45rpx 0 30rpx 0;
}
.PromoterRank .header {
	background: url('@/static/images/integralbg.jpg') no-repeat;
	width: 100%;
	height: 459rpx;
	background-size: 100% 100%;
}
.PromoterRank .header .nav {
	width: 450rpx;
	height: 66rpx;
	border: 1px solid #fff;
	border-radius: 33rpx;
	font-size: 30rpx;
	color: #fff;
	margin: 0 auto;
}
.PromoterRank .header .nav .item {
	width: 223rpx;
	height: 100%;
	text-align: center;
	line-height: 64rpx;
}
.PromoterRank .header .nav .item.font-color-red:nth-of-type(1) {
	background-color: #fff;
	border-radius: 33rpx 0 0 33rpx;
}
.PromoterRank .header .nav .item.font-color-red:nth-of-type(2) {
	background-color: #fff;
	border-radius: 0 33rpx 33rpx 0;
}
.PromoterRank .header .rank {
	padding: 0 20rpx;
	margin-top: 30rpx;
}
.PromoterRank .header .rank .item .pictrue {
	background: url('@/static/images/twoT.png') no-repeat;
	background-size: 100% 100%;
	width: 136rpx;
	height: 177rpx;
	position: relative;
	margin: 0 auto;
}
.PromoterRank .header .rank .item .pictrue image {
	position: absolute;
	width: 130rpx;
	height: 130rpx;
	display: block;
	bottom: 2rpx;
	border-radius: 50%;
	left: 50%;
	margin-left: -65rpx;
}
.PromoterRank .header .rank .item:nth-of-type(2) .pictrue {
	background-image: url('@/static/images/oneT.png');
	width: 156rpx;
	height: 204rpx;
}
.PromoterRank .header .rank .item:nth-of-type(2) .pictrue image {
	width: 150rpx;
	height: 150rpx;
	margin-left: -75rpx;
}
.PromoterRank .header .rank .item:nth-of-type(3) .pictrue {
	background-image: url('@/static/images/threeT.png');
}
.PromoterRank .header .rank .item:nth-of-type(3) .pictrue image {
	margin-left: -64rpx;
}
.PromoterRank .header .rank .item .name {
	font-size: 30rpx;
	color: #fff;
	margin-top: 22rpx;
	text-align: center;
	width: 170rpx;
}
.PromoterRank .header .rank .item .num {
	font-size: 30rpx;
	color: #fff;
	text-align: center;
}
.PromoterRank .list {
	width: 710rpx;
	background-color: #fff;
	border-radius: 20rpx;
	margin: -60rpx auto 0 auto;
	padding: 0 30rpx;
}
.PromoterRank .list .item {
	border-bottom: 1px solid #f3f3f3;
	height: 101rpx;
	font-size: 28rpx;
}
.PromoterRank .list .item .num {
	color: #666;
	width: 70rpx;
}
.PromoterRank .list .item .picTxt {
	width: 350rpx;
}
.PromoterRank .list .item .picTxt .pictrue {
	width: 68rpx;
	height: 68rpx;
}
.PromoterRank .list .item .picTxt .pictrue image {
	width: 100%;
	height: 100%;
	display: block;
	border-radius: 50%;
}
.PromoterRank .list .item .picTxt .text {
	width: 262rpx;
	color: #333;
}
.PromoterRank .list .item .people {
	width: 175rpx;
	text-align: right;
}
</style>