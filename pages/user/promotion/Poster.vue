<template>
	<view class="distribution-posters">
		<view class="slider-banner banner">
			<xSwiper
				:dots="false"
				:autoplay="false"
				:arr="info"
				height="1000rpx"
				previousMargin="60rpx"
				nextMargin="60rpx"
				padding="0 16rpx"
				:scale="true"
				srcName="wap_poster"
				@change="swiperChange"
			></xSwiper>
		</view>
		<view class="preserve acea-row row-center-wrapper">
			<view class="line"></view>
			<view class="tip" @click="savePosterPath">保存海报</view>
			<view class="line"></view>
		</view>
		<x-home></x-home>
	</view>
</template>
<style lang="scss">
page {
	background-color: $uni-bg-color-page;
}
</style>
<style scoped lang="scss">
.distribution-posters .slider-banner {
	width: 100%;
	height: 1000rpx;
	position: relative;
	margin-top: 100rpx;
}

.preserve {
	color: #fff;
	text-align: center;
	margin-top: 20rpx;
}
.preserve .line {
	width: 100rpx;
	height: 2rpx;
	background-color: #fff;
}
.preserve .tip {
	margin: 0 30rpx;
}
</style>
<script>
import xSwiper from '@/components/x-swiper/x-swiper.vue';
import {saveImageToPhotosAlbum } from '@/utils/common.js';
import { getSpreadImg } from '@/api/user';
export default {
	name: 'Poster',
	components: {
		xSwiper
	},
	props: {},
	data: function() {
		return {
			info: [],
			activeIndex: 0
		};
	},
	mounted: function() {
		this.getIndex();
	},
	methods: {
		swiperChange(idx){
			this.activeIndex = idx
		},
		savePosterPath(){
			let info = this.info[this.activeIndex];
			saveImageToPhotosAlbum(info.wap_poster,info.title)
		},
		getIndex: function() {
			let that = this;
			getSpreadImg().then(
				res => {
					that.info = res.data;
				},
				err => {
					that.$showToast(err.msg || err);
				}
			);
		}		
	}
};
</script>
