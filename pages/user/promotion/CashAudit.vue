<template>
  <view class="cash-audit">
    <view class="pictrue"><image src="@/static/images/examine.png" /></view>
    <view class="tip">提现申请已提交，等待人工审核</view>
    <view class="time">{{ time }}</view>
    <view
      class="bnt bg-color-red"
      @click="goPages()"
    >
      好的
    </view>
		<x-home></x-home>
  </view>
</template>
<script>
export default {
  name: "CashAudit",
  components: {},
  props: {},
  data: function() {
    return {
      time: ""
    };
  },
  mounted: function() {
    let myData = new Date();
    this.time = myData.toLocaleString();
  },
	onLoad() {
		this.$setNavigationBarColor('#e93323')
	},
  methods: {
		goPages(path){
			this.$navigator('/pages/user/promotion/UserPromotion','redirectTo');
		}
	}
};
</script>
<style scoped>
	.cash-audit {
		width: 710rpx;
		background-color: #fff;
		border-radius: 6rpx;
		margin: 25rpx auto 0 auto;
		padding: 53rpx 0 57rpx 0;
	}
	
	.cash-audit .pictrue {
		width: 214rpx;
		height: 179rpx;
		margin: 0 auto;
	}
	
	.cash-audit .pictrue image {
		width: 100%;
		height: 100%;
	}
	
	.cash-audit .tip {
		font-size: 32rpx;
		color: #282828;
		margin-top: 40rpx;
		text-align: center;
		padding: 0 40rpx;
	}
	
	.cash-audit .time {
		font-size: 26rpx;
		color: #999;
		text-align: center;
		margin-top: 15rpx;
	}
	
	.cash-audit .bnt {
		font-size: 32rpx;
		color: #fff;
		width: 500rpx;
		height: 86rpx;
		border-radius: 43rpx;
		text-align: center;
		line-height: 86rpx;
		margin: 50rpx auto 0 auto;
	}

</style>