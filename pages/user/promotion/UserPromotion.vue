<template>
	<view class="my-promotion">
		<view class="header">
			<view class="name acea-row row-center-wrapper">
				<view>当前佣金</view>
				<view class="record" @click="goPages('/pages/user/promotion/CashRecord')" >
					提现记录
					<span class="iconfont icon-xiangyou"></span>
				</view>
			</view>
			<view class="num">{{ userInfo.brokerage_price || 0 }}</view>
			<view class="profit acea-row row-between-wrapper">
				<view class="item">
					<view>昨日收益</view>
					<view class="money">{{ userInfo.yesterDay || 0 }}</view>
				</view>
				<view class="item">
					<view>累积已提</view>
					<view class="money">{{ userInfo.extractTotalPrice || 0 }}</view>
				</view>
			</view>
		</view>
		<view class="bnt bg-color-red" @click="openSubscribe('/pages/user/promotion/UserCash')">立即提现</view>
		<view class="list acea-row row-between-wrapper">
			<view class="item acea-row row-center-wrapper row-column" @click="goPages('/pages/user/promotion/Poster')">
				<span class="iconfont icon-erweima"></span>
				<view>推广名片</view>
			</view>
			<view class="item acea-row row-center-wrapper row-column" @click="goPages('/pages/user/promotion/PromoterList')" >
				<span class="iconfont icon-tongji"></span>
				<view>推广人统计</view>
			</view>
			<view class="item acea-row row-center-wrapper row-column" @click="goPages('/pages/user/promotion/CommissionDetails')" >
				<span class="iconfont icon-qiandai"></span>
				<view>佣金记录</view>
			</view>
			<view class="item acea-row row-center-wrapper row-column" @click="goPages('/pages/user/promotion/PromoterOrder')" >
				<span class="iconfont icon-dingdan"></span>
				<view>推广人订单</view>
			</view>
			<view class="item acea-row row-center-wrapper row-column" @click="goPages('/pages/user/promotion/PromoterRank')" >
				<span class="iconfont icon-paihang1"></span>
				<view>推广人排行</view>
			</view>
			<view class="item acea-row row-center-wrapper row-column" @click="goPages('/pages/user/promotion/CommissionRank')" >
				<span class="iconfont icon-paihang"></span>
				<view>佣金排行</view>
			</view>
		</view>
		<x-home></x-home>
	</view>
</template>
<script>
import { getUser } from '@/api/user';
import { openExtrctSubscribe } from '@/utils/SubscribeMessage.js';
export default {
	name: 'UserPromotion',
	components: {},
	props: {},
	data: function() {
		return {
			userInfo: {}
		};
	},
	mounted: function() {
		this.getInfo();
	},
	onLoad() {
		this.$setNavigationBarColor('#e93323')
	},
	methods: {
		goPages(path, type) {
			this.$authNavigator(path, type);
		},
		openSubscribe(path,type) {
		   openExtrctSubscribe().then(()=>{
				 this.$authNavigator(path, type);
			 })
		  },
		getInfo: function() {
			let that = this;
			getUser().then(
				res => {
					that.userInfo = res.data;
				},
				function(err) {
					that.$showToast(err.msg || err);
				}
			);
		},
		toCash: function() {
			this.$router.push({ path: '/user/user_cash' });
		}
	}
};
</script>
<style lang="scss">
page {
	background-color: $uni-bg-color-page;
}
</style>
<style scoped lang="scss">
.my-promotion .header {
	background-image: url('@/static/images/promotionBg.png');
	background-repeat: no-repeat;
	background-size: 100% 100%;
	width: 100%;
	height: 375rpx;
}

.my-promotion .header .name {
	font-size: 30rpx;
	color: #fff;
	padding-top: 56rpx;
	position: relative;
}

.my-promotion .header .name .record {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
	position: absolute;
	right: 20rpx;
}

.my-promotion .header .name .record .iconfont {
	font-size: 25rpx;
	margin-left: 10rpx;
}

.my-promotion .header .num {
	text-align: center;
	color: #fff;
	margin-top: 25rpx;
	font-size: 90rpx;
	font-family: 'GuildfordProBook 5';
}

.my-promotion .header .profit {
	padding: 0 20rpx;
	margin-top: 33rpx;
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
}

.my-promotion .header .profit .item {
	min-width: 200rpx;
	text-align: center;
}

.my-promotion .header .profit .item .money {
	font-size: 34rpx;
	color: #fff;
}

.my-promotion .bnt {
	font-size: 28rpx;
	color: #fff;
	width: 258rpx;
	height: 68rpx;
	border-radius: 50rpx;
	text-align: center;
	line-height: 68rpx;
	margin: -32rpx auto 0 auto;
}

.my-promotion .list {
	padding: 0 20rpx;
	margin-top: 10rpx;
}

.my-promotion .list .item {
	width: 345rpx;
	height: 240rpx;
	border-radius: 20rpx;
	background-color: #fff;
	margin-top: 20rpx;
	font-size: 30rpx;
	color: #666;
}

.my-promotion .list .item .iconfont {
	font-size: 70rpx;
	background-image: linear-gradient(to right, #fc4d3d 0%, #e93323 100%);
	background-image: -webkit-linear-gradient(to right, #fc4d3d 0%, #e93323 100%);
	background-image: -moz-linear-gradient(to right, #fc4d3d 0%, #e93323 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	margin-bottom: 20rpx;
}
</style>
