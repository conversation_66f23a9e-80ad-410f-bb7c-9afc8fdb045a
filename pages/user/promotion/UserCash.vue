<template>
	<view class="cash-withdrawal">
		<view class="nav acea-row">
			<view v-for="(item, index) in navList" class="item font-color-red" @click="swichNav(index, item)" :key="index">
				<view class="line bg-color-red" :class="currentTab === index ? 'on' : ''"></view>
				<view class="iconfont" :class="item.icon + ' ' + (currentTab === index ? 'on' : '')"></view>
				<view>{{ item.name }}</view>
			</view>
		</view>
		<view class="wrapper">
			<view :hidden="currentTab !== 0" class="list">
				<view class="item acea-row row-between-wrapper">
					<view class="name">持卡人</view>
					<view class="input"><input placeholder="请输入持卡人姓名" v-model="post.name" /></view>
				</view>
				<view class="item acea-row row-between-wrapper">
					<view class="name">卡号</view>
					<view class="input"><input placeholder="请填写卡号" v-model="post.cardnum" /></view>
				</view>
				<view class="item acea-row row-between-wrapper">
					<view class="name">银行</view>
					<view class="input">
						<picker @change="bindPickerChange" :value="index" :range="banks">
							<view class="uni-input">{{ banks[index] }}</view>
						</picker>
					</view>
				</view>
				<view class="item acea-row row-between-wrapper">
					<view class="name">提现</view>
					<view class="input"><input :placeholder="'最低提现金额' + minPrice" v-model="post.money" /></view>
				</view>
				<view class="tip">当前可提现金额: {{ commissionCount }}</view>
				<view class="bnt bg-color-red" @click="submitted">提现</view>
			</view>
			<view :hidden="currentTab !== 1" class="list">
				<view class="item acea-row row-between-wrapper">
					<view class="name">微信号</view>
					<view class="input"><input placeholder="请输入微信号" v-model="post.name" /></view>
				</view>
				<view class="item acea-row row-between-wrapper">
					<view class="name">提现</view>
					<view class="input"><input :placeholder="'最低提现金额' + minPrice" v-model="post.money" /></view>
				</view>
				<view class="tip">当前可提现金额: {{ commissionCount }}</view>
				<view class="bnt bg-color-red" @click="submitted">提现</view>
			</view>
			<view :hidden="currentTab !== 2" class="list">
				<view class="item acea-row row-between-wrapper">
					<view class="name">用户名</view>
					<view class="input"><input placeholder="请填写您的支付宝用户名" v-model="post.name" /></view>
				</view>
				<view class="item acea-row row-between-wrapper">
					<view class="name">账号</view>
					<view class="input"><input placeholder="请填写您的支付宝账号" v-model="post.alipay_code" /></view>
				</view>
				<view class="item acea-row row-between-wrapper">
					<view class="name">提现</view>
					<view class="input"><input :placeholder="'最低提现金额' + minPrice" v-model="post.money" /></view>
				</view>
				<view class="tip">当前可提现金额: {{ commissionCount }}</view>
				<view class="bnt bg-color-red" @click="submitted">提现</view>
			</view>
		</view>
		<x-home></x-home>
	</view>
</template>
<script>
import { getBank, postCashInfo } from '@/api/user';
import { RegMoney } from '@/utils/validate';
export default {
	name: 'UserCash',
	components: {},
	props: {},
	data: function() {
		return {
			navList: [
				{ name: '银行卡', type: 'bank', icon: 'icon-yinhangqia' },
				{ name: '微信', type: 'weixin', icon: 'icon-weixin2' },
				{ name: '支付宝', type: 'alipay', icon: 'icon-icon34' }
			],
			post: {
				extract_type: 'bank',
				alipay_code: '',
				money: '',
				name: '',
				bankname: '',
				cardnum: ''
			},
			currentTab: 0,
			minPrice: 0,
			banks: [],
			index: 0,
			commissionCount: 0
		};
	},
	onLoad() {
		this.$setNavigationBarColor('#e93323')
	},
	mounted: function() {
		this.getBank();
	},
	methods: {
		swichNav: function(index, item) {
			this.currentTab = index;
			this.post.extract_type = item.type;
		},
		getBank: function() {
			let that = this;
			getBank().then(
				res => {
					that.banks = res.data.extractBank;
					that.minPrice = res.data.minPrice;
					that.commissionCount = res.data.commissionCount;
				},
				function(err) {
					that.$showToast(err.msg || err);
				}
			);
		},
		bindPickerChange(e) {
			this.index = e.detail.value;
			this.post.bankname = this.banks[e.detail.value];
		},
		async submitted() {
			let bankname = this.post.bankname,
				alipay_code = this.post.alipay_code,
				money = this.post.money,
				name = this.post.name,
				cardnum = this.post.cardnum,
				that = this,
				obj = {
					extract_type: that.post.extract_type,
					money: money
				};
			if (obj.extract_type === 'bank') {
				if (name.length == 0) return that.$showToast('请填写持卡人姓名');
				if (cardnum.length == 0) return that.$showToast('请填写卡号');
				obj.bankname = bankname;
				obj.cardnum = cardnum;
				obj.name = name;
			} else if (obj.extract_type === 'alipay') {
				if (name.length == 0) return that.$showToast('请填写支付宝用户名');
				if (alipay_code.length == 0) return that.$showToast('请填写支付宝账号');
				obj.alipay_code = alipay_code;
				obj.name = name;
			} else if (obj.extract_type === 'weixin') {
				if (name.length == 0) return that.$showToast('请填写提现微信号');
				obj.weixin = name;
			}
			if (!RegMoney(money)) return that.$showToast(money.length===0?'请填写提现金额':'请填写正确提现金额');

			if (parseFloat(money) > parseFloat(that.commissionCount) || parseFloat(that.commissionCount) == 0) return that.$showToast('余额不足');
			if (parseFloat(money) < parseFloat(that.minPrice)) return that.$showToast('最低提现金额' + that.minPrice);
			this.save(obj);
		},
		save: function(info) {
			postCashInfo(info).then(
				res => {
					this.$showToast(res.msg);
					this.$navigator('/pages/user/promotion/CashAudit','redirectTo');
				},
				err => {
					this.$showToast(err.msg || err);
				}
			);
		}
	}
};
</script>
<style scoped>
.cash-withdrawal .nav {
	height: 130rpx;
	box-shadow: 0 10rpx 10rpx #f8f8f8;
	-webkit-box-shadow: 0 10rpx 10rpx #f8f8f8;
	-moz-box-shadow: 0 10rpx 10rpx #f8f8f8;
	-o-box-shadow: 0 10rpx 10rpx #f8f8f8;
}

.cash-withdrawal .nav .item {
	font-size: 26rpx;
	flex: 1;
	-webkit-flex: 1;
	-o-flex: 1;
	-ms-flex: 1;
	text-align: center;
}

.cash-withdrawal .nav .item ~ .item {
	border-left: 1px solid #f0f0f0;
}

.cash-withdrawal .nav .item .iconfont {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	border: 2rpx solid #e93323;
	text-align: center;
	line-height: 37rpx;
	margin: 0 auto 6rpx auto;
	font-size: 22rpx;
}

.cash-withdrawal .nav .item .iconfont.on {
	background-color: #e93323;
	color: #fff;
	border-color: #e93323;
}

.cash-withdrawal .nav .item .line {
	width: 2rpx;
	height: 20rpx;
	margin: 0 auto;
	transition: height 0.3s;
	-webkit-transition: height 0.3s;
	-moz-transition: height 0.3s;
	-o-transition: height 0.3s;
}

.cash-withdrawal .nav .item .line.on {
	height: 39rpx;
}

.cash-withdrawal .wrapper .list {
	padding: 0 30rpx;
}

.cash-withdrawal .wrapper .list .item {
	border-bottom: 1px solid #eee;
	height: 107rpx;
	font-size: 30rpx;
	color: #333;
}

.cash-withdrawal .wrapper .list .item .name {
	width: 130rpx;
}

.cash-withdrawal .wrapper .list .item .input {
	width: 505rpx;
}

.cash-withdrawal .wrapper .list .item .input input::placeholder {
	color: #bbb;
}

.cash-withdrawal .wrapper .list .tip {
	font-size: 26rpx;
	color: #999;
	margin-top: 25rpx;
}

.cash-withdrawal .wrapper .list .bnt {
	font-size: 32rpx;
	color: #fff;
	width: 690rpx;
	height: 90rpx;
	text-align: center;
	border-radius: 50rpx;
	line-height: 90rpx;
	margin: 64rpx auto;
}

.cash-withdrawal .wrapper .list .tip2 {
	font-size: 26rpx;
	color: #999;
	text-align: center;
	margin: 44rpx 0 20rpx 0;
}

.cash-withdrawal .wrapper .list .value {
	height: 135rpx;
	line-height: 135rpx;
	border-bottom: 1px solid #eee;
	width: 690rpx;
	margin: 0 auto;
}

.cash-withdrawal .wrapper .list .value input {
	font-size: 80rpx;
	color: #282828;
	height: 135rpx;
	text-align: center;
	width: 100%;
}

.cash-withdrawal .wrapper .list .value input::placeholder {
	color: #bbb;
}
</style>
