<template>
	<view class="promoter-list" ref="container">
		<view class="header">
			<view class="promoterHeader bg-color-red">
				<view class="headerCon acea-row row-between-wrapper">
					<view>
						<view class="name">推广人数</view>
						<view>
							<span class="num">{{ first + second }}</span>
							人
						</view>
					</view>
					<view class="iconfont icon-tuandui"></view>
				</view>
			</view>
			<view class="nav acea-row row-around">
				<view class="item" :class="screen.grade == 0 ? 'on' : ''" @click="checkGrade(0)">一级({{ first }})</view>
				<view class="item" :class="screen.grade == 1 ? 'on' : ''" @click="checkGrade(1)">二级({{ second }})</view>
			</view>
			<view class="search acea-row row-between-wrapper">
					<view class="input">
						<input placeholder="点击搜索会员名称" v-model="screen.keyword" />
						<span class="iconfont icon-guanbi" @click="screen.keyword=''"></span>
					</view>
				<view class="iconfont icon-sousuo2" @click="submitForm"></view>
			</view>
		</view>
		<view class="list">
			<view class="sortNav acea-row row-middle" :class="fixedState === true ? 'on' : ''">
				<view class="sortItem" @click="sort('childCount')">
					团队排序
					<image src="@/static/images/sort1.png" v-if="childCount == 1" />
					<image src="@/static/images/sort2.png" v-if="childCount == 2" />
					<image src="@/static/images/sort3.png" v-if="childCount == 3" />
				</view>
				<view class="sortItem" @click="sort('numberCount')">
					金额排序
					<image src="@/static/images/sort1.png" v-if="numberCount == 1" />
					<image src="@/static/images/sort2.png" v-if="numberCount == 2" />
					<image src="@/static/images/sort3.png" v-if="numberCount == 3" />
				</view>
				<view class="sortItem" @click="sort('orderCount')">
					订单排序
					<image src="@/static/images/sort1.png" v-if="orderCount == 1" />
					<image src="@/static/images/sort2.png" v-if="orderCount == 2" />
					<image src="@/static/images/sort3.png" v-if="orderCount == 3" />
				</view>
			</view>
			<view :class="fixedState === true ? 'sortList' : ''">
				<view class="item acea-row row-between-wrapper" v-for="(val, index) in spreadList" :key="index">
					<view class="picTxt acea-row row-between-wrapper">
						<view class="pictrue"><image :src="val.avatar" /></view>
						<view class="text">
							<view class="name line1">{{ val.nickname }}</view>
							<view>加入时间: {{ val.time }}</view>
						</view>
					</view>
					<view class="right">
						<view>
							<span class="font-color-red">{{ val.childCount }}</span>
							人
						</view>
						<view>{{ val.orderCount }} 单</view>
						<view>{{ val.numberCount ? val.numberCount : 0 }} 元</view>
					</view>
				</view>
			</view>
		</view>
		<Loading :loaded="loaded" :loading="loading"></Loading>
		<x-home></x-home>
	</view>
</template>
<script>
import { getSpreadUser } from '@/api/user';
import Loading from '@/components/Loading';
export default {
	name: 'PromoterList',
	components: {
		Loading
	},
	props: {},
	data: function() {
		return {
			fixedState: false,
			screen: {
				page: 1,
				limit: 15,
				grade: 0,
				keyword: '',
				sort: ''
			},
			childCount: 2,
			numberCount: 2,
			orderCount: 2,
			loaded: false,
			loading: false,
			spreadList: [],
			loadTitle: '',
			first: '',
			second: ''
		};
	},
	onLoad() {
		this.$setNavigationBarColor('#e93323')
	},
	mounted: function() {
		this.getSpreadUsers();
	},
	watch: {
		'screen.sort': function() {
			this.screen.page = 0;
			this.loaded = false;
			this.loading = false;
			this.spreadList = [];
			this.getSpreadUsers();
		}
	},
	methods: {
		submitForm: function() {
			if(this.screen.keyword.trim().length===0) {
				this.screen.keyword = '';
				return this.$showToast('请输入搜索条件')}
			this.screen.page = 0;
			this.loaded = false;
			this.loading = false;
			this.spreadList = [];
			this.getSpreadUsers();
		},
		getSpreadUsers: function() {
			let that = this,
				screen = that.screen;
			if (that.loaded || that.loading) return;
			that.loading = true;
			getSpreadUser(screen).then(
				res => {
					that.loading = false;
					that.spreadList.push.apply(that.spreadList, res.data.list);
					that.loaded = res.data.list.length < that.screen.limit; //判断所有数据是否加载完成；
					that.loadTitle = that.loaded ? '人家是有底线的' : '上拉加载更多';
					that.screen.page = that.screen.page + 1;
					that.first = res.data.total;
					that.second = res.data.totalLevel;
				},
				err => {
					that.$showToast(err.msg || err);
				},
				300
			);
		},
		checkGrade: function(val) {
			if (val == this.screen.grade) return;
			else {
				this.screen.page = 1;
				this.screen.grade = val;
				this.loading = false;
				this.loaded = false;
				this.spreadList = [];
				this.getSpreadUsers();
			}
		},
		sort: function(types) {
			let that = this;
			switch (types) {
				case 'childCount':
					if (that.childCount == 2) {
						that.childCount = 1;
						that.orderCount = 2;
						that.numberCount = 2;
						that.screen.sort = 'childCount DESC';
					} else if (that.childCount == 1) {
						that.childCount = 3;
						that.orderCount = 2;
						that.numberCount = 2;
						that.screen.sort = 'childCount ASC';
					} else if (that.childCount == 3) {
						that.childCount = 2;
						that.orderCount = 2;
						that.numberCount = 2;
						that.screen.sort = '';
					}
					break;
				case 'numberCount':
					if (that.numberCount == 2) {
						that.numberCount = 1;
						that.orderCount = 2;
						that.childCount = 2;
						that.screen.sort = 'numberCount DESC';
					} else if (that.numberCount == 1) {
						that.numberCount = 3;
						that.orderCount = 2;
						that.childCount = 2;
						that.screen.sort = 'numberCount ASC';
					} else if (that.numberCount == 3) {
						that.numberCount = 2;
						that.orderCount = 2;
						that.childCount = 2;
						that.screen.sort = '';
					}
					break;
				case 'orderCount':
					if (that.orderCount == 2) {
						that.orderCount = 1;
						that.numberCount = 2;
						that.childCount = 2;
						that.screen.sort = 'orderCount DESC';
					} else if (that.orderCount == 1) {
						that.orderCount = 3;
						that.numberCount = 2;
						that.childCount = 2;
						that.screen.sort = 'orderCount ASC';
					} else if (that.orderCount == 3) {
						that.orderCount = 2;
						that.numberCount = 2;
						that.childCount = 2;
						that.screen.sort = '';
					}
					break;
				default:
					that.screen.sort = '';
			}
		}
	},
	onReachBottom() {
		this.getSpreadUsers();
	}
};
</script>
<style scoped>
.promoter-list .header {
	padding-bottom: 12rpx;
}

.promoter-list .nav {
	background-color: #fff;
	height: 86rpx;
	line-height: 86rpx;
	font-size: 28rpx;
	color: #282828;
	border-bottom: 1px solid #eee;
}

.promoter-list .nav .item {
	height: 100%;
}

.promoter-list .nav .item.on {
	color: #e93323;
	border-bottom: 5rpx solid #e93323;
}

.promoter-list .search {
	width: 100%;
	background-color: #fff;
	height: 86rpx;
	padding: 0 30rpx;
}

.promoter-list .search .input {
	width: 630rpx;
	height: 60rpx;
	border-radius: 50rpx;
	background-color: #f5f5f5;
	text-align: center;
	position: relative;
}

.promoter-list .search .input input {
	height: 100%;
	font-size: 26rpx;
	width: 620rpx;
	text-align: center;
}

.promoter-list .search .input input::placeholder {
	color: #bbb;
}

.promoter-list .search .input .iconfont {
	position: absolute;
	right: 28rpx;
	color: #999;
	font-size: 28rpx;
	top: 50%;
	transform: translateY(-50%);
	-webkit-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	-moz-transform: translateY(-50%);
	-o-transform: translateY(-50%);
}

.promoter-list .search .iconfont {
	font-size: 40rpx;
	color: #515151;
}

.promoter-list .list .sortNav {
	background-color: #fff;
	height: 76rpx;
	border-bottom: 1px solid #eee;
	color: #333;
	font-size: 28rpx;
}

.promoter-list .list .sortNav.on {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 5;
}

.promoter-list .list .sortNav .sortItem {
	text-align: center;
	flex: 1;
	-webkit-flex: 1;
	-o-flex: 1;
	-ms-flex: 1;
}

.promoter-list .list .sortNav .sortItem image {
	width: 24rpx;
	height: 24rpx;
	margin-left: 6rpx;
	vertical-align: -3rpx;
}

.promoter-list .list .sortList {
	margin-top: 76rpx;
}

.promoter-list .list .item {
	background-color: #fff;
	border-bottom: 1px solid #eee;
	height: 152rpx;
	padding: 0 30rpx 0 20rpx;
	font-size: 24rpx;
	color: #666;
}

.promoter-list .list .item .picTxt {
	width: 440rpx;
}

.promoter-list .list .item .picTxt .pictrue {
	width: 106rpx;
	height: 106rpx;
	border-radius: 50%;
}

.promoter-list .list .item .picTxt .pictrue image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	border: 3rpx solid #fff;
	box-shadow: 0 0 7rpx #aaa;
	-webkit-box-shadow: 0 0 7rpx #aaa;
	-moz-box-shadow: 0 0 7rpx #aaa;
	-o-box-shadow: 0 0 7rpx #aaa;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

.promoter-list .list .item .picTxt .text {
	width: 304rpx;
	font-size: 24rpx;
	color: #666;
}

.promoter-list .list .item .picTxt .text .name {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 13rpx;
}

.promoter-list .list .item .right {
	width: 240rpx;
	text-align: right;
	font-size: 22rpx;
	color: #333;
}
</style>
