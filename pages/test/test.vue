<template>
    <view class="">
        <view @click="playAudio" style="height: 200rpx;">
            播放
        </view>
        <view @click="pauseAudio" style="height: 200rpx;">
            暂停
        </view>
    </view>
</template>
<script>
    export default {
        data() {
            return {
                audioSrc: 'https://env-00jxh67gcayn-static.normal.cloudstatic.cn/admin/static/sound/qqww.mp3',
                isPlaying: false,
                backgroundAudioManager: null
            }
        },
        onUnload() {
            // 页面卸载时先停止
            if (this.backgroundAudioManager) {
              this.backgroundAudioManager.stop();
            }
          },
        methods: {
            
            initAudio() {
                // 获取背景音频管理器
                this.backgroundAudioManager = uni.getBackgroundAudioManager();
                
              
                // 设置音频源但不播放
                this.backgroundAudioManager.title = '我的音频';
                this.backgroundAudioManager.src = this.audioSrc;
                
                // 监听准备就绪事件，防止自动播放
                this.backgroundAudioManager.onCanplay(() => {
                    console.log('onCanplay---')
                    this.backgroundAudioManager.pause();
                });

                // 监听播放状态
                this.backgroundAudioManager.onPlay(() => {
                    this.isPlaying = true;
                });

                this.backgroundAudioManager.onPause(() => {
                    this.isPlaying = false;
                });
            },

            playAudio() {
                this.backgroundAudioManager.play();
            },

            pauseAudio() {
                this.backgroundAudioManager.pause();
            }
        },
        onLoad() {
            this.initAudio();
        }
    }
</script>
<style lang='scss' scoped>
</style>