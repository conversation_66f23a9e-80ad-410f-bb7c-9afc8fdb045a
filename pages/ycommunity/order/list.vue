<template>
    <view class="my-order">
        <!-- #ifndef MP-TOUTIAO -->
        <view class="nav acea-row row-around">
            <view class="item" :class="{ on: type === 0 }" @click="changeType(0)">
                <view class="image flex flex_align_center">
                    <image src="@/static/images/yuanshi/dfk1.png" mode="widthFix" v-if="type === 0" />
                    <image src="@/static/images/yuanshi/dfk.png" mode="widthFix" v-else />
                </view>
                <view class="font_size20">待付款</view>
            </view>
            <view class="item" :class="{ on: type === 1 }" @click="changeType(1)">
                <view class="image flex flex_align_center">
                    <image src="@/static/images/yuanshi/dsh1.png" mode="widthFix" v-if="type === 1" />
                    <image src="@/static/images/yuanshi/dsh.png" mode="widthFix" v-else />
                </view>
                <view class="font_size20">待参加</view>
            </view>
            <view class="item" :class="{ on: type === 2 }" @click="changeType(2)">
                <view class="image flex flex_align_center">
                    <image src="@/static/images/yuanshi/ywc1.png" mode="widthFix" v-if="type === 2" />
                    <image src="@/static/images/yuanshi/ywc.png" mode="widthFix" v-else />
                </view>
                <view class="font_size20">已完成</view>
            </view>
        </view>
        <!-- #endif -->
        <view class="list">
            <view class="item" v-for="order in orderList" :key="order.id">
                <view class="title acea-row row-between-wrapper">
                    <view class="acea-row row-middle">
                        <text class="sign">活动</text>
                        <text class="time">{{ order._add_time }}</text>
                    </view>
                    <!-- #ifndef MP-TOUTIAO -->
                    <view class="font-color-red">{{ order._status._title }}</view>
                    <!-- #endif -->
                </view>
                <view class="wrap" @click="goOrderDetail(order)">
                    <view class="item-info acea-row r row-top" v-for="cart in order.cartInfo" :key="cart.id">
                        <view class="pictrue">
                            <image :src="cart.activityInfo.image" @click.stop="goGoodDetail(order,true)" />
                            <!-- 	<image
								:src="cart.productInfo.image"
								@click.stop="goGoodDetail(cart,order.cartInfo)"
								v-if="cart.combination_id === 0 && cart.bargain_id === 0 && cart.seckill_id === 0"
							/> -->
                            <!-- <image :src="cart.productInfo.image" @click.stop="goPages(`/pages/activity/GroupDetails?id=${cart.combination_id}`)" v-else-if="cart.combination_id > 0" /> -->
                            <!-- <image :src="cart.productInfo.image" @click.stop="goPages(`/pages/activity/DargainDetails?id=${cart.bargain_id}`)" v-else-if="cart.bargain_id > 0" /> -->
                            <!-- <image :src="cart.productInfo.image" @click.stop="goPages(`/pages/activity/SeckillDetails?id=${cart.seckill_id}`)" v-else-if="cart.seckill_id > 0" /> -->
                        </view>
                        <view class="text ">
                            <view class="name ">{{ cart.activityInfo.store_name }}</view>
                            <view class="desc">{{ cart.activityInfo.attrInfo.suk }}</view>
                            <view class="money flex flex_between">
                                <!-- #ifndef MP-TOUTIAO -->
                                <view>￥
                                    {{ cart.activityInfo.attrInfo ? cart.activityInfo.attrInfo.price : cart.activityInfo.price }}
                                </view>
                                <!-- #endif -->
                                <view>x {{ cart.cart_num }}</view>
                            </view>
                        </view>
                    </view>
                    <view class="totalPrice flex flex_align_center flex_between">
                        <view class="flex flex_align_center">
                            共{{ order.cartInfo.length || 0 }}件

                            <!-- #ifndef MP-TOUTIAO -->
                            商品，
                            <template v-if="order.deposit_paid===0&&order.order_type===1 || order._status._type >1">
                                总金额 <text
                                    class="money ">￥{{ order._status._type >1 ? order.total_price : order.pay_price }}</text>
                            </template>
                            <template v-else>
                                <view v-for="(item,index) in order.registrationInfo.orderGroup" :key="index">
                                    <text v-if="order._status._type === 0&&index===0">
                                        总定金 <text class="money ">￥{{ item.pay_price}}</text>
                                    </text>
                                    <text v-if="order._status._type === 1&&index===1">
                                        总尾款 <text class="money ">￥{{ item.pay_price}}</text>
                                    </text>

                                </view>
                            </template>
                            <!-- #endif -->
                        </view>
                        <view class="btn flex_line_height" @click.stop="goOrderDetail(order)"><text
                                class="font_size20">查看详情</text></view>
                    </view>

                    <!-- #ifndef MP-TOUTIAO -->
                    <view class="bottom acea-row row-right row-middle">
                        <template
                            v-if="(order._status._type === 0 || order._status._type == 9 )|| (order._status._type === 1 && payBtnShow(order.activityInfo))">
                            <view class="bnt default flex_line_height" @click.stop="cancelOrder(order)"><text
                                    class="">取消订单</text></view>
                        </template>
                        <template v-if="order._status._type === 0">
                            <view class="bnt  flex_line_height" @click.stop="paymentTap(order,0)"><text
                                    class="">立即付款</text></view>
                        </template>
                        <template v-if="order._status._type === 1 && payBtnShow(order.activityInfo)">
                            <view class="bnt  flex_line_height" @click.stop="paymentTap(order,1)">付尾款</view>
                        </template>
                        <template v-if="order._status._type === 3">
                            <view class="bnt  flex_line_height" @click.stop="goGoodDetail(order,false)"
                                v-if="order.shipping_type === 2 && order.paid === 1"><text class="">去评价</text></view>
                            <view class="bnt  flex_line_height" @click.stop="goOrderDetail(order)" v-else><text
                                    class="">去评价</text></view>
                        </template>
                    </view>
                    <!-- #endif -->
                </view>
            </view>
        </view>
        <uni-popup ref="balancePopups" type="center" :animation="true" :maskClick="false">
            <view class="balance-box">
                <image class="balance-box-close" src="../../../static/images/yuanshi/close1.png" mode=""
                    @click="closeBalancePopups"></image>
                <view class="balance-box-title">
                    <view class="">
                        您有{{orderListlength}}笔订单签到成功！
                    </view>
                </view>
                <view class="balance-box-btn">
                    <view class="balance-box-btn-center" @click="closeBalancePopups">
                        知道了
                    </view>
                </view>
            </view>
        </uni-popup>
        <uni-popup ref="signedInPopups" type="center" :animation="true">
            <view class="balance-box">
                <image class="balance-box-close" src="../../../static/images/yuanshi/close1.png" mode=""
                    @click="closeSignedInPopups"></image>
                <view class="balance-box-title">
                    <view class="">
                        您已签到成功！
                    </view>
                </view>
                <view class="balance-box-btn">
                    <view class="balance-box-btn-center" @click="closeSignedInPopups">
                        知道了
                    </view>
                </view>
            </view>
        </uni-popup>
        <uni-popup ref="noOrderPopups" type="center" :animation="true">
            <view class="balance-box">
                <image class="balance-box-close" src="../../../static/images/yuanshi/close1.png" mode=""
                    @click="closenoOrderPopups"></image>
                <view class="balance-box-title">
                    <view class="">
                        签到失败！没有查询到您的订单。
                    </view>
                </view>
                <view class="balance-box-btn">
                    <view class="balance-box-btn-center" @click="closenoOrderPopups">
                        知道了
                    </view>
                </view>
            </view>
        </uni-popup>

        <xNodate :arr="orderList" :page="page" :isR="false" imgSrc="/wximage/noOrder.png"></xNodate>
        <Loading :loaded="loaded" :loading="loading"></Loading>
        <Payment v-model="pay" :types="payType" @checked="toPay" :balance="userInfo.now_money"></Payment>
        <x-home></x-home>
        <GeneralWindow :generalActive="generalActive" @closeGeneralWindow="closeGeneralWindow"
            :generalContent="generalContent"></GeneralWindow>
        <Agreement v-model="agreementReadpop" ref="mychild1" @isread="isread" :agreementReadtxt="agreementReadtxt">
        </Agreement>
        <x-authorize @login="updateData"></x-authorize>
    </view>
</template>
<script>
    import storage from '@/utils/storage.js'
    import Agreement from '@/components/Agreement';
    import {
        activityOrderList,
        activityOrderDetail,
        activityBatchOrderVerific
    } from '@/api/community.js';
    import {
        cancelOrderHandle,
        payOrderHandle,
        takeOrderHandle
    } from '@/utils/order.js';
    import Loading from '@/components/Loading';
    import Payment from '@/components/Payment';
    import {
        mapGetters
    } from 'vuex';
    import GeneralWindow from '@/components/GeneralWindow';
    import xNodate from '@/components/x-nodata/x-nodata.vue';
    import {
        openOrderSubscribe
    } from '@/utils/SubscribeMessage.js';
    // #ifdef H5
    import {
        isWeixin
    } from '@/utils/validate.js';
    const _isWeixin = isWeixin();
    // #endif
    // #ifdef MP
    const _isWeixin = true;
    // #endif
    const STATUS = ['待付款', '待发货', '待收货', '待评价', '已完成', '', '', '', '', '待付款'];
    const NAME = 'MyOrder';

    export default {
        name: NAME,
        data() {
            return {
                activity_id: 0,
                isname: '',
                offlinePayStatus: 2,
                orderData: {},
                orderInfo: {}, //当前要支付订单信息
                type: 0,
                page: 1,
                limit: 20,
                loaded: false,
                loading: false,
                orderList: [],
                pay: false,
                payType: ['yue', 'weixin'],
                // #ifdef MP-WEIXIN
                from: 'routine',
                // #endif
                // #ifdef MP-TOUTIAO
                from: 'bytedance',
                // #endif
                // #ifdef H5
                from: _isWeixin ? 'weixin' : 'weixinh5',
                // #endif
                generalActive: false,
                generalContent: {
                    promoterNum: '',
                    title: ''
                },
                paymentType: true, //true时，测评版本直接微信支付
                agreementRead: false, // 协议是否已阅
                agreementReadpop: false,
                agreementReadtxt: '',
                orderListlength: 0,
                noOrder: false, // 没用该订单
            };
        },
        components: {
            Loading,
            xNodate,
            Payment,
            GeneralWindow,
            Agreement
        },
        computed: {
            ...mapGetters(['userInfo']),
        },
        methods: {
            updateData() {
                this.type = 1;
                this.getOrderList('sign', this.activity_id)
            },
            getActivityBatchOrderVerific(array) {
                let data = {
                    verify_order: array,
                    is_confirm: 1
                }
                console.log('核销多个订单参数', data)
                activityBatchOrderVerific(data)
                    .then(res => {
                        console.log('核销多个订单成功', res)
                        if (res.msg == '签到成功') {
                            this.type = 2;
                            this.$refs.balancePopups.open()
                            return;
                        }
                    })
                    .catch(err => {
                        let array_order = this.$storage.get('array_order')
                        if (err == '您已签到成功！' && array_order) {
                            this.type = 2;
                            this.$refs.signedInPopups.open()
                            return;
                        }
                    });
            },
            closeSignedInPopups() {
                this.$refs.signedInPopups.close()
                this.changeType(2)
            },
            closeBalancePopups() {
                this.$refs.balancePopups.close()
                this.changeType(2)
            },
            isread(e) {
                this.agreementRead = e;
                this.paymentTap(this.orderInfo)
            },
            payBtnShow(item) {
                const {
                    status,
                    activity_time,
                    ends_time
                } = item;
                const time = Date.parse(new Date()) / 1000;
                // if(status>0&&time>ends_time){
                // 	return false
                // }
                return true
            },
            goPages(path) {
                this.$navigator(path);
            },
            goGoodDetail(order, type) {
                let cartInfo = order.cartInfo;
                let cart = cartInfo[0];
                let path = '/pages/ycommunity/shop/detail?id=' + cart.activity_id;
                this.$navigator(path);
            },
            goOrderDetail(order) {
                openOrderSubscribe().then(() => {
                    this.$navigator(`/pages/ycommunity/order/detail?order_id=${order.order_id}`);
                });
            },
            setOfflinePayStatus: function(status) {
                var that = this;
                that.offlinePayStatus = status;
                if (status === 1) {
                    if (that.payType.indexOf('offline') < 0) {
                        that.payType.push('offline');
                    }
                }
            },
            takeOrder(order) {
                this.$loadingToast('正在加载中');
                takeOrderHandle(order.order_id, 'community')
                    .then(res => {
                        if ((res.data.gain_integral != '0.00' && res.data.gain_coupon != '0.00') || (res.data
                                .gain_integral > 0 && res.data.gain_coupon > 0)) {
                            this.$hideLoading();
                            this.generalActive = true;
                            this.generalContent = {
                                promoterNum: `恭喜您获得${res.data.gain_coupon}元优惠券以及${res.data.gain_integral}积分，购买商品时可抵现哦～`,
                                title: '恭喜您获得优惠礼包'
                            };
                            return;
                        } else if (res.data.gain_integral != '0.00' || res.data.gain_integral > 0) {
                            this.$hideLoading();
                            this.generalActive = true;
                            this.generalContent = {
                                promoterNum: `恭喜您获得${res.data.gain_integral}积分，购买商品时可抵现哦～`,
                                title: '赠送积分'
                            };
                            return;
                        } else if (res.data.gain_coupon != '0.00' || res.data.gain_coupon > 0) {
                            this.$hideLoading();
                            this.generalActive = true;
                            this.generalContent = {
                                promoterNum: `恭喜您获得${res.data.gain_coupon}元优惠券，购买商品时可抵现哦～`,
                                title: '恭喜您获得优惠券'
                            };
                            return;
                        } else {
                            this.$hideLoading();
                            this.$successToast('收货成功');
                        }
                        this.getOrderData();
                        this.orderList = [];
                        this.page = 1;
                        this.loaded = false;
                        this.loading = false;
                        this.getOrderList();
                    })
                    .catch(err => {
                        this.$hideLoading();
                        this.$showToast(err.msg || err);
                    });
            },
            closeGeneralWindow(msg) {
                this.generalActive = msg;
                this.reload();
                this.getOrderData();
            },
            reload() {
                this.changeType(this.type);
            },
            changeType(type) {
                this.type = type;
                this.orderList = [];
                this.page = 1;
                this.loaded = false;
                this.loading = false;
                this.getOrderList();
            },
            getOrderList(issign, activity_id) {
                if (this.loading || this.loaded) return;
                this.loading = true;
                let data = {
                    page: this.page,
                    limit: this.limit,
                    type: this.type,
                }
                if (activity_id) {
                    data.activity_id = activity_id;
                }
                activityOrderList(data, 'community').then(res => {
                    // console.log('res--',res)
                    this.orderList = this.orderList.concat(res.data);
                    this.page++;
                    this.loaded = res.data.length < this.limit;
                    this.loading = false;
                    if (issign == 'sign' && activity_id) {
                        if (this.orderList.length < 1) {
                            if (activity_id == this.$storage.get('activity_id')) {
                                let array = JSON.parse(this.$storage.get('array_order'))
                                if (this.$storage.get('array_order')) {
                                    let array = JSON.parse(this.$storage.get('array_order'))
                                    this.getActivityBatchOrderVerific(array)
                                }
                            } else {
                                return this.$refs.noOrderPopups.open();
                            }
                        }
                        if (this.orderList.length == 1) {
                            let array = [];
                            array.push(this.orderList[0].id)
                            storage.set('array_order', JSON.stringify(array))
                            storage.set('activity_id', this.activity_id)
                            return this.$navigator('/pages/ycommunity/order/detail?order_id=' + this.orderList[
                                0].order_id + '&name=sign');
                        }
                        if (this.orderList.length > 1) {
                            this.orderListlength = this.orderList.length;
                            let array = []
                            for (let i = 0; i < this.orderList.length; i++) {
                                array.push(this.orderList[i].id)
                            }
                            storage.set('array_order', JSON.stringify(array))
                            storage.set('activity_id', this.activity_id)
                            this.getActivityBatchOrderVerific(array)
                        }
                    }
                }).catch(err => {
                    this.loading = false;
                });
            },
            getStatus(order) {
                return STATUS[order._status._type];
            },
            cancelOrder(order) {
                cancelOrderHandle(order.order_id, 'community')
                    .then(() => {
                        this.getOrderData();
                        this.orderList.splice(this.orderList.indexOf(order), 1);
                    })
                    .catch(() => {
                        this.reload();
                    });
            },
            paymentTap(order, type) {
                var that = this;
                that.agreementReadtxt = order.activityInfo.text_agreement;
                if (type == 1 && order.activityInfo.status == 0) {
                    return this.$showToast('活动未开始，如有疑问请联系官方客服')
                }
                if (!(order.combination_id > 0 || order.bargain_id > 0 || order.seckill_id > 0)) {
                    that.setOfflinePayStatus(order.offlinePayStatus);
                }
                this.orderInfo = order;
                if (!this.agreementRead) {
                    this.$showToast('请先同意协议内容');
                    this.agreementReadpop = true;
                    return;
                }
                if (this.paymentType && _isWeixin) {
                    // #ifndef MP-TOUTIAO
                    this.toPay('weixin');
                    // #endif
                    // #ifdef MP-TOUTIAO
                    this.toPay('bytedance');
                    // #endif
                } else {
                    this.pay = true;
                }
            },
            toPay(type) {
                payOrderHandle(this.orderInfo.order_id, type, this.from, 'community')
                    .then(res => {
                        const {
                            status,
                            result
                        } = res;
                        if (status === 'WECHAT_H5_PAY') {
                            // #ifndef MP-TOUTIAO
                            return that.$navigator('/pages/order/PaymentStatus?orderId=' + order.order_id +
                                '&status=5&source=community');
                            // #endif
                        }
                        // if (status === 'BYTEDANCE_PAY') {
                        //     console.log('字节支付----', result)
                        //     tt.pay({
                        //         orderInfo: {
                        //             order_id: tt_res.orderId,
                        //             order_token: tt_res.orderToken,
                        //         },
                        //         service: 5,
                        //         _debug: 1,
                        //         success(res) {
                        //             if (res.code == 0) {
                        //                 console.log('字节支付成功', res)
                        //                 that.$showToast('支付成功');
                        //                 if (that.BargainId || that.combinationId || that
                        //                     .pinkId || that.seckillId) {
                        //                     return that.$navigator(url, 'reLaunch');
                        //                 }
                        //                 return that.$navigator(url, 'redirectTo');
                        //                 // return that.$navigator('/pages/order/PaymentStatus?orderId=' + order.order_id +
                        //                 // 	'&status=5&source=community');
                        //             }
                        //         },
                        //         fail(res) {
                        //             console.log('字节支付失败', res)
                        //             that.$showToast('取消支付');
                        //             return that.$navigator(url + '&status=2', 'redirectTo');
                        //         },
                        //     });
                        // }
                        const type = parseInt(this.type) || 0;
                        this.changeType(type);
                        this.getOrderData();
                    })
                    .catch(err => {
                        this.$showToast(err.msg || err);
                    });
            },
            closenoOrderPopups() {
                this.$refs.noOrderPopups.close()
            }
        },
        onLoad(option) {
            // const {
            //     type = 0
            // } = option;
            // this.type = Number(type);

            const url = decodeURIComponent(option.q)
            let res = {}
            const query = (url.split('?')[1] || '').trim().replace(/^(\?|#|&)/, '')
            if (!query) {
                return res
            }
            query.split('&').forEach(param => {
                const parts = param.replace(/\+/g, ' ').split('=')
                const key = decodeURIComponent(parts.shift())
                const val = parts.length > 0 ? decodeURIComponent(parts.join('=')) : null
                if (res[key] === undefined) {
                    res[key] = val
                } else if (Array.isArray(res[key])) {
                    res[key].push(val)
                } else {
                    res[key] = [res[key], val]
                }
            })
            this.type = res.type;
            this.isname = res.name;
            this.activity_id = res.activity_id;
            console.log('扫码获取参数', res)
            if (this.type == 1 && this.isname == 'sign' && res.activity_id) {
                this.type = 1;
                this.getOrderList('sign', this.activity_id)
            }
        },
        onShow() {
            // #ifdef MP-TOUTIAO
            uni.setNavigationBarTitle({
                title: '我的活动'
            });
            // #endif
            if (!this.isname) {
                this.changeType(this.type);
            }
        },
        onReachBottom() {
            this.getOrderList();
        }
    };
</script>

<style scoped lang="scss">
    .my-order {
        .nav {
            background-color: #fff;

            border-radius: 0px 0px 70rpx 70rpx;
            box-shadow: 0px 0px 30rpx rgba(107, 127, 153, 0.3);
            padding: 52rpx 0 60rpx 0;

            .item {
                text-align: center;
                font-size: 26rpx;
                color: #282828;
                padding: 28rpx 0 20rpx 0;

                .image {
                    width: 112rpx;
                    height: 112rpx;
                    background: #ffffff;
                    box-shadow: 0px 0px 20rpx rgba(107, 127, 153, 0.2);
                    border-radius: 40rpx;
                    margin-bottom: 4rpx;

                    image {
                        width: 52rpx;
                        height: 52rpx;
                        margin: 0 auto;
                    }
                }

                &.on {
                    color: #ff5656;
                    font-weight: 500;
                }

                .num {
                    margin-top: 10rpx;
                    display: none;
                }
            }
        }

        .list {
            padding: 0 20rpx;
            margin: 40rpx auto 0 auto;

            .item {
                background-color: #fff;
                border-radius: 30rpx;
                box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
                margin-bottom: 30rpx;

                .title {
                    height: 84rpx;
                    padding: 0 30rpx;
                    border-bottom: 1rpx solid #eee;
                    font-size: 28rpx;
                    color: #282828;

                    font-weight: 500;
                    border: 4rpx solid #ffffff;
                    border-radius: 30rpx 30rpx 0px 0px;
                    background: #f2f5f8;

                    .sign {
                        padding: 6rpx 14rpx;
                        background: #50506f;
                        border-radius: 8rpx;
                        font-size: 24rpx;
                        margin-right: 15rpx;
                        color: #ffffff;
                    }

                    .time {
                        font-size: 24rpx;
                        color: #3e3e3e;
                        font-weight: 500;
                    }
                }

                .wrap {
                    padding: 30rpx 40rpx 50rpx 40rpx;

                    .item-info {
                        margin-top: 22rpx;
                        padding-bottom: 32rpx;
                        border-bottom: 1rpx solid #eee;

                        .pictrue {
                            width: 134rpx;
                            height: 113rpx;

                            image {
                                width: 100%;
                                height: 100%;
                                border-radius: 30rpx;
                            }
                        }

                        .text {
                            width: calc(100% - 134rpx);
                            padding-left: 20rpx;
                            font-size: 24rpx;
                            color: #333333;

                            .name {
                                font-size: 32rpx;
                                font-weight: bold;
                                color: #333333;
                            }

                            .desc {
                                margin-top: 14rpx;
                                font-size: 24rpx;
                                height: 72rpx;

                                color: #666666;
                            }

                            .money {
                                font-weight: 500;
                            }
                        }
                    }

                    .totalPrice {
                        font-size: 28rpx;
                        color: #282828;
                        text-align: right;
                        padding: 44rpx 0rpx 40rpx 0;
                        border-bottom: 1rpx solid #eee;

                        .money {
                            font-size: 24rpx;
                        }

                        .btn {
                            width: 120rpx;
                            height: 38rpx;
                            text-align: center;
                            line-height: 38rpx;
                            color: #fff;
                            border-radius: 14rpx;
                            border: 2rpx solid #ff5656;
                            color: #ff5656;
                        }
                    }

                    .bottom {
                        margin-top: 32rpx;

                        .bnt {
                            width: 152rpx;
                            height: 60rpx;
                            text-align: center;
                            line-height: 60rpx;
                            color: #fff;
                            border-radius: 24rpx;
                            font-size: 24rpx;
                            background: #ff5656;

                            &.default {
                                color: #50506f;
                                background: #eaeaea;
                            }
                        }

                        & .bnt~.bnt {
                            margin-left: 17rpx;
                        }
                    }
                }
            }
        }
    }

    .balance-box {
        position: relative;
        width: 600rpx;
        background-color: #fff;
        border-radius: 20rpx;
        padding: 40rpx 30rpx 50rpx;
        box-sizing: border-box;

    }

    .balance-box-close {
        position: absolute;
        top: 2rpx;
        right: 2rpx;
        width: 50rpx !important;
        height: 50rpx !important;
    }

    .balance-box-title {
        width: 100%;
        text-align: center;
        color: #000;
        font-size: 28rpx;
        margin-top: 50rpx;

        view {
            margin-bottom: 20rpx;
        }
    }

    .balance-box-btn {
        width: 100%;
        margin-top: 60rpx;
        overflow: auto;

        view {
            width: 240rpx;
            height: 88rpx;
            line-height: 88rpx;
            text-align: center;
            background-color: #fc5656;
            font-size: 26rpx;
            color: #fff;
            border-radius: 12rpx;
        }

        .balance-box-btn-center {
            margin: 0 auto;
        }

    }
</style>
