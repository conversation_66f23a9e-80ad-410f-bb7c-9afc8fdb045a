<template>
	<view class="order_details">
		<!-- 给header上与data上加on为退款订单-->
		<template v-if="!refundOrder && orderInfo.shipping_type === 2 && orderInfo.paid === 1">
			<view class="writeOff">
				<view class="btn_hx" v-if="status.type === 3">已核销。待评价</view>
				<view class="wrap">
					<view class="title flex flex_align_center flex_between" :class="{ dpj: status.type > 2 }">
						<template v-if="status.type < 3">
							<view class="txt">
								<view>待核销，请到指定地点核销</view>
								<view>待核销</view>
							</view>
							<view class="image">
								<image src="@/static/images/yuanshi/scan.png" mode=""></image>
							</view>
						</template>
						<template v-if="status.type === 3">
							<view class="txt">
								<view>已核销，发布你的测评体验</view>
								<view>{{ orderInfo._add_time }}</view>
							</view>
							<view class="image">
								<image src="@/static/images/yuanshi/check.png" mode=""></image>
							</view>
						</template>
						<template v-if="status.type === 4">
							<view class="txt">
								<view>已核销，已发布测评体验</view>
								<view>{{ orderInfo._add_time }}</view>
							</view>
							<view class="image">
								<image src="@/static/images/yuanshi/check.png" mode=""></image>
							</view>
						</template>
					</view>
					<view class="grayBg">
						<view class="pictrue">
							<image :src="orderInfo.code" />
						</view>
					</view>
					<view class="num">{{ orderInfo._verify_code }}</view>
				</view>

				<view class="rules">
					<!-- 	<view class="item">
						<view class="title ">核销时间</view>
						<view class="info">
							每日：
							<text class="time">{{ system_store.day_time }}</text>
						</view>
					</view> -->
					<view class="item">
						<view class="title">使用说明</view>
						<view class="info">{{ orderInfo.info ? orderInfo.info : '可将二维码出示给店员扫描或提供数字核销码' }}</view>
					</view>
				</view>
			</view>

			<view class="map acea-row row-between-wrapper">
				<view>核销地址</view>
				<view class="place " @click="goMap(orderInfo.activityInfo)">
					<!-- <image src="@/static/images/yuanshi/address.png" mode="widthFix"></image> -->
				</view>
			</view>
		</template>
		<view class="content_wrap" :class="{ radius: orderInfo.shipping_type === 1 }">
			<template v-if="!refundOrder">
				<view class="address">
					<template v-if="orderInfo.shipping_type === 1">
						<view class="name">{{ orderInfo.activityInfo.name }}-<text
								class="phone">{{ orderInfo.user_phone }}</text></view>
						<view>{{ orderInfo.user_address }}</view>
					</template>
					<template v-else>
						<view class="name flex flex_align_center flex_between">
							<view style="max-width: 550rpx;">{{ orderInfo.activityInfo.name }}</view>
							<view class="image" @click="goMap(orderInfo.activityInfo)">
								<image src="@/static/images/yuanshi/address.png" mode="widthFix"></image>
							</view>
						</view>
						<view class="desc">{{ orderInfo.activityInfo.detailed_address }}</view>
						<view class="phone" @click="makePhoneCall(orderInfo.activityInfo.contact_info)"
							v-if="orderInfo.activityInfo.contact_info">
							<image src="@/static/images/yuanshi/phone.png" mode="widthFix"></image>
							<text class="phone">{{ orderInfo.activityInfo.contact_info }}</text>
						</view>
                        <view class="wechatGroup" v-if="status.type == 1 || status.type == 2" @click="addChatGroup" >
                            加入活动群
                        </view>
					</template>
				</view>
			</template>

			<view class="order_goods">
				<view class="wrap">
					<view class="item flex" v-for="cart in orderInfo.cartInfo" :key="cart.id">
						<view class="pictrue" @click="goGoodDetail(cart, orderInfo.cartInfo)">
							<image :src="cart.activityInfo.image" alt="img" class="image" />
						</view>
						<view class="text">
							<view class="name ">{{ cart.activityInfo.store_name }}</view>
							<view class="desc">{{ cart.activityInfo.attrInfo.suk }}</view>
							<view class="money flex flex_between">
								<view>
                                    <!-- #ifndef MP-TOUTIAO -->
                                    ￥
									{{ cart.activityInfo.attrInfo ? cart.activityInfo.attrInfo.price : cart.activityInfo.price }}
									<!-- #endif -->
								</view>
								<view>x {{ cart.cart_num }}</view>
							</view>
							<view class="flex flex_end">
								<view class="evaluate " v-if="status.type === 3 && orderInfo.shipping_type !== 2"
									@click="goPages('/pages/shop/GoodsEvaluate?unique=' + cart.unique)">评价</view>
							</view>
						</view>
					</view>
				</view>
				<view class="detail">
					<view class="item acea-row row-between">
						<view>订单编号：</view>
						<view class="conter acea-row row-middle row-right">
							{{ orderInfo.order_id || '' }}
							<!-- <text class="copy copy-data" @click="copy(orderInfo.order_id)">复制</text> -->
						</view>
					</view>
					<view class="item acea-row row-between">
						<view>下单时间：</view>
						<view class="conter">{{ (orderInfo.add_time_y || '') + ' ' + (orderInfo.add_time_h || '') }}
						</view>
					</view>
					<view class="item acea-row row-between">
						<view>订单类型：</view>
						<view class="conter">{{ orderTypeName || '' }}</view>
					</view>
					<!-- 		<view class="item acea-row row-between">
						<view>支付状态：</view>
						<view class="conter">{{ orderInfo._status._title }}</view>
					</view>
					<view class="item acea-row row-between">
						<view>支付方式：</view>
						<view class="conter">{{ orderInfo._status._payType }}</view>
					</view> -->
					<view class="item acea-row row-between" v-if="orderInfo.mark">
						<view>买家留言：</view>
						<view class="conter">{{ orderInfo.mark }}</view>
					</view>
				</view>
				<view v-if="orderInfo.status != 0">
					<view class="detail" v-if="orderInfo.delivery_type === 'express'">
						<view class="item acea-row row-between">
							<view>配送方式：</view>
							<view class="conter">发货</view>
						</view>
						<view class="item acea-row row-between">
							<view>快递公司：</view>
							<view class="conter">{{ orderInfo.delivery_name || '' }}</view>
						</view>
						<view class="item acea-row row-between">
							<view>快递号：</view>
							<view class="conter">{{ orderInfo.delivery_id || '' }}</view>
						</view>
					</view>

					<view class="detail" v-if="orderInfo.delivery_type === 'send'">
						<view class="item acea-row row-between">
							<view>配送方式：</view>
							<view class="conter">送货</view>
						</view>
						<view class="item acea-row row-between">
							<view>配送人：</view>
							<view class="conter">{{ orderInfo.delivery_name || '' }}</view>
						</view>
						<view class="item acea-row row-between">
							<view>配送电话：</view>
							<view class="conter acea-row row-middle row-right">
								{{ orderInfo.delivery_id || '' }}
								<a :href="'tel:' + orderInfo.delivery_id"><text class="copy">拨打</text></a>
							</view>
						</view>
					</view>
				</view>
				<!--     退款订单详情 -->
				<view class="detail" v-if="refundOrder">
					<view class="item acea-row row-between">
						<view>收货人：</view>
						<view class="conter">{{ orderInfo.real_name || '' }}</view>
					</view>
					<view class="item acea-row row-between">
						<view>联系电话：</view>
						<view class="conter">{{ orderInfo.user_phone || '' }}</view>
					</view>
					<view class="item acea-row row-between">
						<view>收货地址：</view>
						<view class="conter">{{ orderInfo.user_address || '' }}</view>
					</view>
				</view>
			</view>
            
            <!-- #ifndef MP-TOUTIAO -->
			<view class="wrapper">
				<template v-if="orderType">
					<!-- 	<view class="item acea-row row-between">
						<view>支付金额：</view>
						<view class="conter">￥{{ orderInfo.total_price || '' }}</view>
					</view> -->
					<view class="item acea-row row-between"
						v-if="orderInfo.deposit_deductions_price&&orderInfo.deposit_deductions_price!=='0.00'">
						<view>折扣：</view>
						<view class="conter">-￥{{ orderInfo.deposit_deductions_price || '' }}</view>
					</view>
					<view class="item acea-row row-between"
						v-if="orderInfo.promo_code_price&&orderInfo.promo_code_price!=='0.00'">
						<view>兑换码：</view>
						<view class="conter">-￥{{ orderInfo.promo_code_price || '' }}</view>
					</view>
					<view class="item acea-row row-between" v-if="orderInfo.coupon_price > 0">
						<view>优惠券抵扣：</view>
						<view class="conter">-￥{{ orderInfo.coupon_price || '' }}</view>
					</view>
					<view class="item acea-row row-between" v-if="orderInfo.coupon_price > 0">
						<view>优惠券抵扣：</view>
						<view class="conter">-￥{{ orderInfo.coupon_price || '' }}</view>
					</view>
					<view class="item acea-row row-between" v-if="orderInfo.use_integral > 0">
						<view>积分抵扣：</view>
						<view class="conter">-￥{{ orderInfo.deduction_price || '' }}</view>
					</view>
					<view class="item acea-row row-between" v-if="orderInfo.pay_postage > 0">
						<view>运费：</view>
						<view class="conter">￥{{ orderInfo.pay_postage || '' }}</view>
					</view>
				</template>
				<view class="item acea-row row-between" v-for="(item,index) in orderInfo.registrationInfo.orderGroup">
					<view>{{item.order_type}}：</view>
					<view class="conter">￥{{ item.pay_price}}</view>
				</view>
				<view class="item actualPay flex flex_between">
					订单金额：
					<text class="money">￥{{ orderInfo.total_price }}</text>
				</view>
                <view class="item actualPay flex flex_between" v-if="!orderInfo.activityInfo.is_apply">
                	联系人：
                	<text class="money">{{ orderInfo.real_name }} / {{ orderInfo.user_phone }}</text>
                </view>
			</view>
            
			<view class="footer acea-row row-right row-middle" v-if="!refundOrder && offlineStatus">
                <button type="default" class="btn" open-type="share">推荐好友</button>
				<template v-if="status.type === 0">
					<view class="btn default" @click="cancelOrder">取消订单</view>
					<view class="btn " @click="paymentTap(0)">立即付款</view>
				</template>
				<template v-if="status.type === 1">
                    <view class="btn default" @click="cancelOrder">取消订单</view>
					<view class="btn " @click="paymentTap(1)">付尾款</view>
				</template>
				<template v-if="status.type ===2  && orderInfo.refund_status === 0">
					<view class="btn default"
						@click="goOrderRefund(`/pages/order/GoodsReturn?source=community&order_id=${orderInfo.order_id}`)">
						申请退款</view>
				</template>
				<template v-if="status.type === 3">
					<view class="btn default"
						@click="goOrderRefund(`/pages/order/GoodsReturn?source=community&order_id=${orderInfo.order_id}`)">
						申请退款</view>
					<view class="btn  default"
						@click.stop="goPages(`/pages/ycommunity/comment/submit?id=${orderInfo.activityInfo.id}&uid=${orderInfo.uid}`)">
						<text class="">去评价</text>
					</view>
				</template>
				<template v-if="status.type === 4">
					<view class="btn default" @click="delOrder">删除订单</view>
				</template>

				<view class="btn " @click="goOrderConfirm(orderInfo)"
					v-if=" orderInfo.refund_status === 0 && orderInfo.status >= 2">再次购买</view>
			</view>
			<!-- #endif -->
		</view>
        <view class="map acea-row row-between-wrapper" v-if="orderInfo.applyInfo[0]">
        	<view class="information-left">报名信息</view>
            <view class="information-right">
                <text class="information-right-title">报名状态:</text>
                <text v-show="orderInfo.applyInfo[0].status == 0">初始</text>
                <text v-show="orderInfo.applyInfo[0].status == 1">报名成功</text>
                <text v-show="orderInfo.applyInfo[0].status == 2">初审</text>
                <text v-show="orderInfo.applyInfo[0].status == 3">初审不通过</text>
                <text v-show="orderInfo.applyInfo[0].status == 4">预约面试</text>
                <text v-show="orderInfo.applyInfo[0].status == 5">已预约面试</text>
                <text v-show="orderInfo.applyInfo[0].status == 6">面试不通过</text>
                <text v-show="orderInfo.applyInfo[0].status == 7">报名缴费</text>
            </view>
        </view>
        <view class="content_wrap" v-if="orderInfo.activityInfo.is_apply">
        	<view class="order_goods marginb20">
        		<view >
        			<view class="detail marginb20" style="border: 0;">
        				<view class="item acea-row row-between">
        					<view>联系人</view>
        					<view class="conter">{{ orderInfo.real_name }} / {{ orderInfo.user_phone }}</view>
        				</view>
        				<view class="item acea-row row-between border-b">
        					<view v-if="orderInfo.applyInfo[0].real_name">学员姓名： {{orderInfo.applyInfo[0].real_name}}</view>
        					<view v-if="orderInfo.applyInfo[0].age" class="conter">年龄： {{orderInfo.applyInfo[0].age}}岁</view>
        				</view>
        				<view class="item acea-row row-between border-b" v-if="orderInfo.applyInfo[0].sex">
        					<view>性别： {{orderInfo.applyInfo[0].sex == 1?'男':'女'}}</view>
        				</view>
                        
                        <view class="item acea-row row-between" v-if="orderInfo.applyInfo[0].career.length >= 1">
                        	<view>职业： </view>
                        	<view class="conter">
                                <block v-for="(item, index) in orderInfo.applyInfo[0].career" :key="index">
                                    <text >{{item}}</text>
                                </block>
                            </view>
                            
                        </view>
                        <view class="item border-b" v-if="orderInfo.applyInfo[0].learning_experience.length >= 1 || orderInfo.applyInfo[0].skills.length >= 1 ">
                            <view class="experien-title">音乐基础： </view>
                            <view class="experience" v-if="orderInfo.applyInfo[0].learning_experience.length >= 1">
                                <view class="experience-title">
                                    学习经历
                                </view>
                                <block v-for="(item, index) in orderInfo.applyInfo[0].learning_experience">
                                    <view class="experience-item">
                                        <text class="experience-item-left white420">{{item.content}}</text>
                                    </view>
                                </block>
                            </view>
                            <view class="experience" v-if="orderInfo.applyInfo[0].skills.length >= 1">
                                <view class="experience-title">
                                    擅长乐器
                                </view>
                                <block v-for="(item, index) in orderInfo.applyInfo[0].skills">
                                    <view class="experience-item">
                                        <text class="experience-item-left">{{item.cate_name}}</text>
                                        <text class="experience-item-right">{{item.content}}</text>
                                    </view>
                                </block>
                            </view>
                        </view>
                        <view class="item order-video">
                            <view class="order-video-top" v-if="orderInfo.applyInfo[0].video_works">
                                <view class="left">
                                    已提交作品：
                                </view>
                                <video class="right" :src="orderInfo.applyInfo[0].video_works"></video>
                            </view>
                            
                            <view class="order-video-bot" v-if="orderInfo.applyInfo[0].video_links">
                                <view class="order-video-bot-top">
                                    作品外链地址：
                                </view>
                                <view class="order-video-bot-bot">
                                    <text class="left">
                                        {{orderInfo.applyInfo[0].video_links}}
                                    </text>
                                    <view class="right" @click="copyUrl(orderInfo.applyInfo[0].video_links)">
                                        复制
                                    </view>
                                </view>
                            </view>
                        </view>
        			</view>
                    
        		</view>
        	</view>
        </view>
        <uni-popup ref="orderPopup" type="center" :animation="true" @click="addChatGroupClose">
            <view class="" @click.stop=""
                style="width: 400rpx;background-color: #fff;border-radius: 20rpx;padding:20rpx;">
                <view class="wechatGroup-img">
                    <image show-menu-by-longpress="true" :src="orderInfo.join_group_image" mode="widthFix"></image>
                </view>
                <view class="text_left" style="padding:20rpx">
                    重要！扫描二维码加入
                    <!-- #ifndef MP-TOUTIAO -->
                    微信
                    <!-- #endif -->
                    <!-- #ifdef MP-TOUTIAO -->
                    抖音
                    <!-- #endif -->
                    活动消息通知群
                </view>
                <view class="tt_text_left">
                    <view class="tt_text_left_btn1" @click="addChatGroupClose">
                        关闭
                    </view>
                    <view class="tt_text_left_btn2" @click="saveimg(orderInfo.join_group_image)">
                        保存
                    </view>
                </view>
            </view>
        </uni-popup>
        <uni-popup ref="balancePopups" type="center" :animation="true">
            <view class="balance-box">
                <image class="balance-box-close" src="../../../static/images/yuanshi/close1.png" mode=""
                    @click="closeBalancePopups"></image>
                <view class="balance-box-title">
                    <view class="">
                        签到成功
                    </view>
                </view>
                <view class="balance-box-btn">
                    <view class="balance-box-btn-center" @click="closeBalancePopups">
                        知道了
                    </view>
                </view>
            </view>
        </uni-popup>
		<Payment v-model="pay" :types="payType" @checked="toPay" :balance="userInfo.now_money"></Payment>
		<x-home></x-home>
		<GeneralWindow :generalActive="generalActive" @closeGeneralWindow="closeGeneralWindow"
			:generalContent="generalContent"></GeneralWindow>
        <Agreement v-model="agreementReadpop" ref="mychild1" @isread="isread" :agreementReadtxt="orderInfo.activityInfo.text_agreement"></Agreement>
	</view>
</template>

<script>
    import Agreement from '@/components/Agreement';
	import OrderGoods from '@/components/OrderGoods';
	import goMap from '@/mixins/goMap.js';
	import {
		activityOrderDetail,
		activityOrderAgain,
        activityBatchOrderVerific
	} from '@/api/community.js';
	import Payment from '@/components/Payment';
	import {
		mapGetters
	} from 'vuex';
	import {
		cancelOrderHandle,
		takeOrderHandle,
		delOrderHandle,
		payOrderHandle
	} from '@/utils/order.js';

	import GeneralWindow from '@/components/GeneralWindow';
	import {
		authOpenLocation,
		setClipboardData
	} from '@/utils/common.js';
	import {
		openOrderRefundSubscribe
	} from '@/utils/SubscribeMessage.js';
	// #ifdef H5
	import {
		isWeixin
	} from '@/utils/validate.js';

	const _isWeixin = isWeixin();
	// #endif

	// #ifdef MP
	const _isWeixin = true;
	// #endif

	const NAME = 'OrderDetails';
	export default {
		name: NAME,
		components: {
			OrderGoods,
			Payment,
			GeneralWindow,
            Agreement
		},
		mixins: [goMap],
		props: {},
		data: function() {
			return {
                isname:'',
				offlinePayStatus: 2,
				orderTypeName: '普通订单',
				orderTypeNameStatus: true,
				offlineStatus: true,
				id: '',
				orderInfo: {
					_status: {},
					cartInfo: [],
					activityInfo: {},
					registrationInfo: {
						orderGroup: []
					}
				},
				status: {},
				pay: false,
				payType: ['yue', 'weixin'],
				// #ifdef MP-WEIXIN
				from: 'routine',
				// #endif
				// #ifdef MP-TOUTIAO
				from: 'bytedance',
				// #endif
				// #ifdef H5
				from: _isWeixin ? 'weixin' : 'weixinh5',
				// #endif
				mapKay: '',
				mapShow: false,
				generalActive: false,
				generalContent: {
					promoterNum: '',
					title: ''
				},
				paymentType: true ,//true时，测评版本直接微信支付
                agreementRead: false ,// 协议是否已阅
                agreementReadpop: false,
                agreementReadtxt:''
			};
		},
		computed: {
			refundOrder() {
				return this.orderInfo.refund_status > 0;
			},
			orderType() {
				let typ = true;
				this.orderInfo.cartInfo.forEach((item, index) => {
					if (item.type === 'evaluate_product') {
						typ = false;
					}
				});
				return typ;
			},
			...mapGetters(['userInfo'])
		},
		mounted: function() {},
        onShareAppMessage(res) {
            if (res.from === 'button') { // 来自页面内分享按钮
                // console.log(res.target)
            }
            return {
                title: '您的好友' + this.userInfo.nickname + '邀请您一起参加活动',
                imageUrl: this.orderInfo.activityInfo.image,
                path: 'pages/ycommunity/shop/detail?id=' + this.orderInfo.activityInfo.id + '&inviterId=' + this.orderInfo.uid
            }
        },
		onLoad(options) {
			const {
				order_id,
                name
			} = options;
			this.id = order_id;
            this.isname = name
		},
		onShow() {
			this.getDetail();
		},
		methods: {
            closeBalancePopups() {
                this.$refs.balancePopups.close()
            },
            copyUrl(txt){
                let that = this;
                uni.setClipboardData({
                	data: txt,
                	success: function () {
                		return that.$showToast('链接复制成功')
                	}
                });
            },
            saveimg(url) {
                let that = this;
                uni.downloadFile({
                    url: url,
                    header: {
                        "content-type": "application/json",
                    },
                    success: (res) => {
                        console.log('res', res)
                        if (res.statusCode === 200) {
                            uni.saveImageToPhotosAlbum({
                                filePath: res.tempFilePath,
                                success(res) {
                                    return that.$showToast('已保存相册')
                                },
                                fail(res) {
                                    console.log(res);
                                    return that.$showToast('无相册权限')
                                }
                            });
                        }
                    }
                })
            },
            isread(e){
                this.agreementRead = e;
                this.paymentTap()
            },
            addChatGroup() {
                this.$refs.orderPopup.open()
            },
            addChatGroupClose() {
                this.$refs.orderPopup.close()
            },
			goPages(path) {
				this.$navigator(path);
			},
			goGoodDetail(cart, cartInfo) {
				let path = `/pages/shop/GoodsCon?id=${cart.productInfo.id}`,
					typ = true;
				cartInfo.forEach((item, index) => {
					if (item.type === 'evaluate_product') {
						typ = false;
					}
				});
				if (!typ) {
					path = '/pages/yuanshi/evaluate/detail?wid=' + cart.wish_id + '&pid=' + cart.product_id;
				}
				this.$navigator(path);
			},
			makePhoneCall(phone) {
				uni.makePhoneCall({
					phoneNumber: phone //仅为示例
				});
			},

			goOrderRefund(path) {
				openOrderRefundSubscribe().then(() => {
					this.$navigator(path);
				});
			},
			copy(val) {
				setClipboardData(val);
			},
			paymentTap(type) {
                if(type == 1 && this.orderInfo.activityInfo.status == 0){
                    return this.$showToast('活动未开始，如有疑问请联系官方客服')
                }
                if (!this.agreementRead) {
                    this.$showToast('请先同意协议内容');
                    this.agreementReadpop = true;
                    return;
                }
				const {
					status,
					activity_time,
					ends_time
				} = this.orderInfo.activityInfo;
				const time = Date.parse(new Date()) / 1000;
				if (status > 0 && time > ends_time) {
					return this.$showToast('已过截止时间')
				}
				if (this.paymentType && _isWeixin) {
					// #ifndef MP-TOUTIAO
					this.toPay('weixin');
					// #endif
					// #ifdef MP-TOUTIAO
					this.toPay('bytedance');
					// #endif
				} else {
					this.pay = true;
				}
			},
			// 再次购买
			goOrderConfirm(e) {
				activityOrderAgain(e.order_id)
					.then(res => {
						this.$navigator('/pages/ycommunity/shop/submit?cartId=' + res.data.cateId);
					})
					.catch(res => {
						console.log(res);
						this.$showToast(res.msg || res);
					});
			},
			closeGeneralWindow(msg) {
				this.generalActive = msg;
				this.getDetail();
			},
			goBack() {
				const history = getCurrentPages();
				if (history.length > 1) return this.$navigator(-1);
				else return this.$navigator('/pages/order/MyOrder', 'redirectTo');
			},
			cancelOrder() {
				cancelOrderHandle(this.orderInfo.order_id, 'community')
					.then(() => {
						setTimeout(() => this.goBack(), 300);
					})
					.catch(() => {
						this.getDetail();
					});
			},
			takeOrder() {
				this.$loadingToast('正在加载中');
				takeOrderHandle(this.orderInfo.order_id, 'community')
					.then(res => {
						if ((res.data.gain_integral != '0.00' && res.data.gain_coupon != '0.00') || (res.data
								.gain_integral > 0 && res.data.gain_coupon > 0)) {
							this.$hideLoading();
							this.generalActive = true;
							this.generalContent = {
								promoterNum: `恭喜您获得${res.data.gain_coupon}元优惠券以及${res.data.gain_integral}积分，购买商品时可抵现哦～`,
								title: '恭喜您获得优惠礼包'
							};
							return;
						} else if (res.data.gain_integral != '0.00' || res.data.gain_integral > 0) {
							this.$hideLoading();
							this.generalActive = true;
							this.generalContent = {
								promoterNum: `恭喜您获得${res.data.gain_integral}积分，购买商品时可抵现哦～`,
								title: '赠送积分'
							};
							return;
						} else if (res.data.gain_coupon != '0.00' || res.data.gain_coupon > 0) {
							this.$hideLoading();
							this.generalActive = true;
							this.generalContent = {
								promoterNum: `恭喜您获得${res.data.gain_coupon}元优惠券，购买商品时可抵现哦～`,
								title: '恭喜您获得优惠券'
							};
							return;
						} else {
							this.$hideLoading();
							this.$successToast('收货成功');
						}
						this.getDetail();
					})
					.catch(res => {
						this.$hideLoading();
						this.$showToast(res.msg || res);
					});
			},
			delOrder() {
				delOrderHandle(this.orderInfo.order_id, 'community').then(() => {
					setTimeout(() => this.goBack(), 300);
				});
			},
			setOfflinePayStatus: function(status) {
				var that = this;
				that.offlinePayStatus = status;
				if (status === 1 && that.orderTypeNameStatus === true) {
					that.payType.push('offline');
				}
			},
			getOrderStatus: function() {
				let orderInfo = this.orderInfo || {},
					_status = orderInfo._status || {
						_type: 0
					},
					status = {};
				let type = parseInt(_status._type),
					delivery_type = orderInfo.delivery_type,
					seckill_id = orderInfo.seckill_id ? parseInt(orderInfo.seckill_id) : 0,
					bargain_id = orderInfo.bargain_id ? parseInt(orderInfo.bargain_id) : 0,
					combination_id = orderInfo.combination_id ? parseInt(orderInfo.combination_id) : 0;
				status = {
					type: type,
					class_status: 0
				};
				if (type === 1 && combination_id > 0) {
					status.type = 6;
					status.class_status = 1;
				} //查看拼团
				if (type === 2 && delivery_type === 'express') status.class_status = 2; //查看物流
				if (type === 2) status.class_status = 3; //确认收货
				if (type === 4 || type === 0) status.class_status = 4; //删除订单
				if (!seckill_id && !bargain_id && !combination_id && (type === 3 || type === 4)) status.class_status =
					5; //再次购买
				if (type == 9) {
					//线下付款
					status.class_status = 0;
					this.offlineStatus = false;
				}
				this.status = status;
			},
			getDetail() {
				const id = this.id;
                const invite_uid = this.$storage.get('invite_uid')
                console.log('邀请人id',invite_uid)
				if (!id) return this.$showToast('订单不存在');
				activityOrderDetail(id,invite_uid)
					.then(res => {
						this.orderInfo = res.data;
						this.getOrderStatus();
						this.mapKey = res.data.mapKey;
						this.setOfflinePayStatus(this.orderInfo.offlinePayStatus);
                        if(this.isname == 'sign'){
                            this.getActivityBatchOrderVerific([this.orderInfo.id])
                        }
					})
					.catch(err => {
						this.$showToast(err.msg || err);
						// this.$router.go(-1);
					});
			},
            getActivityBatchOrderVerific(array){
                let data = {
                    verify_order:array,
                    is_confirm:1
                }
                console.log('核销订单参数', data)
                activityBatchOrderVerific(data)
                    .then(res => {
                        console.log('单个订单成功', res)
                        this.isname = '';
                        this.getDetail();
                        this.$refs.balancePopups.open()
                    })
                    .catch(err => {
                        console.log('单个订单失败', err)
                    });
            },
			async toPay(type) {
				var that = this;
				payOrderHandle(this.orderInfo.order_id, type, that.from, 'community')
					.then(res => {
						const {
							status,
							result
						} = res;
						if (status === 'WECHAT_H5_PAY') {
							return that.$navigator('/pages/order/PaymentStatus?orderId=' + this.orderInfo
								.order_id + '&status=0&source=community');
						}
						that.getDetail();
					})
					.catch(err => {
						this.$showToast(err.msg || err);
					});
			}
		}
	};
</script>
<style scoped lang="scss">
	.goodCall {
		color: #e93323;
		text-align: center;
		width: 100%;
		height: 86rpx;
		padding: 0 30rpx;
		border-bottom: 1rpx solid #eee;
		font-size: 30rpx;
		line-height: 86rpx;
		background: #fff;
	}

	.geoPage {
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		z-index: 10000;
	}

	.order_details {
		min-height: 100vh;
		padding: 30rpx 22rpx;

		.writeOff {
			.btn_hx {
				width: 670rpx;
				height: 100rpx;
				line-height: 100rpx;
				background: #ff5656;
				border-radius: 40rpx;
				text-align: center;
				margin: 0 auto 40rpx auto;

				font-size: 28rpx;
				color: #ffffff;
			}

			.wrap {
				background-color: #fff;
				border: 6rpx solid #ffffff;
				border-radius: 30rpx;
				overflow: hidden;
				min-height: 800rpx;

				.title {
					font-size: 28rpx;
					color: #ffffff;
					height: 170rpx;
					padding: 44rpx 60rpx;
					background: #fc5656;

					&.dpj {
						background: #f2f5f8;

						color: #50506f;
					}

					.txt {
						padding: 2rpx 0;
					}

					.image {
						image {
							width: 72rpx;
							height: 72rpx;
						}
					}
				}

				.grayBg {
					margin: 30rpx 0;

					.pictrue {
						width: 462rpx;
						height: 462rpx;
						margin: 0 auto;

						image {
							width: 100%;
							height: 100%;
							display: block;
						}
					}
				}

				.num {
					background: #f2f5f8;
					height: 170rpx;
					line-height: 170rpx;

					font-weight: 500;

					color: #666666;
					font-size: 52rpx;
					text-align: center;
				}
			}

			.rules {
				box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
				border-radius: 30rpx;
				background: #ffffff;
				margin: 40rpx 0 30rpx 0rpx;
				padding: 50rpx 40rpx;

				.item {

					// margin-top: 15rpx;
					&:not(:last-child) {
						margin-bottom: 30rpx;
					}

					.title {
						font-size: 28rpx;
						font-weight: bold;
						color: #333333;
					}

					.info {
						margin-top: 10rpx;
						font-size: 24rpx;
						color: #666666;
						font-weight: 400;
					}
				}
			}
		}

		.map {
			height: 98rpx;
			font-size: 28rpx;

			font-weight: bold;

			color: #333333;
			line-height: 98rpx;
			background: #f2f5f8;
			border: 4rpx solid #ffffff;
			margin-top: 13rpx;
			border-radius: 30rpx 30rpx 0px 0px;
			padding: 0 40rpx;
            .information-left {
                float: left;
            }
            .information-right {
                float: right;
                .information-right-title {
                    margin-right: 24rpx;
                }
            }
			.place {
				image {
					width: 27rpx;
				}
			}
		}

		.content_wrap {
			background: #fff;
			padding: 0 40rpx 50rpx 40rpx;
			border-radius: 0 0 30rpx 30rpx;
			box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);

			&.radius {
				border-radius: 30rpx;
			}

			.address {
				color: #333333;
				font-weight: 400;
				font-size: 24rpx;
				padding: 30rpx 0rpx 20rpx 0rpx;
                box-sizing: border-box;
                overflow: hidden;

				.name {
					color: #333333;
					font-weight: bold;
					font-size: 32rpx;

					.image {
						image {
							width: 27rpx;
						}
					}
				}

				.desc {
					margin: 14rpx 0 30rpx 0;
				}

				.phone {
                    float: left;
                    
					image {
                        float: left;
						width: 28rpx;
					}
				}
                
                .wechatGroup {
                    float: right;
                    background-color: #6bb4ff;
                        border-radius: 20rpx;
                        width: 180rpx;
                        height: 60rpx;
                        line-height:60rpx;
                        text-align: center;
                        font-size: 34rpxpx;
                        font-weight: 400;
                        text-align: center;
                        color: #ffffff;
                }
			}

			.order_goods {
                &.marginb20 {
                    margin-bottom: 20rpx;
                }
                
				.wrap {
					padding: 40rpx 0 32rpx 0;
					border-top: 2rpx solid #eaeaea;
					border-bottom: 2rpx solid #eaeaea;

					.item {
						.pictrue {
							width: 134rpx;
							height: 113rpx;

							image {
								width: 100%;
								height: 100%;
								border-radius: 30rpx;
							}
						}

						.text {
							width: calc(100% - 134rpx);
							padding-left: 20rpx;
							font-size: 24rpx;
							color: #333333;

							.name {
								font-size: 32rpx;
								font-weight: bold;
								color: #333333;
							}

							.desc {
								margin-top: 14rpx;
								font-size: 24rpx;
								height: 72rpx;

								color: #666666;
							}

							.money {
								font-weight: 500;
							}
						}
					}
				}

				.detail {
					padding: 40rpx 0;
					border-bottom: 2rpx solid #eaeaea;
                    
                    
                    &.marginb20 {
                        margin-bottom: 20rpx;
                    }
					.item {
						color: #333333;
						font-size: 24rpx;
                        &.order-video {
                            width: 100%;
                        }
                        .order-video-top {
                            width: 100%;
                            overflow: auto;
                            .left {
                                float: left;
                                width: 300rpx;
                            }
                            .right {
                                float: left;
                               width: 200rpx;
                                   height: 200rpx;
                                   border-radius: 12rpx;
                            }
                        }
                        .order-video-bot {
                            width: 100%;
                            .order-video-bot-top {
                                width: 100%;
                                margin-top: 20rpx;
                            }
                            .order-video-bot-bot {
                                width: 100%;
                                overflow: auto;
                                margin-top: 20rpx;
                                
                                .left {
                                    float: left;
                                    width: 500rpx;
                                    word-break: break-all;
                                }
                                .right {
                                    float: right;
                                        width: 120rpx;
                                        height: 50rpx;
                                        line-height: 50rpx;
                                        border: 2rpx solid #333;
                                        border-radius: 12rpx;
                                        text-align: center;
                                }
                            }
                        }
                        
                        &.border-b {
                            border-bottom: 2rpx solid #eaeaea;
                            padding-bottom: 30rpx;
                        } 
                        
						&.item~.item {
							margin-top: 30rpx;
						}
                        .experien-title {
                            width: 100%;
                            height: 80rpx;
                            line-height: 80rpx;
                        }
                        .experience {
                            width: 100%;
                            overflow: auto;
                            margin-bottom: 20rpx;
                            
                            .experience-title {
                                width: 100%;
                                padding-left: 50rpx;
                                box-sizing: border-box;
                                margin-bottom: 8rpx;
                            }
                            .experience-item {
                                width: 100%;
                                padding-left: 100rpx;
                                box-sizing: border-box;
                                overflow: auto;
                                    margin-bottom: 8rpx;
                                
                                .experience-item-left {
                                    float: left;
                                    width: 220rpx;
                                    &.white420 {
                                        width: 420rpx;
                                    }
                                }
                                .experience-item-right {
                                    float: left;
                                }
                            }
                        }
					}
				}
			}

			.wrapper {
				padding: 40rpx 0;

				// border-bottom: 2rpx solid #eaeaea;
				.item {
					color: #333333;
					font-size: 24rpx;

					&.item~.item {
						margin-top: 30rpx;
					}

					&.actualPay {
						color: #333333;
						font-weight: 500;
						font-size: 28rpx;
					}
				}
			}

			.footer {
				padding: 0 58rpx;
				margin-top: 32rpx;

				border-radius: 8rpx 8rpx 0px 0px;
				box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
				background: #ffffff;
				@include fixed_footer(128rpx);

				.btn {
					width: 152rpx;
					height: 60rpx;
					line-height: 60rpx;
					text-align: center;
					background: #ff5656;
					font-size: 24rpx;
					color: #ffffff;
					border-radius: 24rpx;

					&.default {
						color: #50506f;
						background: #eaeaea;
					}
				}

				& .btn~.btn {
					margin-left: 8rpx;
				}
			}
		}
	}
    .wechatGroup-img {
        
        image {
            display: block;
            width: 300rpx;
            height: 300rpx;
            margin: 0 auto;
        }
    }
	.evaluate {
		width: 110rpx;
		height: 40rpx;
		text-align: center;
		line-height: 40rpx;
		color: #fff;
		border-radius: 24rpx;
		font-size: 24rpx;
		background: #ff5656;
		margin-top: 10rpx;
	}
    .tt_text_left {
        width: 100%;
        height: auto;
        overflow: hidden;
        padding: 0 20rpx;
    }
    
    .tt_text_left_btn1 {
        float: left;
        width: 150rpx;
        height: 70rpx;
        margin: 0 auto;
        border: 1rpx solid #e6e6e6;
        border-radius: 10rpx;
        text-align: center;
        line-height: 70rpx;
    }
    
    .tt_text_left_btn2 {
        float: right;
        width: 150rpx;
        height: 70rpx;
        background-color: #e93323;
        color: #fff;
        font-size: 28rpx;
        border-radius: 10rpx;
        text-align: center;
        line-height: 70rpx;
    }
    .balance-box {
        position: relative;
        width: 600rpx;
        background-color: #fff;
        border-radius: 20rpx;
        padding: 40rpx 30rpx 50rpx;
        box-sizing: border-box;
    
    }
    
    .balance-box-close {
        position: absolute;
        top: 2rpx;
        right: 2rpx;
        width: 50rpx !important;
        height: 50rpx !important;
    }
    
    .balance-box-title {
        width: 100%;
        text-align: center;
        color: #000;
        font-size: 28rpx;
        margin-top: 50rpx;
    
        view {
            margin-bottom: 20rpx;
        }
    }
    
    .balance-box-btn {
        width: 100%;
        margin-top: 60rpx;
        overflow: auto;
    
        view {
            width: 240rpx;
            height: 88rpx;
            line-height: 88rpx;
            text-align: center;
            background-color: #fc5656;
            font-size: 26rpx;
            color: #fff;
            border-radius: 12rpx;
        }
    
        .balance-box-btn-center {
            margin: 0 auto;
        }
    
    }
</style>
