<template>
    <view class="comment">
        <view class="flex flex_align_center flex_between">
            <view class="flex flex_align_center avatar">
                <view>
                    <u-avatar :src="detail.avatar" mode="circle"></u-avatar>
                </view>
                <view style="font-size: 24rpx;color: #666666;margin-left: 18px;">
                    <view class="nickname">{{detail.nickname || ''}}</view>
                    <view class="add_time">{{detail.add_time || ''}} | {{detail.browse || 0}}次浏览</view>
                    <text class="homework" v-if="detail.message_type == 1">作业</text>
                </view>
            </view>
            <button class="flex_line_height btn_share" open-type="share" style="background: none;">
                <view class="flex flex_align_center">
                    <image src="@/static/images/community/share.png" mode="widthFix"></image>
                    <text>分享</text>
                </view>
            </button>
        </view>
        <swiper class="swiper" v-if="detail.video.length || detail.image.length">
            <swiper-item class="swiper-item video" v-for="(item1, index1) in detail.video"
                v-if="detail.video&&detail.video.length" :key="item1.name">
                <video :id="`playVideo${index1}`" style="width:750rpx;height: 750rpx;" :show-center-play-btn="false"
                    :controls="isControl" :src="item1.name" custom-cache="false"></video>
                <view class="cover flex flex_align_center flex_around" v-show="!isControl">
                    <image src="@/static/images/community/play.png" @click="playVideo(item1,index1)">
                </view>
            </swiper-item>
            <swiper-item class="swiper-item" v-for="(item1, index1) in detail.image"
                v-if="detail.image&&detail.image.length" :key="item1.name">
                <image :src="item1.name" class="image-item" mode="aspectFit" @click.stop="previewImage(detail.image, item1.name)"></image>
            </swiper-item>
        </swiper>
        <view class="audio" v-if="detail.audio&&detail.audio.length">
            <xPlay ref='xPlay' :music="detail.audio[0].name" />
        </view>
        <view class="wrap">
            <view class="desc">
                <view class=" flex flex_between">
                    <view class="title" style="flex:1">
                        {{detail.title || ''}}
                    </view>
                    <view class="btn flex_line_height" @click="detailShowInput">回复</view>
                </view>
                <view class="relative">
                    <view class="show ">
                        {{detail.comment || ''}}
                    </view>
                    <view class="refining_span" v-if="detail.is_refining">精</view>
                </view>
                <view class="footer flex flex_between">
                    <view class="footer_l">{{detail.add_time}}</view>
                    <view class="footer_r">
                        <view class="flex flex_align_center">
                            <view class="vote-txt" v-if="ids.detailType == 2" @click="likeStars(detail,null,true)">投票</view>
                            <view class="item flex flex_align_center" @click="likeStars(detail,null,true)">
                                <image src="@/static/images/yuanshi/like_.png" mode="widthFix" v-if="detail.is_like">
                                </image>
                                <image src="@/static/images/yuanshi/like.png" mode="widthFix" v-else></image>
                                <text>{{detail.like_count}}</text>
                            </view>
                            <view class="item flex flex_align_center">
                                <image src="@/static/images/yuanshi/comment.png" mode="widthFix"></image>
                                <text >{{detail.comments || 0}}</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="want_go">
                <view class="nav ">
                    <view class="title">{{detail.specialInfo.type == 2?'投票':'点赞'}}的同学们</view>
                    <view class="go" @click="helpAdd">{{detail.specialInfo.type == 2?'投票':'点赞'}}+1</view>
                </view>
                <view class="wrap1 " v-if="detail.like_list.length">
                    <scroll-view scroll-x style="white-space:nowrap;" :scroll-with-animation="true">
                        <view class="item" v-for="(item, index) in detail.like_list" :key="item.uid"
                            @click="gohome('/pages/yuanshi/user/home?id=' + item.uid,true)">
                            <image :src="item.avatar" mode="aspectFill"></image>
                            <view class="name">{{ item.nickname }}</view>
                        </view>
                    </scroll-view>
                </view>
            </view>
            <view class="theme flex flex_between" v-if="detail.specialInfo&&detail.specialInfo.image">
                <view class="theme_l flex  ">
                    <view class="image">
                        <image :src="detail.specialInfo.image" mode="cover"></image>
                    </view>
                    <view class="flex flex_between flex_column">
                        <view class="title">{{detail.specialInfo.title}}</view>
                        <view class="txt">{{detail.specialInfo.partake_user_list.length || 0}}人已参与</view>
                    </view>
                </view>
                <view class="theme_r" @click="godetail">
                    去看看
                </view>
            </view>
            <!-- #ifdef MP-WEIXIN -->
            <view class="status" v-if="is_official_role">
                <view class="status-left">
                    状态
                </view>
                <view class="status-right">
                    <block v-if="detail.is_del == 1">
                        已删除
                    </block>
                    <block v-if="detail.is_del == 0">
                        <text v-if="detail.status == 1">已通过</text>
                        <text v-if="detail.status == 2">未审核</text>
                        <text v-if="detail.status == 3">已拒绝</text>
                    </block>
                </view>
            </view>
            <view class="admin" v-if="is_official_role">
                <view class="admin-left">
                    管理员操作
                </view>
                <view class="admin-right">
                    <view class="admin-right-item" :class="detail.status == 2?'':'gray'" @click="process(1,detail.status)">
                        通过
                    </view>
                    <view class="admin-right-item" :class="detail.status == 2?'':'gray'" @click="process(2,detail.status)">
                        拒绝
                    </view>
                    <view class="admin-right-item" @click="operate(3,'删除')">
                        删除
                    </view>
                    <view class="admin-right-item" @click="operate(1,'禁言')">
                        禁言
                    </view>
                    <view class="admin-right-item" @click="operate(2,'设置精华')">
                        加精
                    </view>
                </view>
            </view>
            <!-- #endif -->
            
            <view class="show_comment" v-if="messageInfo.length">
                <xComment ref="xComment" :arr="messageInfo" @showInput="showInput" :isFloor="false" :isLink="false" pageType="detail" :isshow="true" :isgohome="true" :isdetail="true"/>
            </view>
            <xChat :adjustPosition="adjustPosition" :placeholder="placeholder" :inputShow="inputShow" :uid="uid" @send="submitComment" desc="comment">
            </xChat>
            <x-authorize @login="getSpecialDetail(true)"></x-authorize>
        </view>
    </view>
</template>
<script>
    import xPlay from '@/components/x-play/x-play.vue';
    import xComment from '@/components/x-comment/x-comment/x-comment-new.vue';
    import xChat from '@/components/x-chat/x-chat';
    import { messageReview } from '@/api/yuanshi/user';
    
    import {
        authNavigator,
        navigator,
        getuserInfo
    } from '@/utils/common.js';
    import {
        openShareAll
    } from '@/utils/wechat/share.js';
    import { SHARE_ID } from '@/config.js';
    import xCommentChat from '@/mixins/xCommentChat.js';
    import {
        activityOnlineComment,
        activityOnlineMessageDetail,
        activityOnlineMessageLike,
        activityOnlineMessageUnlike,
        activityOnlineCommentAdd,
        activityMessageReview, //管理员审核留言
        setupActivityRefining, //管理员设置精华
        deleteActivityMessage,//管理员删除留言
        activityOnlineDetail,
    } from '@/api/community.js'
    
    import {
        handleUserMute,
        // handleSetupRefining,
        // handleDelUserMute
    } from '@/api/zsff.js'
    export default {
        components: {
            xPlay,
            xComment,
            xChat
        },
        mixins: [xCommentChat],
        data() {
            return {
                is_official_role:0, 
                messageInfo: [],
                uid: 0,
                placeholder: '',
                inputShow: false,
                scrollDetail: {},
                requestLoading: false,
                page: {
                    page: 1,
                    limit: 20,
                    more: true
                },
                detail: {
                    specialInfo: {},
                    video: [],
                    audio: [],
                    image: [],
                    like_list:[]
                },
                shareConfig: {},
                videoCtx: null,
                isControl: false,
                isShare: '',
                inputIdx: -1,
                ids: {},
                adjustPosition:false,
                enterOpen:false, //跳转打开弹框
            }
        },
        onShow() {
            // #ifndef MP-TOUTIAO
            this.adjustPosition = false
            // #endif
            // #ifdef MP-TOUTIAO
            this.adjustPosition = true
            // #endif
        },
        methods: {
            getEvaluateDetail(did) {
                let from = '';
                // #ifndef MP-TOUTIAO
                from = 'routine';
                // #endif
                // #ifdef MP-TOUTIAO
                from = 'bytedance';
                // #endif
                activityOnlineDetail(did, from)
                    .then(res => {
                        console.log('获取活动成功', res)
                        this.detail.specialInfo = res.data;
                        this.$forceUpdate();
                    })
                    .catch(err => {
                        console.log('获取活动失败', err)
                        this.$navigator(-1);
                    });
            },
            
            helpAdd(){
                if(this.detail.is_like){
                    return this.$showToast('您已' + (this.detail.specialInfo.type == 2?'投票':'点赞'))
                }else {
                    activityOnlineMessageLike({
                        activity_id: this.detail.activity_id,
                        message_id: this.detail.id,
                        
                    }).then(res => {
                        let user_obj = {
                            avatar:this.$store.state.userInfo.avatar,
                            nickname:this.$store.state.userInfo.nickname,
                            uid:this.$store.state.userInfo.uid,
                        }
                        this.detail.like_count++;
                        this.detail.like_list.unshift(user_obj)
                        this.detail.is_like = !this.detail.is_like
                    })
                }
            },
            process(type,status){
                let that = this;
                if(status == 2){
                    if(type == 1){
                        uni.showModal({
                            title:'通过',
                            content: '是否通过审核',
                            success: function(res) {
                                if (res.confirm) {
                                    // console.log('用户点击确定');
                                    let data = {
                                        type: 1, // 类型：1= 已通过 2=已拒绝 3=已删除 
                                        id:that.ids.id
                                    }
                                    activityMessageReview(data).then(res => {
                                        console.log('res',res)
                                        that.detail.status = 1;
                                        return this.$showToast('审核已通过')
                                    }).catch(err => {
                                        
                                    })
                                    
                                } else if (res.cancel) {
                                    // console.log('用户点击取消');
                                }
                            }
                        })
                    }
                    if(type == 2){
                        uni.showModal({
                            title:'拒绝',
                            content: '是否拒绝审核',
                            success: function(res) {
                                if (res.confirm) {
                                    let data = {
                                        type: 2, // 类型：1= 已通过 2=已拒绝 3=已删除 
                                        id:that.ids.id
                                    }
                                    activityMessageReview(data).then(res => {
                                        console.log('res',res)
                                        that.detail.status = 3;
                                        return this.$showToast('审核已拒绝')
                                    }).catch(err => {
                                        
                                    })
                                } else if (res.cancel) {
                                    // console.log('用户点击取消');
                                }
                            }
                        })
                    }
                }else {
                    return that.$showToast('该留言非（待审核）状态')
                }
            },
            operate(pType,tips){
                let that = this;
                uni.showModal({
                    title: tips,
                    content: '确认进行' + tips + '操作吗',
                    success: function(res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                            if (pType === 1) {
                                handleUserMute(that.detail.uid).then(res => {
                                    that.$showToast('禁言成功')
                                })
                            } else if (pType === 2) {
                                setupActivityRefining(that.detail.id).then(res => {
                                    that.detail.is_refining = 1;
                                    that.$showToast('设置精华成功')
                                })
                            } else if (pType === 3) {
                                let param = {
                                    message_id: that.detail.id,
                                    comment_id: 0
                                }
                                deleteActivityMessage(param).then(res => {
                                    that.$showToast('删除成功')
                                    setTimeout(() => {
                                        that.$navigator(-1)
                                    }, 1000)
                                })
                            }
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    }
                })
            },
            gohome(path, type) {
                if(type){
                    this.$authNavigator(path)
                }
            },
            godetail() {
                // const {
                //     sid,
                //     id,
                //     mid
                // } = this.ids;
                // if (this.isShare === 'share') {
                //     const {
                //         sourceInfo,
                //         specialInfo
                //     } = this.detail;
                //     let path = '';
                //     if (sourceInfo.id) {
                //         path = `/pages/yknowledge/course/detail_list?id=${id}&sid=${special_id}`
                //     }
                //     if (specialInfo.id) {
                //         path = `/pages/yknowledge/course/detail?id=${id}&sid=${special_id}`
                //     }
                //     this.$navigator(path, 'redirectTo');
                // } else {
                //     this.$navigator(-1)
                // }
                this.$navigator(`/pages/ycommunity/newActivity/solicitActivity?id=${this.detail.activity_id}`,'redirectTo');
                // 待后台返回url字段跳转
                // this.$linkpress(special_url)
                
            },
            playVideo(item, index) {
                this.isControl = true;
                // this.innerAudioContext.pause();
                this.$refs.xPlay.playPause();
                let ref = uni.createVideoContext(`playVideo${index}`);
                this.videoCtx = ref;
                let tid = setTimeout(() => {
                    ref.play();
                    clearTimeout(tid)
                }, 500)
            },
            previewImage(urls, current) {
                // console.log(urls, current)
                uni.previewImage({
                    urls: urls.map(item => item.name),
                    current: current //地址需为https
                });
            },
            // showInput(type = 1, item, index) {
            // 	const {
            // 		id
            // 	} = item;
            // 	this.pid = id;
            // 	this.inputIdx = index;
            // 	item.zsff = 1;
            // 	this.xComment = this.$refs['xComment' + id][0];
            // 	this.xComment.showInput(type, item, null, null);
            // },
            detailShowInput() {
                const {
                    aid,
                    id,
                } = this.ids;
                this.chatInfo = {
                    type: 1,
                    item: {
                        activity_id: aid,
                        message_id: id,
                        // pid: 0
                    }
                };
                console.log('this.chatInfo', this.chatInfo)
                this.inputShow = true;
                this.placeholder = '对此评论说点什么吧！@' + this.detail.nickname;
            },
            showInput(info, placeholder) {
                info.type = info.type + 1; // 评论详情页去除楼层1
                info.item.source_id = this.detail.source_id;
                this.chatInfo = info;
                this.inputShow = true;
                this.placeholder = placeholder;
            },
            submitComment(val) {
                console.log('查看chatInfo',this.chatInfo)
                const {
                    type,
                    idx1,
                    idx2,
                    item
                } = this.chatInfo;
                let param = {
                    ...this.getActivityOnlineSubmitInfo(),
                    comment: val,
                }
                console.log('查看评论提交参数',param)
                activityOnlineCommentAdd(param).then(res => {
                    this.inputShow = false;
                    this.$showToast('评论提交成功')
                    this.initInfo()
                    this.getComment()
                    console.log('this.messageInfo',this.messageInfo)
                }).catch(err => {
                    this.inputIdx = -1;
                })
            },
            likeStars(item, index, type) {
                const req = item.is_like ? activityOnlineMessageUnlike : activityOnlineMessageLike;
                req({
                    activity_id: item.activity_id,
                    message_id: item.id
                }).then(res => {
                    let user_obj = {
                        avatar:this.$store.state.userInfo.avatar,
                        nickname:this.$store.state.userInfo.nickname,
                        uid:this.$store.state.userInfo.uid,
                    }
                    if (item.is_like) {
                        this.detail.like_count--;
                        this.detail.is_like = !item.is_like
                        for(let i = 0; i < this.detail.like_list.length; i++) {
                        	if (this.detail.like_list[i].uid === user_obj.uid) {
                                 this.detail.like_list.splice(i, 1); 
                        	}
                        }
                    } else {
                        this.detail.like_count++;
                        this.detail.like_list.unshift(user_obj)
                        this.detail.is_like = !item.is_like
                    }
                })
            },
            initInfo() {
                this.requestLoading = false;
                this.messageInfo = [];
                this.page = {
                    page: 1,
                    limit: 20,
                    more: true
                }
            },
            getComment() {
                if (!this.page.more || this.requestLoading) return;
                this.requestLoading = true;
                const {
                    aid,
                    id,
                } = this.ids;
                let obj = {
                    activity_id: aid,
                    message_id: id,
                    page: this.page.page,
                    limit: this.page.limit
                };
                activityOnlineComment(obj)
                    .then(res => {
                        let storeInfo = res.data;
                        storeInfo.forEach((item, index) => {
                            if (typeof item.comment === 'string') {
                                item.content = [item.comment];
                            }
                        });
                        this.messageInfo = this.messageInfo.concat(storeInfo);
                        this.page.more = res.data.length === this.page.limit;
                        this.page.page++;
                        this.requestLoading = false;
                    })
                    .catch(err => {
                        console.log(err);
                        this.$navigator(-1)
                        this.requestLoading = false;
                    });
            },
            shareComment(item) {
                // #ifdef MP
                uni.showShareMenu({
                    withShareTicket: true,
                    menus: ['shareAppMessage', 'shareTimeline']
                })
                // #endif

            },
            getSpecialDetail(type = true) {
                const {
                    aid,
                    id,
                    pageType,
                    detailType
                } = this.ids;
                activityOnlineMessageDetail({
                    activity_id: aid,
                    message_id: id
                }).then(res => {
                    const item = res.data;
                    this.detail = item;
                    console.log('评论详情-', this.detail)
                    this.getEvaluateDetail(aid)
                    this.$forceUpdate();
                    this.shareConfig = {
                        desc: item.comment,
                        title: item.title,
                        link: '/pages/ycommunity/newActivity/detail?share=share&id=' + id + '&aid=' + aid + '&detailType=' + detailType + '&pageType=' + pageType,
                        imgUrl: item.image.length ? item.image[0].name : (item.video.length ? item.video[0]
                            .name +
                            '?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto' : '')
                    };
                    if(this.enterOpen){
                        this.detailShowInput()
                    }
                    // #ifdef H5
                    openShareAll(this.shareConfig);
                    // #endif
                    this.getComment()
                    if(type){
                        getuserInfo().then(res => {
                            this.is_official_role = res.data.is_official_role;
                        });
                    }
                })
            }
        },
        onPageScroll(e) {
            this.inputShow = false;
        },
        onLoad(options) {
            console.log('options---',options)
            this.ids = options;
            const {
                id = 0,
                aid,
                enterOpenStatus = 0,
                detailType = 0
            } = options
            if(enterOpenStatus){
                this.enterOpen = true
            }
            this.isShare = options.share || '';
            this.getSpecialDetail();
        },
        // #ifdef MP
        onShareAppMessage() {
            console.log(this.shareConfig)
            return {
                title: this.shareConfig.title,
                imageUrl: this.shareConfig.imgUrl,
                path: this.shareConfig.link,
                templateId: SHARE_ID
            };
        },
        onShareTimeline() {
            if (this.shareConfig) {
                return {
                    title: this.shareConfig.title,
                    imageUrl: this.shareConfig.imgUrl,
                    query: this.shareConfig.link.split('?')[1]
                };
            }
        }
        // #endif
    }
</script>

<style lang="scss" scoped>
    .comment {
        font-family: PingFang SC, PingFang SC-Regular;

        .avatar {
            padding: 32rpx 24rpx;

            .nickname {
                font-size: 16px;
                font-weight: 700;
                text-align: left;
                color: #333333;
            }

            .add_time {
                font-size: 12px;
                font-weight: 400;
                text-align: left;
                color: #666666;
            }
            .homework {
                border: 2rpx solid #ff5656;
                color: #ff5656;
                font-size: 24rpx;
                font-weight: 400;
                text-align: left;
                padding: 0 8rpx;
            }
        }

        .btn_share {
            width: 140rpx;
            font-size: 24rpx;
            height: 50rpx;
            background: #ffffff;
            border: 2rpx solid #d2d2d2;
            border-radius: 30rpx;
            margin-right: 24rpx;
            
            /* #ifdef MP-TOUTIAO */
            padding-right: 18rpx;
            box-sizing: border-box;
            /* #endif */
            
            image {
                width: 30rpx;
                margin-right: 12rpx;
            }
        }

        ::deep .xcomment .list {
            padding-bottom: 0;
        }

        image {
            width: 100%;
            height: 100%;
        }

        .swiper {
            width: 750rpx;
            height: 750rpx;

            .swiper-item {
                position: relative;
                .image-item {
                    display: block;
                    margin: 0 auto;
                }
                .video {}

                .cover {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    z-index: 9999;
                    top: 0;

                    image {
                        width: 160rpx;
                        height: 160rpx;
                    }
                }

            }
        }

        .audio {
            margin: 20rpx;
        }

        .wrap {
            padding: 74rpx 56rpx;
        }

        .desc {
            font-family: PingFang SC, PingFang SC-Regular;

            .title {
                margin-bottom: 40rpx;
                font-size: 32rpx;
                font-weight: 700;
                text-align: left;
                color: #333333;
            }

            .show {
                font-size: 24rpx;
                font-weight: 400;
                // @include show_line(5);
                min-height: 80rpx;


            }

            .refining_span {
                position: absolute;
                border: 1rpx solid red;
                top: 40rpx;
                right: 20rpx;
                color: red;
                padding: 2rpx 30rpx;
                font-weight: bold;
                font-size: 30rpx;
                letter-spacing: 6rpx;
                transform: rotate(-30deg);
                opacity: 0.6;
                border-radius: 10rpx;
            }

            .btn {
                width: 54px;
                height: 25px;
                margin-left: 10px;
                border: 1px solid rgba(0, 0, 0, 0.078);
                border-radius: 16px;
                font-size: 12px;
                color: #666666;
            }

            .footer {
                margin: 36rpx 0 30rpx 0;

                .footer_l {
                    @include font_size(20);
                    font-weight: 400;
                    color: #999999;
                }
            }

            .footer_r {
                .item {
                    image {
                        width: 24rpx;
                        height: 24rpx;
                    }

                    text {
                        padding: 0 16rpx;
                        font-size: 24rpx;
                        color: #ff5656;
                    }

                    border: none;
                    padding: 0;
                    margin: 0;
                }
            }
        }
        .want_go {
            width: 100%;
            // min-height: 320rpx;
            border-top: 2rpx solid #e2e6ec;
            padding: 24rpx 0;
            box-sizing: border-box;
            
            .nav {
                width: 100%;
                overflow: auto;
                
                .title {
                    float: left;
                    color: #333333;
                    font-weight: bold;
                    font-size: 32rpx;
                }
        
                .go {
                    float: right;
                    width: 148rpx;
                    height: 60rpx;
                    line-height: 60rpx;
                    text-align: center;
                    border: 2rpx solid #ff5656;
                    border-radius: 24rpx;
                    color: #ff5656;
                    font-size: 24rpx;
                }
            }
        
            .wrap1 {
                margin: 50rpx 0 0 0;
        
                // border:none;
                // min-height: auto;
                .item {
                    display: inline-block;
        
                    &:not(:last-child) {
                        padding-right: 8rpx;
                    }
        
                    image {
                        width: 120rpx;
                        height: 120rpx;
                        border-radius: 50%;
                        background-color: #fff;
                    }
        
                    .name {
                        text-align: center;
                        margin: 16rpx 0;
                        color: #999999;
                        font-weight: bold;
                        height: 40rpx !important;
                        font-size: 28rpx;
                        width: 120rpx;
                        @include show_line;
                    }
        
                    .btn {
                        width: 94rpx;
                        height: 40rpx;
                        border: 2rpx solid #d2d2d2;
                        border-radius: 14rpx;
                        margin: 0 auto;
                        color: #999999;
                    }
                }
            }
        }
        .status {
            width: 100%;
            font-size: 28rpx;
            border-top: 2rpx solid #e2e6ec;
            padding: 24rpx 0;
            box-sizing: border-box;
            overflow: auto;
            .status-left {
                float: left;
                width: 150rpx;
                color: #000000;
                
            }
            .status-right {
                float: left;
                color: #ff5656;
            }
        }
        .admin {
            width: 100%;
            overflow: auto;
            font-size: 28rpx;
            margin-bottom: 30rpx;
            
            .admin-left {
                float: left;
            }
            .admin-right {
                display: flex;
                justify-content: space-around;
                overflow: auto;
                
                .admin-right-item {
                    width: 80rpx;
                    height: 80rpx;
                    line-height: 80rpx;
                    text-align: center;
                    border-radius: 50%;
                    color: #ff5656;
                    font-size: 26rpx;
                    border: 2rpx solid #ff5656;
                    
                    &.gray {
                        color: #999999;
                        border: 2rpx solid #999999;
                    }
                }
            }
        }
        .show_comment {
            width: 100%;
            border-top: 2rpx solid #e2e6ec;
        }
        .theme {
            border-top: 2px solid #f7f8fa;
            padding: 20rpx 0;

            .theme_l {
                .image {
                    width: 134rpx;
                    height: 112rpx;
                    margin-right: 20rpx;

                    image {
                        border-radius: 20rpx;
                    }
                }

                .title {
                    width: 360rpx;
                    font-size: 32rpx;
                    font-weight: 700;
                    color: #333333;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    overflow: hidden;
                }

                .txt {
                    font-size: 24rpx;
                    font-weight: 400;
                    color: #666666;
                }
            }

            .theme_r {
                border: 2rpx solid #e9625d;
                border-radius: 24rpx;
                font-size: 24rpx;
                padding: 0rpx 18rpx;
                height: 50rpx;
                line-height: 50rpx;
                color: #ff5656;
            }
        }

    }
    .vote-txt {
        color: #ff5656;
        font-size: 24rpx;
        font-weight: 400;
    }
</style>
