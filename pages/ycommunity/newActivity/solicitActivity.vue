<template>
    <view class="active">
        <view class="swiper relative">
            <xSwiper :arr="detail.slider_image" height="1000rpx" srcName="item" radius="0rpx"
                :autoplay="detail.slider_image.length > 1?true:false" :dots="true" mode="widthFix" @change="dotChange">
            </xSwiper>
            <view class="absolute wrap">
                <view class="dot flex_line_height">{{ dotIdx + 1 }} / {{ detail.slider_image.length }}
                </view>
                <view class="name">{{ detail.title }}</view>
            </view>
        </view>
        <view class="main">
            <view class="wrap">
                <view class="wrap_t">
                    <view class="flex flex_align_center flex_between">
                        <view class="t_l">
                            <view class="l1">
                                <text>{{detail.type == 3?'课程预告：':'活动主题：'}}</text>
                                <view class="time">{{detail.description}}</view>
                            </view>
                        </view>
                        <view class="t_r">
                            <!-- #ifdef MP -->
                            <button class="flex_line_height btn_l" open-type="share">
                                <view class="flex flex_align_center">
                                    <image src="@/static/images/community/share.png" mode="widthFix"></image>
                                    <text class="tt_margin-right">分享</text>
                                </view>
                            </button>
                            <!-- #endif -->
                        </view>
                    </view>
                </view>
                <view class="wrap_b flex">
                    <view class="desc">{{detail.contact_title}}</view>
                    <view v-if="detail.type == 3" class="addChat" @click="goSingUp(3)">课程预约</view>
                    <view v-else class="addChat" @click="addChatGroup" >添加助教</view>
                </view>
            </view>
            <view class="show_title">
                <view class=" flex flex_align_center flex_between">
                    <view class="title">{{detail.type == 3?'课程介绍：':'活动说明：'}}</view>
                    <view class="icon_more">
                    </view>
                </view>
                <view class="conter">
                    <u-parse :html="detail.content" :tag-style="parseStyle" @linkpress="$linkpress">
                    </u-parse>
                </view>
            </view>
            <template v-if="detail.is_partake_users_show">
                <view class="want_go">
                    <view class="nav ">
                        <view class="title">已参与
                        </view>
                    </view>
                    <view class="wrap1 ">
                        <scroll-view scroll-x style="white-space:nowrap;" :scroll-with-animation="true">
                            <view class="item" v-for="(item, index) in detail.partake_user_list" :key="item.uid"
                                @click="goPages('/pages/yuanshi/user/home?id=' + item.uid,true)">
                                <image :src="item.avatar" mode="aspectFill"></image>
                                <view class="name">{{ item.nickname }}</view>
                            </view>
                        </scroll-view>
                    </view>
                </view>
            </template>
            <view class="bottom_line" v-if="detail.type != 3 && detail.is_post_show"></view>
            <view class="message" v-if="detail.type != 3 && detail.is_post_show" :class="{ minHeight: minHeight }">
                <view class="mess_tab flex flex_between">
                    <view class="mess">
                        发帖区（{{ detail.posts || 0 }}）
                    </view>
                    <view class="flex flex_align_center">
                        <!-- <view class="write flex_line_height"
                            @click="goPages('/pages/ycommunity/newActivity/submit?id=' + did,true)">
                            <text>{{detail.type == 1?'我要投稿':'我要发帖'}}</text>
                        </view> -->
                        <view class="write flex_line_height" @click="goSingUp(detail.type)">
                            <text>{{detail.type == 1?'我要投稿':'我要发帖'}}</text>
                        </view>
                    </view>
                </view>
                <view class="tab-comment flex">
                    <view class="item " v-for="(item,index) in tabList"
                        :key="index" @click="tabClick(item,index)" :class="[index===current1?'active':'',voteSet?'voteSet':'']">
                        {{item.name}}
                    </view>
                </view>
                <view class="bottom_line"></view>
                <view class="message_wrap " v-if="messageInfo.length">
                    <xComment ref="xComment" :arr="messageInfo" @showInput="showInput" pageType="list" :isvideo="false" :isgohome="true"
                        @send="getSonCommentstatus" @sendAudio="sendAudio" :detailType="detail.type"/>
                </view>
                <view v-else class="text_center " style="color: #b7b7b7;padding: 20rpx 0;font-size: 24rpx;"><text
                        class="">来添加第一条评论吧</text></view>
            </view>
            <view class="h200"></view>
        </view>
        <view class="active_footer relative" :class="{ hide: footerHidden }" :style="{ opacity: footerOpacity }"
            v-show="footerOpacity > 0">
            <view class="absolute flex flex_align_center flex_between">
                <view class="flex flex_l flex_align_center" style="height: 0;">
                    <view class="item" @click="goPages('/pages/tabBar/index/index',false,'switchTab')">
                        <view>
                            <image src="@/static/images/community/home.png" mode="">
                            </image>
                        </view>
                        <view class="font_size20">首页</view>
                    </view>
                    <!-- #ifdef MP-WEIXIN -->
                    <view class="item"
                        @click="openWeChat">
                        <view>
                            <image src="@/static/images/community/chat.png" mode=""></image>
                        </view>
                        <view class="font_size20">客服</view>
                    </view>
                    <!-- #endif -->
                    <!-- #ifndef MP-WEIXIN -->
                    <view class="item"
                        @click="goPages(`/pages/user/CustomerList?id=${did}&type=0&scence=community`,true)">
                        <view>
                            <image src="@/static/images/community/chat.png" mode=""></image>
                        </view>
                        <view class="font_size20">客服</view>
                    </view>
                    <!-- #endif -->
                </view>
                <view class="btn">
                    <view v-if="detail.type == 1" class="btn_r" @click="goSingUp(detail.type)" >
                        {{detail.submit_button_name || '我要投稿'}}</view>
                    <view v-if="detail.type == 2" class="btn_r" @click="goSingUp(detail.type)" >
                        {{detail.submit_button_name || '发起心愿贴'}}</view>
                    <view v-if="detail.type == 3" class="btn_r" @click="goSingUp(detail.type)" >
                        {{detail.submit_button_name || '我要预约'}}</view>
                </view>
            </view>
        </view>
        <x-authorize @login="updateData"></x-authorize>
        <uni-popup ref="popups" type="center" :animation="true">
            <view class=""
                style="width: 400rpx;background-color: #fff;border-radius: 20rpx;padding:20rpx;border:1px solid #fc5656 ">
                <view class="wechatGroup-img">
                    <image show-menu-by-longpress="true" :src="detail.contact_image" mode="widthFix">
                    </image>
                </view>
                <!-- #ifndef MP-TOUTIAO -->
                <view class="text_center" style="padding:20rpx">{{detail.contact_info}}</view>
                <!-- #endif -->
                <!-- #ifdef MP-TOUTIAO -->
                <view class="tt_text_left">
                    <view class="tt_text_left_btn1" @click="addChatGroupClose">
                        关闭
                    </view>
                    <view class="tt_text_left_btn2" @click="saveimg(detail.activityInfo.douyin_contact_image)">
                        保存
                    </view>
                </view>
                <!-- #endif -->
            </view>
        </uni-popup>
    </view>
</template>

<script>
    import playAudioControl from '@/mixins/playAudio.js';
    import {
        payOrderHandle
    } from '@/utils/order.js';
    import {
        uniSelectorQueryInfo
    } from '@/utils/uni_api.js';
    import {
        authNavigator,
        zxauthNavigator,
        openWeChatCustomerService,
        debounce,
    } from '@/utils/common.js';
    import {
        SHARE_ID,
        WX_KEFU_Link,
        WX_ENTERPRISE_Link
    } from '@/config.js';
    import xSwiper from '@/components/x-swiper/x-swiper.vue';
    import xComment from '@/components/x-comment/x-comment/x-comment-new.vue';
    import {
        userFollow,
        userUnFollow
    } from '@/api/yuanshi/user.js';
    import {
        reportActiveDetail
    } from '@/utils/ReportAnalytics.js';
    import storage from '@/utils/storage.js';
    import {
        openCommunityActiveSubscribe,
        subscribe
    } from '@/utils/SubscribeMessage.js';
    import {
        // activityMessage,
        // activityMessageLike,
        // activityMessageUnLike,
        // activityMessageDel,
        
        activityOnlineDetail,
        activityOnlinePartake,
        activityOnlineMessage,
        activityOnlineMyMessage, // 我的留言评论列表
    } from '@/api/community';
    import {
        RegPhoneAndFixed
    } from '@/utils/validate';

    // #ifdef H5
    import {
        isWeixin
    } from '@/utils/validate.js';
    const _isWeixin = isWeixin();
    import {
        openShareAll
    } from '@/utils/wechat/share.js';
    // #endif
    // #ifdef MP
    const _isWeixin = true;
    // #endif

    export default {
        mixins: [playAudioControl],
        components: {
            xSwiper,
            xComment,
        },
        data() {
            return {
                detail: {
                    slider_image: [],
                    partake_user_list:[]
                },
                // #ifdef MP-WEIXIN
                from: 'routine',
                // #endif
                // #ifdef MP-TOUTIAO
                from: 'bytedance',
                // #endif
                // #ifdef H5
                from: _isWeixin ? 'weixin' : 'weixinh5',
                // #endif
                mixinsParam: {
                    wishId: 12,
                    productId: 76
                },
                messageInfo: [],
                opacity: 0.5,
                scrollLeft: 0,
                initCalcItemScroll: 0,
                scrollDetail: {},
                pannelShow: false,
                pannelInfo: [],
                pannelInfoIdx: -1, //my:编辑删除等操作
                pannelInfo_is_scoring: 0,
                pannelIdx: -1,
                footerOpacity: 0,
                requestLoading: false,
                page: {
                    page: 1,
                    limit: 10,
                    more: true
                },
                footerHidden: false,
                uid: -1,
                inputShow: false,
                keywords: '',
                minHeight: false,
                isOnlyMy: false,
                tipsShow: false,
                tipsShowCheck: false,
                labelShow: false,
                dotIdx: 0,
                did: 0,
                videoCtx: null,
                isFullscreen: false,
                audioPlayIdx: [],
                options: [],
                wish: {
                    city: [],
                    address: '',
                    contact_name: '',
                    phone: '',
                    comment: ''
                },
                paymentType: true, //true时，测评版本直接微信支付
                current1: 0, // 评论筛选初始值
                tabList: [{
                    name: '全部',
                    id: 0
                }, {
                    name: '精华',
                    id: 1
                }, {
                    name: '人气',
                    id: 2
                }, {
                    name: '我的',
                    id: 3
                }],
                tmplIds:['ruzT-FOcqKlq3TL98LiOmsgpklT3zLO-4GsVoCjEiPA'],
                voteSet:false, // 心愿投票设置开启
            };
        },
        watch: {
            autoplay(a, b) {
                console.log(a)
                // this.autoplay = a;
                if (!a) {
                    const idxs = this.audioPlayIdx;
                    this.$set(this.messageInfo[idxs[0]].audio[idxs[1]], 'play', 0)
                }
            },
        },
        methods: {
            tabClick(item, index) {
                this.current1 = index;
                if(this.current1 == 3){
                    this.getSpecialComment(true,true)
                }else {
                    this.getSpecialComment(true,false)
                }
                // const {
                //     top,
                //     height
                // } = this.tabLinkInfo1;
                // // console.log(this.scrollTop, top)
                // if (this.scrollTop < top) {
                //     // #ifdef MP
                //     uni.pageScrollTo({
                //         scrollTop: top,
                //         duration: 300
                //     });
                //     // #endif
                //     // #ifdef H5
                //     uni.pageScrollTo({
                //         scrollTop: top + 44,
                //         duration: 300
                //     });
                //     // #endif
                // }
            },
            openWeChat(){
                openWeChatCustomerService(WX_KEFU_Link,WX_ENTERPRISE_Link,true,this.detail.title,'pages/ycommunity/newActivity/solicitActivity.html?id='+ this.did,this.detail.slider_image[0])
            },
            async toPay(type) {
                var that = this;
                
                payOrderHandle(this.detail.final_payment_info[0].order_id, type, that.from, 'community')
                    .then(res => {
                        // console.log('res---',res)
                        const {
                            status,
                            result
                        } = res;
                        if (status === 'WECHAT_H5_PAY') {
                            return that.$navigator('/pages/order/PaymentStatus?orderId=' + this.detail
                                .final_payment_info[0].order_id + '&status=0&source=community');
                        }
                        if(res.errMsg == 'requestPayment:ok'){
                            this.getEvaluateDetail(this.did);
                            // this.detail.is_final_payment = 0;
                        }
                    })
                    .catch(err => {
                        console.log('err---',err)
                        this.$showToast(err.msg || err);
                    });
            },
            saveimg(url) {
                let that = this;
                uni.downloadFile({
                    url: url,
                    header: {
                        "content-type": "application/json",
                    },
                    success: (res) => {
                        console.log('res', res)
                        if (res.statusCode === 200) {
                            uni.saveImageToPhotosAlbum({
                                filePath: res.tempFilePath,
                                success(res) {
                                    return that.$showToast('已保存相册')
                                    setTimeout(function() {
                                        that.addChatGroupClose()
                                    }, 1500);
                                },
                                fail(res) {
                                    console.log(res);
                                    return that.$showToast('无相册权限')
                                    setTimeout(function() {
                                        that.addChatGroupClose()
                                    }, 1500);
                                }
                            });
                        }
                    }
                })
            },
            addChatGroup() {
                this.$refs.popups.open()
            },
            addChatGroupClose() {
                this.$refs.popups.close()
            },
            fullscreenchange(item, index, index1) {
                this.isFullscreen = false
                console.log('退出全屏')
                let ref = this.videoCtx;
                ref.pause();
            },
            xPlayAudio(item, index, index1) {
                if (!item.name) {
                    return this.$showToast('播放地址不存在')
                }
                if (this.innerAudioContext) {
                    const audio = this.messageInfo[index].audio[index1];
                    this.$set(audio, 'play', Number(!audio.play))
                    if (this.innerAudioContext.src === item.name) {
                        this.innerAudioContext.pause();
                    } else {
                        const idxs = this.audioPlayIdx;
                        if (idxs.length) {
                            this.$set(this.messageInfo[idxs[0]].audio[idxs[1]], 'play', 0)
                        }
                        this.initAudioInfo()
                    }
                    this.audioPlayIdx = [index, index1]
                    this.playAudio(item.name)
                }
            },
            playVideo(item, index, index1) {
                this.innerAudioContext.pause();
                let ref = uni.createVideoContext(`playVideo${index}-${index1}`);
                this.videoCtx = ref;
                ref.requestFullScreen();
                let tid = setTimeout(() => {
                    this.isFullscreen = true;
                    ref.play();
                    clearTimeout(tid)
                }, 500)
            },
            dotChange(idx) {
                this.dotIdx = idx;
            },
            tipsShowCheckClick() {
                this.tipsShowCheck = !this.tipsShowCheck;
                storage.set('tipsShowCheck', this.tipsShowCheck);
            },
            isTipsShow() {
                this.isOnlyMy = false;
                this.onlyShowMy(true);
                this.tipsShow = storage.get('tipsShowCheck') ? false : true;
            },
            showInput(type = 1, item) {
                const {
                    id
                } = item;
                // console.log(this.$refs['xComment' + id]);
                this.inputShow = true;
                this.footerHidden = true;
                this.xComment = this.$refs['xComment' + id][0];
                this.xComment.showInput(type, item, null, null);
            },
            onlyShowMy(typ = false) {
                this.messageInfo = [];
                this.page = {
                    page: 1,
                    limit: this.page.page,
                    more: true
                };
                if (!typ) {
                    this.isOnlyMy = !this.isOnlyMy;
                }
                if (this.isOnlyMy) {
                    this.sendSuccess();
                }
                this.keywords = '';
                this.getSpecialComment();
            },
            chatInput({
                id,
                uid
            }, placeholder) {
                this.inputShow = true;
                this.footerHidden = true;
                this.uid = uid;
                this.xComment = this.$refs['xComment' + id][0];
            },
            sendSuccess() {
                this.inputShow = false;
                this.footerHidden = false;
                this.opacity = 1;
            },
            shareComment(item) {
                this.shareConfig = {
                    desc: item.comment,
                    title: item.title,
                    link: '/pages/ycommunity/newActivity/detail?id=' + item.id,
                    imgUrl: item.image.length ? item.image[0].name : (item.video.length ? item.video[0].name +
                        '?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto' : '')
                };
                let timeId = setTimeout(() => {
                    this.shareConfig = this.copyShareConfig;
                }, 8000)
                // #ifdef H5
                openShareAll(this.shareConfig);
                // #endif
                // #ifdef MP
                this.$showToast('右上角分享')
                wx.hideShareMenu({
                    menus: ['shareTimeline'],
                    success(res) {
                        setTimeout(() => {
                            wx.showShareMenu({
                                withShareTicket: true,
                                menus: ['shareAppMessage', 'shareTimeline'],
                            })
                        }, 1500)
                    }
                })
                // #endif
            },

            likeStars(item, index) {
                let obj = {
                    related_id: this.did,
                    message_id: item.id
                };
                if (item.is_like) {
                    activityMessageUnLike(obj).then(res => {
                        this.messageInfo[index].like_count--;
                        this.messageInfo[index].is_like = !item.is_like;
                    });
                } else {
                    activityMessageLike(obj).then(res => {
                        this.messageInfo[index].like_count++;
                        this.messageInfo[index].is_like = !item.is_like;
                    });
                }
            },
            goSingUp(type) {
                let that = this;
                zxauthNavigator()
                if(type == 3){
                    // 预约类型
                    let userInfo = that.$storage.get('userInfo') || {};
                    // console.log('userInfo',userInfo)
                    if(userInfo.uid){
                        for(let i = 0; i < that.detail.partake_user_list.length;i++){
                            if(userInfo.uid == that.detail.partake_user_list[i].uid){
                                that.goPages('/pages/ycommunity/newActivity/previewAssistant?id=' + that.detail.id, true)
                                return
                            }
                        }
                    }
                    if(that.detail.is_push_message){
                        // #ifdef MP-WEIXIN
                        uni.getSetting({
                            withSubscriptions: true,
                            success(res) {
                                if(res.subscriptionsSetting.mainSwitch){
                                    uni.requestSubscribeMessage({
                                        tmplIds: that.tmplIds,
                                        success(res) {
                                        },
                                        fail(res) {
                                        },
                                        complete: function(res) {
                                            let param = {
                                                activity_id: that.did,
                                                type:that.detail.type  ,//类型 1-参与活动、2-投票、3-预约
                                            };
                                            if(res['ruzT-FOcqKlq3TL98LiOmsgpklT3zLO-4GsVoCjEiPA'] == 'accept'){
                                            // reject
                                                param.is_push = 1;
                                            }else {
                                                param.is_push = 0;
                                            }
                                            // console.log('提交参数', param)
                                            activityOnlinePartake(param)
                                                .then(res => {
                                                    // console.log('提交预约成功', res)
                                                    let obj = {
                                                        nickname:userInfo.nickname,
                                                        uid:userInfo.uid,
                                                        avatar:userInfo.avatar
                                                    }
                                                    that.detail.partake_user_list.push(obj)
                                                    that.goPages('/pages/ycommunity/newActivity/previewAssistant?id=' + that.detail.id, true)
                                                })
                                                .catch(err => {
                                                    console.log('提交预约失败', err)
                                                });
                                        },
                                    })
                                }else { // 用户没有点击“总是保持以上，不再询问”则每次都会调起订阅消息
                                    uni.openSetting({ // 打开设置页
                                        success(res) {
                                            // console.log(res.authSetting) 
                                        }
                                    });
                                }
                            },
                            fail(err) {
                                console.log('获取设置err',err)
                            },
                        })
                        // #endif
                        // #ifndef MP-WEIXIN
                        let param = {
                            activity_id: that.did,
                            type:that.detail.type  ,//类型 1-参与活动、2-投票、3-预约
                            is_push:0
                        };
                        // console.log('提交参数', param)
                        activityOnlinePartake(param)
                            .then(res => {
                                // console.log('提交预约成功', res)
                                let obj = {
                                    nickname:userInfo.nickname,
                                    uid:userInfo.uid,
                                    avatar:userInfo.avatar
                                }
                                that.detail.partake_user_list.push(obj)
                                that.goPages('/pages/ycommunity/newActivity/previewAssistant?id=' + that.detail.id, true)
                            })
                            .catch(err => {
                                console.log('提交预约失败', err)
                            });
                        // #endif
                    }else {
                        let param = {
                            activity_id: that.did,
                            type:that.detail.type  ,//类型 1-参与活动、2-投票、3-预约
                            is_push:0
                        };
                        activityOnlinePartake(param)
                            .then(res => {
                                // console.log('提交预约成功', res)
                                let obj = {
                                    nickname:userInfo.nickname,
                                    uid:userInfo.uid,
                                    avatar:userInfo.avatar
                                }
                                that.detail.partake_user_list.push(obj)
                                that.goPages('/pages/ycommunity/newActivity/previewAssistant?id=' + that.detail.id, true)
                            })
                            .catch(err => {
                                console.log('提交预约失败', err)
                            });
                    }
                }else {
                    // 征文和心愿
                    // 预约类型
                    let userInfo = that.$storage.get('userInfo') || {};
                    // console.log('userInfo',userInfo)
                    if(userInfo.uid){
                        for(let i = 0; i < that.detail.partake_user_list.length;i++){
                            if(userInfo.uid == that.detail.partake_user_list[i].uid){
                                that.goPages('/pages/ycommunity/newActivity/submit?id=' + that.detail.id, true)
                                return
                            }
                        }
                    }
                    if(that.detail.is_push_message){
                        // #ifdef MP-WEIXIN
                        uni.getSetting({
                            withSubscriptions: true,
                            success(res) {
                                if(res.subscriptionsSetting.mainSwitch){
                                    uni.requestSubscribeMessage({
                                        tmplIds: that.tmplIds,
                                        success(res) {
                                        },
                                        fail(res) {
                                        },
                                        complete: function(res) {
                                            let param = {
                                                activity_id: that.did,
                                                type:that.detail.type  ,//类型 1-参与活动、2-投票、3-预约
                                            };
                                            if(res['ruzT-FOcqKlq3TL98LiOmsgpklT3zLO-4GsVoCjEiPA'] == 'accept'){
                                            // reject
                                                param.is_push = 1;
                                            }else {
                                                param.is_push = 0;
                                            }
                                            // console.log('提交参数', param)
                                            activityOnlinePartake(param)
                                                .then(res => {
                                                    // console.log('提交预约成功', res)
                                                    let obj = {
                                                        nickname:userInfo.nickname,
                                                        uid:userInfo.uid,
                                                        avatar:userInfo.avatar
                                                    }
                                                    that.detail.partake_user_list.push(obj)
                                                    that.goPages('/pages/ycommunity/newActivity/submit?id=' + that.detail.id, true)
                                                })
                                                .catch(err => {
                                                    console.log('提交预约失败', err)
                                                });
                                        },
                                    })
                                }else { // 用户没有点击“总是保持以上，不再询问”则每次都会调起订阅消息
                                    uni.openSetting({ // 打开设置页
                                        success(res) {
                                            // console.log(res.authSetting) 
                                        }
                                    });
                                }
                            },
                            fail(err) {
                                console.log('获取设置err',err)
                            },
                        })
                        // #endif
                        // #ifndef MP-WEIXIN
                        let param = {
                            activity_id: that.did,
                            type:that.detail.type  ,//类型 1-参与活动、2-投票、3-预约
                            is_push:0
                        };
                        // console.log('提交参数', param)
                        activityOnlinePartake(param)
                            .then(res => {
                                // console.log('提交预约成功', res)
                                let obj = {
                                    nickname:userInfo.nickname,
                                    uid:userInfo.uid,
                                    avatar:userInfo.avatar
                                }
                                that.detail.partake_user_list.push(obj)
                                that.goPages('/pages/ycommunity/newActivity/submit?id=' + that.detail.id, true)
                            })
                            .catch(err => {
                                console.log('提交预约失败', err)
                            });
                        // #endif
                    }else {
                        let param = {
                            activity_id: that.did,
                            type:that.detail.type  ,//类型 1-参与活动、2-投票、3-预约
                            is_push:0
                        };
                        activityOnlinePartake(param)
                            .then(res => {
                                // console.log('提交预约成功', res)
                                let obj = {
                                    nickname:userInfo.nickname,
                                    uid:userInfo.uid,
                                    avatar:userInfo.avatar
                                }
                                that.detail.partake_user_list.push(obj)
                                that.goPages('/pages/ycommunity/newActivity/submit?id=' + that.detail.id, true)
                            })
                            .catch(err => {
                                console.log('提交预约失败', err)
                            });
                    }
                }
            },
            goPages(path, type, pathType) {
                if (type) {
                    this.$authNavigator(path);
                } else {
                    this.$navigator(path, pathType);
                }
            },
            navClick(type) {
                let initCalcItemScroll = this.initCalcItemScroll,
                    oldScrollLeft = this.scrollDetail.scrollLeft || 0;
                if (type === 'left') {
                    if (oldScrollLeft > 0) {
                        this.scrollLeft = oldScrollLeft > initCalcItemScroll ? oldScrollLeft - initCalcItemScroll : 0;
                    }
                } else {
                    this.scrollLeft = oldScrollLeft + initCalcItemScroll;
                }
            },
            scrollX(e) {
                this.scrollDetail = e.detail;
            },
            getEvaluateDetail(did) {
                let from = '';
                // #ifndef MP-TOUTIAO
                from = 'routine';
                // #endif
                // #ifdef MP-TOUTIAO
                from = 'bytedance';
                // #endif
                activityOnlineDetail(did, from)
                    .then(res => {
                        // console.log('获取活动成功', res)
                        this.detail = res.data;
                        if(this.detail.type == 2){
                            this.tabList[2] = {
                                name: '投票排名(Top10)',
                                id: 2
                            }
                            this.voteSet = true;
                        }
                        this.$updateTitle(this.detail.title);
                        this.shareConfig = {
                            desc: this.detail.introduction,
                            title: this.detail.title,
                            link: '/pages/ycommunity/newActivity/solicitActivity?id=' + did,
                            imgUrl: this.detail.slider_image[0]
                        };
                        // #ifdef H5
                        openShareAll(this.shareConfig);
                        // #endif
                    })
                    .catch(err => {
                        console.log('获取活动失败', err)
                        this.$navigator(-1);
                    });
            },
            initInfo() {
                this.requestLoading = false;
                this.messageInfo = [];
                this.page = {
                    page: 1,
                    limit: 10,
                    more: true
                }
            },
            getSpecialComment: debounce(function(init,status) {
                init && this.initInfo();
                if (!this.page.more || this.requestLoading) return;
                this.requestLoading = true;
                let obj = {
                        activity_id: this.did,
                        page: this.page.page,
                        limit: this.page.limit,
                        // sort: 
                    };
                let req = '';
                if(status){
                    req = activityOnlineMyMessage;
                    obj.message_id = 0
                }else {
                    req = activityOnlineMessage;
                    obj.sort = this.tabList[this.current1].id
                }
                req(obj).then(res => {
                    let info = res.data;
                    info.forEach((item, index) => {
                        if (typeof item.comment === 'string') {
                            item.content = [item.comment];
                        }
                    });
                    this.messageInfo = this.messageInfo.concat(info);
                    this.messageInfo.forEach(item=>{
                        if(item.comment.length > 90){
                            item.isMore = true
                            item.contentAll = true
                            // if(this.ids.open_comment){
                            //     item.contentAll = false
                            // }
                        }else{
                            item.isMore = false
                            item.contentAll = false
                        }
                    })
                    // console.log('课程评论-', this.page)
                    this.page.more = res.data.length === this.page.limit;
                    this.page.page++;
                    this.requestLoading = false;
                    
                }).catch(err => {
                        console.log('activityMessage', err);
                        this.requestLoading = false;
                    });
            }, 200, true),
            previewImage(urls, current) {
                uni.previewImage({
                    urls: urls.map(item => item.name),
                    current: current //地址需为https
                });
            },
            updateData() {
                this.getEvaluateDetail(this.did);
                this.messageInfo = [];
                this.page = {
                    page: 1,
                    limit: this.page.limit,
                    more: true
                };
                this.getSpecialComment();
            },
            delMyEvaluate(item, index) {
                let _this = this;
                this.$showModal('提示', '是否要删除', {
                    success: function(res) {
                        console.log(res);
                        if (res.confirm) {
                            activityMessageDel(item.id)
                                .then(res => {
                                    _this.$showToast('删除成功');
                                    _this.messageInfo.splice(index, 1);
                                })
                                .catch(err => {
                                    _this.$showToast('删除失败');
                                });
                        }
                    },
                    fail: function() {}
                });
            },
        },
        onPageScroll(e) {
            this.inputShow = false;
            this.footerHidden = false;
            this.opacity = (e.scrollTop / 400).toFixed(1);
            if (e.scrollTop > 200) {
                this.footerOpacity = (e.scrollTop / 400).toFixed(1);
            } else {
                this.footerOpacity = 0;
            }
        },
        mounted() {
            let _this = this;
            uniSelectorQueryInfo('#calcItem', _this)
                .then(res => {
                    _this.initCalcItemScroll = res.width;
                })
                .catch(err => {});
        },
        onLoad(option) {
            const {
                id = 1,
            } = option;
            this.did = id;
            console.log('活动id', this.did)
            this.getSpecialComment();
        },
        onShow() {
            this.getEvaluateDetail(this.did);
            this.initAudioInfo();
            this.audioPlayIdx = [];
            if (this.innerAudioContext) {
                this.innerAudioContext.pause();
            }
        },
        onReachBottom() {
            if(this.detail.type == 2 && this.current1 == 2){
                console.log('top10条')
            }else {
                if (this.messageInfo.length) {
                    this.getSpecialComment();
                }
            }
        },
        onHide() {
            if (this.audioPlayIdx.length) {
                this.destoryAudioCtx();
                const index = this.audioPlayIdx[0],
                    index1 = this.audioPlayIdx[1];
                const audio = this.messageInfo[index].audio[index1];
                this.$set(audio, 'play', 0)
            }

        },
        // #ifdef MP
        onShareAppMessage() {
            console.log('分享', this.shareConfig)
            return {
                title: this.shareConfig.title,
                imageUrl: this.shareConfig.imgUrl,
                path: this.shareConfig.link + '&share=share',
                templateId: SHARE_ID
            };
        },
        // 朋友圈分享不支持自定义页面路径
        onShareTimeline() {
            if (this.shareConfig) {
                return {
                    title: this.shareConfig.title,
                    imageUrl: this.shareConfig.imgUrl,
                    query: this.shareConfig.link.split('?')[1]
                };
            }
        }
        // #endif
    };
</script>
<style>
    page {
        background: #f7f7f9;
    }
</style>
<style scoped lang="scss">
    .tab-comment {
        border-top: 2rpx solid #d8d8d8;
        border-bottom: 2rpx solid #d8d8d8;
        width: 690rpx;
        margin: 16rpx auto 18rpx;
    }
    
    .tab-comment .item.active {
        background: #f4f4f4;
        font-weight: 700;
        color: #333;
    }
    .tab-comment .item.voteSet {
        font-size: 22rpx;
    }
    .tab-comment .item.voteSet:nth-child(3) {
        padding-left: 0rpx;
        font-size: 22rpx;
        text-align: center;
    }
    
    .tab-comment .item:last-child {
        border-right: none;
    }
    
    .tab-comment .item {
        width: 25%;
        padding-left: 20rpx;
        height: 64rpx;
        line-height: 64rpx;
        font-size: 24rpx;
        background: #ffffff;
        font-weight: 500;
        color: #999999;
        border-right: 2rpx solid #d8d8d8;
    }
    
    .bottom_line {
        height: 2rpx;
        background: #e2e6ec;
        margin: 0 48rpx;
    }

    button {
        width: 300rpx;
        margin: 30rpx auto;
    }

    .swiper {
        .swiper-set {
            position: absolute;
            top: 20rpx;
            right: 20rpx;
            z-index: 99;

            image {
                width: 50rpx;
                height: 50rpx;
            }
        }

        .wrap {
            color: #fff;
            padding: 50rpx 48rpx;
            bottom: 0rpx;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.46) 100%);

            image {
                width: 100%;
                height: 100%;

                border-radius: 4rpx;
            }

            .dot {
                width: 96rpx;
                height: 44rpx;
                border: 1rpx solid #ffffff;
                border-radius: 32rpx;
                font-size: 24rpx;
            }

            .name {
                font-size: 56rpx;
                margin-top: 40rpx;
                margin-bottom: 12rpx;
            }

        }
    }

    .active {
        image {
            width: 100%;
            height: 100%;
        }

        .icon_more {
            font-size: 24rpx;

            color: #999999;

            .iconfont {
                font-size: 24rpx;
                color: #555555;
            }
        }

        .padding {
            padding: 32rpx 36rpx 32rpx 34rpx;
            margin: 0 20rpx 20rpx 20rpx;
            background: #fff;
        }

        .main {
            position: absolute;
            width: 100%;
            margin-top: -18rpx;

            .wrap {
                border: 2rpx solid #fc5656;
                border-radius: 30rpx;
                box-shadow: 0px 0px 40rpx 0px rgba(107, 127, 153, 0.20);
                margin: 0 20rpx;

                .wrap_t {
                    min-height: 152rpx;
                    background: #f2f5f8;
                    padding: 32rpx;
                    border-radius: 30rpx 30rpx 0 0;

                    .t_l {
                        .l1 {
                            font-size: 32rpx;
                            font-weight: 700;
                            text-align: left;
                            color: #333333;

                            .time {
                                padding: 14rpx 20rpx 4rpx;
                                font-size: 24rpx;
                                font-weight: 400;
                                color: red;
                                color: #666666;
                                line-height: 40rpx;
                                box-sizing: border-box;
                            }
                        }

                        .l2 {
                            margin-top: 6rpx;
                            font-size: 24rpx;
                            font-weight: 400;
                            text-align: left;
                            color: #666666;
                        }
                    }

                    .t_r {
                        button {
                            width: 184rpx;
                            height: 76rpx;
                            background: #ffffff;
                            border: 2rpx solid #d2d2d2;
                            border-radius: 30rpx;

                            image {
                                width: 30rpx;
                                margin-right: 12rpx;
                            }
                        }
                    }
                }

                .wrap_b {
                   border-top: 2rpx solid #e2e6ec;
                   margin: 0 28rpx 22rpx 36rpx;
                   padding: 28rpx 0 22rpx 0;
                   font-size: 24rpx;
                   font-weight: 400;
                   text-align: left;
                   color: #666666;

                    .desc {
                        flex: 1;
                        margin-top: 10rpx;
                    }

                    .addChat {
                        margin-left: 32rpx;
                        width: 136rpx;
                        height: 50rpx;
                        line-height: 50rpx;
                        background: #50506f;
                        border-radius: 24rpx;
                        font-size: 24rpx;
                        font-weight: 400;
                        text-align: center;
                        color: #ffffff;
                    }
                }


            }


            .show_title {
                font-weight: 400;
                background: none;
                margin-top: 70rpx;
                padding: 0 64rpx 0 48rpx;

                .title {
                    font-size: 32rpx;
                    font-weight: bold;
                    color: #333333;
                }

                .desc {
                    margin: 40rpx 0 60rpx 0;
                    @include show_line(5);
                }

                .conter {
                    margin: 40rpx 0 60rpx 0;
                }
            }

            .want_go {
                margin: 60rpx 48rpx;
                padding-top: 70rpx;
                min-height: 320rpx;
                border-top: 1px solid #e2e6ec;

                .nav {
                    .title {
                        color: #333333;
                        font-weight: bold;
                        font-size: 32rpx;
                    }

                    .go {
                        width: 148rpx;
                        height: 60rpx;
                        border: 2rpx solid #ff5656;
                        border-radius: 24rpx;
                        margin: 0 64rpx 0 0;
                        color: #ff5656;
                        font-size: 24rpx;
                    }
                }

                .wrap1 {
                    margin: 50rpx 0 0 0;

                    // border:none;
                    // min-height: auto;
                    .item {
                        // display: inline-block;
                        display: table-cell;

                        &:not(:last-child) {
                            padding-right: 8rpx;
                        }

                        image {
                            width: 120rpx;
                            height: 120rpx;
                            border-radius: 50%;
                            background-color: #fff;
                        }

                        .name {
                            text-align: center;
                            margin: 16rpx 0;
                            color: #999999;
                            font-weight: bold;
                            height: 40rpx !important;
                            font-size: 28rpx;
                            width: 120rpx;
                            @include show_line;
                        }

                        .btn {
                            width: 94rpx;
                            height: 40rpx;
                            border: 2rpx solid #d2d2d2;
                            border-radius: 14rpx;
                            margin: 0 auto;
                            color: #999999;
                        }
                    }
                }
            }

            .goods {
                padding: 70rpx 0 70rpx 48rpx;
            }
            .h200 {
                height: 200rpx;
            }
            .message {
                margin-top: 70rpx;
                min-height: 500rpx;
                
                &.minHeight {
                    min-height: 100vh;
                }

                .bottom_line {
                    height: 2rpx;
                    background: #e2e6ec;
                    margin: 0 30rpx;
                }

                .mess_tab {
                    padding: 0rpx 48rpx 32rpx 48rpx;

                    .mess {
                        font-size: 32rpx;

                        color: #333333;
                        font-weight: bold;

                        text {
                            padding: 0 16rpx;

                            font-weight: 400;
                            color: #999999;
                        }
                    }

                    .write {
                        width: 148rpx;
                        height: 60rpx;
                        border: 2rpx solid #ff5561;

                        background: #ff5656;
                        color: #fff;
                        border-radius: 24rpx;

                        text {
                            display: inline-block;
                            font-size: 24rpx;
                        }

                        .tips {
                            z-index: 999;
                            width: 356rpx;
                            height: 150rpx;
                            background: #ffffff;
                            border-radius: 30rpx;
                            box-shadow: 0px 0px 30rpx 0px rgba(107, 127, 153, 0.3);
                            text-align: left;
                            color: #50506f;
                            padding: 22rpx 50rpx;
                            bottom: -185rpx;
                            font-family: PingFang SC, PingFang SC-Regular;
                            font-weight: 400;

                            .txt {
                                font-size: 32rpx;
                                margin-bottom: 20rpx;
                            }

                            .txt1 {
                                font-size: 24rpx;

                                .image {
                                    margin-right: 10rpx;
                                    height: 36rpx;

                                    image {
                                        width: 36rpx;
                                        height: 36rpx;
                                    }
                                }

                                .btn {
                                    padding: 4rpx 26rpx;
                                    border: 2rpx solid #50506f;
                                    border-radius: 18rpx;
                                    transform: scale(0.82);
                                    transform-origin: right;
                                }
                            }

                            &::before {
                                position: absolute;
                                content: '';
                                z-index: 999999;
                                width: 0rpx;
                                height: 0rpx;
                                top: -30rpx;
                                right: 40%;
                                // background: red;
                                border-bottom: 40rpx solid #fff;
                                border-left: 10rpx solid transparent;
                                border-right: 30rpx solid transparent;
                                transform: skew(35deg);
                            }
                        }

                        &.item {
                            color: #ff5656;
                            background: none;
                            margin-right: 20rpx;

                            &.active {
                                border: 2rpx solid #50506f;
                                color: #50506f;
                            }
                        }
                    }
                }

                .mess_nav {
                    margin: 0 31rpx;
                    padding: 20rpx 0 24rpx 0;

                    // border-top: 2rpx solid #d8d8d8;
                    // border-bottom: 2rpx solid #d8d8d8;
                    .nav {
                        .item {
                            flex: 1;
                            color: #333333;
                            text-align: center;

                            .num {
                                color: #999999;
                            }

                            &:not(:last-child) {
                                border-right: 2rpx solid #d8d8d8;
                            }

                            .iconfont {
                                font-size: 24rpx;
                                font-weight: bold;
                            }
                        }
                    }


                }

                .message_wrap {
                    padding: 0 54rpx 0 48rpx;
                    background: none;

                    >view.item {
                        padding-bottom: 20rpx;

                        >view:not(.mess_avatar) {
                            padding-left: 52rpx;
                        }

                        &:not(:last-child) {
                            border-bottom: 2rpx solid #e2e6ec;
                        }
                    }

                    .mess_avatar {
                        margin: 50rpx 0 20rpx 0;
                        padding-bottom: 14rpx;

                        .avatar {
                            image {
                                width: 60rpx;
                                height: 60rpx;
                                border-radius: 50%;
                                margin-right: 32rpx;
                            }

                            text {
                                color: #101010;
                                font-size: 28rpx;
                            }
                        }

                        .btn {
                            width: 108rpx;
                            height: 50rpx;
                            margin-left: 20rpx;
                            border: 1px solid rgba(0, 0, 0, 0.078);
                            border-radius: 32rpx;

                            font-size: 24rpx;

                            color: #666666;
                        }
                    }

                    .score {
                        margin-bottom: 40rpx;

                        .txt {
                            padding: 8rpx 16rpx;

                            border-radius: 20rpx;
                            border: 2rpx solid #ff5656;
                            color: #ff5656;
                            // letter-spacing: 12rpx;
                            font-weight: 500;
                            font-size: 28rpx;
                            margin-right: 12rpx;
                        }

                        .process {
                            // width: 276rpx;
                            height: 58rpx;
                            padding: 14rpx 20rpx;
                            background: #ffffff;
                            opacity: 1;
                            border-radius: 20rpx;

                            .num {
                                margin-left: 14rpx;
                                color: #ff5656;
                                font-weight: bold;
                                font-size: 40rpx;
                            }
                        }
                    }

                    .label {
                        ::deep.xlabel {
                            >.wrap {
                                margin-bottom: 36rpx;
                                background: none;

                                .flex {
                                    background: none;
                                }

                                .more {
                                    width: 60rpx;
                                    height: 60rpx;
                                    border-radius: 30rpx;

                                    text {
                                        width: 60rpx;
                                        height: 60rpx;
                                        border-radius: 30rpx;
                                    }
                                }
                            }
                        }
                    }

                    .mess_des {
                        margin-top: 42rpx;
                        font-size: 24rpx;
                        line-height: 36rpx;
                        color: #101010;
                        margin-bottom: 20rpx;

                        .del {
                            text-align: right;
                            color: #999;
                        }

                        .refining_span {
                            position: absolute;
                            border: 1rpx solid red;
                            top: -40rpx;
                            right: 20rpx;
                            color: red;
                            padding: 2rpx 30rpx;
                            font-weight: bold;
                            font-size: 30rpx;
                            letter-spacing: 6rpx;
                            transform: rotate(-30deg);
                            opacity: 0.6;
                            border-radius: 10rpx;
                        }
                    }

                    .mess_image {
                        height: 148rpx;
                        margin-bottom: 20rpx;
                        margin-left: -20rpx;

                        .nav {
                            width: 20rpx;
                            line-height: 148rpx;
                            text-align: center;

                            text {
                                font-size: 24rpx;
                                font-weight: bolder;
                            }
                        }

                        .wrap1 {
                            width: calc(100% - 40rpx);

                            .item {
                                // display: inline-block;
                                width: 136rpx;
                                height: 136rpx;

                                &:not(:last-child) {
                                    margin-right: 6rpx;
                                }

                                image {
                                    width: 136rpx;
                                    height: 136rpx;
                                }

                                &.video {
                                    position: relative;

                                    .cover {
                                        position: absolute;
                                        width: 100%;
                                        height: 100%;
                                        z-index: 10;
                                        top: 0;

                                        image {
                                            width: 120rpx;
                                            height: 120rpx;
                                        }
                                    }
                                }

                                &.audio {
                                    // .cover {
                                    display: flex;
                                    align-items: center;
                                    justify-content: space-around;

                                    image {
                                        width: 120rpx;
                                        height: 120rpx;
                                    }

                                    // }
                                }
                            }
                        }
                    }

                    .my_handle {
                        background: #fff;
                        padding: 12rpx 10rpx 12rpx 82rpx;
                        margin-bottom: 30rpx;
                        border-radius: 32rpx;
                        box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);

                        .txt {
                            color: #ff5656;
                        }

                        .btn {
                            width: 152rpx;
                            height: 60rpx;
                            background: #eaeaea;
                            opacity: 1;
                            border-radius: 24rpx;

                            color: #50506f;
                            font-size: 24rpx;
                            margin-left: 8rpx;
                        }
                    }

                    .mess_handle {
                        font-size: 24rpx;
                        color: #ff5656;
                        margin: 32rpx 0 40rpx 0;

                        .time {
                            color: #999999;
                        }

                        .item {
                            image {
                                width: 34rpx;
                                height: 24rpx;
                            }

                            text {
                                padding: 0 16rpx;
                                font-size: 24rpx;

                                color: #ff5656;
                            }
                        }
                    }

                    .comment {
                        padding-left: 0rpx !important;
                        margin-left: 52rpx;
                        background: #eaeaea;

                        max-height: 600rpx;
                        overflow-y: scroll;
                    }
                }
            }
        }


        .active_footer {
            @include fixed_footer(112rpx);
            box-shadow: 0px 0px 40px 0px rgba(107, 127, 153, 0.20);
            background: #f7f8fa;

            &.hide {
                display: none;
            }

            .flex_l {
                width: calc(100% - 400rpx);
                padding: 0 60rpx;

                .item {
                    // width: calc((100% - 400rpx) / 2);
                    width: 50%;
                    text-align: center;
                    font-size: 24rpx;

                    color: #3e3e3e;

                    image {
                        width: 40rpx;
                        height: 40rpx;
                    }
                }
            }

            .btn {
                width: 400rpx;
                height: 112rpx;
                line-height: 112rpx;
                text-align: center;

                border-radius: 30rpx 0px 0px 0px;
                font-size: 32rpx;
                background: #666666;
                color: #ffffff;
                font-weight: bold;

                .btn_r {
                    background: #ff5656;
                    border-radius: 30rpx 0px 0px 0px;
                }
            }
        }
    }

    .tt_text_left {
        width: 100%;
        height: auto;
        overflow: hidden;
        padding: 0 20rpx;
        margin-top: 20rpx;
    }

    .tt_text_left_btn1 {
        float: left;
        width: 150rpx;
        height: 70rpx;
        margin: 0 auto;
        border: 1rpx solid #e6e6e6;
        border-radius: 10rpx;
        text-align: center;
        line-height: 70rpx;
    }

    .tt_text_left_btn2 {
        float: right;
        width: 150rpx;
        height: 70rpx;
        background-color: #e93323;
        color: #fff;
        font-size: 28rpx;
        border-radius: 10rpx;
        text-align: center;
        line-height: 70rpx;
    }

    .wechatGroup-img {

        image {
            display: block;
            width: 300rpx;
            height: 300rpx;
            margin: 0 auto;
        }
    }

    // #ifdef MP-TOUTIAO
    .tt_margin-right {
        margin-right: 28rpx;
    }

    // #endif
</style>
