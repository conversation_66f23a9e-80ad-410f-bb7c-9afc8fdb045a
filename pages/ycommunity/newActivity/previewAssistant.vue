<template>
    <view class="content">
        <view class="content-box">
            <view class="content-top">
                <image class="content-top-logo" src="../../../static/images/logo4.png" mode=""></image>
                <view class="content-top-title">
                    <view class="title">
                        课程预约
                    </view>
                    <view class="details">
                        {{detail.contact_info}}
                    </view>
                </view>
            </view>
            <image v-if="detail.contact_image" class="content-cen" :src="detail.contact_image"
                mode="widthFix" show-menu-by-longpress="true"></image>
        </view>
    </view>
</template>

<script>
    import {
        activityOnlineDetail,
    } from '@/api/community';

    export default {

        data() {
            return {
                detail:{}
            };
        },
        onLoad(option) {
            this.getEvaluateDetail(option.id)
        },
        methods: {
            getEvaluateDetail(id) {
                let from = '';
                // #ifndef MP-TOUTIAO
                from = 'routine';
                // #endif
                // #ifdef MP-TOUTIAO
                from = 'bytedance';
                // #endif
                activityOnlineDetail(id, from)
                    .then(res => {
                        // console.log('获取活动成功', res)
                        this.detail = res.data;
                    })
                    .catch(err => {
                        console.log('获取活动失败', err)
                        this.$navigator(-1);
                    });
            },
        }
    };
</script>
<style>
    page {
        padding-bottom: 0 !important;
    }

    .content {
        width: 100%;
        min-height: 100vh;
        padding: 30rpx 30rpx 0;
        background: #f2f3f8;
        box-sizing: border-box;
    }

    .content .content-box {
        width: 100%;
        padding: 0;
        margin: 0;
        overflow: auto;
    }

    .content .content-box .add-button {
        width: 280rpx;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        background-color: #0091CC;
        border-radius: 10rpx;
        margin: 100rpx auto;
        color: #ffffff;
        font-size: 28rpx;
    }

    .content .content-box .content-cen {
        width: 100%;
        display: inline-block;
        margin: 0 auto;
    }

    .content .content-box .content-top {
        width: 100%;
        overflow: auto;
        min-height: 118rpx;
        padding: 30rpx;
        box-sizing: border-box;
        background: #f6f7fb;
    }

    .content .content-box .content-top .content-top-logo {
        float: left;
        width: 242rpx;
        height: 80rpx;
    }

    .content .content-box .content-top .content-top-title {
        float: left;
        width: 388rpx;
        padding-left: 26rpx;
        box-sizing: border-box;
    }

    .content .content-box .content-top .content-top-title .title {
        width: 100%;
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
    }

    .content .content-box .content-top .content-top-title .details {
        width: 100%;
        font-size: 26rpx;
        color: #666666;
        margin-top: 10rpx;
    }

    .content .content-box .content-top .content-top-title .details.addheight {
        margin-top: 40rpx;
    }
</style>