<template>
	<view class="wrap">
		<view class="item">
			<view class="label">标题</view>
			<view class="input"><input type="text" v-model="form.name" name="name" /></view>
		</view>
		<view class="item">
			<view class="label">正文</view>
			<view class="input textarea">
				<xTextarea height="200rpx" name="desc" v-model="form.desc"></xTextarea>
			</view>
		</view>
		<view class="item">
			<view>
				<view class="wrap_show">
					<view class="filesImage">
						<view v-for="(item, index) in imageValue" :key="index" class="item">
							<image class="" :src="item.path" mode="aspectFill" @click.stop="prviewImage(item, index)">
							</image>
							<view class="icon-del-box" @click.stop="delFile('image', index)">
								<image src="@/static/images/community/close.png" mode="aspectFill">
								</image>
							</view>
						</view>
                        
						<view v-for="(item, index) in videoValue" :key="index" class="item">
							<video :src="item.path" controls @play="playVideo" custom-cache="false"></video>
							<view class="icon-del-box" @click.stop="delFile('video', index)">
								<image src="@/static/images/community/close.png" mode="aspectFill">
								</image>
							</view>
						</view>
					</view>
					<view class="play flex" v-if="isAudio&&audioValue.length">
						<view class="icon" @click="playAudio">
							<image src="@/static/images/community/play.png" v-if="!autoplay" />
							<image src="@/static/images/community/pause.png" v-else />
						</view>
						<view class="play_r">
							<view class="">音频文件</view>
							<view class="progress" @click="sliderChange">
								<u-slider v-model="progress" block-width="6" @end="sliderChange" step="1"
									:use-slot="true">
									<view class="">
										<view class="badge-button">
											<image src="@/static/images/community/scale.png">
										</view>
									</view>
								</u-slider>
							</view>
						</view>
						<view class="icon-del-box" @click.stop="delFile('recorder')">
							<image src="@/static/images/community/close.png" mode="aspectFill">
							</image>
						</view>
					</view>
				</view>
				<view class="flex xFile">
					<view>
						<xFileActionSheet :disabledCloud="true" :disabledCloudConfig="imageConfig" ref="filesImage"
							:auto-upload="false" fileMediatype="image" mode="grid" @select="select"
							@progress="uploadProgress" @success="success" @fail="fail" @show="show">
							<view class="upload">
								<image src="@/static/images/community/icon1.png" mode=""></image>
							</view>
						</xFileActionSheet>
					</view>
					<view :style="videoShow">
						<xFileActionSheet :disabledCloud="true" :limit="1" :opts="{limitDuration:35}"
							returnType="object" :disabledCloudConfig="videoConfig" ref="filesVideo" :auto-upload="false"
							fileMediatype="video" mode="grid" @select="select" @progress="uploadProgress"
							@success="success" @fail="fail" @show="show">
							<view class="upload">
								<image src="@/static/images/community/icon3.png" mode="widthFix"></image>
							</view>
						</xFileActionSheet>
					</view>
					<!-- #ifdef MP -->
					<view :style="adudioShow">
						<xFileActionSheet :disabledCloud="true" :disabledCloudConfig="audioConfig" :limit="1"
							ref="filesAudio" :auto-upload="false" fileMediatype="audio" fileExtname="mp3" mode="grid"
							@recorder="voice" @progress="uploadProgress" @success="success" @fail="fail"
							@select="select" @show="show">
							<view class="upload">
								<image src="@/static/images/community/icon2.png" mode="heightFix"></image>
							</view>
						</xFileActionSheet>
					</view>
					<!-- #endif -->
				</view>
			</view>
			<x-recorder ref="xRecorder" :duration="180000" @start="start" @stop="stop" @select="select" />
		</view>
		<view class="item" @click="getLocation">
			<view class="input flex flex_align_center flex_between">
				<view class="flex flex_align_center">
					<view class="block flex_line_height">
						<image src="@/static/images/yuanshi/address.png" mode="widthFix"></image>
					</view>
					<view>
						{{address.address}}{{address.name}}
					</view>
				</view>
				<view class="iconfont icon-jiantou"></view>
			</view>
		</view>
        <!-- #ifdef MP-WEIXIN -->
        <view class="item avatar">
        	<view class="avatar">
        	    <view class="avatar-left">
        	        头像
        	    </view>
                <button class="avatar-right" open-type="chooseAvatar" @chooseavatar="onChooseAvatar" type="default">
                    <image class="avatar-right-img" :src="AuthorizedAvatar && avatar?avatar:'../../../static/images/noPictrue.png'"></image>
                    <image class="avatar-right-alter" src="@/static/images/alter.png" mode="widthFix"/>
                </button>
        	</view>
        	<view class="nickname">
        	    <view class="nickname-left">
        	        昵称
        	    </view>
        	    <input class="nickname-right" v-model="nickname" @blur="getNickname" @confirm="getNickname" type="nickname" placeholder="请输入微信昵称" placeholder-style="font-size:24rpx;color:#ccc"/>
        	</view>
        </view>
        <!-- #endif -->
		<view class="item">
			<view @click="check = !check">
				<xProtocol v-model="check"></xProtocol>
			</view>
		</view>
		<u-button hover-class="none" :custom-style="customStyle" :disabled="disabled" @click="readyToSubmit">确认上传
		</u-button>


		<u-mask :show="maskShow" :custom-style="{background: 'rgba(0, 0, 0, 0.3)'}">
			<view class="xmask relative flex flex_align_center flex_around">
				<view class="absolute" @click="cancelTask">
					<u-icon name="close-circle-fill" size="40"></u-icon>
				</view>
				<view class="text_center">
					<u-loading mode="circle " size="45" color="#ffffff"></u-loading>
					<view class="txt">
						{{uploadTypeTxt}}上传中
					</view>
				</view>

			</view>
		</u-mask>
        <!-- #ifdef MP-WEIXIN -->
        <x-agreement ref="agreement"></x-agreement>
        <!-- #endif -->
		<!-- <xMap ref="xmap"/> -->
	</view>
</template>

<script>
	import xTextarea from '@/components/x-textarea/x-textarea.vue';
	import xProtocol from '@/components/x-protocol/x-protocol';
	import xFileActionSheet from '@/components/x-file-picker/x-file-action-sheet.vue';
	import xRecorder from '@/components/x-recorder/x-recorder.vue';
	import playAudio from '@/mixins/playAudio.js';
    import { postUserEdit, getUser } from '@/api/user';
    import { uploadImg } from '@/utils/upload.js';
	// // 播放
	import {
		uploadAudio,
		uploadVideo,
		uploadImage,
		activityMessageAdd
	} from '@/api/community';
	import {
		authChooseLocation
	} from '@/utils/common.js';
	import {
		getSpecialMessageAdd,
	} from '@/api/yknowledge.js'
	import {
		VUE_APP_API_URL
	} from '@/config.js'
	var innerAudioContext = null;
	export default {
		mixins: [playAudio],
		components: {
			xTextarea,
			xProtocol,
			xFileActionSheet,
			xRecorder,
		},
		data() {
			return {
				maskShow: false,
				maskTxt: '文件上传中',
				customStyle: {
					height: '100rpx',
					lineHeight: '100rpx',
					background: '#ff5656',
					fontSize: '24rpx',
					color: ' #ffffff',
					borderRadius: ' 40rpx',
				},
				form: {
					desc: '',
					name: '',
				},
				address: {
					address: "",
					latitude: 39.834161349,
					longitude: 116.281887674,
					name: "所在位置",
				},
				check: false,
				disabled: false,
				imageValue: [],
				videoValue: [],
				audioValue: [],
				imageFinish: true,
				videoFinish: true,
				audioFinish: true,
				imageConfig: {
					url: 'https://dev.shop.arthorize.com/api/upload/image',
					header: {
						['Authori-zation']: 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJkZXYuc2hvcC5hcnRob3JpemUuY29tIiwiYXVkIjoiZGV2LnNob3AuYXJ0aG9yaXplLmNvbSIsImlhdCI6MTYyNjY4OTQyMSwibmJmIjoxNjI2Njg5NDIxLCJleHAiOjE2MjcyOTQyMjEsImp0aSI6eyJpZCI6MSwidHlwZSI6InVzZXIifX0.ExNDKamehX6wXXrbuuEjOK1e4C0uuHPe1vwMvfoVO2c'
					}
				},
				videoConfig: {},
				audioConfig: {},
				isRecorder: false,
				isAudio: false,
				failMsg: {
					image: '',
					video: '',
					audio: '',
				},
				gift: '0',
				uploadTask: [],
				uploadType: '',
				uploadAbort: false,
                userInfo:{},
                avatar:'',
                AuthorizedAvatar:false,
                AuthorizedNickname:false,
                nickname:''
			};
		},
		computed: {
			videoShow() {
				return this.videoValue.length < 1 ? 'display:block' : 'display:none'
			},
			adudioShow() {
				return this.audioValue.length < 1 ? 'display:block' : 'display:none'
			},
			uploadTypeTxt() {
				let txt = '';
				switch (this.uploadType) {
					case 'image':
						txt = '图片'
						break;
					case 'video':
						txt = '视频'
						break;
					case 'audio':
						txt = '音频'
						break;
					default:
						txt = '文件'
						break;
				}
				return txt;
			}
		},
		methods: {
            getNickname(e){
                console.log('昵称',e.detail.value.trim())
                if(e.detail.value.trim() == '微信用户' || e.detail.value.trim() == ''){
                    return this.$showToast('输入昵称不规范')
                }else {
                    this.nickname = e.detail.value
                    this.AuthorizedNickname = true;
                }
            },
            async onChooseAvatar(e){
                this.avatar = e.detail.avatarUrl;
                this.AuthorizedAvatar = true;
                console.log('头像',this.avatar)
                let res = await uploadImg(this.avatar);
                this.avatar = res[0];
            },
            getUserInfo: function() {
            	let that = this;
            	getUser().then(res => {
            		this.userInfo = res.data;
                    if(this.userInfo.avatar == '' || !this.userInfo.avatar || this.userInfo.avatar == 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132' || this.userInfo.avatar == 'https://thirdwx.qlogo.cn/mmopen/vi_32/Z22RuflpLpHqnZy0icITdAuMrwXdaMyibm58t2dibBOH7a5sWW8lVew8OWfia3oJwKT56dHYSgkIWiaicnZ5kRmenibCw/132'){
                        this.AuthorizedAvatar = false
                    }else {
                        this.AuthorizedAvatar = true
                        this.avatar = this.userInfo.avatar;
                    }
                    if(this.userInfo.nickname.trim() == '' || !this.userInfo.nickname || this.userInfo.nickname == '微信用户'){
                        this.AuthorizedNickname = false
                    }else {
                        this.AuthorizedNickname = true
                        this.nickname = this.userInfo.nickname;
                    }
                    
            	});
            },
			maskStatus(type, txt = '') {
				this.maskShow = type;
				this.maskTxt = txt;
			},
			cancelTask() {
				this.uploadAbort = true;
				if (this.uploadTask.length) {
					this.uploadTask.forEach(item => {
						item.abort();
					})
				}
				this.maskStatus(false, '取消上传')
				this.$showToast(`${this.uploadTypeTxt}已取消上传`);
			},
			// 获取上传状态
			select(e, type, files) {
				this.isRecorder = false
				if (type === 'image') {
					this.imageValue = files;
					console.log('imageValue', this.imageValue);
				} else if (type === 'video') {
					this.videoValue = files;
					console.log('videoValue', this.videoValue);
				} else if (type === 'audio' || type === 'recorder') {
					this.audioValue = files;
					this.isAudio = true;
					console.log('audioValue', this.audioValue);
					if (files.length) {
						if (type === 'recorder') {
							this.isRecorder = true
						}
						this.initPlay(files[0].path);
					}
				}

			},
			show(index, type) {
				if (type === 'audio') {
					this.initAudioContext();
				}
			},
			// 获取上传进度
			uploadProgress(e, type) {
				const {
					progressEvent,
					tempFile,
					progress,
					index
				} = e;
				this.uploadType = tempFile.fileType;
				this.uploadTask[index] = progressEvent.uploadTask;
			},
			// 上传成功
			success(e, type) {
				console.log(`${this.uploadTypeTxt}上传完成`, e)
				const {
					tempFiles = [], tempFilePaths = []
				} = e;

				// this.maskStatus(true,'文件上传中');
				if (type === 'image') {
					this.imageFinish = true;
				}
				if (type === 'video') {
					this.videoFinish = true;
				}
				if (type === 'audio') {
					this.audioFinish = true;
					if (this.isRecorder) {
						this.audioValue = e.tempFiles;
					}
				}
				console.log(type, this.imageFinish, this.videoFinish, this.audioFinish);
				if (this.imageFinish && this.videoFinish && this.audioFinish) {
					this.$showToast(`上传完成`);
					this.maskStatus(false, '上传完成')
					this.submit()
				} else if (!this.imageFinish || !this.videoFinish || !this.audioFinish) {
					console.log(`${this.uploadTypeTxt}上传完成,随机下一个`, this.uploadTask.length);
					!this.uploadAbort && this.upload();
				}
			},

			// 上传失败
			fail(e, type) {
				console.log('上传失败：', e, type);
				const {failMsg}= e.tempFiles[0];
				if (type === 'image') {
					this.imageFinish = false;
				}
				if (type === 'video') {
					this.videoFinish = false;
				}
				if (type === 'audio') {
					this.audioFinish = false;
				}
				if(failMsg === 'uploadFile:fail timeout'){
					this.$showToast('网络超时，请稍后再试' )
				}else if(failMsg === 'uploadFile:fail'){
					this.$showToast('上传失败，请稍后再试' )
				}else if(failMsg === 'uploadFile:fail'){
					this.$showToast('上传异常，请稍后再试' )
				}
				this.maskStatus(false, failMsg)
			},

			upload() {
				this.uploadTask = [];
				this.uploadAbort = false;
				const {
					imageValue = [],
						videoValue = [],
						audioValue = [],
						imageFinish = true,
						videoFinish = true,
						audioFinish = true,
				} = this;
				if (!this.checkUploaded(imageValue)&&!imageFinish && imageValue.length) {
					this.$refs.filesImage.upload()
				} else if (!this.checkUploaded(videoValue)&&!videoFinish && videoValue.length) {
					this.$refs.filesVideo.upload();
				} else if (!this.checkUploaded(audioValue)&&!audioFinish && audioValue.length) {
					this.$refs.filesAudio.upload(this.isRecorder ? audioValue : null);
				} else {
					console.log('没有可上传文件')
					this.maskStatus(false);
					this.submit()
				}
			},
			checkUploaded(files) {
				console.log(files)
				let i = 0;
				files.forEach((v, index) => {
					if (v.status && v.status === 'success') {
						i++;
					}
				});
				return i === files.length
			},
			voice() {
				this.$refs.xRecorder.startRecord();
			},
			start() {

			},
			stop(voice) {},
			cancel() {
				this.$refs.xRecorder.cancelRecord();
			},
			prviewImage(img, index) {
				let urls = [];
				this.imageValue.forEach(i => {
					urls.push(i.path);
				});
				uni.previewImage({
					urls: urls,
					current: index
				});
			},
			delFile(type, index = 0) {
				if (type === 'image') {
					this.$refs.filesImage.delFile(index);
				} else if (type === 'video') {
					this.$refs.filesVideo.delFile(index);
				} else if (type === 'audio') {
					this.$refs.filesAudio.delFile(index);
					this.innerAudioContext.pause();
					this.isAudio = false;
				} else if (type === 'recorder') {
					this.audioValue = [];
					this.innerAudioContext.pause();
					this.isAudio = false;
				}
			},
			async getLocation() {
				let res = await authChooseLocation();
				this.address = res;
				console.log('getLocation', res)
			},
			readyToSubmit() {
                let that = this;
				const {
					desc,
					name
				} = this.form;
				const {
					imageValue,
					videoValue,
					audioValue
				} = this;
				if (!name) {
					return this.$showToast('请填写标题')
				}
				if (!desc) {
					return this.$showToast('请填写正文')
				}
                
                // #ifdef MP-WEIXIN
                if (!this.AuthorizedAvatar || !this.avatar) {
                    return this.$showToast('请完善头像信息')
                }
                if (!this.AuthorizedNickname || this.nickname == '') {
                    return this.$showToast('请完善昵称信息')
                }
                if (this.nickname == '微信用户' || this.nickname.trim() == '') {
                    return this.$showToast('昵称不规范')
                }
                // #endif
                
				if (!this.check) {
					return this.$showToast('请勾选协议');
				}
                // #ifdef MP-WEIXIN
                postUserEdit({
                	nickname: this.nickname,
                	avatar: this.avatar
                }).then(
                	res => {
                        if (imageValue.length || videoValue.length || audioValue.length) {
                        	that.maskStatus(true);
                        	that.imageFinish = !imageValue.length;
                        	that.videoFinish = !videoValue.length;
                        	that.audioFinish = !audioValue.length;
                        	that.upload()
                        } else {
                        	that.submit()
                        }
                	},
                	error => {
                        console.log('error---',error)
                	}
                );
                // #endif
                // #ifndef MP-WEIXIN
                if (imageValue.length || videoValue.length || audioValue.length) {
                	that.maskStatus(true);
                	that.imageFinish = !imageValue.length;
                	that.videoFinish = !videoValue.length;
                	that.audioFinish = !audioValue.length;
                	that.upload()
                } else {
                	that.submit()
                }
                // #endif
				

			},
			submit() {
				if (this.uploadAbort) return //取消上传之后，服务器仍有返回情况
				this.$loadingToast('正在提交中', {
					mask: true
				})
				const {
					desc,
					name
				} = this.form;
				const {
					imageValue,
					videoValue,
					audioValue,
					address
				} = this;
				let obj = {
					title: name,
					comment: desc,
					type: 0,
					related_id: this.did,
					position: address.latitude + ',' + address.longitude,
                    // #ifdef MP-TOUTIAO
                    from: 'bytedance',
                    // #endif
                    // #ifndef MP-TOUTIAO
                    from :'routine',
                    // #endif
				};
				if (imageValue.length) {
					obj.image = imageValue.map(item => item.url);
				}
				if (videoValue.length) {
					obj.video = videoValue.map(item => item.url);
				}
				if (audioValue.length) {
					obj.audio = audioValue.map(item => item.url || item.path);
				}
				// return;
				let req = activityMessageAdd;
				if (this.gift === '1') {
					obj.special_id = this.did;
					req = getSpecialMessageAdd;
				}
                
				req(obj).then(res => {
					this.$hideLoading();
					// this.maskStatus(false,'文件上传中');
					this.$showToast('上传成功')
					const history = getCurrentPages();
					if (history.length > 1) {
						let beforePage = history[history.length - 2];
						console.log('更新上一个页面', beforePage.$vm)
						beforePage.$vm.updateData();
					}
					this.$navigator(1)
				}).catch(err => {
					this.$hideLoading()
                    this.$showToast(err)
					// this.maskStatus(false,'文件上传中');
				})
			}
		},
		mounted() {
			const token = this.$storage.get('token');
			this.imageConfig = {
				url: `${VUE_APP_API_URL}/upload/image`,
				header: {
					['Authori-zation']: 'Bearer ' + token
				}
			};
			this.audioConfig = {
				url: `${VUE_APP_API_URL}/upload/audio`,
				header: {
					['Authori-zation']: 'Bearer ' + token
				}
			};
			this.videoConfig = {
				url: `${VUE_APP_API_URL}/upload/video`,
				header: {
					['Authori-zation']: 'Bearer ' + token
				}
			}
		},
		onLoad(option) {
			const {
				id,
				gift = '0'
			} = option;
			this.did = id;
			this.gift = gift

			uni.getNetworkType({ //调用获取网络类型函数

				success: function(res) {

					console.log(res)

				},

			})
            this.getUserInfo()

		},
	};
</script>

<style scoped lang="scss">
	.xmask {
		width: 200rpx;
		height: 200rpx;
		margin: 40vh auto;
		background: rgba($color: #000000, $alpha: 0.5);
		color: #fff;
		border-radius: 20rpx;

		.absolute {
			width: 40rpx;
			top: -10rpx;
			right: -10rpx;
		}
	}

	.wrap {
		padding: 60rpx 40rpx;


		.display_block {
			display: block;
		}

		.display_none {
			display: none;
		}

		image {
			width: 100%;
			height: 100%;
		}

		.item {
			margin-bottom: 46rpx;
            
            &.avatar {
                padding: 40rpx;
                box-sizing: border-box;
                font-size: 28rpx;
                color: #282828;
            }
            .avatar {
                width: 100%;
                overflow: auto;
                
                .avatar-left {
                    float: left;
                    width: 200rpx;
                }
                .avatar-right {
                    position: relative;
                    float: left;
                    width: 96rpx;
                    height: 96rpx;
                    .avatar-right-img {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                    }
                    .avatar-right-alter {
                            width: 30rpx;
                            height: 30rpx;
                            border-radius: 50%;
                            position: absolute;
                            bottom: 0;
                            right: 0;
                    }
                }
            }
            .nickname {
                margin-top: 30rpx;
                width: 100%;
                overflow: auto;
                .nickname-left {
                    float: left;
                    width: 200rpx;
                }
                .nickname-right {
                       float: left;
                       width: 200rpx;
                       height: 52rpx;
                       line-height: 52rpx;
                       border: 2rpx solid #ddd;
                       border-radius: 6rpx;
                       padding-left: 10rpx;
                }
            }

			.label {
				font-size: 24rpx;
				font-weight: 400;
				line-height: 34rpx;
				color: #666666;
				padding: 0 0 20rpx 20rpx;
			}

			.input {
				border: 2rpx solid rgba(0, 0, 0, 0);
				box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
				border-radius: 26rpx;
				padding: 26rpx 40rpx;
				background: #ffffff;

				&:not(.textarea) {
					min-height: 80rpx;
					display: flex;
					align-items: center;
				}

				input {
					font-size: 24rpx;
					width: 100%;
				}

				.block {
					margin-right: 20rpx;

					image {
						width: 30rpx;

					}
				}
			}
		}
	}

	.xFile {
		margin: 20rpx 0;

		>view {
			&:not(:last-child) {
				margin-right: 30rpx;
			}
		}

		.upload {
			display: flex;
			align-items: center;
			justify-content: space-around;
			width: 200rpx;
			height: 200rpx;
			background: #f4f4f4;
			border: 2rpx dashed #d2d2d2;
			border-radius: 26rpx;

			image {
				width: 68rpx;
				height: 68rpx;
			}
		}
	}

	.wrap_show {
		.play {
			position: relative;
			border: 6rpx solid #ffffff;
			border-radius: 30rpx;
			box-shadow: 0px 4rpx 20rpx 0px rgba(0, 0, 0, 0.06);
			padding: 38rpx 26rpx;

			.icon {
				width: 96rpx;
				height: 96rpx;

				image {
					width: 96rpx;
					height: 96rpx;
				}
			}

			.play_r {
				flex: 1;
				padding: 10rpx 0 10rpx 20rpx;
				font-size: 32rpx;
				font-weight: 700;
				color: #666666;

				.progress {
					margin-top: 16rpx;
					width: 100%;
					padding: 0 10rpx;

					.badge-button {
						width: 32rpx;
						height: 32rpx;
					}
				}
			}
		}
	}

	.filesImage {
		display: flex;
		flex-wrap: wrap;

		.item {
			position: relative;
			// margin-top: 40rpx;
			margin-bottom: 10rpx;
			width: 200rpx;
			height: 200rpx;
			margin-right: 30rpx;
			/* #ifndef APP-NVUE */
			display: flex;
			/* #endif */
			align-items: center;
			justify-content: center;
			border-radius: 26rpx;
			overflow: hidden;

			&:nth-child(3n) {
				margin-right: 0rpx;
			}

			image,
			video {
				width: 220rpx;
				height: 100%;
				border-radius: 26rpx;
			}

		}
	}

	.icon-del-box {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		align-items: center;
		justify-content: center;
		position: absolute;
		top: 0;
		right: 0;
		height: 48rpx;
		width: 48rpx;
		border-radius: 50%;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 2;
	}

	.rotate {
		position: absolute;
		transform: rotate(90deg);
	}
</style>
