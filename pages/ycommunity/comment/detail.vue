<template>
	<view class="comment">
		<view class="flex flex_align_center flex_between">
			<view class="flex  flex_align_center avatar">
				<view>
					<u-avatar :src="detail.avatar" mode="circle"></u-avatar>
				</view>
				<view style="font-size: 24rpx;color: #666666;margin-left: 18px;">
					<view class="nickname">{{detail.nickname}}</view>
					<view class="add_time">{{detail.add_time}} | {{detail.browse}}次浏览</view>
				</view>
			</view>
			<button class="flex_line_height btn_share" open-type="share" style="background: none;">
				<view class="flex flex_align_center">
					<image src="@/static/images/community/share.png" mode="widthFix"></image>
					<text>分享</text>
				</view>
			</button>
		</view>
		<swiper class="swiper" v-if="detail.video.length || detail.image.length">
			<swiper-item class="swiper-item video" v-for="(item1, index1) in detail.video"
				v-if="detail.video&&detail.video.length" :key="item1.name">
				<video :id="`playVideo${index1}`" style="width:750rpx;height: 750rpx;" :show-center-play-btn="false"
					:controls="isControl" :src="item1.name" custom-cache="false"></video>
				<view class="cover flex flex_align_center flex_around" v-show="!isControl">
					<image src="@/static/images/community/play.png" @click="playVideo(item1,index1)">
				</view>
			</swiper-item>
			<swiper-item class="swiper-item" v-for="(item1, index1) in detail.image"
				v-if="detail.image&&detail.image.length" :key="item1.name">
				<image :src="item1.name" mode=""></image>
			</swiper-item>
		</swiper>
		<view class="audio" v-if="detail.audio&&detail.audio.length">
			<xPlay ref='xPlay' :music="detail.audio[0].name" />
		</view>
		<view class="wrap">
			<view class="desc">
				<view class=" flex flex_between">
					<view class="title" style="flex:1">
						{{detail.title}}
					</view>
					<view class="btn flex_line_height" @click="inputShow = true" v-if="!isOnlyMy">回复</view>
				</view>
				<view class="relative">
					<view class="show ">
						{{detail.comment}}
					</view>
					<view class="refining_span" v-if="detail.is_refining">精</view>
				</view>

				<view class="footer flex flex_between">
					<view class="footer_l">{{detail.add_time}}</view>
					<view class="footer_r">
						<view class="flex flex_align_center">
							<view class="item flex flex_align_center" @click="likeStars(detail,null,true)">
								<image src="@/static/images/yuanshi/like_.png" mode="widthFix" v-if="detail.is_like">
								</image>
								<image src="@/static/images/yuanshi/like.png" mode="widthFix" v-else></image>
								<text>{{detail.like_count}}</text>
							</view>
							<view class="item flex flex_align_center">
								<image src="@/static/images/yuanshi/comment.png" mode="widthFix"></image>
								<text>{{detail.comments}}</text>
							</view>
							<!-- #ifdef MP -->
							<!-- 		<view class="item flex flex_align_center">
								<button class="flex_line_height btn_l" open-type="share" style="background: none;">
									<view class="flex flex_align_center">
										<image src="@/static/images/community/share.png" mode="widthFix"></image>
									</view>
								</button>
							</view> -->
							<!-- #endif -->

						</view>
					</view>
				</view>
			</view>
			<view class="theme flex flex_between">
				<view class="theme_l flex  ">
					<view class="image">
						<image :src="detail.activityInfo.image" mode="cover"></image>
					</view>
					<view class="">
						<view class="title">{{detail.activityInfo.name}}</view>
						<view class="txt">{{detail.activityInfo.participate_number}}人正在参加活动 丨
							{{detail.activityInfo.messages}}篇评论
						</view>
					</view>
				</view>
				<view class="theme_r" @click="goPages(detail.activityInfo.id)">
					去看看
				</view>
			</view>
			<view class="show_comment">
				<view class="item" v-for="(item, index) in messageInfo" :key="item.id">
					<view class="mess_avatar flex flex_align_center flex_between">
						<view class="avatar flex_align_center flex">
							<image :src="item.avatar"></image>
							<text>{{ item.nickname }}</text>
						</view>
						<view class="btn flex_line_height" @click="showInput(2, item)" v-if="!isOnlyMy">回复</view>
					</view>
					<view class="mess_des" v-if="item.content">
						<view v-for="(item1, index1) in item.content" :key="index1">
							<text>{{ item1 }}</text>
						</view>
					</view>
					<view class="mess_handle ">
						<view class="flex flex_align_center flex_between">
							<view class="time">{{ item.add_time }}</view>
							<view class="flex flex_align_center">
								<view class="item flex flex_align_center" @click="likeStars(item, index,false)">
									<image src="@/static/images/yuanshi/like_.png" mode="widthFix" v-if="item.is_like">
									</image>
									<image src="@/static/images/yuanshi/like.png" mode="widthFix" v-else></image>
									<text>{{ item.like_count || 0 }}</text>
								</view>
								<view class="item flex flex_align_center">
									<image src="@/static/images/yuanshi/comment.png" mode="widthFix"></image>
									<text>{{ item.comment_count || 0 }}</text>
								</view>
							</view>
						</view>
					</view>
					<view class="mess_comment">
						<xComment :ref="'xComment' + item.id" pageType="community" :ctype="2" :rid="item.id"
							:chat="false" @showInput="chatInput" @success="sendSuccess" :info="item.children_reply"
							:time="false" :tips="false" />
                        
					</view>
				</view>

			</view>
			<xChat :adjustPosition="adjustPosition" :placeholder="placeholder" :inputShow="inputShow" :uid="uid" @send="submitComment" desc="comment">
			</xChat>
		</view>
	</view>
</template>

<script>
	import xPlay from '@/components/x-play/x-play.vue';
	import xComment from '@/components/x-comment/x-comment/x-comment';
	import xChat from '@/components/x-chat/x-chat';
	import {
		openShareAll
	} from '@/utils/wechat/share.js';
    import { SHARE_ID } from '@/config.js';
	import {
		activityComment,
		activityMessage,
		activityMessageDetail,
		activityMessageLike,
		activityMessageUnLike,
		activityCommentLike,
		activityCommentUnLike,
		activityCommentAdd
	} from '@/api/community';
	export default {
		components: {
			xPlay,
			xComment,
			xChat
		},
		data() {
			return {
				playState: false,
				tabActive: 0,
				arr: [{
					num: 1,
					label: '评论'
				}, {
					num: 2,
					label: '点赞'
				}],
				isOnlyMy: false,
				messageInfo: [],
				uid: 0,
				placeholder: '',
				inputShow: false,
				scrollDetail: {},
				scrollLeft: 0,
				requestLoading: false,
				page: {
					page: 1,
					limit: 20,
					more: true
				},
				detail: {
					activityInfo: {},
					video: [],
					audio: [],
					image: []
				},
				shareConfig: {},
				videoCtx: null,
				isControl: false,
				isShare: '',
                adjustPosition: false
			}
		},
        onShow() {
            // #ifndef MP-TOUTIAO
            this.adjustPosition = false
            // #endif
            // #ifdef MP-TOUTIAO
            this.adjustPosition = true
            // #endif
        },
		methods: {
			goPages(id) {
				if (id === this.detail.activity_id) {
					if (this.isShare === 'share') {
						this.$navigator('/pages/ycommunity/shop/detail?id=' + id, 'redirectTo')
					} else {
						this.$navigator(-1)
					}
				} else {
					this.$navigator('/pages/ycommunity/shop/detail?id=' + id)
				}
			},
			playVideo(item, index) {
				this.isControl = true;
				// this.innerAudioContext.pause();

				this.$refs.xPlay.playPause();
				let ref = uni.createVideoContext(`playVideo${index}`);
				this.videoCtx = ref;
				let tid = setTimeout(() => {
					ref.play();
					clearTimeout(tid)
				}, 500)
			},
			navClick(type) {
				let initCalcItemScroll = this.initCalcItemScroll,
					oldScrollLeft = this.scrollDetail.scrollLeft || 0;
				if (type === 'left') {
					if (oldScrollLeft > 0) {
						this.scrollLeft = oldScrollLeft > initCalcItemScroll ? oldScrollLeft - initCalcItemScroll : 0;
					}
				} else {
					this.scrollLeft = oldScrollLeft + initCalcItemScroll;
				}
			},
			scrollX(e) {
				this.scrollDetail = e.detail;
			},
			tabClick(item, type) {

			},
			previewImage(urls, current) {
				uni.previewImage({
					urls: urls,
					current: current //地址需为https
				});
			},
			showInput(type = 1, item) {
				const {
					id
				} = item;
				this.xComment = this.$refs['xComment' + id][0];
				this.xComment.showInput(type, item, null, null);
			},
			chatInput({
				id,
				uid
			}, placeholder) {
				this.inputShow = true;
				this.uid = uid;
				this.xComment = this.$refs['xComment' + id][0];
				this.placeholder = placeholder;
			},
			submitComment(val) {
				const {
					avatar,
					nickname,
					uid
				} = this.$store.state.userInfo;
				const did = Number(this.did)
                let param = {
                    related_id: this.did,
                    comment: val,
                    pid: 0,
                    // #ifdef MP-TOUTIAO
                    from:'bytedance',
                    // #endif
                    // #ifndef MP-TOUTIAO
                    from:'routine',
                    // #endif
                }
				activityCommentAdd(param).then(res => {
					res.data.created_at = '刚刚';
					res.data.is_my = true;
					res.data.children_reply = [];
					if (typeof res.data.comment === 'string') {
						res.data.content = [res.data.comment];
					}
					this.messageInfo.unshift(res.data);
				})

				// this.xComment.submitComment(val);
			},
			likeStars(item, index, type) {
				let obj = {};
				if (type) {
					obj = {
						related_id: item.activity_id,
						message_id: item.id
					}
				} else {
					obj.related_id = item.related_id
					obj.comment_id = item.id
				}
				if (item.is_like) {
					if (type) {
						activityMessageUnLike(obj).then(res => {
							this.detail.like_count--;
							this.detail.is_like = !item.is_like
						})
					} else {
						activityCommentUnLike(obj).then(res => {
							this.messageInfo[index].like_count--;
							this.messageInfo[index].is_like = !item.is_like
						})
					}
				} else {
					if (type) {
						activityMessageLike(obj).then(res => {
							this.detail.like_count++;
							this.detail.is_like = !item.is_like
						})
					} else {
						activityCommentLike(obj).then(res => {
							this.messageInfo[index].like_count++;
							this.messageInfo[index].is_like = !item.is_like
						})
					}
				}
				// this.messageInfo[index].is_like = !item.is_like;
			},
			getComment() {
				if (!this.page.more || this.requestLoading) return;
				this.requestLoading = true;
				let obj = {
					related_id: this.did,
					page: this.page.page,
					limit: this.page.limit
				};

				activityComment(obj)
					.then(res => {
						let storeInfo = res.data;
						storeInfo.forEach((item, index) => {
							if (typeof item.comment === 'string') {
								item.content = [item.comment];
							}
						});
						this.messageInfo = this.messageInfo.concat(storeInfo);
						this.page.more = res.data.length === this.page.limit;
						this.page.page++;
						this.requestLoading = false;
					})
					.catch(err => {
						console.log(err);
						this.$navigator(-1)
						this.requestLoading = false;
					});
			},
			shareComment(item) {
				// #ifdef MP
				uni.showShareMenu({
					withShareTicket: true,
					menus: ['shareAppMessage', 'shareTimeline']
				})
				// #endif

			},
		},
		onPageScroll(e) {
			this.inputShow = false;
		},
		onLoad(options) {
			this.did = options.id;
			this.isShare = options.share || ''
			activityMessageDetail(options.id).then(res => {
				console.log(res)
				const item = res.data;
				this.detail = item;

				this.shareConfig = {
					desc: item.comment,
					title: item.title,
					link: '/pages/ycommunity/comment/detail?share=1&id=' + item.id,
					imgUrl: item.image.length ? item.image[0].name : (item.video.length ? item.video[0].name +
						'?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto' : '')
				};
				// #ifdef H5
				openShareAll(this.shareConfig);
				// #endif
				this.getComment()
			})
		},
		// #ifdef MP
		onShareAppMessage() {
			return {
				title: this.shareConfig.title,
				imageUrl: this.shareConfig.imgUrl,
				path: this.shareConfig.link,
                templateId: SHARE_ID
			};
		},
		// 朋友圈分享不支持自定义页面路径
		// onShareTimeline() {
		// 	if (this.shareConfig) {
		// 		return {
		// 			title: this.shareConfig.title,
		// 			imageUrl: this.shareConfig.imgUrl,
		// 			query: this.shareConfig.link.split('?')[1]
		// 		};
		// 	}
		// }
		// #endif
	}
</script>

<style lang="scss" scoped>
	.comment {
		font-family: PingFang SC, PingFang SC-Regular;

		.avatar {
			padding: 32rpx 24rpx;

			.nickname {
				font-size: 16px;
				font-weight: 700;
				text-align: left;
				color: #333333;
			}

			.add_time {
				font-size: 12px;
				font-weight: 400;
				text-align: left;
				color: #666666;
			}
		}

		.btn_share {
			width: 140rpx;
			font-size: 24rpx;
			height: 50rpx;
			background: #ffffff;
			border: 2rpx solid #d2d2d2;
			border-radius: 30rpx;
			margin-right: 24rpx;

			image {
				width: 30rpx;
				margin-right: 12rpx;
			}
		}

		::deep .xcomment .list {
			padding-bottom: 0;
		}

		image {
			width: 100%;
			height: 100%;
		}

		.swiper {
			width: 750rpx;
			height: 750rpx;

			.swiper-item {
				position: relative;

				.video {}

				.cover {
					position: absolute;
					width: 100%;
					height: 100%;
					z-index: 9999;
					top: 0;

					image {
						width: 160rpx;
						height: 160rpx;
					}
				}

			}
		}

		.audio {
			margin: 20rpx;
		}

		.wrap {
			padding: 74rpx 56rpx;
		}

		.desc {
			font-family: PingFang SC, PingFang SC-Regular;

			.title {
				margin-bottom: 40rpx;
				font-size: 32rpx;
				font-weight: 700;
				text-align: left;
				color: #333333;
			}

			.show {
				font-size: 24rpx;
				font-weight: 400;
				// @include show_line(5);
				min-height: 80rpx;


			}

			.refining_span {
				position: absolute;
				border: 1rpx solid red;
				top: 40rpx;
				right: 20rpx;
				color: red;
				padding: 2rpx 30rpx;
				font-weight: bold;
				font-size: 30rpx;
				letter-spacing: 6rpx;
				transform: rotate(-30deg);
				opacity: 0.6;
				border-radius: 10rpx;
			}

			.btn {
				width: 54px;
				height: 25px;
				margin-left: 10px;
				border: 1px solid rgba(0, 0, 0, 0.078);
				border-radius: 16px;
				font-size: 12px;
				color: #666666;
			}

			.footer {
				margin: 36rpx 0 30rpx 0;

				.footer_l {
					@include font_size(20);
					font-weight: 400;
					color: #999999;
				}
			}

			.footer_r {
				.item {
					image {
						width: 24rpx;
						height: 24rpx;
					}

					text {
						padding: 0 16rpx;
						font-size: 24rpx;
						color: #ff5656;
					}

					border: none;
					padding: 0;
					margin: 0;
				}
			}
		}

		.theme {
			border-top: 2px solid #f7f8fa;

			.theme_l {
				.image {
					width: 134rpx;
					height: 112rpx;
					margin-right: 20rpx;

					image {
						border-radius: 20rpx;
					}
				}

				.title {
					width: 360rpx;
					font-size: 32rpx;
					font-weight: 700;
					color: #333333;
				}

				.txt {
					font-size: 24rpx;
					font-weight: 400;
					color: #666666;
				}
			}

			.theme_r {
				border: 2rpx solid #e9625d;
				border-radius: 24rpx;
				font-size: 24rpx;
				padding: 0rpx 18rpx;
				height: 50rpx;
				line-height: 50rpx;
				color: #ff5656;
			}
		}

		.show_comment {

			.header {
				margin: 62rpx 0;
			}

			.mess_avatar {
				margin: 50rpx 0 20rpx 0;
				padding-bottom: 14rpx;

				.avatar {
					image {
						width: 60rpx;
						height: 60rpx;
						border-radius: 50%;
						margin-right: 32rpx;
						border: 2rpx solid #b5937f;
					}

					text {
						color: #101010;
						font-size: 28rpx;
					}
				}

				.btn {
					width: 108rpx;
					height: 50rpx;
					margin-left: 20rpx;
					border: 1px solid rgba(0, 0, 0, 0.078);
					border-radius: 32rpx;

					font-size: 24rpx;

					color: #666666;
				}
			}

			.mess_des {
				margin-top: 42rpx;
				font-size: 24rpx;
				line-height: 36rpx;
				color: #101010;
				margin-bottom: 20rpx;
			}

			.mess_handle {
				font-size: 24rpx;

				color: #ff5656;
				margin: 32rpx 0 40rpx 0;

				.time {
					color: #999999;
				}

				.item {
					image {
						width: 24rpx;
						height: 24rpx;
					}

					text {
						padding: 0 16rpx;
						font-size: 24rpx;

						color: #ff5656;
					}
				}
			}

			.mess_image {
				height: 148rpx;
				margin-bottom: 20rpx;
				margin-left: -20rpx;

				.nav {
					width: 20rpx;
					line-height: 148rpx;
					text-align: center;

					text {
						font-size: 24rpx;
						font-weight: bolder;
					}
				}

				.wrap1 {
					width: calc(100% - 40rpx);

					.item {
						display: inline-block;

						&:not(:last-child) {
							padding-right: 6rpx;
						}

						image {
							width: 136rpx;
							height: 136rpx;
						}
					}
				}
			}

			.mess_comment {
				padding-left: 0rpx !important;
				margin-left: 52rpx;
				background: #eaeaea;

				max-height: 600rpx;
				overflow-y: scroll;
			}
		}
	}
</style>
