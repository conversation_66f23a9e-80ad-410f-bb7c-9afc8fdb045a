<template>
	<view class="past_active">
		<view class="item flex"  v-for="(item, index) in pastActivity" :key="index"
			:id="`t${index}`" @click="goPages('/pages/ycommunity/shop/detail?id=' + item.id)">
			<view class="item_l">
				<image :src="item.image" alt="往期活动" mode="aspectFill"></image>
			</view>
			<view class="item_r">
				<view class="r_title">{{item.name}}</view>
				<view class="r_time">活动时间 {{item.activity_time}}</view>
				<!-- <view class="r_desc">{{item.info}} 众筹时间截止 {{item.end_time}}</view> -->
                <view class="r_desc">{{item.info}}</view>
				<view class="r_bottom flex flex_between">
                    <!-- #ifndef MP-TOUTIAO -->
                    <view class="price" v-show="item.price">￥{{item.price}}</view>
                    <!-- #endif -->
					<!-- <view class="limit">限制人数：{{item.hint}}</view> -->
				</view>
			</view>
		</view>
		<view v-if="pastActivity.length===0" class="text_center" style="margin-top: 200rpx;color:#999">暂无往期活动</view>
	</view>
</template>

<script>
	import {
		activityPast
	} from '@/api/community';
	export default {
		data() {
			return {
				pastActivity: []
			}
		},
		methods: {
			goPages(path, type) {
				this.$navigator(path, type);
			}
		},
		mounted() {
            let from = '';
            // #ifndef MP-TOUTIAO
            from = 'routine';
            // #endif
            // #ifdef MP-TOUTIAO
            from = 'bytedance';
            // #endif
			activityPast({},from).then(res => {
				this.pastActivity = res.data
			})
		}
	}
</script>

<style scoped lang="scss">
	.past_active {
		padding: 20rpx;

		image {
			width: 100%;
			height: 100%;
		}

		.item {
			margin-bottom: 34rpx;

			.item_l {
				width: 310rpx;
				height: 260rpx;
				margin-right: 40rpx;

				image {
					border-radius: 30rpx;
				}
			}

			.item_r {
				flex: 1;
				text-align: left;

				.r_title {
					@include show_line(1);
					font-weight: 700;
					font-size: 28rpx;
					color: #333333;
				}

				.r_time {
					font-size: 24rpx;
					color: #666;
				}

				.r_desc {
					@include font_size;
					transform-origin: left;
					color: #999999;
					margin: 16rpx 0 34rpx 0;
				}

				.r_bottom {
					.price {
						font-size: 28rpx;
						color: #333333;
					}

					.limit {
						@include font_size;
						@include show_line(1);
						color: #999999;
					}
				}
			}
		}
	}
</style>
