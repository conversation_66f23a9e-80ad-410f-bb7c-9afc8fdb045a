<template>
    <view class="">
        <view class="admin-box" v-show="navIndex == 1">
            <view class="order-title">
                <view class="left">
                    报名情况
                </view>
            </view>
            <view class="activityclass">
                活动类型：{{enrolldata.activity_type}}
            </view>
            <view class="activityclass-item">
                <view class="activityclass-item-left">
                    预报名：{{enrolldata.pre_register}}人
                </view>
                <view class="activityclass-item-right">
                    预报名剩余名额：{{enrolldata.surplus_pre_register}}人
                </view>
            </view>
            <view class="activityclass-item">
                <view class="activityclass-item-left">
                    确认报名：{{enrolldata.confirm_enrollment}}人
                </view>
                <view class="activityclass-item-right">
                    确认报名剩余名额：{{enrolldata.surplus_confirm_enrollment}}人
                </view>
            </view>
            <view class="activityclass-item">
                <view class="activityclass-item-left">
                    待付尾款：{{enrolldata.pending_balance}}人
                </view>
            </view>
            <view class="enroll">
                <view class="enroll-title">
                    报名订单
                </view>
                <view class="enroll-nav">
                    <view class="enroll-nav-left">
                        <block v-for="(item,index) in enrollNavList" :key="index">
                            <view class="enroll-nav-left-item" :class="enrollNavIndex == index ?'activite':''" @click="checkEnrollIndex(index)">{{item.name}}</view>
                        </block>
                    </view>
                    <view class="enroll-nav-right" @click="enrollRefresh">
                        刷新
                    </view>
                </view>
                <view class="userbox">
                    <block v-for="(item, index) in enrolldataList" :key="index">
                        <view class="userbox-item">
                            <view class="userbox-item-right">
                                <view class="top">
                                    <image class="top-left" :src="item.avatar" mode=""></image>
                                    <view class="top-right">
                                        {{item.nickname}} /<text class="top-right-id">id:{{item.uid}}</text>
                                    </view>
                                </view>
                                
                                <view class="item">
                                    <text class="item-title">
                                        单号
                                    </text>
                                    <text class="item-center">{{item.order_id}}</text>
                                </view>
                                <view class="item">
                                    <text class="item-title">
                                        联系人
                                    </text>
                                    <text class="item-center">{{item.real_name}}</text>
                                </view>
                                <view class="item">
                                    <text class="item-title">
                                        联系电话
                                    </text>
                                    <text class="item-center">{{item.user_phone}}</text>
                                </view>
                                <view class="item">
                                    <text class="item-title">
                                        数量
                                    </text>
                                    <text class="item-center">X {{item.total_num}}</text>
                                </view>
                                <view class="item">
                                    <text class="item-title">
                                        下单时间
                                    </text>
                                    <text class="item-center">{{item.add_time}}</text>
                                </view>
                                <view class="bot">
                                    <view class="bot-left">
                                        <text class="bot-left-title">订单状态</text>
                                        <!-- 原订单状态 0 未完成 1 已完成 2已结束 3已取消 -->
                                        <text v-if="item.order_type == 1 && item.status == 0">待付款</text>
                                        <text v-if="item.order_type == 2 && item.status == 0">待付尾款</text>
                                        <text v-if="item.status == 1">待参加</text>
                                        <text v-if="item.status == 2">已完成</text>
                                        <text v-if="item.status == 3">已取消</text>
                                    </view>
                                    <view class="bot-right" @click="orderDetails(item)">
                                        详情
                                    </view>
                                </view>
                            </view>
                        </view>
                    </block>
                </view>
            </view>
        </view>
        <view class="admin-box" v-show="navIndex == 2">
            <view class="order-title">
                <view class="left">
                    签到台
                </view>
            </view>
            <view class="activityclass-item">
                <view class="activityclass-item-left" style="color: black;">
                    活动类型：{{enrolldata.activity_type}}
                </view>
                <view class="activityclass-item-right-btn" @click="signInCode">
                    活动签到码
                </view>
            </view>
            <view class="activityclass-item">
                <view class="activityclass-item-left" style="color: black;">
                    应到人数：{{signdata.attendance}}人
                </view>
                <view class="activityclass-item-right" style="color: red;">
                    签到人数：{{signdata.actual_attendance}}人
                </view>
            </view>
            <view class="enroll">
                <view class="enroll-nav">
                    <view class="enroll-nav-left">
                        <block v-for="(item,index) in signNavList" :key="index">
                            <view class="enroll-nav-left-item" :class="signNavIndex == item.id ?'activite':''" @click="checkSingIndex(item.id)">{{item.name}}</view>
                        </block>
                    </view>
                    <view class="enroll-nav-right" @click="signRefresh">
                        刷新
                    </view>
                </view>
                <view class="userbox">
                    <block v-for="(item, index) in signdataList" :key="index">
                        <view class="userbox-item">
                            <view class="userbox-item-right">
                                <view class="top">
                                    <image class="top-left" :src="item.avatar" mode=""></image>
                                    <view class="top-right">
                                        {{item.nickname}} /<text class="top-right-id">id:{{item.uid}}</text>
                                    </view>
                                </view>
                                <view class="item">
                                    <text class="item-title">
                                        单号
                                    </text>
                                    <text class="item-center">{{item.order_id}}</text>
                                </view>
                                <view class="item">
                                    <text class="item-title">
                                        联系人
                                    </text>
                                    <text class="item-center">{{item.real_name}}</text>
                                </view>
                                <view class="item">
                                    <text class="item-title">
                                        联系电话
                                    </text>
                                    <text class="item-center">{{item.user_phone}}</text>
                                </view>
                                <view class="item">
                                    <text class="item-title">
                                        数量
                                    </text>
                                    <text class="item-center">X {{item.total_num}}</text>
                                </view>
                                <view class="item">
                                    <text class="item-title">
                                        下单时间
                                    </text>
                                    <text class="item-center">{{item.add_time}}</text>
                                </view>
                                <view class="bot">
                                    <view class="bot-left">
                                        <text class="bot-left-title">订单状态</text>
                                        <!-- 原订单状态 0 未完成 1 已完成 2已结束 3已取消 -->
                                        <text v-if="item.status == 0">待付款</text>
                                        <text v-if="item.status == 1">待参加</text>
                                        <text v-if="item.status == 2">已完成</text>
                                        <text v-if="item.status == 3">已取消</text>
                                    </view>
                                    <view class="bot-right" @click="orderDetails(item)">
                                        详情
                                    </view>
                                </view>
                            </view>
                        </view>
                    </block>
                </view>
            </view>
        </view>
        <view class="admin-box" v-show="navIndex == 3">
            <view class="order-title">
                <view class="left">
                    面试预约计划
                </view>
                <view class="right" @click="addadmin">
                    新增面试
                </view>
            </view>
            <block v-for="(item, index) in list" :key="index">
                <view class="order-box">
                    <view class="order-time">
                        <text class="time-left">开始时间</text>
                        <text class="time-right">{{item.start_time}}</text>
                    </view>
                    <view class="order-time">
                        <text class="time-left">结束时间</text>
                        <text class="time-right">{{item.end_time}}</text>
                    </view>
                    <view class="order-time">
                        <text class="time-left">预约进度</text>
                        <text class="time-right">{{item.progress_num}} / {{item.total_num}}</text>
                    </view>
                    <view class="order-btn">
                        <view class="btn" @click="goAdminDetails(item.activity_id,item.id)">
                            详情
                        </view>
                        <view class="btn" @click="getEventGetAppointedFile(item.id)">
                            已约清单
                        </view>
                        <view class="btn" @click="adminedit(item.activity_id,item.id)">
                            修改
                        </view>
                        <button class="btn" type="default" open-type="share"
                            @click="sharepage(item.id,item.activity_id)">分享预约入口</button>
                    </view>
                </view>
            </block>
        </view>
        <view class="bottom-nav">
            <block v-for="(item,index) in navlist" :key="index">
                <view :class="navIndex==index+1?'activite':''" @click="checkIndex(index+1)">{{item.name}}</view>
            </block>
        </view>
        <uni-popup ref="popups" type="center" :animation="true">
            <view class="" style="width: 400rpx;background-color: #fff;border-radius: 20rpx;padding:20rpx;border:1px solid #fc5656 ">
                <view class="wechatGroup-title">
                    签到二维码
                </view>
                <view class="wechatGroup-img">
                    <image :src="signdata.sign_in_qrcode" mode="" show-menu-by-longpress="true" mode="widthFix"></image>
                </view>
                <view class="tt_text_left">
                    <view class="tt_text_left_btn1" @click="signInCodeClose">
                        关闭
                    </view>
                    <view class="tt_text_left_btn2" @click="saveimg(signdata.sign_in_qrcode)">
                        保存
                    </view>
                </view>
            </view>
        </uni-popup>
        <Loading :loaded="loaded" :loading="loading"></Loading>
    </view>
</template>

<script>
    import Loading from '@/components/Loading';
    import {
        activityDetail,
        eventProgramList,
        eventGetAppointedFile,
        checkInDeskOrderList,
        eventApplicationSituation
    } from '@/api/community';
    export default {
        components: {
            Loading
        },
        props: {},
        data: function() {
            return {
                page: 1,
                limit: 20,
                loaded: false,
                loading: false,
                
                did: 0, // 活动id
                detail:{},
                list: [],
                appointId: 0, // 分享给用户的预约记录id
                activity_id: 0,
                navIndex:1,
                navlist: [{
                        name: '报名情况'
                    },
                    {
                        name: '签到台'
                    },
                    {
                        name: '面试预约'
                    },
                    {
                        name: '核销工具'
                    },
                ],
                enrollNavIndex:0,
                enrollNavList:[
                    {
                        name: '全部',
                    },
                    {
                        name: '待付款'
                    },
                    {
                        name: '待参加'
                    },
                ],
                enrolldata:{
                    activity_type:'',
                    pre_register:'',
                    surplus_pre_register:'',
                    confirm_enrollment:0,
                    surplus_confirm_enrollment:'',
                    pending_balance:'',
                },
                enrolldataList:[],
                signNavIndex:0,
                signNavList:[
                    {
                        name: '全部',
                        id:0
                    },
                    {
                        name: '签到订单',
                        id:2
                    },
                    {
                        name: '未签到订单',
                        id:1
                    },
                ],
                signdata:{
                    actual_attendance:0,
                    attendance:0,
                },
                signdataList:[]
            };
        },
        mounted: function() {},
        onShareAppMessage(res) {
            if (res.from === 'button') { // 来自页面内分享按钮
                // console.log(res.target)
            }
            return {
                title: '线上面试预约',
                imageUrl: this.detail.activityInfo.image,
                path: 'pages/ycommunity/shop/userSubscribe?appointId=' + this.appointId + '&did=' + this.activity_id + '&type=1'
            }
        },
        onLoad(options) {
            const {
                id
            } = options;
            this.did = id;
            // console.log('活动id', id)
            this.getEventApplicationSituation(0)
            this.getCheckInDeskOrderList(0)
            this.getEventProgramList(1);
        },
        onShow() {
            this.getEventProgramList(1);
        },
        methods: {
            signInCode() {
                this.$refs.popups.open()
            },
            signInCodeClose(){
                this.$refs.popups.close()
            },
            orderDetails(item) {
                this.goPages('/pages/ycommunity/order/detail?order_id=' + item.order_id)
            },
            // 签到台切换
            checkSingIndex(type){
                this.signNavIndex = type;
                this.page = 1;
                this.signdataList =[]
                this.getCheckInDeskOrderList(type)
            },
            signRefresh(){
                this.page = 1;
                this.signdataList =[]
                this.getCheckInDeskOrderList(this.signNavIndex)
                return this.$showToast('签到订单刷新成功');
            },
            // 报名切换
            checkEnrollIndex(index){
                this.enrollNavIndex = index;
                this.page = 1;
                this.enrolldataList =[]
                this.getEventApplicationSituation(this.enrollNavIndex)
            },
            enrollRefresh(){
                this.page = 1;
                this.enrolldataList =[]
                this.getEventApplicationSituation(this.enrollNavIndex)
                return this.$showToast('报名信息刷新成功');
            },
            // 底部总切换
            checkIndex(index){
              this.navIndex = index;
              this.page = 1;
              this.loaded = false;
              this.loading = false;
              if(this.navIndex == 1){
                  this.enrolldataList =[]
                  this.getEventApplicationSituation(this.enrollNavIndex)
              }
              if(this.navIndex == 2){
                  this.signdataList = []
                  this.getCheckInDeskOrderList(this.signNavIndex)
              }
              if(this.navIndex == 3){
                  this.list = [];
                  this.getEventProgramList(0);
              }
              if(this.navIndex == 4){
                  this.goPages('/pages/orderAdmin/OrderCancellation',true)
              }
            },
            addadmin(){
                // if(!this.list[0]){
                //     this.goPages('/pages/ycommunity/shop/adminEdit?id=' + this.did + '&addId=1',true)
                // }else {
                //     return this.$showToast('本次活动已添加面试计划，无法再次添加');
                // }
                this.goPages('/pages/ycommunity/shop/adminEdit?id=' + this.did + '&addId=1',true)
            },
            getEvaluateDetail(did, inviterId) {
                let from = '';
                // #ifndef MP-TOUTIAO
                from = 'routine';
                // #endif
                // #ifdef MP-TOUTIAO
                from = 'bytedance';
                // #endif
                activityDetail(did, from, inviterId)
                    .then(res => {
                        console.log('获取活动成功', res)
                        this.detail = res.data;
                    })
                    .catch(err => {
                        console.log('获取活动失败', err)
                        this.$navigator(-1);
                    });
            
                this.getComment();
            },
            adminedit(did,id) {
                if (did || id) {
                    this.goPages('/pages/ycommunity/shop/adminEdit?id=' + did + '&addId=2' + '&appointId=' + id, true)
                } else {
                    return this.$showToast('活动不存在');
                }
            },
            goAdminDetails(did,id) {
                if (did || id) {
                    this.goPages('/pages/ycommunity/shop/adminDetails?id=' + did + '&appointId=' + id, true)
                } else {
                    return this.$showToast('活动不存在');
                }
            },
            getEventProgramList(type) {
                this.loading = true;
                this.page = 1;
                this.list = [];
                let data = {
                    activity_id: this.did,
                    page: this.page,
                    limit: this.limit,
                }
                eventProgramList(data)
                    .then(res => {
                        this.list = this.list.concat(res.data)
                        this.page++;
                        this.loaded = res.data.length < this.limit;
                        this.loading = false;
                        console.log('获取面试预约计划列表成功', this.list)
                        if(type == 1){
                            // 仅首次进入获取详情
                            this.getEvaluateDetail(this.did,0)
                        }
                    })
                    .catch(err => {
                        // console.log('获取面试预约计划列表失败', err)
                    });
            },
            getEventApplicationSituation(type){
                // if (this.loading || this.loaded) return;
                this.loading = true;
                let data = {
                    type:type, // 0=全部  1=待付款 2=待参加
                    activity_id: this.did,
                    page: this.page,
                    limit: this.limit,
                }
                eventApplicationSituation(data)
                    .then(res => {
                        this.enrolldata = res.data;
                        this.enrolldataList = this.enrolldataList.concat(res.data.order_list)
                        this.page++;
                        this.loaded = res.data.order_list.length < this.limit;
                        this.loading = false;
                    })
                    .catch(err => {
                        // console.log('获取面试预约计划列表失败', err)
                        // this.$navigator(-1);
                    });
            },
            getCheckInDeskOrderList(type){
                this.loading = true;
                let data = {
                    type:type, // 0=全部  1=未签到订单 2=已签到订单
                    activity_id: this.did,
                    page: this.page,
                    limit: this.limit,
                }
                checkInDeskOrderList(data)
                    .then(res => {
                        this.signdata = res.data;
                        this.signdataList = this.signdataList.concat(res.data.order_list)
                        this.page++;
                        this.loaded = res.data.order_list.length < this.limit;
                        this.loading = false;
                    })
                    .catch(err => {
                        // console.log('获取面试预约计划列表失败', err)
                        // this.$navigator(-1);
                    });
            },
            getEventGetAppointedFile(id) {  
                if(id){
                    let data = {
                        id:id,
                    }
                    eventGetAppointedFile(data)
                        .then(res => {
                            // console.log('获取已约清单成功', res)
                            let urlx = res.data.reservation_list_file;
                            if(!urlx){
                                return this.$showToast('请检查下载地址');
                            }
                            uni.downloadFile({
                                header: {
                                    'Content-Type': "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=GBK",
                                },
                                url: urlx,
                                success(res) {
                                    console.log(res);
                                    var filePath = res.tempFilePath;
                                    uni.openDocument({
                                      filePath: filePath,
                                      fileType:'xlsx',
                                      showMenu: true,
                                    // fileType: "xls",
                                      success: function (res) {
                                        console.log('打开文档成功');
                                      }
                                    });
                                },
                                fail(res) {
                                    uni.showToast({
                                        icon: "none",
                                        title: res.errMsg,
                                    });
                                },
                            });
                        })
                        .catch(err => {
                            console.log('获取已约清单失败', err)
                            return this.$showToast('获取清单失败');
                            // this.$navigator(-1);
                        });
                }else {
                    return this.$showToast('活动不存在');
                }
            },
            saveimg(url) {
                let that = this;
                if(!url){
                    return this.$showToast('图片不存在');
                }
                uni.downloadFile({
                    url: url,
                    header: {
                        "content-type": "application/json",
                    },
                    success: (res) => {
                        console.log('res', res)
                        if (res.statusCode === 200) {
                            uni.saveImageToPhotosAlbum({
                                filePath: res.tempFilePath,
                                success(res) {
                                    that.$showToast('已保存相册')
                                    setTimeout(function() {
                                        that.signInCodeClose()
                                    }, 1500);
                                },
                                fail(res) {
                                    console.log(res);
                                    that.$showToast('无相册权限')
                                    setTimeout(function() {
                                        that.signInCodeClose()
                                    }, 1500);
                                }
                            });
                        }
                    }
                })
            },
            goPages(path, type, pathType) {
                if (type) {
                    this.$authNavigator(path);
                } else {
                    this.$navigator(path, pathType);
                }
            },
            sharepage(id, activity_id) {
                this.appointId = id;
                this.activity_id = activity_id
            }
        },
        onReachBottom() {
            // this.getOrderList();
            if(this.navIndex == 1){
                this.getEventApplicationSituation(this.enrollNavIndex)
            }
            if(this.navIndex == 2){
                this.getCheckInDeskOrderList(this.signNavIndex)
            }
            if(this.navIndex == 3){
                this.getEventProgramList(0);
            }
        }
    };
</script>
<style scoped lang="scss">
    .bottom-nav {
        position: fixed;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 140rpx;
        background-color: #fff;
        line-height: 100rpx;
        box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
        view {
            position: relative;
            float: left;
            text-align: center;
            width: 25%;
            color: #3e3e3e;
            font-weight: bold;
            
            &.activite {
                color: #3399FF;
            }
        }
    }
    .admin-box {
        width: 100%;
        padding: 40rpx 20rpx 140rpx;
        box-sizing: border-box;
        overflow: auto;
        .activityclass {
            width: 100%;
            padding: 0 14rpx;
            overflow: auto;
            margin-bottom: 20rpx;
        }
        .activityclass-item {
            width: 100%;
            padding: 0 14rpx;
            overflow: auto;
            margin-bottom: 20rpx;
            .activityclass-item-left {
                float: left;
                color: red;
            }
            .activityclass-item-right {
                float: right;
            }
            .activityclass-item-right-btn {
                float: right;
                width: 152rpx;
                height: 60rpx;
                line-height: 60rpx;
                text-align: center;
                font-size: 24rpx;
                font-family: PingFang SC, PingFang SC-Regular;
                font-weight: 400;
                text-align: center;
                border-radius: 10rpx;
                color: #50506f;
                border: 2rpx solid #50506f;
            }
        }
        .enroll {
            width: 100%;
            padding: 0 14rpx;
            overflow: auto;
            .enroll-title {
                width: 100%;
                overflow: auto;
            }
            .enroll-nav {
                width: 100%;
                overflow: auto;
                margin-bottom: 20rpx;
                
                .enroll-nav-left {
                    float: left;
                    width: 450rpx;
                    margin-top: 16rpx;
                    
                    .enroll-nav-left-item {
                        float: left;
                        text-align: center;
                        width: 150rpx;
                        &.activite {
                            color: #3399FF;
                        }
                    }
                }
                .enroll-nav-right {
                    float: right;
                    width: 152rpx;
                    height: 60rpx;
                    line-height: 60rpx;
                    text-align: center;
                    font-size: 24rpx;
                    font-family: PingFang SC, PingFang SC-Regular;
                    font-weight: 400;
                    text-align: center;
                    border-radius: 24rpx;
                    background: #eaeaea;
                    color: #50506f;
                }
            }
            .userbox {
                width: 100%;
                overflow: auto;
                .userbox-item {
                    width: 100%;
                    background: #eff5fc;
                    border-radius: 16rpx;
                    margin-bottom: 16rpx;
                    padding: 32rpx 24rpx 32rpx 36rpx;
                    box-sizing: border-box;
                    overflow: auto;
                    .userbox-item-right {
                        float: left;
                        width: 100%;
                        overflow: auto;
                        .top {
                            width: 100%;
                            overflow: auto;
                            margin-bottom: 16rpx;
                        
                            .top-left {
                                float: left;
                                width: 88rpx;
                                height: 88rpx;
                                margin-right: 50rpx;
                                border-radius: 50%;
                            }
                        
                            .top-right {
                                float: left;
                                font-size: 22rpx;
                                font-family: PingFang SC, PingFang SC-Regular;
                                font-weight: 400;
                                text-align: center;
                                color: #646464;
                                margin-top: 28rpx;
                                .top-right-id {
                                    margin-left: 12rpx;
                                }
                            }
                        }
                        .item {
                            width: 100%;
                            font-size: 22rpx;
                            font-family: PingFang SC, PingFang SC-Regular;
                            font-weight: 400;
                            text-align: left;
                            color: #6bb4ff;
                            padding-left: 10rpx;
                            box-sizing: border-box;
                            margin-bottom: 16rpx;
                            overflow: auto;
                
                            .item-title {
                                float: left;
                                width: 128rpx;
                            }
                
                            .item-center {
                                float: left;
                            }
                        }
                
                        .bot {
                            width: 100%;
                            font-size: 22rpx;
                            font-family: PingFang SC, PingFang SC-Regular;
                            font-weight: 400;
                            text-align: left;
                            color: #6bb4ff;
                            padding-left: 10rpx;
                            box-sizing: border-box;
                            margin-bottom: 16rpx;
                            overflow: auto;
                
                            .bot-left {
                                float: left;
                
                                .bot-left-title {
                                    float: left;
                                    width: 128rpx;
                                }
                            }
                
                            .bot-right {
                                float: right;
                                width: 98rpx;
                                height: 38rpx;
                                line-height: 38rpx;
                                text-align: center;
                                background: rgba(107, 180, 255, 0.20);
                                border: 2rpx solid #6bb4ff;
                                border-radius: 22rpx;
                                font-size: 24rpx;
                                font-family: PingFang SC, PingFang SC-Medium;
                                font-weight: 500;
                                color: #3399ff;
                            }
                        }
                    }
                }
            }
            
        }
        .order-title {
            width: 100%;
            overflow: auto;
            padding: 0 14rpx;
            box-sizing: border-box;
            margin-bottom: 24rpx;
        
            .left {
                float: left;
                font-size: 32rpx;
                font-family: PingFang SC, PingFang SC-Semibold;
                font-weight: 600;
                text-align: left;
                color: #646464;
            }
        
            .right {
                float: right;
                width: 152rpx;
                height: 60rpx;
                line-height: 60rpx;
                text-align: center;
                background: #50506f;
                border-radius: 24rpx;
                font-size: 24rpx;
                font-family: PingFang SC, PingFang SC-Regular;
                font-weight: 400;
                text-align: center;
                color: #ffffff;
            }
        
        }
        .order-box {
            width: 710rpx;
            min-height: 322rpx;
            background: #ffffff;
            border-radius: 30rpx;
            box-shadow: 0 0 40rpx 0 rgba(107, 127, 153, 0.20);
            padding: 36rpx 40rpx 48rpx;
            box-sizing: border-box;
            margin-bottom: 24rpx;

            .order-time {
                width: 100%;
                font-size: 24rpx;
                font-family: PingFang SC, PingFang SC-Regular;
                font-weight: 400;
                text-align: left;
                color: #333333;
                overflow: auto;
                margin-bottom: 10rpx;

                .time-left {
                    float: left;
                    margin-right: 32rpx;
                }

                .time-right {
                    float: left;
                }
            }

            .order-btn {
                width: 100%;
                margin-top: 22rpx;
                padding-top: 32rpx;
                border-top: 2rpx solid #d2d2d2;
                box-sizing: border-box;

                .btn {

                    float: left;
                    height: 60rpx;
                    line-height: 60rpx;
                    text-align: center;
                    font-size: 24rpx;
                    font-family: PingFang SC, PingFang SC-Regular;
                    font-weight: 400;
                    text-align: center;
                    color: #50506f;
                    background: #eaeaea;
                    border-radius: 24rpx;
                    margin-right: 16rpx;
                    padding: 0 30rpx;
                }

                .btn:last-child {
                    margin-right: 0;
                }

            }
        }
    }
    .tt_text_left {
        width: 100%;
        height: auto;
        overflow: hidden;
        padding: 0 20rpx;
        margin-top: 50rpx;
    }
    
    .tt_text_left_btn1 {
        float: left;
        width: 150rpx;
        height: 70rpx;
        margin: 0 auto;
        border: 1rpx solid #e6e6e6;
        border-radius: 10rpx;
        text-align: center;
        line-height: 70rpx;
    }
    
    .tt_text_left_btn2 {
        float: right;
        width: 150rpx;
        height: 70rpx;
        background-color: #e93323;
        color: #fff;
        font-size: 28rpx;
        border-radius: 10rpx;
        text-align: center;
        line-height: 70rpx;
    }
    .wechatGroup-title {
        width: 100%;
        text-align: center;
        padding: 40rpx 0;
    }
    .wechatGroup-img {
    
        image {
            display: block;
            width: 300rpx;
            height: 300rpx;
            margin: 0 auto;
        }
    }
    
</style>
