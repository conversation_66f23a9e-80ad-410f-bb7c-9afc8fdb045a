<template>
    <view class="admin-box">
        <view class="agentAppoint">
            <view class="agentAppoint-title">
                代人预约
            </view>
            <view class="agentAppoint-tips">
                *注意事项：
            </view>
            <view class="agentAppoint-desc">
                活动+1报名的用户可以代替+1预约面试时间，请与您+1沟通好后确认预约时间
            </view>
            <view class="agentAppoint-input">
                <view class="title">
                    面试人姓名
                </view>
                <input class="input" type="text" placeholder="请输入姓名" v-model="agentName"
                    placeholder-style="font-size: 16px; color:#d2d2d2">
            </view>
            <view class="agentAppoint-input">
                <view class="title">
                    面试人联系电话
                </view>
                <input class="input" type="text" placeholder="请输入联系电话" v-model.number="agentPhone"
                    placeholder-style="font-size: 16px; color:#d2d2d2" maxlength="11">
            </view>
        </view>
        <view class="appointments-but" @click="confirm">
            确认
        </view>
        <view class="appointments-but cancel" @click="backPage">
            取消
        </view>
    </view>
</template>

<script>
    import {
        RegPhone,
        RegFixedPhone,
        RegPhoneAndFixed,
        isWeixin
    } from '@/utils/validate';
    import {
        eventAdd,
        eventProgramList,
        eventOrderList,
    } from '@/api/community';

    export default {
        props: {},
        data: function() {
            return {
                // did:4, // 活动id
                agentName: '',
                agentPhone: '',
                appointId:0, // 分享给用户的预约记录id
                did:0
            };
        },
        mounted: function() {},

        onLoad(options) {
            console.log('options', options)
            const {
                appointId,
                did
            } = options;
            this.appointId = appointId;
            this.did = did;
        },
        onShow() {

        },
        methods: {
            confirm() {
                if (!this.agentName) {
                    return this.$showToast('请输入面试者姓名');
                }
                if (!this.agentPhone) {
                    return this.$showToast('请输入面试者电话');
                }
                if (!RegPhoneAndFixed(this.agentPhone)) {
                    return this.$showToast('请填写正确的手机号');
                }
                let obj = {
                    agentName: this.agentName,
                    agentPhone: this.agentPhone,
                };
                let agentData = encodeURIComponent(JSON.stringify(obj))
                this.goPages('/pages/ycommunity/shop/userSubscribe?did=' + this.did + '&appointId=' + this.appointId + '&type=2' + '&agentData=' + agentData + '&isgetlist=1',false,'redirectTo')
            },
            backPage() {
                this.$navigator(-1);
            },
            goPages(path, type, pathType) {
                if (type) {
                    this.$authNavigator(path);
                } else {
                    this.$navigator(path, pathType);
                }
            },
        }
    };
</script>
<style scoped lang="scss">
    .admin-box {
        width: 100%;
        padding: 40rpx 20rpx;
        box-sizing: border-box;
        overflow: auto;

        .agentAppoint {
            width: 710rpx;
            min-height: 624rpx;
            background: #ffffff;
            border-radius: 30rpx;
            box-shadow: 0 0 40rpx 0 rgba(107, 127, 153, 0.20);
            margin: 0 auto 32rpx;
            padding: 36rpx 40rpx 80rpx;
            box-sizing: border-box;

            .agentAppoint-title {
                width: 100%;
                font-size: 32rpx;
                font-family: PingFang SC, PingFang SC-Semibold;
                font-weight: 600;
                text-align: left;
                color: #646464;
                margin-bottom: 20rpx;
            }

            .agentAppoint-tips {
                width: 100%;
                font-size: 26rpx;
                font-family: PingFang SC, PingFang SC-Bold;
                font-weight: 700;
                text-align: left;
                color: #646464;
                margin-bottom: 16rpx;
            }

            .agentAppoint-desc {
                width: 100%;
                font-size: 22rpx;
                font-family: PingFang SC, PingFang SC-Medium;
                font-weight: 500;
                text-align: left;
                margin-bottom: 20rpx;
            }

            .agentAppoint-input {
                width: 100%;
                padding: 22rpx 0 24rpx;
                border-bottom: 2rpx solid #d2d2d2;
                box-sizing: border-box;

                .title {
                    width: 100%;
                    font-size: 24rpx;
                    font-family: PingFang SC, PingFang SC-Regular;
                    font-weight: 400;
                    text-align: left;
                    color: #666666;
                    margin-bottom: 14rpx;
                }

                .input {
                    width: 100%;
                    font-size: 24rpx;
                    font-family: PingFang SC, PingFang SC-Regular;
                    font-weight: 400;
                    text-align: left;
                    color: #666666;
                    font-size: 32rpx;
                    font-weight: 400;
                    text-align: left;
                }
            }
        }

        .appointments-but {
            width: 670rpx;
            height: 100rpx;
            line-height: 100rpx;
            background: #50506f;
            border-radius: 40rpx;
            text-align: center;
            font-size: 24rpx;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: center;
            color: #ffffff;
            margin: 0 auto 20rpx;

            &.cancel {
                color: #50506f;
                background: #e4e4e4;
            }
        }


    }
</style>
