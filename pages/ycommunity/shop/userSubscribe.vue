<template>
    <view class="admin-box">
        <block v-if="isStatus">
            <view class="activity-box">
                <view class="activity-box-title">
                    线上面试预约
                </view>
                <view class="activity-box-tips">
                    *注意事项
                </view>
                <view class="activity-box-desc">
                    {{programDetail.content}}
                </view>
            </view>
            <view class="activity-tips">
                *操作说明：先选定日期，然后选择可面试时间段
            </view>
            <view class="select-date">
                <view class="select-date-title">
                    选择日期
                </view>
                <view class="select-date-scroll">
                    <scroll-view class="scroll-view_H" scroll-x="true">
                        <block v-for="(item, index) in datetimeArr" :key="index">
                            <view class="scroll-view-item_H" @click="selecdate(item,index)"
                                :class="[item.label == datetext ?'active':'',item.is_select == 1?'unactive':'']">
                                <view class="week">
                                    {{item.week}}
                                </view>
                                <view class="date">
                                    {{item.label}}
                                </view>
                                <view class="ischoose">
                                    {{item.is_select == 0?'可预约':'无可预约'}}
                                </view>
                            </view>
                        </block>
                    </scroll-view>
                </view>
            </view>
            <view class="select-time">
                <view class="select-time-title">
                    上午时间
                </view>
                <view class="select-date-box" v-if="timeArray.length > 0">
                    <block v-for="(items, indexs) in timeArray" :key="indexs">
                        <view class="select-date-box-item" v-if="items.period.indexOf('下午')"
                            @click="selectime(items,indexs)"
                            :class="[items.period == timetext ?'active':'',items.is_select == 1?'unactive':'']">
                            <view class="time">
                                {{items.period.slice(2)}}
                            </view>
                            <view class="reservable">
                                {{items.is_select == 1?'无可预约':'可预约'}}
                            </view>
                        </view>
                    </block>
                </view>
                <view class="select-date-box" v-else>
                    请选择日期
                </view>
            </view>
            <view class="select-time">
                <view class="select-time-title">
                    下午时间
                </view>
                <view class="select-date-box" v-if="timeArray.length > 0">
                    <block v-for="(items, indexs) in timeArray" :key="indexs">
                        <view class="select-date-box-item" v-if="items.period.indexOf('上午')"
                            @click="selectime(items,indexs)"
                            :class="[items.period == timetext ?'active':'',items.is_select == 1?'unactive':'']">
                            <view class="time">
                                {{items.period.slice(2)}}
                            </view>
                            <view class="reservable">
                                {{items.is_select == 1?'无可预约':'可预约'}}
                            </view>
                        </view>
                    </block>
                </view>
                <view class="select-date-box" v-else>
                    请选择日期
                </view>
            </view>
            <view class="appointments-txt">
                预约时间：{{datetext}} {{timetext}}
            </view>
            <view class="appointments-but" @click="subscribe">
                确认预约
            </view>
        </block>
        <view class="" v-else>

        </view>
        <uni-popup ref="AppnintPopups" type="center" :animation="true" :maskClick="false">
            <view class="balance-box">
                <view class="balance-box-close">
                    <image class="close-img" src="../../../static/images/yuanshi/admin-close.png" mode=""
                        @click="closeAppnintPopups"></image>
                </view>
                <view class="balance-box-title">
                    <view class="">
                        只有指定用户才能使用预约工具
                    </view>
                    <view class="">
                        请查询本次活动官方公示名单或咨询客服工作人员
                    </view>
                </view>
                <view class="balance-box-btn">
                    <view class="balance-box-btn-center" @click="goPages('/pages/ycommunity/shop/detail?id=' + did)">
                        知道了
                    </view>
                </view>
            </view>
        </uni-popup>
        <uni-popup ref="EndPopups" type="center" :animation="true" :maskClick="false">
            <view class="balance-box">
                <view class="balance-box-close">
                    <image class="close-img" src="../../../static/images/yuanshi/admin-close.png" mode=""
                        @click="closeAppnintPopups"></image>
                </view>
                <view class="balance-box-title">
                    <view class="">
                        本次活动面试预约已结束，谢谢使用！
                    </view>
                </view>
                <view class="balance-box-btn">
                    <view class="balance-box-btn-center" @click="goPages('/pages/ycommunity/shop/detail?id=' + did)">
                        知道了
                    </view>
                </view>
            </view>
        </uni-popup>
        <x-authorize @login="updateData"></x-authorize>
    </view>
</template>

<script>
    import {
        eventAdd,
        eventProgramList,
        eventOrderList,
        userInterviewsAdd, //
        userInterviewsmyList,
        userProgramDetails,
        userInterviewsEdit, // 用户修改预约
        userIsConditions // 用户是否有条件预约
    } from '@/api/community';
    export default {
        props: {},
        data: function() {
            return {
                dateindex:-1,
                programDetail: {},
                isStatus: 0, // 是否指定用户
                list: [],
                datetext: '',
                timetext: '',
                did: 0, // 活动id
                type: 0, //1本人 2代人
                userappId: 0, // 用户修改所用的预约成功记录id
                isAppointdata: true, // 活动是否过期
                activityEnd: false, // 活动预约是否结束
                appointId: 0, // 用户使用的预约记录id
                timeArray: [],
                agentData: {}, // 代人预约信息
                datetimeArr: [],
                isgetlist: 0 // 是否再获取面试记录
            };
        },
        mounted: function() {},

        onLoad(options) {
            console.log('options', options)
            const {
                appointId,
                did,
                type,
                agentData,
                userappId,
                isgetlist
            } = options;
            this.appointId = appointId;
            this.userappId = userappId;
            this.did = did;
            this.type = Number(type);
            this.isgetlist = isgetlist
            if (agentData) {
                this.agentData = JSON.parse(decodeURIComponent(agentData));
            }
            if (!this.isgetlist) {
                this.getUserInterviewsmyList() // 获取用户面试记录
            }
        },
        onShow() {
            this.getUserIsConditions()
        },
        methods: {
            updateData() {
                if (!this.isgetlist) {
                    this.getUserInterviewsmyList() // 获取用户面试记录
                }
                this.getUserIsConditions()
            },
            subscribe() {
                if (!this.datetext) {
                    return this.$showToast('请选择预约日期');
                }
                if (!this.timetext) {
                    return this.$showToast('请选择预约时间');
                }
                let id;
                if (this.userappId) {
                    id = this.userappId
                } else {
                    id = this.appointId
                }
                let data = {
                    id: id, // 添加-分享给用户的预约记录id  修改-用户预约成功的记录表id
                    type: this.type, //1本人  2代人
                    date: this.datetext, //日期
                    period: this.timetext, // 时间段
                }
                if (this.type == 2) {
                    // 代人参数
                    data.mark = this.agentData.agentName + ',' + this.agentData.agentPhone; // 面试人姓名
                }
                let req = null;
                if (this.userappId) {
                    req = userInterviewsEdit(data)
                } else {
                    req = userInterviewsAdd(data)
                }
                req.then(res => {
                        console.log('用户面试预约', res)
                        if (this.userappId) {
                            this.goPages('/pages/ycommunity/shop/userSuccess?userappId=' + this.userappId +
                                '&appointId=' + this.appointId + '&did=' + this.did,false,'redirectTo')
                        } else {
                            this.goPages('/pages/ycommunity/shop/userSuccess?userappId=' + res.data[0].id +
                                '&appointId=' + this.appointId + '&did=' + this.did,false,'redirectTo')
                        }
                    })
                    .catch(err => {
                        console.log('用户面试预约失败', err)
                        if(err == '添加失败，此时间段已被预约!'){
                            this.getUserProgramDetails(1) //获取面试预约计划时间段
                        }
                        if(err == '重复面试'){
                            this.getUserInterviewsmyList() // 获取用户面试记录
                        }
                        // this.$navigator(-1);
                    });
            },
            getUserIsConditions() {
                let data = {
                    id: this.appointId
                }
                userIsConditions(data)
                    .then(res => {
                        console.log('用户是否能使用', res)
                        this.isStatus = res.data.status;
                        if (!this.isStatus) {
                            return this.$refs.AppnintPopups.open() // 是否指定用户来源
                        }
                        this.getUserProgramDetails() //获取面试预约计划时间段
                    })
                    .catch(err => {
                        console.log('用户是否能使用', err)
                        // this.$navigator(-1);
                    });
            },
            // 获取面试预约计划时间段
            getUserProgramDetails(type) {
                let data = {
                    id: this.appointId
                }
                userProgramDetails(data)
                    .then(res => {
                        this.programDetail = res.data;
                        console.log('获取用户面试预约计划详情', this.programDetail)
                        var nowtime = new Date().getTime();
                        var endtime = (new Date(this.programDetail.end_time)).valueOf() // 获取活动结束时间戳 
                        if (nowtime >= endtime) {
                            return this.$refs.EndPopups.open() // 活动预约结束弹窗
                        }
                        var datetimeArr = [];
                        var nowDate = this.getNowDate(); //获取当前日期
                        var nowtime = (new Date()).getHours()
                        for (let i = 0; i < this.programDetail.time_period.length; i++) {
                            var datetimeobj = {
                                label: '',
                                is_select: '',
                                value: []
                            }
                            var is_select;
                            if (this.compareDate(nowDate, this.programDetail.time_period[i][0].date) < 0) {
                                is_select = 1;
                            }else {
                                is_select = 1
                                var datetimeArray = [];
                                for (let j = 0; j < this.programDetail.time_period[i].length; j++) {
                                    let periodnum = 0;
                                    if(this.compareDate(nowDate,this.programDetail.time_period[i][0].date) == 0){
                                        periodnum = Number(this.programDetail.time_period[i][j].period.replace(/[^\d]/g,' '));
                                        if(1<= periodnum && periodnum <= 6){
                                            periodnum += 12
                                        }
                                        if( periodnum <= nowtime + 3){
                                            this.programDetail.time_period[i][j].is_select = 1
                                        }
                                    }
                                    if (this.programDetail.time_period[i][j].is_select == 0) {
                                        is_select = 0
                                    }
                                    datetimeArray.push(this.programDetail.time_period[i][j]);
                                }
                            }
                            datetimeobj.label = this.programDetail.time_period[i][0].date;
                            datetimeobj.is_select = is_select;
                            datetimeobj.value = this.shallowCopy(datetimeArray);
                            datetimeArr.push(datetimeobj);
                        }
                        this.datetimeArr = datetimeArr;
                        for (let i = 0; i < this.datetimeArr.length; i++) {
                            this.datetimeArr[i].week = '周' + this.getweekday(this.datetimeArr[i].label)
                        }
                        if(type == 1){
                            // 当预定时间则刷当日时间段
                            this.selecdate(this.datetimeArr[this.dateindex],this.dateindex)
                        }
                        console.log('面试时间详情-----------', this.datetimeArr)
                    })
                    .catch(err => {
                        console.log('获取用户面试预约计划详情失败', err)
                        // this.$navigator(-1);
                    });
            },
            compareDate(startDate, endDate) {
                if (this.checkDate(startDate) && this.checkDate(endDate)) {
                    var sdate = new Date(startDate.replace(/\-/g, "\/"));
                    var edate = new Date(endDate.replace(/\-/g, "\/"));
                    return edate - sdate;
                } else {
                    console.log('日期不合法')
                }
            },
            checkDate(strDate) {
                if (strDate.length > 0) {
                    var reg = /^(\d{2,4})-(\d{1,2})-(\d{1,2})$/g;
                    if (!reg.test(strDate)) {
                        return false;
                    }
                }
                return true;
            },
            
            getNowDate(){
                var date = new Date();
                  var year = date.getFullYear() // 年
                  var month = date.getMonth() + 1; // 月
                  var day = date.getDate(); // 日
                  if (month >= 1 && month <= 9) {
                    month = "0" + month;
                  }
                  if (day >= 0 && day <= 9) {
                    day = "0" + day;
                  }
                  return year + "-" + month + "-" + day;
            },
            getweekday(date) {
                var weekArray = new Array("日", "一", "二", "三", "四", "五", "六");
                var week = weekArray[new Date(date).getDay()];
                return week;
            },
            getUserInterviewsmyList() {
                let data = {
                    type: 1,
                    schedule_id:this.appointId,
                    limit: '',
                    page: ''
                }
                userInterviewsmyList(data)
                    .then(res => {
                        console.log('获取用户面试预约记录列表', res)
                        let list = res.data;
                        let id = 0;
                        for (let i = 0; i < list.length; i++) {
                            if (list[i].status != 7) {
                                id = list[i].id
                            }
                        }
                        if (id) {
                            this.goPages('/pages/ycommunity/shop/userSuccess?userappId=' + id + '&appointId=' + this
                                .appointId + '&did=' + this.did,false,'redirectTo')
                        }
                    })
                    .catch(err => {
                        // console.log('获取用户面试预约记录失败', err)
                        // this.$navigator(-1);
                    });
            },

            shallowCopy(arr) {
                var newArr = Array.isArray(arr) ? [] : {}
                for (let key in arr) {
                    newArr[key] = arr[key]
                }
                return newArr
            },
            getEveryDayDateByBetweenDate(start_date, end_date) {
                var startTime = this.getDate(start_date);
                var endTime = this.getDate(end_date);
                var dateArr = [];
                while ((endTime.getTime() - startTime.getTime()) > 0) {
                    var year = startTime.getFullYear();
                    var month = (startTime.getMonth() + 1).toString().length === 1 ? "0" + (parseInt(startTime
                        .getMonth().toString(), 10) + 1) : (startTime.getMonth() + 1);
                    var day = startTime.getDate().toString().length === 1 ? "0" + startTime.getDate() : startTime
                        .getDate();
                    dateArr.push(year + "-" + month + "-" + day);
                    startTime.setDate(startTime.getDate() + 1);

                }
                dateArr.push(end_date)
                return dateArr;
            },

            duibi(a, b, flag) {
                var arr = a.split("-");
                var starttime = new Date(arr[0], arr[1], arr[2]);
                var starttimes = starttime.getTime();
                var arrs = b.split("-");
                var endTime = new Date(arrs[0], arrs[1], arrs[2]);
                var endTimes = endTime.getTime();
                // 进行日期比较
                if (endTimes >= starttimes) {
                    flag = true;
                    return flag;
                } else {
                    flag = false;
                    return flag;
                }
            },
            getEveryDayDateByBetweenDate(start_date, end_date) {
                var startTime = this.getDate(start_date);
                var endTime = this.getDate(end_date);
                var dateArr = [];
                while ((endTime.getTime() - startTime.getTime()) > 0) {
                    var year = startTime.getFullYear();
                    var month = (startTime.getMonth() + 1).toString().length === 1 ? "0" + (parseInt(startTime
                        .getMonth().toString(), 10) + 1) : (startTime.getMonth() + 1);
                    var day = startTime.getDate().toString().length === 1 ? "0" + startTime.getDate() : startTime
                        .getDate();
                    dateArr.push(year + "-" + month + "-" + day);
                    startTime.setDate(startTime.getDate() + 1);

                }
                dateArr.push(end_date)
                return dateArr;
            },
            getDate(datestr) {
                var temp = datestr.split("-");
                if (temp[1] === '01') {
                    temp[0] = parseInt(temp[0], 10) - 1;
                    temp[1] = '12';
                } else {
                    temp[1] = parseInt(temp[1], 10) - 1;
                }
                var date = new Date(temp[0], temp[1], temp[2]);
                return date;
            },
            goPages(path, type, pathType) {
                if (type) {
                    this.$authNavigator(path);
                } else {
                    this.$navigator(path, pathType);
                }
            },
            closeAppnintPopups() {
                this.goPages('/pages/ycommunity/shop/detail?id=' + this.did)
                this.$refs.AppnintPopups.close()
            },
            goPages(path, type, pathType) {
                if (type) {
                    this.$authNavigator(path);
                } else {
                    this.$navigator(path, pathType);
                }
            },
            selecdate(item, index) {
                console.log(item,index)
                this.dateindex = index;
                if (item.is_select == 1) {
                    return this.$showToast('当前日期不可预约');
                }
                this.timeArray = this.shallowCopy(item.value);
                this.datetext = item.label
                this.timetext = '';
            },
            selectime(item, index) {
                if (item.is_select == 1) {
                    return this.$showToast('当前时间不可预约');
                }
                this.timetext = item.period;
            },
            shallowCopy(arr) {
                var newArr = Array.isArray(arr) ? [] : {}
                for (let key in arr) {
                    newArr[key] = arr[key]
                }
                return newArr
            },
        }
    };
</script>
<style scoped lang="scss">
    .admin-box {
        width: 100%;
        padding: 60rpx 40rpx;
        box-sizing: border-box;
        overflow: auto;


        .activity-box {
            width: 100%;
            min-height: 300rpx;
            background: #ffffff;
            border-radius: 30rpx;
            box-shadow: 0 0 40rpx 0 rgba(107, 127, 153, 0.20);
            padding: 36rpx 40rpx 44rpx;
            box-sizing: border-box;
            margin-bottom: 48rpx;

            .activity-box-title {
                width: 100%;
                font-size: 32rpx;
                font-family: PingFang SC, PingFang SC-Semibold;
                font-weight: 600;
                text-align: left;
                color: #646464;
                margin-bottom: 20rpx;
            }

            .activity-box-tips {
                width: 100%;
                font-size: 26rpx;
                font-family: PingFang SC, PingFang SC-Bold;
                font-weight: 700;
                text-align: left;
                color: #646464;
                margin-bottom: 16rpx;
            }

            .activity-box-desc {
                width: 100%;
                font-size: 22rpx;
                font-family: PingFang SC, PingFang SC-Medium;
                font-weight: 500;
                text-align: left;
                color: #646464;
                padding-bottom: 32rpx;
                border-bottom: 2rpx solid #d2d2d2;
            }

        }

        .activity-tips {
            width: 100%;
            font-size: 22rpx;
            font-family: PingFang SC, PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            color: #3399ff;
            padding: 0 24rpx;
            box-sizing: border-box;
            margin-bottom: 32rpx;
        }

        .select-date {
            width: 100%;
            overflow: auto;

            .select-date-title {
                width: 100%;
                font-size: 32rpx;
                font-family: PingFang SC, PingFang SC-Semibold;
                font-weight: 600;
                text-align: left;
                color: #646464;
                padding: 0 24rpx;
                box-sizing: border-box;
                margin-bottom: 32rpx;
            }

            .select-date-scroll {
                width: 100%;
                padding: 0 10rpx;

                .scroll-view_H {
                    white-space: nowrap;
                    width: 100%;

                    .scroll-view-item_H {
                        display: inline-block;
                        width: 170rpx;
                        height: 170rpx;
                        font-size: 22rpx;
                        font-family: PingFang SC, PingFang SC-Regular;
                        font-weight: 400;
                        text-align: left;
                        color: #646464;
                        background: #ffffff;
                        border: 2rpx solid #e4e4e4;
                        border-radius: 16rpx;
                        margin-right: 18rpx;
                        text-align: center;
                        padding-top: 34rpx;
                        box-sizing: border-box;

                        &.active {
                            background: #d4e8fd;
                            border: 2rpx solid #6bb4ff;
                            color: #3399ff;
                        }

                        &.unactive {
                            background: #e4e4e4;
                            border: 0;
                            color: #b3b3b3;
                        }
                    }

                }
            }
        }

        .select-time {
            width: 100%;
            overflow: auto;

            .select-time-title {
                font-size: 32rpx;
                font-family: PingFang SC, PingFang SC-Semibold;
                font-weight: 600;
                text-align: left;
                color: #646464;
                margin-top: 48rpx;
                margin-bottom: 32rpx;
            }

            .select-date-box {
                width: 100%;

                .select-date-box-item {
                    float: left;
                    width: 92rpx;
                    height: 170rpx;
                    font-size: 22rpx;
                    font-family: PingFang SC, PingFang SC-Regular;
                    font-weight: 400;
                    text-align: left;
                    color: #646464;
                    background: #ffffff;
                    border: 2rpx solid #e4e4e4;
                    border-radius: 16rpx;
                    padding: 35rpx 10rpx 0;
                    box-sizing: border-box;
                    text-align: center;
                    margin-right: 18rpx;

                    &.active {
                        background: #d4e8fd;
                        border: 2rpx solid #6bb4ff;
                        color: #3399ff;
                    }

                    &.unactive {
                        background: #e4e4e4;
                        border: 0;
                        color: #b3b3b3;
                    }

                    .time {
                        margin-bottom: 10rpx;
                    }
                }
            }
        }

        .appointments-txt {
            width: 670rpx;
            height: 80rpx;
            line-height: 80rpx;
            background: rgba(255, 255, 255, 0.00);
            border: 2rpx solid #50506f;
            border-radius: 42rpx;
            font-size: 28rpx;
            font-family: PingFang SC, PingFang SC-Bold;
            font-weight: 700;
            text-align: center;
            color: #50506f;
            margin: 72rpx auto 20rpx;
        }

        .appointments-but {
            width: 670rpx;
            height: 100rpx;
            line-height: 100rpx;
            background: #50506f;
            border-radius: 40rpx;
            text-align: center;
            font-size: 24rpx;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: center;
            color: #ffffff;
            margin: 0 auto;
        }

        .balance-box {
            width: 600rpx;
            background-color: #fff;
            border-radius: 30rpx;
            padding: 12rpx 12rpx 48rpx;
            box-sizing: border-box;
            width: 622rpx;
            height: 344rpx;
            border: 4rpx solid #50506f;
        }

        .balance-box-close {
            width: 100%;
            overflow: auto;

            .close-img {
                float: right;
                width: 56rpx;
                height: 56rpx;
            }
        }

        .balance-box-title {
            width: 100%;
            text-align: center;
            font-size: 28rpx;
            font-family: PingFang SC, PingFang SC-Bold;
            font-weight: 700;
            text-align: center;
            color: #50506f;
            padding: 22rpx 30rpx 0;
            box-sizing: border-box;
            // view {
            //     margin-bottom: 20rpx;
            // }
        }

        .balance-box-btn {
            width: 100%;
            margin-top: 20rpx;
            overflow: auto;

            .balance-box-btn-center {
                width: 248rpx;
                height: 64rpx;
                line-height: 64rpx;
                text-align: center;
                background: #50506f;
                border-radius: 32rpx;
                font-size: 24rpx;
                font-family: PingFang SC, PingFang SC-Regular;
                font-weight: 400;
                text-align: center;
                color: #ffffff;
                margin: 0 auto;
            }
        }

    }
</style>
