<template>
    <view class="admin-box">
        <view class="admin-box-top">
            <view class="title">
                制作面试计划
            </view>
            <view class="desc">
                <view class="desc-text">
                    通过初选以后公示初选名单-群通知入选用户面试-面试预约工具流程：勾选报名订单以确认面试人员，指定面试时间段，生成面试计划，微信分享小程序给被面试人,被面试人通过分享入口进入预约工具页（只有选定名单中的用户可以操作预约工具)，面试日期更改（用户需提前一天在小程序中更改，否则需要人工沟通)一工作人员在面试时间前一天通过报名联系人电话确认面试时间和面试方式，采用“腾讯会议”或者微信-面试完成，用户等待面试结果，面试通过的用户会在小程序“我的活动”>“待付款”>点我的报名订单>”订单详情页”中”报名信息”一栏显示报名状态为”报名缴费”-全部面试完成后，公示报名通过名单
                </view>
            </view>
            <textarea class="textarea" v-model="content" placeholder="请输入活动注意事项" maxlength="200"></textarea>
            <view class="add" @click="addEventAdd">
                确认提交
            </view>
        </view>
        <view class="selec-time">
            <view class="selec-time-title">
                选择日期
            </view>
            <view class="selec-time-content" @click="show = true">
                <view class="startDate">
                    {{startDate?startDate:'选择开始时间'}}
                </view>
                <text class="divider"> ~ </text>
                <view class="endDate">
                    {{endDate?endDate:'选择结束时间'}}
                </view>
                <view class="dividers"></view>
                <image class="calendar-img" src="../../../static/images/yuanshi/calendar.png" mode="">
                </image>
            </view>
            <view class="selec-time-add" @click="generate(19,60)">
                生成
            </view>
            <block v-for="(item,index) in datetimeArr" :key="index">
                <block v-for="(items,indexs) in item.value" :key="indexs">
                    <view class="time-box-item" :class="items.is_select == 1?'active':''">
                        <view class="title">
                            {{item.label}} {{items.value}}
                        </view>
                        <view class="btn" @click="deltime(index,indexs,items)">
                            删除
                        </view>
                    </view>
                </block>
            </block>
        </view>

        <view class="user-box">
            <view class="user-box-title">
                勾选确认预约名单
            </view>
            <view class="user-box-btn">
                <view class="left" @click="selecAll">
                    全部{{selecallStauts?'选择':'取消'}}
                </view>
                <!-- <view class="left right" @click="refresh">
                    刷新
                </view> -->
            </view>
            <block v-for="(item, index) in userlist" :key="index">
                <view class="userbox-item">
                    <view class="userbox-item-left" @click="checkboxChange(index, item)">
                        <image class="img" src="../../../static/images/yuanshi/check.png" v-if="item.is_reservation_list" mode="">
                        </image>
                        <image class="img" src="../../../static/images/yuanshi/uncheck.png" v-else mode=""></image>
                    </view>
                    <view class="userbox-item-right">
                        <view class="top">
                            <image class="top-left" :src="item.avatar" mode=""></image>
                            <view class="top-right">
                                {{item.nickname}}
                            </view>
                        </view>
                        <view class="item">
                            <text class="item-title">
                                单号
                            </text>
                            <text class="item-center">{{item.order_id}}</text>
                        </view>
                        <view class="item">
                            <text class="item-title">
                                联系人
                            </text>
                            <text class="item-center">{{item.real_name}}</text>
                        </view>
                        <view class="item">
                            <text class="item-title">
                                联系电话
                            </text>
                            <text class="item-center">{{item.user_phone}}</text>
                        </view>
                        <view class="item">
                            <text class="item-title">
                                数量
                            </text>
                            <text class="item-center">X {{item.total_num}}</text>
                        </view>
                        <view class="item">
                            <text class="item-title">
                                下单时间
                            </text>
                            <text class="item-center">{{item.add_time}}</text>
                        </view>
                        <view class="bot">
                            <view class="bot-left">
                                <text class="bot-left-title">订单状态</text>
                                <text v-if="item.order_type == 1 && item.status == 0">待付款</text>
                                <text v-if="item.order_type == 2 && item.status == 0">待付尾款</text>
                                <text v-if="item.status == 1">已完成</text>
                                <text v-if="item.status == 2">已结束</text>
                                <text v-if="item.status == 3">已取消</text>
                            </view>
                            <view class="bot-right" @click="orderDetails(item)">
                                详情
                            </view>
                        </view>
                    </view>
                   
                </view>
            </block>
            <view class="divider"></view>
        </view>
        <u-calendar v-model="show" ref="calendar" @change="changeTime" mode="range" active-bg-color="#50506f"
            btn-type="default" start-text="开始" end-text="结束" :max-date="maxDate" :min-date="minDate">
        </u-calendar>
        <Loading :loaded="loaded" :loading="loading"></Loading>
    </view>
</template>

<script>
    import Loading from '@/components/Loading';
    import {
        eventAdd,
        eventEdit,
        eventProgramList,
        eventOrderList,
        eventDetail,
        eventProgramDetail,
    } from '@/api/community';
    export default {
        props: {},
        components: {
            Loading
        },
        data: function() {
            return {
                content: '',
                addId: 0,
                did: 0, // 活动id
                appointId: 0, // 计划表id
                isadmin: 1, // 是否管理员
                show: false,
                minDate: '', // 可选最小日期
                maxDate: '', // 可选最大日期
                startDate: '',
                endDate: '',
                datetimeArr: [],
                getArraycopy: [], // 获取详情时间段
                userlist: [],
                checkboxValue1: [],
                arr: [],
                programDetail: {},
                isSubmit: 0,
                selecallStauts: true,
                page: 1,
                limit: 20,
                loaded: false,
                loading: false,
                refreshStatus:false
            };
        },
        mounted: function() {},

        onLoad(options) {
            console.log('页面参数', options)
            const {
                id,
                addId,
                appointId
            } = options;
            this.did = id;
            this.addId = addId; // 1新增  2修改
            this.appointId = appointId;
            console.log('活动id', id)
            this.getEventOrderList(0)
            if (this.addId == 2) {
                this.getEventProgramDetail()
            }
        },

        onShow() {
            this.getdata()
        },
        methods: {
            // refresh(){
            //     this.refreshStatus = true;
            //     this.page = 1;
            //     this.userlist = [];
            //     this.getEventOrderList(1)
            // },
            getdata() {
                this.minDate = this.getNowDate();
                this.maxDate = this.addDate(15)
                // console.log('最小日期',this.minDate)
                // console.log('最大日期',this.maxDate)
            },
            getNowDate(){
                var date = new Date();
                  var year = date.getFullYear() // 年
                  var month = date.getMonth() + 1; // 月
                  var day = date.getDate(); // 日
                  if (month >= 1 && month <= 9) {
                    month = "0" + month;
                  }
                  if (day >= 0 && day <= 9) {
                    day = "0" + day;
                  }
                  return year + "-" + month + "-" + day;
            },
            addDate(days) {
                var date = new Date();
                date.setDate(date.getDate() + days);
                return date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate()
            },
            getFormatDate(arg) {
                if (arg == undefined || arg == '') {
                    return '';
                }
                var re = arg + '';
                if (re.length < 2) {
                    re = '0' + re;
                }
                return re;
            },
            checkboxChange(index, item) {
                if(item.is_reservation_list){
                    this.userlist[index].is_reservation_list = 0
                }else {
                    this.userlist[index].is_reservation_list = 1
                }
            },
            selecAll() {
                if(this.selecallStauts){
                    for (let i = 0; i < this.userlist.length; i++) {
                        this.userlist[i].is_reservation_list = 1
                    }
                }else {
                    for (let i = 0; i < this.userlist.length; i++) {
                        this.userlist[i].is_reservation_list = 0
                    }
                }
                this.selecallStauts = !this.selecallStauts
                console.log('this.selecallStauts',this.selecallStauts)
            },
            orderDetails(item) {
                this.goPages('/pages/ycommunity/order/detail?order_id=' + item.order_id)
            },
            addEventAdd() {
                let _this = this;
                let checkboxValue1 = []
                if (!this.startDate) {
                    return this.$showToast('请选择开始日期');
                }
                if (!this.endDate) {
                    return this.$showToast('请选择结束日期');
                }
                if (this.datetimeArr.length < 1) {
                    return this.$showToast('请生成并确认面试时间段');
                }
                console.log('this.userlist',this.userlist)
                for (let i = 0; i < this.userlist.length; i++) {
                    if(this.userlist[i].is_reservation_list == 1){
                        checkboxValue1.push(this.userlist[i])
                    }
                }
                if (checkboxValue1.length < 1) {
                    return this.$showToast('请选择预约名单');
                }
                let datearray = [];
                for (let i = 0; i < this.datetimeArr.length; i++) {
                    let obj = {
                        label: '',
                        value: [],
                        value_timestamp: [] // 时间戳数组
                    }
                    obj.label = this.datetimeArr[i].label;
                    let array = [];
                    let value_timestamp = [];
                    for (let j = 0; j < this.datetimeArr[i].value.length; j++) {
                        array.push(this.datetimeArr[i].value[j].value)
                        let snums = 0;
                        if(this.datetimeArr[i].value[j].value.indexOf('下午')){
                            let snum = 0;
                            snum = Number(this.datetimeArr[i].value[j].value.replace(/[^\d]/g,' '))
                            if (9 <= snum && snum <= 12){
                                snums = snum + ':00'
                            }
                        }
                        if(this.datetimeArr[i].value[j].value.indexOf('上午')){
                            let snum = 0;
                            snum = Number(this.datetimeArr[i].value[j].value.replace(/[^\d]/g,' '))
                            if (1 <= snum && snum <= 6){
                                snums = (snum+12) + ':00'
                            }
                        }
                        value_timestamp.push(snums)
                    }
                    obj.value = this.shallowCopy(array);
                    obj.value_timestamp = this.shallowCopy(value_timestamp);
                    datearray.push(obj)
                }
                let array = []
                for (let i = 0; i < checkboxValue1.length; i++) {
                    let obj = {}
                    obj.uid = checkboxValue1[i].uid;
                    obj.activity_id = this.did;
                    obj.oid = checkboxValue1[i].id;
                    obj.student_id = checkboxValue1[i].student_id
                    array.push(obj)
                }
                let obj = {
                    activity_id: _this.did,
                    start_time: _this.startDate + '09:00:00',
                    end_time: _this.endDate + '19:00:00',
                    time_period: datearray,
                    reservation_list: array,
                    content: _this.content
                };
                if (this.addId == 1) {
                    // console.log('添加面试预约计划提交参数', obj);
                    eventAdd(obj).then(res => {
                            // console.log('添加面试预约计划成功', res);
                            this.$showToast('添加面试预约计划成功');
                            setTimeout(function() {
                                uni.navigateBack({
                                    delta: 1
                                })
                            }, 1000);
                        })
                        .catch(err => {
                            console.log('添加面试预约计划失败', err);
                        });
                }
                if (this.addId == 2) {
                    obj.id = this.appointId
                    // console.log('修改面试预约计划提交参数', obj);
                    eventEdit(obj).then(res => {
                            // console.log('修改面试预约计划成功', res);
                            this.$showToast('修改面试预约计划成功');
                            setTimeout(function() {
                                uni.navigateBack({
                                    delta: 1
                                })
                            }, 1000);
                        })
                        .catch(err => {
                            console.log('修改面试预约计划失败', err);
                        });
                }
            },
            changeTime(e) {
                // console.log('e', e)
                this.startDate = e.startDate
                this.endDate = e.endDate
            },
            goPages(path, type, pathType) {
                if (type) {
                    this.$authNavigator(path);
                } else {
                    this.$navigator(path, pathType);
                }
            },
            // 获取已报名付款成功的订单列表
            getEventOrderList(type) {
                this.loading = true;
                let data = {
                    activity_id: this.did,
                    limit: this.limit,
                    page: this.page,
                }
                if (this.addId == 2) {
                    data.schedule_id = this.appointId
                }
                eventOrderList(data)
                    .then(res => {
                        this.userlist = this.userlist.concat(res.data);
                        this.page++;
                        this.loaded = res.data.length < this.limit;
                        this.loading = false;
                        if (type == 1) {
                            this.$showToast('人员列表刷新成功');
                        }
                        // console.log('获取已报名付款成功的订单列表成功', this.userlist)
                    })
                    .catch(err => {
                        console.log('获取已付款名单err', err)
                        // this.$navigator(-1);
                    });
            },

            getEventProgramDetail() {
                let data = {
                    id: this.appointId,
                }
                eventProgramDetail(data)
                    .then(res => {
                        // console.log('获取详情成功', res)
                        let programDetail = res.data;
                        this.content = programDetail.content;
                        this.startDate = programDetail.start_time.substring(0, 10);
                        this.endDate = programDetail.end_time.substring(0, 10);
                        var datetimeArr = [];
                        for (let i = 0; i < programDetail.time_period.length; i++) {
                            var datetimeobj = {
                                label: '',
                                value: []
                            }
                            var datetimeArray = [];
                            for (let j = 0; j < programDetail.time_period[i].length; j++) {
                                if (programDetail.time_period[i][j].is_select == 1) {
                                    var timesObj = {
                                        is_select: '',
                                        value: ''
                                    }
                                    timesObj.is_select = 1;
                                    timesObj.value = programDetail.time_period[i][j].period
                                    datetimeArray.push(timesObj);
                                }
                                if (programDetail.time_period[i][j].is_select == 0) {
                                    var timesObj = {
                                        is_select: '',
                                        value: ''
                                    }
                                    timesObj.is_select = 0;
                                    timesObj.value = programDetail.time_period[i][j].period
                                    datetimeArray.push(timesObj);
                                }
                            }
                            datetimeobj.label = programDetail.time_period[i][0].date;
                            datetimeobj.value = this.shallowCopy(datetimeArray);
                            datetimeArr.push(datetimeobj);
                        }
                        this.datetimeArr = datetimeArr;
                        this.getArraycopy = this.shallowCopy(datetimeArr);
                        
                        // console.log('this.datetimeArr====',this.datetimeArr)
                        // console.log('this.getArraycopy====',this.getArraycopy)
                    })
                    .catch(err => {
                        console.log('获取详情失败', err)
                        this.$navigator(-1);
                    });
            },
            deltime(index, index2, items) {
                if (items.is_select == 1) {
                    return this.$showToast('当前时段已有人预约不可删除');
                }
                this.datetimeArr[index].value.splice(index2, 1)
                console.log('时间区间', this.datetimeArr)
            },
            generate(hours, step) {
                if(this.addId == 2){
                    let that = this;
                    uni.showModal({
                    	title: '提示',
                    	content: '将重置所有时间点，是否确认？',
                    	success: function (res) {
                    		if (res.confirm) {
                    			if (that.startDate == '' || that.endDate == '') {
                    			    return that.$showToast('请选择开始时间与结束时间段');
                    			}
                    			that.datetimeArr = [];
                    			let arrayDate = that.getEveryDayDateByBetweenDate(that.startDate, that.endDate);
                    			var datetimeArr = [];
                    			var minutes = 60
                    			var timeArr = []
                    			hours = hours
                    			step = step
                    			for (var i = 9; i < hours; i++) {
                    			    var str = '';
                    			    str = i;
                    			    if (i > 6 && i < 13) {
                    			        str = '上午' + ' ' + str
                    			    }
                    			    if (i >= 13 && i <= 19) {
                    			        str = '下午' + ' ' + (str - 12)
                    			    }
                    			    for (var j = 0; j < minutes; j++) {
                    			        if (j % step == 0) {
                    			            var s = j < 10 ? '点' : '点';
                    			            if (i < 9) {
                    			                var ii = "0" + (i + 1);
                    			            } else {
                    			                var ii = i + 1;
                    			            }
                    			            s = str + s
                    			            timeArr.push(s)
                    			        }
                    			    }
                    			}
                    			var datetimeArray = [];
                    			for (var m = 0; m < timeArr.length; m++) {
                    			    var timesObj = {
                    			        is_select: '',
                    			        value: ''
                    			    }
                    			    timesObj.is_select = 0;
                    			    timesObj.value = timeArr[m];
                    			    datetimeArray.push(timesObj);
                    			}
                    			for (var n = 0; n < arrayDate.length; n++) {
                    			    var datetimeobj = {
                    			        label: '',
                    			        value: []
                    			    }
                    			    datetimeobj.label = arrayDate[n];
                    			    datetimeobj.value = that.shallowCopy(datetimeArray);
                    			    datetimeArr.push(datetimeobj);
                    			}
                    			that.datetimeArr = datetimeArr;
                    			for (let i = 0; i < that.getArraycopy.length; i++) {
                    			    for (let j = 0; j < that.getArraycopy[i].value.length; j++) {
                    			        if (that.getArraycopy[i].value[j].is_select == 1) {
                    			            // 控制日期
                    			            for(let n = 0; n < that.datetimeArr.length; n++){
                    			                if(that.datetimeArr[n].label == that.getArraycopy[i].label) {
                    			                    // 控制日期下时间段
                    			                    for(let m = 0; m < that.datetimeArr[n].value.length; m++){
                    			                        if( that.datetimeArr[n].value[m].value == that.getArraycopy[i].value[j].value){
                    			                            let obj ={
                    			                                is_select:1,
                    			                                value:that.datetimeArr[n].value[m].value
                    			                            }
                    			                            that.datetimeArr[n].value.splice(m, 1,obj);
                    			                        }
                    			                    }
                    			                }
                    			            }
                    			            
                    			        }
                    			    }
                    			}
                    			that.$showToast('生成时间段成功');
                    		} else if (res.cancel) {
                    			console.log('用户点击取消');
                    		}
                    	}
                    });
                }
                if(this.addId == 1){
                    if (this.startDate == '' || this.endDate == '') {
                        return this.$showToast('请选择开始时间与结束时间段');
                    }
                    this.datetimeArr = [];
                    let arrayDate = this.getEveryDayDateByBetweenDate(this.startDate, this.endDate);
                    var datetimeArr = [];
                    var minutes = 60
                    var timeArr = []
                    hours = hours
                    step = step
                    for (var i = 9; i < hours; i++) {
                        var str = '';
                        str = i;
                        if (i > 6 && i < 13) {
                            str = '上午' + ' ' + str
                        }
                        if (i >= 13 && i <= 19) {
                            str = '下午' + ' ' + (str - 12)
                        }
                        for (var j = 0; j < minutes; j++) {
                            if (j % step == 0) {
                                var s = j < 10 ? '点' : '点';
                                if (i < 9) {
                                    var ii = "0" + (i + 1);
                                } else {
                                    var ii = i + 1;
                                }
                                s = str + s
                                timeArr.push(s)
                            }
                        }
                    }
                    var datetimeArray = [];
                    for (var m = 0; m < timeArr.length; m++) {
                        var timesObj = {
                            is_select: '',
                            value: ''
                        }
                        timesObj.is_select = 0;
                        timesObj.value = timeArr[m];
                        datetimeArray.push(timesObj);
                    }
                    for (var n = 0; n < arrayDate.length; n++) {
                        var datetimeobj = {
                            label: '',
                            value: []
                        }
                        datetimeobj.label = arrayDate[n];
                        datetimeobj.value = this.shallowCopy(datetimeArray);
                        datetimeArr.push(datetimeobj);
                    }
                    this.datetimeArr = datetimeArr;
                    for (let i = 0; i < this.getArraycopy.length; i++) {
                        for (let j = 0; j < this.getArraycopy[i].value.length; j++) {
                            if (this.getArraycopy[i].value[j].is_select == 1) {
                                // 控制日期
                                for(let n = 0; n < this.datetimeArr.length; n++){
                                    if(this.datetimeArr[n].label == this.getArraycopy[i].label) {
                                        // 控制日期下时间段
                                        for(let m = 0; m < this.datetimeArr[n].value.length; m++){
                                            if( this.datetimeArr[n].value[m].value == this.getArraycopy[i].value[j].value){
                                                // this.datetimeArr[n].value[m].is_select = 1; 
                                                let obj ={
                                                    is_select:1,
                                                    value:this.datetimeArr[n].value[m].value
                                                }
                                                this.datetimeArr[n].value.splice(m, 1,obj);
                                            }
                                        }
                                    }
                                }
                                
                            }
                        }
                    }
                    this.$showToast('生成时间段成功');
                }
                console.log('生成时间段', this.datetimeArr)
            },
            shallowCopy(arr) {
                var newArr = Array.isArray(arr) ? [] : {}
                for (let key in arr) {
                    newArr[key] = arr[key]
                }
                return newArr
            },
            getEveryDayDateByBetweenDate(start_date, end_date) {
                var startTime = this.getDate(start_date);
                var endTime = this.getDate(end_date);
                var dateArr = [];
                while ((endTime.getTime() - startTime.getTime()) > 0) {
                    var year = startTime.getFullYear();
                    var month = (startTime.getMonth() + 1).toString().length === 1 ? "0" + (parseInt(startTime
                        .getMonth().toString(), 10) + 1) : (startTime.getMonth() + 1);
                    var day = startTime.getDate().toString().length === 1 ? "0" + startTime.getDate() : startTime
                        .getDate();
                    dateArr.push(year + "-" + month + "-" + day);
                    startTime.setDate(startTime.getDate() + 1);

                }
                dateArr.push(end_date)
                return dateArr;
            },
            getDate(datestr) {
                var temp = datestr.split("-");
                if (temp[1] === '01') {
                    temp[0] = parseInt(temp[0], 10) - 1;
                    temp[1] = '12';
                } else {
                    temp[1] = parseInt(temp[1], 10) - 1;
                }
                var date = new Date(temp[0], temp[1], temp[2]);
                return date;
            },
            refresh(){
                setTimeout(()=>{
                    this.page = 1;
                    this.userlist = [];
                    this.getEventOrderList(1)
                    //停止下拉刷新
                    uni.stopPullDownRefresh();
                },1000)
            }
        },
        onReachBottom() {
            this.getEventOrderList(0)
        },
        onPullDownRefresh() {
            this.refresh()
        },
    };
</script>
<style scoped lang="scss">
    .admin-box {
        width: 100%;
        padding: 40rpx 20rpx;
        box-sizing: border-box;
        overflow: auto;

        .admin-box-top {
            width: 710rpx;
            background: #ffffff;
            border-radius: 30rpx;
            padding: 36rpx 20rpx 64rpx;
            box-sizing: border-box;
            box-shadow: 0 0 40rpx 0 rgba(107, 127, 153, 0.20);
            overflow: auto;

            .textarea {
                width: 100%;
                height: 150rpx;
                border: 2rpx solid #d2d2d2;
                border-radius: 14rpx;
                padding: 20rpx 30rpx;
                box-sizing: border-box;
                font-size: 26rpx;
            }

            .title {
                width: 100%;
                font-size: 32rpx;
                font-family: PingFang SC, PingFang SC-Semibold;
                font-weight: 600;
                text-align: left;
                margin-bottom: 32rpx;
                padding: 0 24rpx;
                box-sizing: border-box;
            }

            .desc {
                padding: 0 24rpx 0;
                box-sizing: border-box;

                .desc-text {
                    width: 100%;
                    font-size: 22rpx;
                    font-family: PingFang SC, PingFang SC-Medium;
                    font-weight: 500;
                    text-align: left;
                    color: #646464;
                    padding: 0 0 40rpx;
                    box-sizing: border-box;
                    border-bottom: 2rpx solid #eaeaea;
                }
            }

            .add {
                width: 670rpx;
                height: 100rpx;
                line-height: 100rpx;
                text-align: center;
                font-size: 24rpx;
                font-family: PingFang SC, PingFang SC-Regular;
                font-weight: 400;
                text-align: center;
                color: #ffffff;
                background: #50506f;
                border-radius: 40rpx;
                margin: 48rpx auto 0;

            }
        }

        .selec-time {
            width: 710rpx;
            background: #ffffff;
            border-radius: 30rpx;
            padding: 36rpx 20rpx 64rpx;
            box-sizing: border-box;
            box-shadow: 0 0 40rpx 0 rgba(107, 127, 153, 0.20);
            margin-top: 48rpx;
            overflow: auto;


            .selec-time-title {
                width: 100%;
                padding: 0 22rpx;
                font-size: 32rpx;
                font-family: PingFang SC, PingFang SC-Semibold;
                font-weight: 600;
                text-align: left;
                color: #646464;
                box-sizing: border-box;
                margin-bottom: 32rpx;
            }

            .selec-time-content {
                position: relative;
                width: 100%;
                height: 80rpx;
                background: rgba(255, 255, 255, 0.00);
                border: 2rpx solid #50506f;
                border-radius: 42rpx;
                margin-bottom: 32rpx;

                .startDate {
                    position: absolute;
                    left: 86rpx;
                    top: 20rpx;
                    font-size: 28rpx;
                    font-family: PingFang SC, PingFang SC-Semibold;
                    font-weight: 600;
                    text-align: center;
                    color: #50506f;
                }

                .divider {
                    position: absolute;
                    left: 278rpx;
                    top: 20rpx;
                    font-size: 28rpx;
                    font-family: PingFang SC, PingFang SC-Semibold;
                    font-weight: 600;
                    text-align: center;
                    color: #50506f;
                }

                .endDate {
                    position: absolute;
                    left: 320rpx;
                    top: 20rpx;
                    font-size: 28rpx;
                    font-family: PingFang SC, PingFang SC-Semibold;
                    font-weight: 600;
                    text-align: center;
                    color: #50506f;
                }

                .dividers {
                    position: absolute;
                    right: 118rpx;
                    top: 20rpx;
                    width: 4rpx;
                    height: 38rpx;
                    border: 3rpx solid #50506f;
                    border-radius: 8rpx;
                }

                .calendar-img {
                    position: absolute;
                    right: 40rpx;
                    top: 20rpx;
                    width: 40rpx;
                    height: 36rpx;
                }
            }

            .selec-time-add {
                width: 670rpx;
                height: 100rpx;
                line-height: 100rpx;
                text-align: center;
                font-size: 24rpx;
                font-family: PingFang SC, PingFang SC-Regular;
                font-weight: 400;
                text-align: center;
                color: #ffffff;
                background: #50506f;
                border-radius: 40rpx;
                margin-bottom: 32rpx;
            }

            .time-box-item {
                width: 100%;
                height: 84rpx;
                line-height: 84rpx;
                background: #eff5fc;
                border-radius: 16rpx;
                padding: 0 24rpx 0 40rpx;
                box-sizing: border-box;
                margin-bottom: 16rpx;

                .title {
                    float: left;
                    font-size: 22rpx;
                    font-family: PingFang SC, PingFang SC-Regular;
                    font-weight: 400;
                    text-align: left;
                    color: #6bb4ff;
                }

                .btn {
                    float: right;
                    width: 152rpx;
                    height: 50rpx;
                    line-height: 50rpx;
                    text-align: center;
                    font-size: 24rpx;
                    font-family: PingFang SC, PingFang SC-Medium;
                    font-weight: 500;
                    color: #3399ff;
                    background: rgba(107, 180, 255, 0.20);
                    border: 2rpx solid #6bb4ff;
                    border-radius: 24rpx;
                    margin-top: 18rpx;

                }

                &.active {
                    .title {
                        color: #d2d2d2;
                    }

                    .btn {
                        color: #d2d2d2;
                        border: 2rpx solid #d2d2d2;
                        background-color: #fff;
                    }
                }
            }
        }
        .user-box {
            width: 710rpx;
            background: #ffffff;
            border-radius: 30rpx;
            box-shadow: 0 0 40rpx 0 rgba(107, 127, 153, 0.20);
            overflow: auto;
            margin: 48rpx auto 0;
            // padding: 36rpx 24rpx 48rpx 36rpx;
            padding: 36rpx 20rpx 48rpx;
            box-sizing: border-box;

            .user-box-title {
                width: 100%;
                font-size: 32rpx;
                font-family: PingFang SC, PingFang SC-Semibold;
                font-weight: 600;
                text-align: left;
                color: #646464;
                padding: 0 22rpx;
                box-sizing: border-box;
            }

            .user-box-btn {
                width: 100%;
                overflow: auto;
                margin-top: 40rpx;
                margin-bottom: 32rpx;

                .left {
                    float: left;
                    width: 152rpx;
                    height: 60rpx;
                    line-height: 60rpx;
                    text-align: center;
                    font-size: 24rpx;
                    font-family: PingFang SC, PingFang SC-Regular;
                    font-weight: 400;
                    text-align: center;
                    color: #3399ff;
                    background: #eff5fc;
                    border-radius: 24rpx;

                    &.right {
                        float: right;
                        background: #eaeaea;
                        color: #50506f;
                    }

                }
            }

            .divider {
                width: 630rpx;
                height: 2rpx;
                background: #eaeaea;
                margin: 32rpx auto 0;
            }

            .userbox-item {
                width: 670rpx;
                min-height: 440rpx;
                background: #eff5fc;
                border-radius: 16rpx;
                margin-bottom: 16rpx;
                padding: 32rpx 24rpx 32rpx 36rpx;
                box-sizing: border-box;

                .userbox-item-left {
                    float: left;
                    width: 43rpx;
                    margin-right: 23rpx;

                    .img {
                        width: 100%;
                        height: 42rpx;
                        margin-top: 168rpx;
                    }
                }

                .userbox-item-right {
                    float: left;
                    width: 540rpx;
                    overflow: auto;

                    .top {
                        width: 100%;
                        overflow: auto;
                        margin-bottom: 16rpx;

                        .top-left {
                            float: left;
                            width: 88rpx;
                            height: 88rpx;
                            margin-right: 50rpx;
                            border-radius: 50%;
                        }

                        .top-right {
                            float: left;
                            font-size: 22rpx;
                            font-family: PingFang SC, PingFang SC-Regular;
                            font-weight: 400;
                            text-align: center;
                            color: #646464;
                            margin-top: 28rpx;
                        }
                    }

                    .item {
                        width: 100%;
                        font-size: 22rpx;
                        font-family: PingFang SC, PingFang SC-Regular;
                        font-weight: 400;
                        text-align: left;
                        color: #6bb4ff;
                        padding-left: 10rpx;
                        box-sizing: border-box;
                        margin-bottom: 16rpx;
                        overflow: auto;

                        .item-title {
                            float: left;
                            width: 128rpx;
                        }

                        .item-center {
                            float: left;
                        }
                    }

                    .bot {
                        width: 100%;
                        font-size: 22rpx;
                        font-family: PingFang SC, PingFang SC-Regular;
                        font-weight: 400;
                        text-align: left;
                        color: #6bb4ff;
                        padding-left: 10rpx;
                        box-sizing: border-box;
                        margin-bottom: 16rpx;
                        overflow: auto;

                        .bot-left {
                            float: left;

                            .bot-left-title {
                                float: left;
                                width: 128rpx;
                            }
                        }

                        .bot-right {
                            float: right;
                            width: 98rpx;
                            height: 38rpx;
                            line-height: 38rpx;
                            text-align: center;
                            background: rgba(107, 180, 255, 0.20);
                            border: 2rpx solid #6bb4ff;
                            border-radius: 22rpx;
                            font-size: 24rpx;
                            font-family: PingFang SC, PingFang SC-Medium;
                            font-weight: 500;
                            color: #3399ff;
                        }
                    }
                }
            }
        }
    }
</style>
