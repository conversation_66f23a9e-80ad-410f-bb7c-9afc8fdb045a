<template>
    <view class="active">
        <view class="swiper relative">
            <view class="swiper-set" v-if="isActiveAdmin" @click="goPages('/pages/ycommunity/shop/adminIndex?id=' + did,true)">
                <image src="../../../static/images/yuanshi/edit.png" mode=""></image>
            </view>
            <xSwiper :arr="detail.activityInfo.slider_image" height="1000rpx" srcName="item" radius="0rpx"
                :autoplay="detail.activityInfo.slider_image.length > 1?true:false" :dots="true" mode="widthFix"
                @change="dotChange"></xSwiper>
            <view class="absolute wrap">
                <view class="dot flex_line_height">{{ dotIdx + 1 }} / {{ detail.activityInfo.slider_image.length }}
                </view>
                <view class="name">{{ detail.activityInfo.name }}</view>
            </view>
        </view>
        <view class="main">
            <view class="wrap">
                <view class="wrap_t">
                    <view class="flex flex_align_center flex_between">
                        <view class="t_l" v-if="detail.activityInfo.status < 3">
                            <view class="l1">
                                <!-- #ifdef MP-TOUTIAO
                                <text v-show="detail.activityInfo.status <= 1">预</text>
                                #endif -->
                                <!-- <text v-if="detail.activityInfo.status===0">众筹</text> -->
                                <text v-if="detail.activityInfo.status >= 0">报名</text>
                                <text>截止时间：</text>
                                <view class="time" :class="detail.activityInfo.status > 1?'black':''">
                                    {{detail.activityInfo.end_time}}</view>
                            </view>
                            <view class="l2">
                                <text v-if="detail.activityInfo.status == 0">已</text>
                                <text v-if="detail.activityInfo.status > 0">确认</text>
                                <!-- <text v-if="detail.activityInfo.status===0">众筹：{{detail.activityInfo.crowdfunding_numbers}}</text> -->
                                <text
                                    v-if="detail.activityInfo.status === 0">报名：{{detail.activityInfo.crowdfunding_numbers}}</text>
                                <text
                                    v-if="detail.activityInfo.status > 0 ">报名：{{detail.activityInfo.signup_numbers}}</text>
                                人
                            </view>
                        </view>
                        <view class="t_l" v-else>
                            <view class="l1">
                                <text class="time">已结束</text>
                            </view>
                        </view>
                        <view class="t_r">
                            <!-- #ifdef MP -->
                            <button class="flex_line_height btn_l" open-type="share">
                                <view class="flex flex_align_center">
                                    <image src="@/static/images/community/share.png" mode="widthFix"></image>
                                    <text class="tt_margin-right">分享</text>
                                </view>
                            </button>
                            <!-- #endif -->
                        </view>
                    </view>
                </view>
                <view class="wrap_c flex flex_align_center flex_between">
                    <view class="c_l">
                        <view><text class="label">活动时间：</text>{{detail.activityInfo.activity_time}}</view>
                        <view><text class="label">地点：</text>{{detail.activityInfo.detailed_address}}</view>
                        <view><text class="label">活动主题：</text>{{detail.activityInfo.introduction}}</view>
                    </view>
                    <view class="c_r" @click="goMap(detail.activityInfo)">
                        <image src="@/static/images/yuanshi/address.png" mode="widthFix"></image>
                    </view>
                </view>
                <view class="wrap_b flex">
                    <view class="desc">{{detail.activityInfo.hint}}</view>
                    <view class="addChat" @click="addChatGroup" >加入群聊</view>
                </view>
            </view>
            <wrapVip></wrapVip>
            <view class="show_title " v-if="detail.activityInfo.description">
                <view class=" flex flex_align_center flex_between">
                    <view class="title">活动简介</view>
                    <view class="icon_more">
                        <!-- 查看更多 -->
                        <!-- <text class="iconfont icon-jiantou"></text> -->
                    </view>
                </view>
                <view class="conter">
                    <u-parse :html="detail.activityInfo.description" :tag-style="parseStyle" @linkpress="$linkpress">
                    </u-parse>
                </view>
                <view class="wish" v-if="detail.activityInfo.is_wish == 1">
                    <view class="wish-title">
                        {{detail.activityInfo.wish_title}}
                    </view>
                    <view class="wish-title2">
                        {{detail.activityInfo.wish_desc}}
                    </view>
                    <view class="wish-item">
                        <view class="wish-item-title">
                            城市：
                        </view>
                        <view class="wish-item-centen">
                            <input class="input" v-model="wish.address" type="text" placeholder-style="font-size: 12px;"
                                placeholder="请输入城市" @focus="inputhide" @blur="inputhide">
                        </view>
                    </view>
                    <view class="wish-item">
                        <view class="wish-item-title">
                            联系人：
                        </view>
                        <view class="wish-item-centen">
                            <input class="input" v-model="wish.contact_name" type="text"
                                placeholder-style="font-size: 12px;" placeholder="请输入联系人" @focus="inputhide"
                                @blur="inputhide">
                        </view>
                    </view>
                    <view class="wish-item">
                        <view class="wish-item-title">
                            电话：
                        </view>
                        <view class="wish-item-centen">
                            <input class="input" type="text" v-model="wish.phone" placeholder-style="font-size: 12px;"
                                placeholder="请输入电话" @focus="inputhide" @blur="inputhide">
                        </view>
                    </view>
                    <view class="wish-item">
                        <view class="wish-item-title">
                            还有想说的：
                        </view>
                        <!-- :show-confirm-bar="false" :fixed="true"
                        @focus="inputhide" @blur="inputhide" -->
                        <view class="wish-item-centen">
                            <textarea class="textarea" v-model="wish.comment" placeholder-style="font-size: 12px;"
                                placeholder="请输入备注信息" ></textarea>
                        </view>
                    </view>
                    <view class="wish-submit" @click="wishSubmit">
                        填好了
                    </view>
                </view>
            </view>
            <template v-if="detail.RegisteredList && detail.RegisteredList.length">
                <view class="want_go">
                    <view class="nav ">
                        <view class="title">已报名
                            <!-- <text v-if="detail.activityInfo.status===0">众筹</text> -->
                            <!-- <text v-if="detail.activityInfo.status===1">报名</text> -->
                        </view>
                    </view>
                    <view class="wrap1 ">
                        <scroll-view scroll-x style="white-space:nowrap;" :scroll-with-animation="true">
                            <view class="item" v-for="(item, index) in detail.RegisteredList" :key="item.uid"
                                @click="goPages('/pages/yuanshi/user/home?id=' + item.uid,true)">
                                <image :src="item.avatar" mode="aspectFill"></image>
                                <view class="name">{{ item.nickname }}</view>
                                <view class="btn flex_line_height"
                                    @click.stop="goFollow(item.is_follow, item.uid, index)">
                                    {{ item.is_follow ? '已跟随' : '跟随' }}
                                </view>
                            </view>
                        </scroll-view>
                    </view>
                </view>
            </template>
            <view class="bottom_line"></view>
            <view class="message" :class="{ minHeight: minHeight }">
                <view class="mess_tab flex flex_between">
                    <view class="mess">
                        评论区
                        <text class="font_size20">{{ detail.message_number || 0 }}条评论</text>
                    </view>
                    <view class="flex flex_align_center">
                        <!-- <view class="write item flex_line_height relative" :class="{ active: isOnlyMy }"
							@click="onlyShowMy()">
							<text>{{ isOnlyMy ? '查看全部' : '只看我的' }}</text>
							<view class="absolute tips" v-if="!isOnlyMy && tipsShow">
								<view class="txt">点此查看我的评论</view>
								<view class="flex flex_align_center flex_between txt1">
									<view class="flex flex_align_center" @click.stop="tipsShowCheckClick">
										<view class="image">
											<image src="@/static/images/yuanshi/check.png" mode="widthFix"
												v-if="tipsShowCheck"></image>
											<image src="@/static/images/yuanshi/uncheck.png" mode="widthFix" v-else>
											</image>
										</view>
										<text>不再提示</text>
									</view>
									<view class="btn" @click.stop="tipsShow = false">确定</view>
								</view>
							</view>
						</view> -->
                        <view class="write flex_line_height"
                            @click="goPages('/pages/ycommunity/comment/submit?id=' + did,true)">
                            <text>我来评论</text>
                        </view>
                    </view>
                </view>
                <template v-if="detail.all_evaluation_latitude && detail.all_evaluation_latitude.length">
                    <view class="bottom_line"></view>
                    <view class="mess_nav relative">
                        <view class="nav flex flex_around">
                            <view class="item" v-for="(item, index) in detail.all_evaluation_latitude" :key="index"
                                @click="pannelClick(item, index)">
                                <view class="num">{{ item.reviewers_number || 0 }}人评论</view>
                                <view class="">
                                    <text class="font_size20">{{ item.cate_name }}</text>
                                    <text class="iconfont icon-xiangxia"></text>
                                </view>
                            </view>
                        </view>
                    </view>
                </template>
                <view class="bottom_line"></view>
                <view class="message_wrap " v-if="messageInfo.length">
                    <view class="item" v-for="(item, index) in messageInfo" :key="item.id">
                        <view class="mess_avatar flex flex_align_center flex_between">
                            <view class="avatar flex_align_center flex">
                                <image :src="item.avatar"></image>
                                <text>{{ item.nickname }}</text>
                            </view>
                            <view class="btn flex_line_height" @click="showInput(1, item)" v-if="!isOnlyMy">回复
                            </view>
                        </view>
                        <view class="mess_des relative" v-if="item.content"
                            @click="goPages('/pages/ycommunity/comment/detail?id='+item.id)">
                            <view v-for="(item1, index1) in item.content" :key="index1">
                                <text>{{ item1 }}</text>
                            </view>
                            <view class="refining_span" v-if="item.is_refining">精</view>
                            <view class="del">
                                <text @click.stop="delMyEvaluate(item, index)" v-if="detail.uid===item.uid">删除</text>
                            </view>
                        </view>
                        <view class="mess_handle ">
                            <view class="flex flex_align_center flex_between">
                                <view class="time">{{ item.add_time }}</view>
                                <view class="flex flex_align_center">
                                    <view class="item flex flex_align_center" @click="likeStars(item, index)">
                                        <image src="@/static/images/yuanshi/like_.png" mode="widthFix"
                                            v-if="item.is_like"></image>
                                        <image src="@/static/images/yuanshi/like.png" mode="widthFix" v-else></image>
                                        <text>{{ item.like_count || 0 }}</text>
                                    </view>
                                    <view class="item flex flex_align_center">
                                        <image src="@/static/images/yuanshi/comment.png" mode="widthFix"></image>
                                        <text>{{ item.comments || 0 }}</text>
                                    </view>
                                    <!-- <view class="item flex flex_align_center" @click="shareComment(item)">
										<image src="@/static/images/community/share.png" mode="widthFix"></image>
									</view> -->
                                </view>
                            </view>
                        </view>
                        <view class="mess_image flex"
                            v-if="item.video&&item.video.length || item.audio&&item.audio.length || item.image&&item.image.length">
                            <view class="nav rote180" @click="navClick('left')"><text
                                    class="iconfont icon-jiantou "></text></view>
                            <view class="wrap1 ">
                                <scroll-view scroll-x style="white-space:nowrap;" :scroll-with-animation="true"
                                    :scroll-left="scrollLeft" @scroll="scrollX">
                                    <view class="flex flex_align_center">

                                        <view class="item video" v-for="(item1, index1) in item.video"
                                            v-if="item.video&&item.video.length" :key="item1.name">
                                            <video :id="`playVideo${index}-${index1}`"
                                                style="width:136rpx;height: 136rpx;" :show-center-play-btn="false"
                                                :controls="isFullscreen"
                                                @fullscreenchange="fullscreenchange(item1,index,index1)"
                                                :src="item1.name" custom-cache="false"></video>

                                            <view class="cover flex flex_align_center flex_around">
                                                <image src="@/static/images/community/play.png"
                                                    @click="playVideo(item1,index,index1)">
                                            </view>
                                        </view>
                                        <view class="item audio" v-for="(item1, index1) in item.audio"
                                            v-if="item.audio&&item.audio.length" :key="item1.name">
                                            <view class="flex flex_align_center">
                                                <image src="@/static/images/community/play.png" v-if="item1.play===0"
                                                    @click="xPlayAudio(item1,index,index1)">
                                                    <image src="@/static/images/community/pause.png" v-else
                                                        @click="xPlayAudio(item1,index,index1)">
                                            </view>
                                        </view>
                                        <view class="item" v-for="(item1, index1) in item.image"
                                            :id="index1 === 0 ? 'calcItem' : ''" :key="index1"
                                            v-if="item.image&&item.image.length">
                                            <image :src="item1.name" mode="aspectFill"
                                                @click="previewImage(item.image, item1.name)">
                                            </image>
                                        </view>
                                    </view>
                                </scroll-view>
                            </view>
                            <view class="nav" @click="navClick('right')"><text class="iconfont icon-jiantou"></text>
                            </view>
                        </view>

                        <!-- 	<template v-if="isOnlyMy && (item.status === 2 || item.status === 3)">
							<view class="flex flex_align_center flex_between my_handle">
								<view class="txt">
									<text class="font_size20" v-if="item.status === 2">您的评论正在审核中…</text>
									<text class="font_size20" v-if="item.status === 3">您的评论审核不通过</text>
								</view>
								<view class="flex flex_align_center">
									<view class="btn flex_line_height"
										@click="goPages('/pages/yuanshi/evaluate/submit?type=edit&id=' + mixinsParam.wishId + '&pid=' + mixinsParam.productId + '&eid=' + item.id)">
										编辑
									</view>
									<view class="btn flex_line_height" @click="delMyEvaluate(item, index)">删除</view>
								</view>
							</view>
						</template> -->

                        <view class="comment">
                            <xComment :ref="'xComment' + item.id" pageType="community" :ctype="2" :rid="item.id"
                                :chat="false" @showInput="chatInput" @success="sendSuccess" :time="false" :tips="false"
                                :info="item.children_reply"></xComment>
                        </view>
                    </view>
                </view>
                <view v-else class="text_center " style="color: #b7b7b7;padding: 20rpx 0;font-size: 24rpx;"><text
                        class="">来添加第一条评论吧</text></view>
            </view>
        </view>
        <view class="notice-bar" :class="{ hide: footerHidden }" v-if="detail.activityInfo.is_countdown" :style="{ opacity: footerOpacity }">
            <view class="notice-bar-box">
                {{detail.activityInfo.offer_notes}}<view class="text">{{countobj.day}}</view>天<view class="text">{{countobj.hou}}</view>: <view class="text">{{countobj.min}}</view>: <view class="text">{{countobj.sec}}</view>
            </view>
        </view>
        <view class="active_footer relative" :class="{ hide: footerHidden }" :style="{ opacity: footerOpacity }"
            v-show="footerOpacity > 0">
            <view class="absolute flex flex_align_center flex_between">
                <view class="flex flex_l flex_align_center" style="height: 0;">
                    <view class="item" @click="goPages('/pages/tabBar/index/index',false,'switchTab')">
                        <view>
                            <image src="@/static/images/community/home.png" mode="">
                            </image>
                        </view>
                        <view class="font_size20">首页</view>
                    </view>
                    <!-- #ifdef MP-WEIXIN -->
                    <view class="item"
                        @click="openWeChat">
                        <view>
                            <image src="@/static/images/community/chat.png" mode=""></image>
                        </view>
                        <view class="font_size20">客服</view>
                    </view>
                    <!-- #endif -->
                    <!-- #ifndef MP-WEIXIN -->
                    <view class="item"
                        @click="goPages(`/pages/user/CustomerList?id=${did}&type=0&scence=community`,true)">
                        <view>
                            <image src="@/static/images/community/chat.png" mode=""></image>
                        </view>
                        <view class="font_size20">客服</view>
                    </view>
                    <!-- #endif -->
                </view>
                <view class="btn">
                    <view class="btn_r" @click="goSingUp" v-if="detail.activityInfo.status<3">
                        立即报名</view>
                    <view class="btn_r bg-color-hui" v-else>{{detail.activityInfo.stock===0?'已售罄':'已结束'}}</view>
                </view>
            </view>
        </view>
        <x-authorize @login="updateData"></x-authorize>
        <xChat :adjustPosition="adjustPosition" :placeholder="placeholder" :inputShow="inputShow" :uid="uid"
            @send="submitComment" desc="comment">
        </xChat>

        <uni-popup ref="popups" type="center" :animation="true">
            <view class=""
                style="width: 400rpx;background-color: #fff;border-radius: 20rpx;padding:20rpx;border:1px solid #fc5656 ">
                <view class="wechatGroup-img">
                    <!-- #ifndef MP-TOUTIAO -->
                    <image show-menu-by-longpress="true" :src="detail.activityInfo.contact_image" mode="widthFix">
                    </image>
                    <!-- #endif -->
                    <!-- #ifdef MP-TOUTIAO -->
                    <image :src="detail.activityInfo.douyin_contact_image" mode="widthFix">
                    </image>
                    <!-- #endif -->
                </view>
                <!-- #ifndef MP-TOUTIAO -->
                <view class="text_center" style="padding:20rpx">{{detail.activityInfo.contact_info}}</view>
                <!-- #endif -->
                <!-- #ifdef MP-TOUTIAO -->
                <view class="tt_text_left">
                    <view class="tt_text_left_btn1" @click="addChatGroupClose">
                        关闭
                    </view>
                    <view class="tt_text_left_btn2" @click="saveimg(detail.activityInfo.douyin_contact_image)">
                        保存
                    </view>
                </view>
                <!-- #endif -->
            </view>
        </uni-popup>
        <uni-popup ref="balancePopups" type="center" :animation="true">
            <view class="balance-box">
                <image class="balance-box-close" src="../../../static/images/yuanshi/close1.png" mode=""
                    @click="closeBalancePopups"></image>
                <view class="balance-box-title">
                    <view class="">
                        查询到您在本次活动有{{detail.final_payment_info.length}}笔订单需要支付
                    </view>
                    <view class="">
                        请选择支付尾款或者重新报名？
                    </view>
                </view>
                <view class="balance-box-btn">
                    <view class="balance-box-btn-left" @click="goPayBalance">
                        付尾款（¥{{detail.final_payment_info[0]?detail.final_payment_info[0].pay_price:''}}）
                    </view>
                    <view class="balance-box-btn-right" @click="reEnroll">
                        重新报名
                    </view>
                </view>
            </view>
        </uni-popup>
        <Agreement v-model="agreementReadpop" ref="mychild1" @isread="isread"
            :agreementReadtxt="detail.activityInfo.text_agreement"></Agreement>
    </view>
</template>

<script>
    import {
        payOrderHandle
    } from '@/utils/order.js';

    import Agreement from '@/components/Agreement';
    import yuanshiDetail from '@/mixins/yuanshiDetail';
    import goMap from '@/mixins/goMap.js';
    import {
        uniSelectorQueryInfo
    } from '@/utils/uni_api.js';
    import {
        initTime,
        authNavigator,
        toLogin,
        checkLogin,
        autoAuth,
        zxauthNavigator,
        openWeChatCustomerService
    } from '@/utils/common.js';
    import {
        SHARE_ID,
        WX_KEFU_Link,
        WX_ENTERPRISE_Link
    } from '@/config.js';
    import xRecom from '@/components/yuanshi/x-recom.vue';
    import xChat from '@/components/x-chat/x-chat';
    import xSwiper from '@/components/x-swiper/x-swiper.vue';
    import wrapVip from '@/components/yuanshi/wrap-vip';
    import xComment from '@/components/x-comment/x-comment/x-comment';
    import xLabel from '@/components/x-label/x-label.vue';
    import xProgress from '@/components/x-progress/x-progress.vue';
    import CountDown from '@/components/CountDown';
    import {
        userFollow,
        userUnFollow
    } from '@/api/yuanshi/user.js';
    import {
        reportActiveDetail
    } from '@/utils/ReportAnalytics.js';
    import storage from '@/utils/storage.js';
    import {
        openCommunityActiveSubscribe
    } from '@/utils/SubscribeMessage.js';
    import playAudioControl from '@/mixins/playAudio.js';
    import {
        activityDetail,
        activityComment,
        activityMessage,
        activityMessageLike,
        activityMessageUnLike,
        activityCheckBuylimit,
        activityMessageDel,
        activityAddWish
    } from '@/api/community';
    import {
        RegPhoneAndFixed
    } from '@/utils/validate';

    // #ifdef H5
    import {
        isWeixin
    } from '@/utils/validate.js';
    const _isWeixin = isWeixin();
    import {
        openShareAll
    } from '@/utils/wechat/share.js';
    // #endif
    // #ifdef MP
    const _isWeixin = true;
    // #endif


    export default {
        mixins: [yuanshiDetail, goMap, playAudioControl],
        components: {
            xSwiper,
            xComment,
            xLabel,
            xRecom,
            xProgress,
            CountDown,
            wrapVip,
            xChat,
            Agreement
        },
        data() {
            return {
                isActiveAdmin:false,
                countobj:{
                    day:0,
                    hou:0,
                    min:0,
                    sec:0
                },
                // #ifdef MP-WEIXIN
                from: 'routine',
                // #endif
                // #ifdef MP-TOUTIAO
                from: 'bytedance',
                // #endif
                // #ifdef H5
                from: _isWeixin ? 'weixin' : 'weixinh5',
                // #endif
                detail: {
                    activityInfo: {
                        slider_image: [],
                        is_follow: false
                    },
                    storeSeckillInfo: {
                        isSeckillEnd: {},
                        storeInfo: {}
                    },
                    official_comment: {},
                    final_payment_info:[]
                },
                mixinsParam: {
                    wishId: 12,
                    productId: 76
                },
                messageInfo: [],
                opacity: 0.5,
                scrollLeft: 0,
                initCalcItemScroll: 0,
                scrollDetail: {},
                pannelShow: false,
                pannelInfo: [],
                pannelInfoIdx: -1, //my:编辑删除等操作
                pannelInfo_is_scoring: 0,
                pannelIdx: -1,
                footerOpacity: 0,
                requestLoading: false,
                page: {
                    page: 1,
                    limit: 20,
                    more: true
                },
                footerHidden: false,
                uid: -1,
                placeholder: '',
                inputShow: false,
                keywords: '',
                minHeight: false,
                isOnlyMy: false,
                tipsShow: false,
                tipsShowCheck: false,
                labelShow: false,
                dotIdx: 0,
                did: 0,
                videoCtx: null,
                isFullscreen: false,
                audioPlayIdx: [],
                options: [],
                wish: {
                    city: [],
                    address: '',
                    contact_name: '',
                    phone: '',
                    comment: ''
                },
                adjustPosition: false,
                paymentType: true, //true时，测评版本直接微信支付
                agreementRead: false, // 协议是否已阅
                agreementReadpop: false,
                agreementReadtxt: '',
                inviterId: 0, // 邀请人id
                liveCountTimes:null
            };
        },
        watch: {
            autoplay(a, b) {
                console.log(a)
                // this.autoplay = a;
                if (!a) {
                    const idxs = this.audioPlayIdx;
                    this.$set(this.messageInfo[idxs[0]].audio[idxs[1]], 'play', 0)
                }
            },
        },
        methods: {
            openWeChat(){
                openWeChatCustomerService(WX_KEFU_Link,WX_ENTERPRISE_Link,true,this.detail.activityInfo.name,'pages/ycommunity/shop/detail.html?id='+ this.did,this.detail.activityInfo.image)
            },
            reEnroll() {
                const {
                    many_quota
                } = this.detail.activityInfo;
                if (many_quota > 0) {
                    activityCheckBuylimit(this.did).then(res => {
                        const {
                            data
                        } = res;
                        // console.log(data)
                        if (data.surplus_quota > 0) {
                            this.goPages('/pages/ycommunity/shop/singup?id=' + this.did + '&inviterId=' + this
                                .inviterId)
                        } else {
                            this.$showToast('已超出最大购买数量' + data.many_quota)
                        }
                    }).catch(err => {
                        this.$showToast(err.msg || err)
                    })
                } else {
                    openCommunityActiveSubscribe().then(res => {
                        this.goPages('/pages/ycommunity/shop/singup?id=' + this.did + '&inviterId=' + this
                            .inviterId, true)
                        this.closeBalancePopups()
                    })
                }
            },
            isread(e) {
                this.agreementRead = e;
                this.goPayBalance()
            },
            goPayBalance() {
                if (this.detail.final_payment_info.length <= 1) {
                    if (this.detail.activityInfo.status == 0) {
                        return this.$showToast('活动未开始，如有疑问请联系官方客服')
                    }
                    if (!this.agreementRead) {
                        this.$showToast('请先同意协议内容');
                        this.agreementReadpop = true;
                        return;
                    }
                    const {
                        status,
                        activity_time,
                        ends_time
                    } = this.detail.activityInfo;
                    const time = Date.parse(new Date()) / 1000;
                    if (status > 0 && time > ends_time) {
                        return this.$showToast('已过截止时间')
                    }
                    if (this.paymentType && _isWeixin) {
                        // #ifndef MP-TOUTIAO
                        this.toPay('weixin');
                        // #endif
                        // #ifdef MP-TOUTIAO
                        this.toPay('bytedance');
                        // #endif
                    } else {
                        this.pay = true;
                    }
                } else {
                    this.$navigator('/pages/ycommunity/order/list?type=0');
                    this.closeBalancePopups()
                }
            },
            async toPay(type) {
                var that = this;
                
                payOrderHandle(this.detail.final_payment_info[0].order_id, type, that.from, 'community')
                    .then(res => {
                        console.log('res---',res)
                        const {
                            status,
                            result
                        } = res;
                        if (status === 'WECHAT_H5_PAY') {
                            return that.$navigator('/pages/order/PaymentStatus?orderId=' + this.detail
                                .final_payment_info[0].order_id + '&status=0&source=community');
                            this.closeBalancePopups()
                        }
                        if(res.errMsg == 'requestPayment:ok'){
                            this.closeBalancePopups()
                            this.getEvaluateDetail(this.did, this.inviterId);
                            // this.detail.is_final_payment = 0;
                        }
                    })
                    .catch(err => {
                        console.log('err---',err)
                        this.$showToast(err.msg || err);
                        this.closeBalancePopups()
                    });
            },
            inputhide(e) {
                // console.log('输入聚焦',e)
                this.inputShow = false;
                this.footerHidden = false;
            },
            saveimg(url) {
                let that = this;
                uni.downloadFile({
                    url: url,
                    header: {
                        "content-type": "application/json",
                    },
                    success: (res) => {
                        console.log('res', res)
                        if (res.statusCode === 200) {
                            uni.saveImageToPhotosAlbum({
                                filePath: res.tempFilePath,
                                success(res) {
                                    return that.$showToast('已保存相册')
                                    setTimeout(function() {
                                        that.addChatGroupClose()
                                    }, 1500);
                                },
                                fail(res) {
                                    console.log(res);
                                    return that.$showToast('无相册权限')
                                    setTimeout(function() {
                                        that.addChatGroupClose()
                                    }, 1500);
                                }
                            });
                        }
                    }
                })
            },
            wishSubmit() {
                let _this = this;
                zxauthNavigator()
                if (_this.wish.address == '') {
                    return this.$showToast('请输入城市')
                }
                if (_this.wish.contact_name == '') {
                    return this.$showToast('请输入联系人')
                }
                if (_this.wish.phone <= 0) {
                    return this.$showToast('请输入电话')
                }
                if (!RegPhoneAndFixed(_this.wish.phone)) {
                    return this.$showToast('请填写正确的联系方式');
                }
                let obj = {
                    address: _this.wish.address,
                    contact_name: _this.wish.contact_name,
                    phone: _this.wish.phone,
                    comment: _this.wish.comment,
                    // #ifdef MP-TOUTIAO
                    from:'bytedance',
                    // #endif
                    // #ifndef MP-TOUTIAO
                    from :'routine',
                    // #endif
                };
                console.log('活动心愿提交参数', obj)
                activityAddWish(obj).then(res => {
                        console.log('活动心愿提交成功', res);
                        if (res.status == 200) {
                            this.$showToast('提交成功')
                            setTimeout(() => {
                                _this.wish.city = [];
                                _this.wish.address = '';
                                _this.wish.contact_name = '';
                                _this.wish.phone = '';
                                _this.wish.comment = '';
                            }, 1000)
                        }
                    })
                    .catch(err => {
                        console.log('活动心愿提交失败', err);
                    });
            },
            addChatGroup() {
                this.$refs.popups.open()
            },
            addChatGroupClose() {
                this.$refs.popups.close()
            },
            closeBalancePopups() {
                this.$refs.balancePopups.close()
            },
            fullscreenchange(item, index, index1) {
                this.isFullscreen = false
                console.log('退出全屏')
                let ref = this.videoCtx;
                ref.pause();
            },
            xPlayAudio(item, index, index1) {
                if (!item.name) {
                    return this.$showToast('播放地址不存在')
                }
                if (this.innerAudioContext) {
                    const audio = this.messageInfo[index].audio[index1];
                    this.$set(audio, 'play', Number(!audio.play))
                    if (this.innerAudioContext.src === item.name) {
                        this.innerAudioContext.pause();
                    } else {
                        const idxs = this.audioPlayIdx;
                        if (idxs.length) {
                            this.$set(this.messageInfo[idxs[0]].audio[idxs[1]], 'play', 0)
                        }
                        this.initAudioInfo()
                    }
                    this.audioPlayIdx = [index, index1]
                    this.playAudio(item.name)
                }
            },
            playVideo(item, index, index1) {
                this.innerAudioContext.pause();
                let ref = uni.createVideoContext(`playVideo${index}-${index1}`);
                this.videoCtx = ref;
                ref.requestFullScreen();
                let tid = setTimeout(() => {
                    this.isFullscreen = true;
                    ref.play();
                    clearTimeout(tid)
                }, 500)
            },
            dotChange(idx) {
                this.dotIdx = idx;
            },
            tipsShowCheckClick() {
                this.tipsShowCheck = !this.tipsShowCheck;
                storage.set('tipsShowCheck', this.tipsShowCheck);
            },
            isTipsShow() {
                this.isOnlyMy = false;
                this.onlyShowMy(true);
                this.tipsShow = storage.get('tipsShowCheck') ? false : true;
            },
            showInput(type = 1, item) {
                const {
                    id
                } = item;
                // console.log(this.$refs['xComment' + id]);
                this.inputShow = true;
                this.footerHidden = true;
                this.xComment = this.$refs['xComment' + id][0];
                this.xComment.showInput(type, item, null, null);
            },
            onlyShowMy(typ = false) {
                this.messageInfo = [];
                this.page = {
                    page: 1,
                    limit: this.page.page,
                    more: true
                };
                if (!typ) {
                    this.isOnlyMy = !this.isOnlyMy;
                }
                if (this.isOnlyMy) {
                    this.sendSuccess();
                }
                this.keywords = '';
                this.getComment();
            },
            chatInput({
                id,
                uid
            }, placeholder) {
                this.inputShow = true;
                this.footerHidden = true;
                this.uid = uid;
                this.xComment = this.$refs['xComment' + id][0];
                this.placeholder = placeholder;
            },
            submitComment(val) {
                this.xComment.submitComment(val);
            },
            sendSuccess() {
                this.inputShow = false;
                this.footerHidden = false;
                this.opacity = 1;
            },
            shareComment(item) {
                this.shareConfig = {
                    desc: item.comment,
                    title: item.title,
                    link: '/pages/ycommunity/comment/detail?id=' + item.id,
                    imgUrl: item.image.length ? item.image[0].name : (item.video.length ? item.video[0].name +
                        '?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto' : '')
                };
                let timeId = setTimeout(() => {
                    this.shareConfig = this.copyShareConfig;
                }, 8000)
                // #ifdef H5
                openShareAll(this.shareConfig);
                // #endif
                // #ifdef MP
                this.$showToast('右上角分享')
                wx.hideShareMenu({
                    menus: ['shareTimeline'],
                    success(res) {
                        setTimeout(() => {
                            wx.showShareMenu({
                                withShareTicket: true,
                                menus: ['shareAppMessage', 'shareTimeline'],
                            })
                        }, 1500)
                    }
                })
                // #endif
            },

            likeStars(item, index) {
                let obj = {
                    related_id: this.did,
                    message_id: item.id
                };
                if (item.is_like) {
                    activityMessageUnLike(obj).then(res => {
                        this.messageInfo[index].like_count--;
                        this.messageInfo[index].is_like = !item.is_like;
                    });
                } else {
                    activityMessageLike(obj).then(res => {
                        this.messageInfo[index].like_count++;
                        this.messageInfo[index].is_like = !item.is_like;
                    });
                }
            },
            goSingUp() {
                // #ifdef MP-TOUTIAO
                this.$refs.popups.open()
                return this.$showToast('抖音平台用户，请加入群聊后再进行报名')
                // #endif
                if ( (this.detail.activityInfo.status == 1 || this.detail.activityInfo.status == 2) && this.detail.is_final_payment) {
                    this.$refs.balancePopups.open()
                    return;
                }
                const {
                    many_quota
                } = this.detail.activityInfo;
                if (many_quota > 0) {
                    activityCheckBuylimit(this.did).then(res => {
                        const {
                            data
                        } = res;
                        // console.log(data)
                        if (data.surplus_quota > 0) {
                            this.goPages('/pages/ycommunity/shop/singup?id=' + this.did + '&inviterId=' + this
                                .inviterId)
                        } else {
                            this.$showToast('已超出最大购买数量' + data.many_quota)
                        }
                    }).catch(err => {
                        this.$showToast(err.msg || err)
                    })
                } else {
                    openCommunityActiveSubscribe().then(res => {
                        this.goPages('/pages/ycommunity/shop/singup?id=' + this.did + '&inviterId=' + this
                            .inviterId, true)
                    })
                }
            },
            goPages(path, type, pathType) {
                if (type) {
                    this.$authNavigator(path);
                } else {
                    this.$navigator(path, pathType);
                }
            },
            navClick(type) {
                let initCalcItemScroll = this.initCalcItemScroll,
                    oldScrollLeft = this.scrollDetail.scrollLeft || 0;
                if (type === 'left') {
                    if (oldScrollLeft > 0) {
                        this.scrollLeft = oldScrollLeft > initCalcItemScroll ? oldScrollLeft - initCalcItemScroll : 0;
                    }
                } else {
                    this.scrollLeft = oldScrollLeft + initCalcItemScroll;
                }
            },
            scrollX(e) {
                this.scrollDetail = e.detail;
            },

            getEvaluateDetail(did, inviterId) {
                let from = '';
                // #ifndef MP-TOUTIAO
                from = 'routine';
                // #endif
                // #ifdef MP-TOUTIAO
                from = 'bytedance';
                // #endif
                activityDetail(did, from, inviterId)
                    .then(res => {
                        console.log('获取活动成功', res)
                        this.detail = res.data;
                        if(this.detail.activityInfo.is_manage == 1 && this.$store.state.userInfo.is_official_role == 1){
                            this.isActiveAdmin = true;
                        }
                        if(this.detail.activityInfo.is_countdown){
                            let time = this.detail.activityInfo.end_discount_time * 1000;
                            this.getLiveTimeCount(time)
                        }
                        const {
                            activityInfo
                        } = res.data;
                        this.$updateTitle(activityInfo.name);
                        this.shareConfig = {
                            desc: activityInfo.introduction,
                            title: activityInfo.name,
                            link: '/pages/ycommunity/shop/detail?id=' + did,
                            imgUrl: activityInfo.slider_image[0]
                        };
                        this.copyShareConfig = this.shareConfig;
                        // #ifdef H5
                        openShareAll(this.shareConfig);
                        // #endif
                        reportActiveDetail({
                            id: did,
                            store_name: activityInfo.name,
                            uid: this.$store.state.userInfo.uid || 0
                        });
                    })
                    .catch(err => {
                        console.log('获取活动失败', err)
                        this.$navigator(-1);
                    });

                this.getComment();
            },
            getComment() {
                if (!this.page.more || this.requestLoading) return;
                this.requestLoading = true;
                let obj = {
                        related_id: this.did,
                        page: this.page.page,
                        limit: this.page.limit
                    },
                    req = '';
                if (this.isOnlyMy) {
                    req = evaluationMe(obj);
                } else {
                    req = activityMessage(obj);
                }
                req.then(res => {
                        let storeInfo = res.data;
                        if (!this.minHeight) {
                            this.minHeight = storeInfo.length ? true : false;
                        }
                        storeInfo.forEach((item, index) => {
                            if (typeof item.comment === 'string') {
                                item.content = [item.comment];
                            }
                        });
                        this.messageInfo = this.messageInfo.concat(storeInfo);
                        this.page.more = res.data.length === this.page.limit;
                        this.page.page++;
                        this.requestLoading = false;
                    })
                    .catch(err => {
                        console.log('activityMessage', err);
                        this.requestLoading = false;
                    });
            },
            // btnAttention() {
            // 	let product_id = this.mixinsParam.productId,
            // 		is_follow = this.detail.storeInfo.is_follow,
            // 		follow_test_number = this.detail.storeInfo.follow_test_number;
            // 	console.log('关注相关操作');
            // 	if (is_follow) {
            // 		evaluationUnAttention(product_id).then(res => {
            // 			this.detail.storeInfo.is_follow = false;
            // 			this.detail.storeInfo.follow_test_number = follow_test_number > 1 ? follow_test_number -
            // 				1 : 0;
            // 			this.$showToast('已取消');
            // 		});
            // 	} else {
            // 		evaluationAttention(product_id).then(res => {
            // 			this.detail.storeInfo.is_follow = true;
            // 			this.detail.storeInfo.follow_test_number = follow_test_number + 1;
            // 			this.$showToast('关注成功');
            // 		});
            // 	}
            // },
            goFollow(type, id, idx) {
                if (this.$store.state.userInfo.uid === id) {
                    return this.$showToast('不能对自己进行相关操作');
                }
                if (type) {
                    userUnFollow(id).then(res => {
                        this.detail.RegisteredList[idx].is_follow = false;
                        this.$showToast('已取消');
                    });
                } else {
                    userFollow(id).then(res => {
                        this.$showToast('跟随成功');
                        this.detail.RegisteredList[idx].is_follow = true;
                    });
                }
            },
            previewImage(urls, current) {
                uni.previewImage({
                    urls: urls.map(item => item.name),
                    current: current //地址需为https
                });
            },

            updateData() {
                this.getEvaluateDetail(this.did, this.inviterId);
                this.messageInfo = [];
                this.page = {
                    page: 1,
                    limit: this.page.limit,
                    more: true
                };
                this.getComment();
            },
            delMyEvaluate(item, index) {
                let _this = this;
                this.$showModal('提示', '是否要删除', {
                    success: function(res) {
                        console.log(res);
                        if (res.confirm) {
                            activityMessageDel(item.id)
                                .then(res => {
                                    _this.$showToast('删除成功');
                                    _this.messageInfo.splice(index, 1);
                                })
                                .catch(err => {
                                    _this.$showToast('删除失败');
                                });
                        }
                    },
                    fail: function() {}
                });
            },
            getLiveTimeCount(stime) {
                this.liveCountTimes = setInterval(() => {
                    let nowTime = new Date().getTime();
                    let preTime = stime;
                    let obj = null;
                    if (preTime - nowTime > 0) {
                        let time = (preTime - nowTime) / 1000;
                        let day = parseInt(time / (60 * 60 * 24));
                        let hou = parseInt(time % (60 * 60 * 24) / 3600);
                        let min = parseInt(time % (60 * 60 * 24) % 3600 / 60);
                        let sec = parseInt(time % (60 * 60 * 24) % 3600 % 60);
                        obj = {
                            day: day < 10 ? '0' + day : day,
                            hou: hou < 10 ? '0' + hou : hou,
                            min: min < 10 ? '0' + min : min,
                            sec: sec < 10 ? '0' + sec : sec
                        };
                        this.liveCountdown = obj.day + '天' + obj.hou + '时' + obj.min + '分' + obj.sec + '秒'
                        this.$forceUpdate();
                        this.countobj = obj;
                        
                    } else {
                        obj = {
                            day: '00',
                            hou: '00',
                            min: '00',
                            sec: '00'
                        };
                        this.countobj = obj;
                        clearInterval(this.liveCountTimes)
                    }
                }, 1000)
            },
        },
        onHide(){
            
        },
        onUnload(){
            clearInterval(this.liveCountTimes)
        },
        onPageScroll(e) {
            this.inputShow = false;
            this.footerHidden = false;
            this.opacity = (e.scrollTop / 400).toFixed(1);
            if (e.scrollTop > 200) {
                this.footerOpacity = (e.scrollTop / 400).toFixed(1);
            } else {
                this.footerOpacity = 0;
            }
        },
        mounted() {
            let _this = this;
            uniSelectorQueryInfo('#calcItem', _this)
                .then(res => {
                    _this.initCalcItemScroll = res.width;
                })
                .catch(err => {});
        },
        onLoad(option) {
            const {
                id = 1,
                inviterId
            } = option;
            this.did = id;
            console.log('活动id', this.did)
            console.log('邀请人id', inviterId)
            if (inviterId && inviterId != 'undefined') {
                this.inviterId = Number(inviterId);
            }
        },
        onShow() {
            // #ifndef MP-TOUTIAO
            this.adjustPosition = false
            // #endif
            // #ifdef MP-TOUTIAO
            this.adjustPosition = true
            // #endif
            this.getEvaluateDetail(this.did, this.inviterId);
            this.getComment();
            
            this.initAudioInfo();
            this.audioPlayIdx = [];
            if (this.innerAudioContext) {
                this.innerAudioContext.pause();
            }
        },
        onReachBottom() {
            if (this.messageInfo.length) {
                this.getComment();
            }
        },
        onHide() {
            if (this.audioPlayIdx.length) {
                this.destoryAudioCtx();
                const index = this.audioPlayIdx[0],
                    index1 = this.audioPlayIdx[1];
                const audio = this.messageInfo[index].audio[index1];
                this.$set(audio, 'play', 0)
            }

        },
        // #ifdef MP
        onShareAppMessage() {
            console.log('分享', this.shareConfig)
            return {
                title: this.shareConfig.title,
                imageUrl: this.shareConfig.imgUrl,
                path: this.shareConfig.link + '&share=share',
                templateId: SHARE_ID
            };
        },
        // 朋友圈分享不支持自定义页面路径
        onShareTimeline() {
            if (this.shareConfig) {
                return {
                    title: this.shareConfig.title,
                    imageUrl: this.shareConfig.imgUrl,
                    query: this.shareConfig.link.split('?')[1]
                };
            }
        }
        // #endif
    };
</script>
<style>
    page {
        background: #f7f7f9;
    }
</style>
<style scoped lang="scss">
    .bottom_line {
        height: 2rpx;
        background: #e2e6ec;
        margin: 0 48rpx;
    }

    button {
        width: 300rpx;
        margin: 30rpx auto;
    }

    .swiper {
        .swiper-set {
            position: absolute;
            top: 20rpx;
            right: 20rpx;
            z-index: 99;

            image {
                width: 50rpx;
                height: 50rpx;
            }
        }

        .wrap {
            color: #fff;
            padding: 50rpx 48rpx;
            bottom: 0rpx;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.46) 100%);

            image {
                width: 100%;
                height: 100%;

                border-radius: 4rpx;
            }

            .dot {
                width: 96rpx;
                height: 44rpx;
                border: 1rpx solid #ffffff;
                border-radius: 32rpx;
                font-size: 24rpx;
            }

            .name {
                font-size: 56rpx;
                margin-top: 40rpx;
                margin-bottom: 12rpx;
            }

        }
    }

    .active {
        image {
            width: 100%;
            height: 100%;
        }

        .icon_more {
            font-size: 24rpx;

            color: #999999;

            .iconfont {
                font-size: 24rpx;
                color: #555555;
            }
        }

        .padding {
            padding: 32rpx 36rpx 32rpx 34rpx;
            margin: 0 20rpx 20rpx 20rpx;
            background: #fff;
        }

        .main {
            position: absolute;
            width: 100%;
            margin-top: -18rpx;

            .wrap {
                border: 2rpx solid #fc5656;
                border-radius: 30rpx;
                box-shadow: 0px 0px 40rpx 0px rgba(107, 127, 153, 0.20);
                min-height: 454rpx;
                margin: 0 20rpx;

                .wrap_t {
                    min-height: 152rpx;
                    background: #f2f5f8;
                    border: 4rpx solid #ffffff;
                    border-radius: 0px 0px 30rpx 30rpx;
                    padding: 32rpx;
                    border-radius: 30rpx 30rpx 0 0;

                    .t_l {
                        .l1 {
                            font-size: 32rpx;
                            font-weight: 700;
                            text-align: left;
                            color: #333333;

                            .time {
                                padding: 4rpx 0;
                                font-size: 28rpx;
                                font-weight: 400;
                                color: red;
                                color: #ff5656;
                                line-height: 40rpx;

                                &.black {
                                    color: #000000;
                                }
                            }
                        }

                        .l2 {
                            margin-top: 6rpx;
                            font-size: 24rpx;
                            font-weight: 400;
                            text-align: left;
                            color: #666666;
                        }
                    }

                    .t_r {
                        button {
                            width: 184rpx;
                            height: 76rpx;
                            background: #ffffff;
                            border: 2rpx solid #d2d2d2;
                            border-radius: 30rpx;

                            image {
                                width: 30rpx;
                                margin-right: 12rpx;
                            }
                        }
                    }
                }

                .wrap_c {
                    margin: 28rpx 28rpx 22rpx 36rpx;

                    .c_l {
                        font-size: 24rpx;

                        .label {
                            font-weight: bold;
                            font-size: 28rpx;
                        }
                    }

                    .c_r {
                        image {
                            width: 28rpx;
                            height: 32rpx;
                        }
                    }
                }

                .wrap_b {
                    border-top: 2rpx solid #e2e6ec;
                    margin: 28rpx 28rpx 22rpx 36rpx;
                    padding: 28rpx 0 22rpx 0;
                    font-size: 24rpx;
                    font-weight: 400;
                    text-align: left;
                    color: #666666;

                    .desc {
                        flex: 1;
                    }

                    .addChat {
                        margin-left: 32rpx;
                        width: 136rpx;
                        height: 50rpx;
                        line-height: 50rpx;
                        background: #50506f;
                        border-radius: 24rpx;
                        font-size: 24rpx;
                        font-weight: 400;
                        text-align: center;
                        color: #ffffff;
                    }
                }


            }


            .show_title {
                font-weight: 400;
                background: none;
                margin-top: 70rpx;
                padding: 0 64rpx 0 48rpx;

                .title {
                    font-size: 32rpx;
                    font-weight: bold;
                    color: #333333;
                }

                .desc {
                    margin: 40rpx 0 60rpx 0;
                    @include show_line(5);
                }

                .conter {
                    margin: 40rpx 0 60rpx 0;
                }

                .wish {
                    width: 100%;
                    height: auto;
                    opacity: 1;
                    background: #ffffff;
                    border-radius: 30rpx;
                    box-shadow: 0 0 40rpx 0 rgba(107, 127, 153, 0.2);
                    margin: 0 auto;
                    padding: 64rpx 40rpx;
                    box-sizing: border-box;

                    &.relative {
                        margin-top: 30rpx;
                    }

                    .wish-title {
                        width: 100%;
                        text-align: center;
                        font-size: 32rpx;
                        font-weight: bold;
                        color: #333333;
                    }

                    .wish-title2 {
                        width: 100%;
                        font-size: 28rpx;
                        color: #000;
                        margin: 32rpx auto;
                    }

                    .wish-submit {
                        width: 220rpx;
                        text-align: center;
                        font-size: 24rpx;
                        height: 60rpx;
                        line-height: 60rpx;
                        color: #fff;
                        background-color: #0081ff;
                        margin: 0 auto;
                        border-radius: 10rpx;
                    }

                    .wish-item {
                        width: 100%;
                        font-size: 24rpx;
                        margin-bottom: 20rpx;
                        overflow: hidden;

                        .wish-item-title {
                            float: left;
                            width: 148rpx;
                            font-size: 24rpx;
                            padding-top: 10rpx;
                            box-sizing: border-box;
                        }

                        .wish-item-centen {
                            float: right;
                            width: 400rpx;

                            &.relative {
                                position: relative;
                            }

                            .input {
                                border: 2rpx solid #9999995e;
                                height: 56rpx;
                                border-radius: 8rpx;
                                padding: 0 20rpx;
                                box-sizing: border-box;
                            }

                            .textarea {
                                border: 2rpx solid #9999995e;
                                background-color: #f9f9f9;
                                border-radius: 8rpx;
                                box-sizing: border-box;
                                width: 100%;
                                height: 150rpx;
                                border-radius: 8rpx;
                                padding: 20rpx;
                                margin: 0 0 20rpx;
                                font-size: 24rpx;

                                &.relative {
                                    margin: 0;
                                    padding: 20rpx 20rpx 40rpx;
                                }
                            }

                            .wish-item-centen-text {
                                position: absolute;
                                right: 20rpx;
                                bottom: 12rpx;
                            }
                        }
                    }
                }
            }

            .want_go {
                margin: 60rpx 48rpx;
                padding-top: 70rpx;
                min-height: 320rpx;
                border-top: 1px solid #e2e6ec;

                .nav {
                    .title {
                        color: #333333;
                        font-weight: bold;
                        font-size: 32rpx;
                    }

                    .go {
                        width: 148rpx;
                        height: 60rpx;
                        border: 2rpx solid #ff5656;
                        border-radius: 24rpx;
                        margin: 0 64rpx 0 0;
                        color: #ff5656;
                        font-size: 24rpx;
                    }
                }

                .wrap1 {
                    margin: 50rpx 0 0 0;

                    // border:none;
                    // min-height: auto;
                    .item {
                        display: inline-block;

                        &:not(:last-child) {
                            padding-right: 8rpx;
                        }

                        image {
                            width: 120rpx;
                            height: 120rpx;
                            border-radius: 50%;
                            background-color: #fff;
                        }

                        .name {
                            text-align: center;
                            margin: 16rpx 0;
                            color: #999999;
                            font-weight: bold;
                            height: 40rpx !important;
                            font-size: 28rpx;
                            width: 120rpx;
                            @include show_line;
                        }

                        .btn {
                            width: 94rpx;
                            height: 40rpx;
                            border: 2rpx solid #d2d2d2;
                            border-radius: 14rpx;
                            margin: 0 auto;
                            color: #999999;
                        }
                    }
                }
            }

            .goods {
                padding: 70rpx 0 70rpx 48rpx;
            }

            .message {
                margin-top: 70rpx;
                margin-bottom: 200rpx;
                min-height: 500rpx;

                &.minHeight {
                    min-height: 100vh;
                }

                .bottom_line {
                    height: 2rpx;
                    background: #e2e6ec;
                    margin: 0 30rpx;
                }

                .mess_tab {
                    padding: 0rpx 48rpx 32rpx 48rpx;

                    .mess {
                        font-size: 32rpx;

                        color: #333333;
                        font-weight: bold;

                        text {
                            padding: 0 16rpx;

                            font-weight: 400;
                            color: #999999;
                        }
                    }

                    .write {
                        width: 148rpx;
                        height: 60rpx;
                        border: 2rpx solid #ff5561;

                        background: #ff5656;
                        color: #fff;
                        border-radius: 24rpx;

                        text {
                            display: inline-block;
                            font-size: 24rpx;
                        }

                        .tips {
                            z-index: 999;
                            width: 356rpx;
                            height: 150rpx;
                            background: #ffffff;
                            border-radius: 30rpx;
                            box-shadow: 0px 0px 30rpx 0px rgba(107, 127, 153, 0.3);
                            text-align: left;
                            color: #50506f;
                            padding: 22rpx 50rpx;
                            bottom: -185rpx;
                            font-family: PingFang SC, PingFang SC-Regular;
                            font-weight: 400;

                            .txt {
                                font-size: 32rpx;
                                margin-bottom: 20rpx;
                            }

                            .txt1 {
                                font-size: 24rpx;

                                .image {
                                    margin-right: 10rpx;
                                    height: 36rpx;

                                    image {
                                        width: 36rpx;
                                        height: 36rpx;
                                    }
                                }

                                .btn {
                                    padding: 4rpx 26rpx;
                                    border: 2rpx solid #50506f;
                                    border-radius: 18rpx;
                                    transform: scale(0.82);
                                    transform-origin: right;
                                }
                            }

                            &::before {
                                position: absolute;
                                content: '';
                                z-index: 999999;
                                width: 0rpx;
                                height: 0rpx;
                                top: -30rpx;
                                right: 40%;
                                // background: red;
                                border-bottom: 40rpx solid #fff;
                                border-left: 10rpx solid transparent;
                                border-right: 30rpx solid transparent;
                                transform: skew(35deg);
                            }
                        }

                        &.item {
                            color: #ff5656;
                            background: none;
                            margin-right: 20rpx;

                            &.active {
                                border: 2rpx solid #50506f;
                                color: #50506f;
                            }
                        }
                    }
                }

                .mess_nav {
                    margin: 0 31rpx;
                    padding: 20rpx 0 24rpx 0;

                    // border-top: 2rpx solid #d8d8d8;
                    // border-bottom: 2rpx solid #d8d8d8;
                    .nav {
                        .item {
                            flex: 1;
                            color: #333333;
                            text-align: center;

                            .num {
                                color: #999999;
                            }

                            &:not(:last-child) {
                                border-right: 2rpx solid #d8d8d8;
                            }

                            .iconfont {
                                font-size: 24rpx;
                                font-weight: bold;
                            }
                        }
                    }


                }

                .message_wrap {
                    padding: 0 54rpx 0 48rpx;
                    background: none;

                    >view.item {
                        padding-bottom: 20rpx;

                        >view:not(.mess_avatar) {
                            padding-left: 52rpx;
                        }

                        &:not(:last-child) {
                            border-bottom: 2rpx solid #e2e6ec;
                        }
                    }

                    .mess_avatar {
                        margin: 50rpx 0 20rpx 0;
                        padding-bottom: 14rpx;

                        .avatar {
                            image {
                                width: 60rpx;
                                height: 60rpx;
                                border-radius: 50%;
                                margin-right: 32rpx;
                            }

                            text {
                                color: #101010;
                                font-size: 28rpx;
                            }
                        }

                        .btn {
                            width: 108rpx;
                            height: 50rpx;
                            margin-left: 20rpx;
                            border: 1px solid rgba(0, 0, 0, 0.078);
                            border-radius: 32rpx;

                            font-size: 24rpx;

                            color: #666666;
                        }
                    }

                    .score {
                        margin-bottom: 40rpx;

                        .txt {
                            padding: 8rpx 16rpx;

                            border-radius: 20rpx;
                            border: 2rpx solid #ff5656;
                            color: #ff5656;
                            // letter-spacing: 12rpx;
                            font-weight: 500;
                            font-size: 28rpx;
                            margin-right: 12rpx;
                        }

                        .process {
                            // width: 276rpx;
                            height: 58rpx;
                            padding: 14rpx 20rpx;
                            background: #ffffff;
                            opacity: 1;
                            border-radius: 20rpx;

                            .num {
                                margin-left: 14rpx;
                                color: #ff5656;
                                font-weight: bold;
                                font-size: 40rpx;
                            }
                        }
                    }

                    .label {
                        ::deep.xlabel {
                            >.wrap {
                                margin-bottom: 36rpx;
                                background: none;

                                .flex {
                                    background: none;
                                }

                                .more {
                                    width: 60rpx;
                                    height: 60rpx;
                                    border-radius: 30rpx;

                                    text {
                                        width: 60rpx;
                                        height: 60rpx;
                                        border-radius: 30rpx;
                                    }
                                }
                            }
                        }
                    }

                    .mess_des {
                        margin-top: 42rpx;
                        font-size: 24rpx;
                        line-height: 36rpx;
                        color: #101010;
                        margin-bottom: 20rpx;

                        .del {
                            text-align: right;
                            color: #999;
                        }

                        .refining_span {
                            position: absolute;
                            border: 1rpx solid red;
                            top: -40rpx;
                            right: 20rpx;
                            color: red;
                            padding: 2rpx 30rpx;
                            font-weight: bold;
                            font-size: 30rpx;
                            letter-spacing: 6rpx;
                            transform: rotate(-30deg);
                            opacity: 0.6;
                            border-radius: 10rpx;
                        }
                    }

                    .mess_image {
                        height: 148rpx;
                        margin-bottom: 20rpx;
                        margin-left: -20rpx;

                        .nav {
                            width: 20rpx;
                            line-height: 148rpx;
                            text-align: center;

                            text {
                                font-size: 24rpx;
                                font-weight: bolder;
                            }
                        }

                        .wrap1 {
                            width: calc(100% - 40rpx);

                            .item {
                                // display: inline-block;
                                width: 136rpx;
                                height: 136rpx;

                                &:not(:last-child) {
                                    margin-right: 6rpx;
                                }

                                image {
                                    width: 136rpx;
                                    height: 136rpx;
                                }

                                &.video {
                                    position: relative;

                                    .cover {
                                        position: absolute;
                                        width: 100%;
                                        height: 100%;
                                        z-index: 10;
                                        top: 0;

                                        image {
                                            width: 120rpx;
                                            height: 120rpx;
                                        }
                                    }
                                }

                                &.audio {
                                    // .cover {
                                    display: flex;
                                    align-items: center;
                                    justify-content: space-around;

                                    image {
                                        width: 120rpx;
                                        height: 120rpx;
                                    }

                                    // }
                                }
                            }
                        }
                    }

                    .my_handle {
                        background: #fff;
                        padding: 12rpx 10rpx 12rpx 82rpx;
                        margin-bottom: 30rpx;
                        border-radius: 32rpx;
                        box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);

                        .txt {
                            color: #ff5656;
                        }

                        .btn {
                            width: 152rpx;
                            height: 60rpx;
                            background: #eaeaea;
                            opacity: 1;
                            border-radius: 24rpx;

                            color: #50506f;
                            font-size: 24rpx;
                            margin-left: 8rpx;
                        }
                    }

                    .mess_handle {
                        font-size: 24rpx;
                        color: #ff5656;
                        margin: 32rpx 0 40rpx 0;

                        .time {
                            color: #999999;
                        }

                        .item {
                            image {
                                width: 34rpx;
                                height: 24rpx;
                            }

                            text {
                                padding: 0 16rpx;
                                font-size: 24rpx;

                                color: #ff5656;
                            }
                        }
                    }

                    .comment {
                        padding-left: 0rpx !important;
                        margin-left: 52rpx;
                        background: #eaeaea;

                        max-height: 600rpx;
                        overflow-y: scroll;
                    }
                }
            }
        }

        .notice-bar {
            @include fixed_footer(180rpx);
            &.hide {
                display: none;
            }
            .notice-bar-box {
                width: 724rpx;
                height: 72rpx;
                line-height: 72rpx;
                border: 2rpx solid #e93323;
                border-radius: 12rpx 12rpx 0 0;
                background-color: #fff;
                margin: 0 auto;
                line-height: 72rpx;
                
                font-size: 20rpx;
                font-family: PingFang SC, PingFang SC-Regular;
                font-weight: 400;
                text-align: left;
                color: #fc5756;
                padding: 0 40rpx;
                box-sizing: border-box;
                .text {
                    display: inline-block;
                    width: 32rpx;
                    height: 32rpx;
                    line-height: 32rpx;
                    font-size: 20prx;
                    text-align: center;
                    font-family: PingFang SC, PingFang SC-Regular;
                    font-weight: 400;
                    border-radius: 4rpx;
                    color: #ffffff;
                    background-color: #fd5757;
                    margin: 0 6rpx;
                }
            }
        }

        .active_footer {
            @include fixed_footer(112rpx);
            box-shadow: 0px 0px 40px 0px rgba(107, 127, 153, 0.20);
            background: #f7f8fa;

            &.hide {
                display: none;
            }

            .flex_l {
                width: calc(100% - 400rpx);
                padding: 0 60rpx;

                .item {
                    // width: calc((100% - 400rpx) / 2);
                    width: 50%;
                    text-align: center;
                    font-size: 24rpx;

                    color: #3e3e3e;

                    image {
                        width: 40rpx;
                        height: 40rpx;
                    }
                }
            }

            .btn {
                width: 400rpx;
                height: 112rpx;
                line-height: 112rpx;
                text-align: center;

                border-radius: 30rpx 0px 0px 0px;
                font-size: 32rpx;
                background: #666666;
                color: #ffffff;
                font-weight: bold;

                .btn_r {
                    background: #ff5656;
                    border-radius: 30rpx 0px 0px 0px;
                }
            }
        }
    }

    .tt_text_left {
        width: 100%;
        height: auto;
        overflow: hidden;
        padding: 0 20rpx;
        margin-top: 20rpx;
    }

    .tt_text_left_btn1 {
        float: left;
        width: 150rpx;
        height: 70rpx;
        margin: 0 auto;
        border: 1rpx solid #e6e6e6;
        border-radius: 10rpx;
        text-align: center;
        line-height: 70rpx;
    }

    .tt_text_left_btn2 {
        float: right;
        width: 150rpx;
        height: 70rpx;
        background-color: #e93323;
        color: #fff;
        font-size: 28rpx;
        border-radius: 10rpx;
        text-align: center;
        line-height: 70rpx;
    }

    .wechatGroup-img {

        image {
            display: block;
            width: 300rpx;
            height: 300rpx;
            margin: 0 auto;
        }
    }

    // #ifdef MP-TOUTIAO
    .tt_margin-right {
        margin-right: 28rpx;
    }

    // #endif
    .balance-box {
        position: relative;
        width: 600rpx;
        background-color: #fff;
        border-radius: 20rpx;
        padding: 40rpx 30rpx 50rpx;
        box-sizing: border-box;

    }

    .balance-box-close {
        position: absolute;
        top: 2rpx;
        right: 2rpx;
        width: 50rpx !important;
        height: 50rpx !important;
    }

    .balance-box-title {
        width: 100%;
        text-align: center;
        color: #000;
        font-size: 28rpx;
        margin-top: 50rpx;

        view {
            margin-bottom: 20rpx;
        }
    }

    .balance-box-btn {
        width: 100%;
        margin-top: 60rpx;
        overflow: auto;

        view {
            width: 240rpx;
            height: 88rpx;
            line-height: 88rpx;
            text-align: center;
            background-color: #fc5656;
            font-size: 26rpx;
            color: #fff;
            border-radius: 12rpx;
        }

        .balance-box-btn-left {
            float: left;
        }

        .balance-box-btn-right {
            float: right;
        }
    }
</style>
