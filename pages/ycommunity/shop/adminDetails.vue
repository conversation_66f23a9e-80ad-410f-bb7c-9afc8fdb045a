<template>
    <view class="admin-box">
        <view class="details-title">
            时间：{{details.start_times}} - {{details.end_times}}
        </view>
        <view class="details-box">
            <view class="details-box-title">
                设定可面试时间
            </view>
            <view class="details-box-center">
                <block v-for="(item, index) in details.interview_time_list" :key="index">
                    <view class="content-item">
                        {{item}}
                    </view>
                </block>
            </view>
            <view class="dividers"></view>
        </view>
        <view class="details-box">
            <view class="details-box-title">
                面试用户名单
            </view>
            <view class="details-box-center">
                <block v-for="(item, index) in details.user_list" :key="index">
                    <view class="content-item">
                        {{item}}
                    </view>
                </block>
            </view>
            <view class="dividers"></view>
        </view>
        <view class="details-title number">
            <view class="details-title-left">
                已约人数：{{details.approxs}}人
            </view>
            <view class="details-title-right" @click="download(details.reservation_list_file)">
                下载预约清单
            </view>
        </view>
        <view class="details-box">
            <view class="details-box-title">
                预约日志
            </view>
            <view class="details-box-center">
                <block v-for="(item, index) in details.logs" :key="index">
                    <view class="content-item">
                        {{item}}
                    </view>
                </block>
            </view>
            <view class="dividers"></view>
        </view>
    </view>
</template>

<script>
    import {
        eventAdd,
        eventProgramList,
        eventOrderList,
        eventDetail
    } from '@/api/community';
    export default {
        props: {},
        data: function() {
            return {
                did: 0, // 活动id
                appointId: 0, //预约计划表id
                details:{}
            };
        },
        mounted: function() {},
        onLoad(options) {
            console.log('预约页面参数', options)
            const {
                id,
                appointId
            } = options;
            this.did = id;
            this.appointId = appointId;
        },
        onShow() {
            this.getEventDetail()
        },
        methods: {
            download(urlx){
                if(!urlx){
                    return this.$showToast('请检查下载地址');
                }
                uni.downloadFile({
                    header: {
                        'Content-Type': "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=GBK",
                    },
                    url: urlx,
                    success(res) {
                        console.log(res);
                        // uni.saveFile({
                        //       tempFilePath: tempFilePaths[0],
                        //       success: function (res) {
                        //         var savedFilePath = res.savedFilePath;
                        //       }
                        //     });
                        var filePath = res.tempFilePath;
                        uni.openDocument({
                          filePath: filePath,
                          fileType:'xlsx',
                          showMenu: true,
                        // fileType: "xls",
                          success: function (res) {
                            console.log('打开文档成功');
                          }
                        });
                    },
                    fail(res) {
                        uni.showToast({
                            icon: "none",
                            title: res.errMsg,
                        });
                    },
                });
                console.log('下载清单')
            },
            getEventDetail() {
                let data = {
                    id: this.appointId
                }
                eventDetail(data)
                    .then(res => {
                        console.log('获取详情', res)
                        this.details = res.data;
                        this.details.start_times = (this.format(this.details.start_time*1000)).substring(0,10)
                        this.details.end_times = (this.format(this.details.end_time*1000)).substring(0,10)
                    })
                    .catch(err => {
                        console.log('获取失败', err)
                        this.$navigator(-1);
                    });
            },
            add0(m){return m<10?'0'+m:m },
            format(shijianchuo) {
                var time = new Date(shijianchuo);
                var y = time.getFullYear();
                var m = time.getMonth() + 1;
                var d = time.getDate();
                var h = time.getHours();
                var mm = time.getMinutes();
                var s = time.getSeconds();
                return y + '-' + this.add0(m) + '-' + this.add0(d) + ' ' + this.add0(h) + ':' + this.add0(mm) + ':' + this.add0(s);
            },
        }
    };
</script>
<style scoped lang="scss">
    .admin-box {
        width: 100%;
        padding: 40rpx 20rpx;
        box-sizing: border-box;
        overflow: auto;

        .details-title {
            width: 688rpx;
            height: 100rpx;
            line-height: 100rpx;
            background: #eff5fc;
            border: 2rpx solid #3399ff;
            border-radius: 40rpx;
            font-size: 32rpx;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: center;
            color: #3399ff;
            margin: 0 auto 32rpx;

            &.number {
                padding: 0 26rpx 0 64rpx;
                box-sizing: border-box;
                margin-bottom: 20rpx;

                .details-title-left {
                    float: left;
                }

                .details-title-right {
                    float: right;
                    width: 208rpx;
                    height: 50rpx;
                    font-size: 24rpx;
                    font-family: PingFang SC, PingFang SC-Medium;
                    font-weight: 500;
                    text-align: center;
                    color: #3399ff;
                    line-height: 50rpx;
                    background: rgba(107, 180, 255, 0.20);
                    border: 2rpx solid #6bb4ff;
                    border-radius: 24rpx;
                    margin-top: 24rpx;
                }

            }
        }

        .details-box {
            width: 710rpx;
            background: #ffffff;
            border-radius: 30rpx;
            box-shadow: 0 0 40rpx 0 rgba(107, 127, 153, 0.20);
            margin: 0 auto 20rpx;
            padding: 36rpx 20rpx 48rpx;
            box-sizing: border-box;

            .details-box-title {
                width: 100%;
                font-size: 32rpx;
                font-family: PingFang SC, PingFang SC-Semibold;
                font-weight: 600;
                text-align: left;
                color: #646464;
                padding: 0 22rpx;
                box-sizing: border-box;
                margin-bottom: 20rpx;
            }

            .details-box-center {
                width: 670rpx;
                background: #eff5fc;
                border-radius: 16rpx;
                box-sizing: border-box;
                padding: 12rpx 24rpx;
                box-sizing: border-box;

                .content-item {
                    width: 100%;
                    font-size: 22rpx;
                    font-family: PingFang SC, PingFang SC-Medium;
                    font-weight: 500;
                    text-align: left;
                    color: #646464;
                }
            }

            .dividers {
                width: 630rpx;
                height: 2rpx;
                background: #eaeaea;
                margin: 28rpx auto 0;
            }
        }


    }
</style>
