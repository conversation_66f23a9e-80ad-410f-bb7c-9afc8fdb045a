<template>
    <view class="" style="padding-bottom: 80rpx;">
        <view class="address">
            <view class="flex flex_align_center flex_between">
                <view class="info">
                    <view class="">
                        <text>活动主题：</text>{{activityInfo.introduction}}
                    </view>
                    <view v-if="activityInfo.detailed_address">
                        <text>地点：</text>{{ activityInfo.detailed_address}}
                    </view>
                    <view class="time">
                        <text>活动时间：</text>{{activityInfo.activity_time}}
                    </view>
                </view>
                <view class="block" @click="goMap(activityInfo)">
                    <image src="@/static/images/yuanshi/address.png" mode="widthFix"></image>
                </view>
            </view>
            <view class="tips info">{{activityInfo.hint}}</view>
        </view>
        <view class="wrap">
            <view class="info">
                <view class="title">
                    报名项目
                </view>
                <view style="padding: 30rpx 40rpx;">
                    <view class="info-box">
                        <image class="info-box-left" :src="activityInfo.image" mode=""></image>
                        <view class="info-box-right">
                            <view class="name">
                                {{activityInfo.name}}
                            </view>
                            <view class="des">
                                {{activityInfo.introduction}}
                            </view>
                            <view class="pre">
                                <view class="pre-left">
                                    ¥ {{activityInfo.price}}
                                </view>
                                <view class="pre-right">
                                    <block v-for="(item,index) in cartInfo" :key="item.id">
                                        X {{item.cart_num}}
                                    </block>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view class="info-box-foot">
                        <view class="info-box-foot-left">
                            <view class="">
                                共报名：{{totalNum}} 人,总金额：¥ <text
                                    class="info-box-foot-left-text">{{priceGroup.totalPrice}}</text> 元
                            </view>
                            <!-- 字节不可见 sta
                            <template v-if="activityInfo.status===0">
                                <view class="">
                                    活动目前报名中，须支付定金：{{priceGroup.dpPrice}} 元
                                </view>
                                <view class="tips">
                                    说明:报名成功后请补齐报名尾款，否则视为放弃活动名额， 截止日{{activityInfo.end_time}}
                                </view>
                            </template>  
                            字节不可见 end -->
                        </view>
                        <view class="info-box-foot-right"
                            @click="goPages('/pages/ycommunity/shop/detail?id=' + cartInfo[0].activity_id + '&inviterId=' + this.inviterId)">
                            查看详情
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="concats" style="position: relative; z-index: 99;">
            <view class="concat-title">
                <view class="concat-title-left">联系人</view>
                <view class="concat-title-right" @click="addressTap">地址薄</view>
            </view>

            <view class="concat-item">
                <view class="concat-item-left">姓名</view>
                <input class="concat-item-right" type="text" placeholder-style="font-size: 16px; color:#d2d2d2"
                    placeholder="请输入姓名" v-model="contacts" />
            </view>
            <view class="concat-item">
                <view class="concat-item-left">联系电话</view>
                <input class="concat-item-right" type="text" placeholder-style="font-size: 16px;color:#d2d2d2"
                    placeholder="请输入联系电话" v-model.number="contactsTel" />
            </view>
            <view v-if="activityInfo.is_spare" class="concat-item">
                <view class="concat-item-left">备用联系方式</view>
                <input class="concat-item-right" type="text" placeholder-style="font-size: 16px;color:#d2d2d2"
                    v-model="alternateContact" placeholder="微信号或QQ号" />
            </view>
            <view v-if="activityInfo.is_channels" class="concat-item">
                <view class="concat-item-left">获知活动渠道（可多选）</view>
                <view class="channel-box border-b2" style="padding-bottom: 34rpx;">
                    <block v-for="(item, index) in occupaArr" :key="index">
                        <view class="sex-radios-box call" @click="checkboxChange2(index, item)"
                            :class="item.name == '其他'?'qita':''">

                            <image class="sex-radios-box-img" src="@/static/images/yuanshi/check1.png" mode=""
                                v-if="item.isCheck"></image>
                            <image class="sex-radios-box-img" src="@/static/images/yuanshi/uncheck1.png" mode="" v-else>
                            </image>
                            <view class="sex-radios-box-title" :class="item.isCheck?'active':''">
                                {{item.name}}
                            </view>
                            <input @click.stop class="sex-radios-box call input"
                                placeholder-style="font-size: 12px;color:#d2d2d2" v-if="item.name == '其他'"
                                v-model="item.content" type="text">
                        </view>
                    </block>
                </view>
            </view>
        </view>
        <view class="concats" v-if="activityInfo.is_apply"
            style="position: relative; z-index: 88;padding-top: 158rpx;margin-top: -126rpx;">
            <view class="concat-title">
                <view class="concat-title-left">报名资料填写</view>
            </view>
            <view class="concat-item">
                <view class="concat-item-left">姓名</view>
                <input class="concat-item-right" type="text" placeholder-style="font-size: 16px; color:#d2d2d2"
                    placeholder="*真实姓名,非公开信息" v-model="student.name" />
            </view>
            <view class="concat-item">
                <view class="concat-item-left">年龄</view>
                <input class="concat-item-right" type="number" placeholder-style="font-size: 16px; color:#d2d2d2"
                    placeholder="请输入学员年龄" v-model.number="student.age" />
            </view>
            <view class="concat-item">
                <view class="concat-item-left">性别</view>
                <view class="channel-box border-b2">
                    <block v-for="(item, index) in sexRadioList" :key="index">
                        <view class="sex-radios-box" @click="sexradios(item.id)">
                            <image class="sex-radios-box-img" src="@/static/images/yuanshi/check1.png" mode=""
                                v-if="student.sex === item.id"></image>
                            <image class="sex-radios-box-img" src="@/static/images/yuanshi/uncheck1.png" mode="" v-else>
                            </image>
                            <view class="sex-radios-box-title" :class="student.sex == item.id?'active':''">
                                {{item.name}}
                            </view>
                        </view>
                    </block>
                </view>
            </view>
            <view class="concat-item">
                <view class="concat-item-left">职业</view>
                <view class="channel-box border-b2">
                    <block v-for="(item, index) in activityInfo.registrationInfo.career_list" :key="index">
                        <view class="sex-radios-box call" @click="careeradios(item)"
                            :class="item == '其他' || item == '音乐从业者'?'qita':''">

                            <image class="sex-radios-box-img" src="@/static/images/yuanshi/check1.png" mode=""
                                v-if="student.occupa === item"></image>
                            <image class="sex-radios-box-img" src="@/static/images/yuanshi/uncheck1.png" mode="" v-else>
                            </image>
                            <view class="sex-radios-box-title" :class="student.occupa === item?'active':''">
                                {{item}}
                            </view>
                            <input @click.stop class="sex-radios-box call input" :class="item == '音乐从业者'?'input2':''"
                                placeholder-style="font-size: 12px;color:#d2d2d2" v-model="studentOccupacongyezhe" v-if="item == '音乐从业者'"
                                type="text">
                            <input @click.stop class="sex-radios-box call input"
                                placeholder-style="font-size: 12px;color:#d2d2d2" v-model="studentOccupaqita"
                                v-if="item == '其他'" type="text">
                        </view>
                    </block>

                </view>
            </view>
            <view class="concat-item">
                <view class="concat-item-left">音乐基础（可添加多个）</view>
                <view class="channel-box border-b2">
                    <view class="learning">
                        <input class="learning-content" v-model="learncontent" type="text"
                            placeholder-style="font-size: 16px;color:#d2d2d2" placeholder="学习经历 (例如:钢琴 2年)">
                        <view class="learning-add" @click="okLearnExperience">
                            添加更多
                        </view>
                    </view>
                    <view class="learning-box" v-if="student.experience.length >= 1">
                        <block v-for="(item, index) in student.experience">
                            <view class="learning-box-item">
                                <view class="learning-box-item-left">
                                    {{item.content}}
                                </view>
                                <image class="learning-box-item-del" src="../../../static/images/yuanshi/fail.png"
                                    mode="" @click="delLearnExperience(item, index)"></image>
                            </view>
                        </block>
                    </view>
                </view>
            </view>
            <view class="concat-item">
                <view class="concat-item-left">擅长乐器（可多选）</view>
                <view class="channel-box border-b2">
                    <block v-for="(item, index) in arr" :key="index">
                        <view class="channel-box-musical" :class="item.cate_name == '其他'?'qita':''">
                            <view class="channel-box-musical-left" @click="checkboxChange(index,item)">
                                <image class="sex-radios-box-img" src="@/static/images/yuanshi/check1.png" mode=""
                                    v-if="item.isCheck"></image>
                                <image class="sex-radios-box-img" src="@/static/images/yuanshi/uncheck1.png" mode=""
                                    v-else></image>
                            </view>
                            <view class="channel-box-musical-right" :class="item.cate_name == '其他'?'qita':''">
                                <text class="title" :class="item.isCheck?'active':''">{{item.cate_name}}</text>
                                <input class="input" :class="item.cate_name == '其他'?'qita':''" v-model="item.content" type="text">
                            </view>

                        </view>
                    </block>
                </view>
            </view>
            <view class="concat-item">
                <view class="concat-item-left">上传视频作品 <text class="title">(*视频mp4格式，文件体积超过100M可能上传失败，请用百度网盘或其它网盘分享功能，并将作品完整地址填在下方)</text> </view>
                <view class="channel-box border-b2" style="padding-bottom: 36rpx;">
                    <view class="channel-box-video">
                        <view v-for="(item, index) in videoValue" :key="index" class="item">
                            <video :src="item.path" controls custom-cache="false"></video>
                            <view class="icon-del-box" @click.stop="delFile('video', index)">
                                <image src="../../../static/images/community/icon_close.png" mode="aspectFill"></image>
                            </view>
                        </view>
                        <xFileActionSheet :style="videoShow" :disabledCloud="true" :limit="1"
                            :opts="{limitDuration:999}" returnType="object" :disabledCloudConfig="videoConfig"
                            ref="filesVideo" :auto-upload="false" fileMediatype="video" mode="grid" @select="select"
                            @progress="uploadProgress" @success="success" @fail="fail" @getSonValue="getSonValue">
                            <view class="upload-video">
                                <image src="@/static/images/up-video.png" mode="widthFix"></image>
                            </view>
                        </xFileActionSheet>
                    </view>
                </view>
            </view>
            <view class="concat-item">
                <view class="concat-item-left">指定视频地址(*提交前确认地址可用)</view>
                <view class="channel-box border-b2" style="padding: 0;">
                    <view class="videourl-box">
                        <input class="videourl-box-left" type="text" v-model="student.videoUrl" placeholder="https://"
                            placeholder-style="font-size: 16px;color:#d2d2d2">
                        <view class="videourl-box-right" @click="student.videoUrl = ''">
                            <image class="videourl-box-right-image" src="../../../static/images/yuanshi/close.png"
                                mode=""></image>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <view class="concats" v-if="activityInfo.is_questionnaire"
            style="position: relative; z-index: 77;padding-top: 158rpx;margin-top: -126rpx;">
            <view class="concat-title">
                <view class="concat-title-left">调查问卷</view>
            </view>
            <view class="concat-item">
                <view class="channel-box border-b2 relative" style="padding-bottom: 68rpx;">
                    <textarea style="width: 100%; height:120rpx " placeholder-style="font-size: 16px;color:#d2d2d2"
                        maxlength="200" placeholder="简单谈谈对于本次活动您有哪些期待？或者您想解决哪些问题？" v-model="questionnaire"
                        :show-confirm-bar="false" :fixed="true"></textarea>
                    <text class="questionnaire-wrap_pay-text">{{questionnaire.length}}/200字</text>
                </view>
            </view>
        </view>

        <view class="wrap_pay">
            <view class="title">
                订单备注
            </view>
            <view class="wrap-pay-item">
                <view class="concat-item">
                    <view class="channel-box border-b2 relative" style="padding-bottom: 68rpx;">
                        <textarea style="width: 100%; height:120rpx " placeholder-style="font-size: 16px;color:#d2d2d2"
                            maxlength="150" :placeholder="activityOrderNotes" v-model="mark" :show-confirm-bar="false"
                            :fixed="true"></textarea>
                        <text class="questionnaire-wrap_pay-text">{{mark.length}}/150字</text>
                    </view>
                </view>

                <view class="item" style="border-top: 2rpx solid #e2e6ec;padding-top: 24rpx;">
                    <!-- #ifndef MP-TOUTIAO -->
                    <view>支付方式</view>
                    <view class="methods flex flex_align_center flex_between" @click="payItem('weixin')">
                        <image src="@/static/images/yuanshi/wxpay.png" mode=""></image>
                        <image src="@/static/images/yuanshi/check.png" mode="" v-if="active === 'weixin'"></image>
                        <image src="@/static/images/yuanshi/uncheck.png" mode="" v-else></image>
                    </view>
                    <!-- #endif -->
                    <!-- #ifdef MP-TOUTIAO -->
                    <!-- <view class="methods flex flex_align_center flex_between" @click="payItem('bytedance')">
                        <image style="width: 46rpx; height: 46rpx; margin-right: 10rpx;"
                            src="@/static/images/yuanshi/douyin.png" mode=""></image>
                        <image src="@/static/images/yuanshi/check.png" mode="" v-if="active === 'bytedance'"></image>
                        <image src="@/static/images/yuanshi/uncheck.png" mode="" v-else></image>
                    </view> -->
                    <!-- #endif -->
                    <!-- #ifndef MP-TOUTIAO -->
                    <view class="methods flex flex_align_center flex_between" @click="payItem('yue')" v-if="!isWeixin">
                        <view class="tip">
                            余额支付
                            <text>可用余额：{{ userInfo.now_money || 0 }}</text>
                        </view>
                        <image src="@/static/images/yuanshi/check.png" mode="" v-if="active === 'yue'"></image>
                        <image src="@/static/images/yuanshi/uncheck.png" mode="" v-else></image>
                    </view>
                    <view class="methods flex flex_align_center flex_between"
                        v-if="offlinePayStatus === 1 && deduction === false && shipping_type === 0"
                        @click="payItem('offline')">
                        <view class="tip">线下支付</view>
                        <image src="@/static/images/yuanshi/check.png" mode="" v-if="active === 'offline'"></image>
                        <image src="@/static/images/yuanshi/uncheck.png" mode="" v-else></image>
                    </view>
                    <!-- #endif -->
                </view>
            </view>
        </view>
        <!-- #ifndef MP-TOUTIAO -->
        <view class="wrap_pay" style="margin-top: 30rpx;" v-if="couponCodeRatio">
            <view class="title">
                兑换码
            </view>
            <view class="wrap-pay-item" style="padding-bottom: 70rpx;">
                <view class="concat-item">
                    <view class="channel-box border-b2 relative" style="border-bottom: 2rpx solid #d2d2d2;">
                        <textarea style="width: 100%; height:206rpx " placeholder-style="font-size: 16px;color:#d2d2d2"
                            placeholder="请输入兑换码" v-model="coupon_code" :show-confirm-bar="false"
                            :fixed="true"></textarea>

                    </view>
                </view>
            </view>
        </view>
        <!-- #endif -->
        <view class="footer acea-row row-between-wrapper">

            <view class="footer_l">
                <!-- #ifndef MP-TOUTIAO -->
                {{activityInfo.status===0 ?  '定金' : '合计'}}

                <text>￥{{orderPrice.pay_price}}</text>
                <!-- #endif -->
            </view>

            <view class="footer_r" @click="createOrder1">立即报名</view>
        </view>
        <AddressWindow @checked="changeAddress" @redirect="addressRedirect" v-model="showAddress" ref="mychild"
            :checked="addressInfoId">
        </AddressWindow>
        <Agreement v-model="agreementReadpop" ref="mychild1" @isread="isread" :agreementReadtxt="agreementReadtxt">
        </Agreement>
        <u-mask :show="maskShow" :custom-style="{background: 'rgba(0, 0, 0, 0.3)'}">
            <view class="xmask relative flex flex_align_center flex_around">
                <view class="absolute" @click="cancelTask">
                    <u-icon name="close-circle-fill" size="40"></u-icon>
                </view>
                <view class="text_center">
                    <u-loading mode="circle " size="45" color="#ffffff"></u-loading>
                    <view class="txt">
                        {{uploadTypeTxt}}上传中
                    </view>
                </view>
            </view>
        </u-mask>
    </view>
</template>

<script>
    import goMap from '@/mixins/goMap.js';
    import AddressWindow from '@/components/AddressWindow';
    import Agreement from '@/components/Agreement';
    import xFileActionSheet from '@/components/x-file-picker/x-file-action-sheet.vue';
    import {
        LONGITUDE,
        LATITUDE,
        VUE_APP_API_URL
    } from '@/config.js';
    import {
        activityOrderConfirm,
        activityOrderComputed,
        activityOrderCreate,
        activityCheckPromocode
    } from '@/api/community';
    import {
        openCommunitySubscribe
    } from '@/utils/SubscribeMessage.js';
    import {
        getMyMessageDetail,
        editMyMessageDetail,
    } from '@/api/zsff.js'
    import {
        RegPhone,
        RegFixedPhone,
        RegPhoneAndFixed,
        isWeixin
    } from '@/utils/validate';
    // #ifdef H5
    import {
        pay
    } from '@/utils/wechat/pay.js';
    const _isWeixin = isWeixin();
    // #endif

    // #ifdef MP
    const _isWeixin = true;
    // #endif
    import storage from '@/utils/storage.js';
    export default {
        mixins: [goMap],
        components: {
            AddressWindow,
            Agreement,
            xFileActionSheet
        },
        data() {
            return {
                scene: 0, // 默认微信场景值
                traceId:'', // 分享id
                
                tempFilePaths: [],
                videoConfig: {},
                videoValue: [],
                uploadTask: [],
                uploadType: '',
                videoFinish: true,
                maskShow: false,
                maskTxt: '文件上传中',
                uploadAbort: false,
                form: {
                    desc: '',
                    name: '',
                },

                activityOrderNotes: '',
                activityOrderNoteStatus: '',
                // -----------------------------支付开始-----------
                // #ifdef MP-WEIXIN
                from: 'routine',
                // #endif
                // #ifdef MP-TOUTIAO
                from: 'bytedance',
                active: 'bytedance',
                // #endif
                // #ifndef MP-TOUTIAO
                active: _isWeixin ? 'weixin' : 'yue',
                // #endif
                // #ifdef H5
                from: _isWeixin ? 'weixin' : 'weixinh5',
                // #endif
                deduction: true,
                isWeixin: process.env.NODE_ENV === 'development' ? false : _isWeixin,

                offlinePayStatus: 2,
                orderPrice: {
                    pay_price: '1000'
                },
                // -----------------------------支付结束-----------
                showAddress: false,
                addressLoaded: false,
                totalNum: 0,
                contacts: '',
                contactsTel: '',
                alternateContact: '', // 备用联系方式
                zxlist: [],

                mark: '',
                userInfo: this.$store.state.userInfo,
                cartInfo: [],
                activityInfo: {},
                priceGroup: {},
                orderKey: '',
                coupon_code: '',
                useIntegral: '',
                couponCodeRatio: 0,
                agreementRead: false, // 协议是否已阅
                agreementReadpop: false,
                agreementReadtxt: '',
                addressInfoId: 0,

                sexRadioList: [{
                        name: '男',
                        id: 1
                    },
                    {
                        name: '女',
                        id: 2
                    }
                ],
                student: {
                    name: '',
                    age: '',
                    sex: -1,
                    channel: '', // 渠道
                    occupa: '', // 职业
                    videoUrl: '', //指定视频链接
                    experience: [], // 学习经历
                },
                channelradqt: false,
                OtherChannel: '',
                checkboxValue1: [],
                learncontent: '',
                questionnaire: '', // 问卷调查
                inviterId: 0,
                arr: [],
                occupaArr: [],
                selecOccupaArr: [],
                studentOccupaqita: '',
                studentOccupacongyezhe: '',

            }
        },
        computed: {
            videoShow() {
                return this.videoValue.length < 1 ? 'display:block' : 'display:none'
            },
            uploadTypeTxt() {
                let txt = '';
                switch (this.uploadType) {
                    case 'video':
                        txt = '视频'
                        break;
                    default:
                        txt = '文件'
                        break;
                }
                return txt;
            }
        },
        methods: {
            goPages(path, type, pathType) {
                if (type) {
                    this.$authNavigator(path);
                } else {
                    this.$navigator(path, pathType);
                }
            },
            okLearnExperience() {
                let data = {};
                if (this.learncontent == '') {
                    return this.$showToast('请填写学习经历');
                }
                data.content = this.learncontent;
                this.student.experience.push(data)
                this.$showToast('添加成功');
                this.learncontent = '';
                console.log('学习经历', this.student.experience)
            },
            delLearnExperience(item, index) {
                this.student.experience.splice(index, 1)
            },
            // 获取上传状态
            select(e, type, files) {
                if (type === 'video') {
                    this.videoValue = files;
                    console.log('videoValue', this.videoValue);
                }

            },
            // 获取上传进度
            uploadProgress(e, type) {
                const {
                    progressEvent,
                    tempFile,
                    progress,
                    index
                } = e;
                this.uploadType = tempFile.fileType;
                this.uploadTask[index] = progressEvent.uploadTask;
                // console.log('获取上传进度', this.uploadTask)
            },
            // 上传失败
            fail(e, type) {
                console.log('上传失败---：', e, type);
                const {
                    failMsg
                } = e.tempFiles[0];


                if (type === 'image') {
                    this.imageFinish = false;
                }
                if (type === 'video') {
                    this.videoFinish = false;
                }
                if (type === 'audio') {
                    this.audioFinish = false;
                }
                if (failMsg === 'uploadFile:fail timeout') {
                    this.$showToast('网络超时，请稍后再试')
                } else if (failMsg === 'uploadFile:fail') {
                    this.$showToast('上传失败，请稍后再试')
                } else if (failMsg === 'uploadFile:fail') {
                    this.$showToast('上传异常，请稍后再试')
                }
                this.maskStatus(false, failMsg)
            },
            upload() {
                this.uploadTask = [];
                this.uploadAbort = false;
                const {
                    videoValue = [],
                        videoFinish = true,
                } = this;
                if (!this.checkUploaded(videoValue) && !videoFinish && videoValue.length) {
                    this.$refs.filesVideo.upload();
                } else {
                    console.log('没有可上传文件')
                    this.maskStatus(false);
                }
            },
            checkUploaded(files) {
                console.log(files)
                let i = 0;
                files.forEach((v, index) => {
                    if (v.status && v.status === 'success') {
                        i++;
                    }
                });
                return i === files.length
            },
            getSonValue(res) {
                if (!res) {
                    this.maskStatus(false)
                    this.$showToast('文件过大，上传失败')
                }
            },
            maskStatus(type, txt = '') {
                this.maskShow = type;
                this.maskTxt = txt;
            },
            cancelTask() {
                this.uploadAbort = true;
                if (this.uploadTask.length) {
                    this.uploadTask.forEach(item => {
                        item.abort();
                    })
                }
                this.maskStatus(false, '取消上传')
                this.$showToast(`${this.uploadTypeTxt}已取消上传`);
            },
            delFile(type, index = 0) {
                if (type === 'video') {
                    this.$refs.filesVideo.delFile(index);
                } else if (type === 'recorder') {
                    this.audioValue = [];
                    this.innerAudioContext.pause();
                    this.isAudio = false;
                }
            },
            readyToSubmit() {
                const {
                    desc,
                    name
                } = this.form;
                const {
                    videoValue,
                } = this;

                if (videoValue.length) {
                    this.maskStatus(true);
                    this.videoFinish = !videoValue.length;
                    this.upload()
                } else {
                    // this.submit()
                }
            },
            sexRadioGroupChange(e) {
                console.log(e)
            },
            checkboxChange(e, info) {
                let arr = [...this.arr];
                let selarr = [...this.checkboxValue1];
                if (arr[e].isCheck == false) {
                    arr[e].isCheck = true;
                    selarr.push(info)
                } else {
                    arr[e].isCheck = false;
                    var index11 = selarr.indexOf(info)
                    selarr.splice(index11, 1)
                }
                this.arr = arr;
                this.checkboxValue1 = selarr;
                // console.log('this.arr',this.arr)
                // console.log('this.checkboxValue1',this.checkboxValue1)
            },
            checkboxChange2(e, info) {
                let arr = [...this.occupaArr];
                let selarr = [...this.selecOccupaArr];
                if (arr[e].isCheck == false) {
                    arr[e].isCheck = true;
                    selarr.push(info)
                } else {
                    arr[e].isCheck = false;
                    var index11 = selarr.indexOf(info)
                    selarr.splice(index11, 1)

                }
                this.occupaArr = arr;
                this.selecOccupaArr = selarr;
                // console.log('this.occupaArr', this.occupaArr)
                // console.log('this.selecOccupaArr', this.selecOccupaArr)
            },

            isread(e) {
                this.agreementRead = e;
                this.createOrder1()
            },
            addressTap: function() {
                this.showAddress = true;
                if (!this.addressLoaded) {
                    this.addressLoaded = true;
                    this.$refs.mychild.getAddressList();
                }
            },
            // 获取门店列表数据
            addressRedirect() {
                this.addressLoaded = false;
                this.showAddress = false;
            },
            changeAddress(addressInfo) {
                this.addressInfoId = addressInfo.id
                this.contacts = addressInfo.real_name;
                this.contactsTel = addressInfo.phone;
            },
            payItem: function(index) {
                this.active = index;
                this.computedPrice();
            },
            sexradios: function(index) {
                this.student.sex = index;
                // console.log('性别', this.student.sex)
            },
            careeradios(item) {
                this.student.occupa = item;
            },
            computedPrice() {
                activityOrderComputed(this.orderKey, {
                    payType: this.active,
                    coupon_code: this.coupon_code,
                    useIntegral: this.useIntegral,
                }).then(res => {
                    const data = res.data;
                    this.orderPrice = data.result;
                }).catch(err => {
                    console.log('error', err);
                    this.$showToast(err.msg || err, 'error');
                });
            },
            async checkPromocode(type) {
                let res = await activityCheckPromocode(this.coupon_code);
                if (res.data.status) {
                    if (type) this.$showToast('兑换码可用')
                    this.computedPrice();
                    return true
                } else {
                    this.$showToast('兑换码无效');
                    this.coupon_code = '';
                    return false
                }
            },
            // 上传成功
            success(e, type) {
                // console.log(`${this.uploadTypeTxt}上传完成`, e)
                const {
                    tempFiles = [], tempFilePaths = []
                } = e;
                if (type === 'video') {
                    this.videoFinish = true;
                }
                console.log(type, this.videoFinish);
                if (this.videoFinish) {
                    // this.$showToast(`上传完成`);
                    this.maskStatus(false, '上传完成')
                    console.log('视频上传完成', tempFilePaths)
                    this.tempFilePaths = tempFilePaths;
                    this.createOrder()

                } else if (!this.videoFinish) {
                    console.log(`${this.uploadTypeTxt}上传完成,随机下一个`, this.uploadTask.length);
                    !this.uploadAbort && this.upload();
                }
            },
            async createOrder1() {
                let that = this;
                if (this.coupon_code) {
                    let type = await this.checkPromocode(false)
                    if (!type) return;
                }
                if (!this.active) return this.$showToast('请选择支付方式');
                if (this.active == 'yue' && parseFloat(that.userInfo.now_money) < parseFloat(that.orderPrice.pay_price))
                    return that.$showToast('余额不足！');

                if (this.activityInfo.status > 1) {
                    return this.$showToast('报名已截止，如有疑问请联系官方客服')
                }
                if (this.activityInfo.status >= 3) {
                    return this.$showToast('该活动已结束');
                }
                if ((this.contacts === '' || this.contactsTel === '')) return this.$showToast(
                    '请填写联系人或联系人电话');
                if (!RegPhoneAndFixed(this.contactsTel)) {
                    return this.$showToast('请填写正确的手机号');
                }
                if (!this.contacts) {
                    return this.$showToast('请填写您的真实姓名');
                }
                if (this.activityInfo.is_spare && !this.alternateContact) {
                    return this.$showToast('请填写您的备用联系方式');
                }
                if (this.activityInfo.is_channels && this.selecOccupaArr.length <= 0) {
                    return this.$showToast('请选择您对本次活动的获知渠道')
                }
                if (this.selecOccupaArr.length >= 1) {
                    for (let i = 0; i < this.selecOccupaArr.length; i++) {
                        if (this.selecOccupaArr[i].name == '其他' && this.selecOccupaArr[i].content == '') {
                            return this.$showToast('请填写其他活动获知渠道');
                        }
                    }
                }
                if (this.activityInfo.is_apply && !this.student.name) {
                    return this.$showToast('请填写参与活动的人员姓名');
                }
                if (this.activityInfo.is_apply && !this.student.age) {
                    return this.$showToast('请填写参与活动的人员年龄');
                }
                if (this.activityInfo.is_apply && (this.student.age <= 0 || this.student.age >= 100)) {
                    return this.$showToast('请填写参与活动的人员的正确年龄');
                }
                if (this.activityInfo.is_apply && this.student.sex == -1) {
                    return this.$showToast('请选择参与活动的人员性别');
                }
                if (this.activityInfo.is_apply && !this.student.occupa) {
                    return this.$showToast('请选择参与活动的人员职业');
                }
                if (this.activityInfo.is_apply && (this.student.occupa == '音乐从业者' && !this.studentOccupacongyezhe)) {
                    return this.$showToast('请填写活动的人员职业的详细信息');
                }
                if (this.activityInfo.is_apply && (this.student.occupa == '其他' && !this.studentOccupaqita)) {
                    return this.$showToast('请填写活动的人员职业的详细信息');
                }
                // console.log('this.student.occupa', this.student.occupa)
                if (this.activityInfo.is_apply && this.checkboxValue1.length <= 0) {
                    return this.$showToast('请选择参与活动的人员擅长乐器种类')
                }
                if (this.checkboxValue1.length >= 1) {
                    for (let i = 0; i < this.checkboxValue1.length; i++) {
                        if (this.checkboxValue1[i].content == '') {
                            return this.$showToast('请填写已选乐器种类的详细信息');
                        }
                    }
                }
                if (this.activityInfo.is_apply && (this.videoValue.length <= 0 && !this.student.videoUrl)) {
                    return this.$showToast('请上传参与活动人员的视频作品或指定视频地址链接');
                }
                if (this.activityInfo.is_questionnaire && !this.questionnaire) {
                    return this.$showToast('请填写对本活动调查问卷');
                }

                if (this.activityOrderNoteStatus && !this.mark) {
                    return this.$showToast('请填写备注信息');
                }
                // #ifndef MP-TOUTIAO
                if ((this.activityInfo.status == 1 || this.activityInfo.status == 2) && !this.agreementRead) {
                    this.$showToast('请先同意协议内容');
                    this.agreementReadpop = true;
                    return;
                }
                // #endif
                if (this.videoValue.length > 0) {
                    this.readyToSubmit()
                } else {
                    this.createOrder()
                }
            },
            async createOrder() {
                let that = this;
                if (this.coupon_code) {
                    let type = await this.checkPromocode(false)
                    if (!type) return;
                }
                if (!this.active) return this.$showToast('请选择支付方式');
                if (this.active == 'yue' && parseFloat(that.userInfo.now_money) < parseFloat(that.orderPrice
                        .pay_price))
                    return that.$showToast('余额不足！');

                if (this.activityInfo.status > 1) {
                    return this.$showToast('报名已截止，如有疑问请联系官方客服')
                }
                if (this.activityInfo.status >= 3) {
                    return this.$showToast('该活动已结束');
                }
                if ((this.contacts === '' || this.contactsTel === '')) return this.$showToast(
                    '请填写联系人或联系人电话');

                if (!RegPhoneAndFixed(this.contactsTel)) {
                    return this.$showToast('请填写正确的手机号');
                }
                if (!this.contacts) {
                    return this.$showToast('请填写您的真实姓名');
                }
                if (this.activityInfo.is_spare && !this.alternateContact) {
                    return this.$showToast('请填写您的备用联系方式');
                }
                if (this.activityInfo.is_channels && this.selecOccupaArr.length <= 0) {
                    return this.$showToast('请选择您对本次活动的获知渠道')
                }
                if (this.selecOccupaArr.length >= 1) {
                    for (let i = 0; i < this.selecOccupaArr.length; i++) {
                        if (this.selecOccupaArr[i].name == '其他' && this.selecOccupaArr[i].content == '') {
                            return this.$showToast('请填写其他活动获知渠道');
                        }
                    }
                }
                if (this.activityInfo.is_apply && !this.student.name) {
                    return this.$showToast('请填写参与活动的人员姓名');
                }
                if (this.activityInfo.is_apply && !this.student.age) {
                    return this.$showToast('请填写参与活动的人员年龄');
                }
                if (this.activityInfo.is_apply && (this.student.age < 0 || this.student.age > 100)) {
                    return this.$showToast('请填写参与活动的人员的正确年龄');
                }
                if (this.activityInfo.is_apply && this.student.sex == -1) {
                    return this.$showToast('请选择参与活动的人员性别');
                }
                if (this.activityInfo.is_apply && !this.student.occupa) {
                    return this.$showToast('请选择参与活动的人员职业');
                }
                if (this.activityInfo.is_apply && (this.student.occupa == '音乐从业者' && !this.studentOccupacongyezhe)) {
                    return this.$showToast('请填写活动的人员职业的详细信息');
                }
                if (this.activityInfo.is_apply && (this.student.occupa == '其他' && !this.studentOccupaqita)) {
                    return this.$showToast('请填写活动的人员职业的详细信息');
                }
                if (this.activityInfo.is_apply && this.checkboxValue1.length <= 0) {
                    return this.$showToast('请选择参与活动的人员擅长乐器种类')
                }
                if (this.checkboxValue1.length >= 1) {
                    for (let i = 0; i < this.checkboxValue1.length; i++) {
                        if (this.checkboxValue1[i].content == '') {
                            return this.$showToast('请填写已选乐器种类的详细信息');
                        }
                    }
                }
                if (this.activityInfo.is_apply && (this.videoValue.length <= 0 && !this.student.videoUrl)) {
                    return this.$showToast('请上传参与活动人员的视频作品或指定视频地址链接');
                }
                if (this.activityInfo.is_questionnaire && !this.questionnaire) {
                    return this.$showToast('请填写对本活动调查问卷');
                }

                if (this.activityOrderNoteStatus && !this.mark) {
                    return this.$showToast('请填写备注信息');
                }
                // #ifndef MP-TOUTIAO
                if ((this.activityInfo.status == 1 || this.activityInfo.status == 2) && !this.agreementRead) {
                    this.$showToast('请先同意协议内容');
                    this.agreementReadpop = true;
                    return;
                }
                // #endif
                let occupadata = '';
                if(this.student.occupa == '其他'){
                    occupadata = this.student.occupa + ',' +  this.studentOccupaqita
                }
                if(this.student.occupa == '音乐从业者'){
                    occupadata = this.student.occupa + ',' + this.studentOccupacongyezhe
                }
                if(this.student.occupa != '其他' || this.student.occupa != '音乐从业者'){
                    occupadata = this.student.occupa;
                }
                for (let i = 0; i < this.checkboxValue1.length; i++) {
                    delete this.checkboxValue1[i].id;
                    delete this.checkboxValue1[i].isCheck;
                    delete this.checkboxValue1[i].child;
                }
                let student_card = {};
                student_card.real_name = this.student.name;
                student_card.age = this.student.age;
                student_card.sex = this.student.sex;
                student_card.career = occupadata;
                student_card.learning_experience = this.student.experience;
                student_card.skills = this.checkboxValue1;
                student_card.video_works = this.tempFilePaths[0];
                student_card.video_links = this.student.videoUrl;
                let array = []
                array[0] = student_card;
                let OccupaArray = []
                // console.log('this.selecOccupaArr---',this.occupaArr)
                for (let i = 0; i < this.selecOccupaArr.length; i++) {
                    if (this.selecOccupaArr[i].name == '其他') {
                        // delete this.selecOccupaArr[i].content;
                        this.selecOccupaArr[i].name = this.selecOccupaArr[i].content;
                        OccupaArray.push(this.selecOccupaArr[i].name)
                    } else {
                        OccupaArray.push(this.selecOccupaArr[i].name)
                    }
                }
                this.traceId = await this.getTraceId();  //获取推广id
                openCommunitySubscribe()
                    .then(() => {
                        this.$loadingToast('生成订单中');
                        let obj = {
                            real_name: this.contacts,
                            phone: this.contactsTel,
                            payType: this.active,
                            from: this.from,
                            mark: this.mark || '',
                            useIntegral: this.useIntegral,
                            coupon_code: this.coupon_code,
                            // #ifdef MP-TOUTIAO    
                            apply_type: 1,
                            // #endif
                            student_card: array,
                            spare_contact: this.alternateContact,
                            questionnaire: this.questionnaire,
                            channel_name: OccupaArray.join(","),
                            invite_uid: this.inviterId?this.inviterId:0,
                            // scene: this.scene,//场景值
                            // trace_id: this.traceId, 
                        };
                        activityOrderCreate(this.orderKey, obj).then(res => {
                                this.$hideLoading();
                                const data = res.data;
                                console.log('data------', data)
                                let url = '/pages/order/PaymentStatus?source=community&orderId=' + data
                                    .result.key + '&msg=' + res.msg + '&inviterId=' + this.inviterId ;
                                if (this.inviterId) {
                                    storage.set('invite_uid', this.inviterId);
                                }
                                switch (data.status) {
                                    case 'ORDER_EXIST':
                                    case 'EXTEND_ORDER':
                                    case 'PAY_DEFICIENCY':
                                    case 'PAY_ERROR':
                                        that.$showToast(res.msg);
                                        if (data.result.orderId) {
                                            that.$navigator(url + '&status=0', 'redirectTo');
                                        }
                                        break;
                                    case 'SUCCESS':
                                        // #ifndef MP-TOUTIAO
                                        that.$showToast(res.msg);
                                        // #endif
                                        // #ifdef MP-TOUTIAO
                                        that.$showToast('提交成功');
                                        // #endif
                                        // #ifdef H5
                                        that.$navigator(url + '&status=1', 'redirectTo');
                                        // #endif
                                        // #ifdef MP
                                        return that.$navigator(url, 'redirectTo');
                                        // #endif
                                        break;
                                    case 'WECHAT_H5_PAY':
                                        // #ifndef MP-TOUTIAO
                                        that.$navigator(url + '&status=2', 'redirectTo');
                                        setTimeout(() => {
                                            location.href = data.result.jsConfig.mweb_url;
                                        }, 100);
                                        // #endif
                                        break;
                                    case 'BYTEDANCE_PAY':
                                    case 'BYTEDANCE_ORDER':
                                        // #ifndef MP-TOUTIAO
                                        that.$navigator(url + '&status=2', 'redirectTo');
                                        setTimeout(() => {
                                            location.href = data.result.jsConfig.mweb_url;
                                        }, 100);
                                        // #endif
                                        // #ifdef MP-TOUTIAO
                                        let tt_res = res.data.result;
                                        // console.log('字节支付----', tt_res)
                                        tt.pay({
                                            orderInfo: {
                                                order_id: tt_res.orderId,
                                                order_token: tt_res.orderToken,
                                            },
                                            service: 5,
                                            _debug: 1,
                                            success(res) {
                                                if (res.code == 0) {
                                                    that.$showToast('提交成功');
                                                    if (that.BargainId || that.combinationId || that
                                                        .pinkId || that.seckillId) {
                                                        return that.$navigator(url, 'reLaunch');
                                                    }
                                                    return that.$navigator(url, 'redirectTo');
                                                }
                                            },
                                            fail(res) {
                                                console.log('字节支付失败', res)
                                                that.$showToast('取消提交');
                                                return that.$navigator(url + '&status=2',
                                                    'redirectTo');
                                            },
                                        });
                                        // #endif
                                        break;
                                    case 'WECHAT_PAY':
                                        // #ifdef H5
                                        pay(data.result.jsConfig).finally(() => {
                                            that.$navigator(url + '&status=4', 'redirectTo');
                                        });
                                        // #endif
                                        // #ifdef MP-WEIXIN
                                        let jsConfig = res.data.result.jsConfig;
                                        // requestOrderPayment
                                        wx.requestPayment({
                                            timeStamp: jsConfig.timestamp,
                                            nonceStr: jsConfig.nonceStr,
                                            package: jsConfig.package,
                                            signType: jsConfig.signType,
                                            paySign: jsConfig.paySign,
                                            success: function(res) {
                                                console.log('微信支付成功',res)
                                                that.$showToast('支付成功');
                                                if (that.BargainId || that.combinationId || that
                                                    .pinkId || that.seckillId) {
                                                    return that.$navigator(url, 'reLaunch');
                                                }
                                                return that.$navigator(url, 'redirectTo');
                                            },
                                            fail: function(e) {
                                                console.log('微信支付失败',e)
                                                that.$showToast('取消支付');
                                                return that.$navigator(url + '&status=2',
                                                    'redirectTo');
                                            },
                                            complete: function(e) {
                                                //关闭当前页面跳转至订单状态
                                                if (res.errMsg == 'requestPayment:cancel')
                                                    return that
                                                        .$navigator(url + '&status=2',
                                                            'redirectTo');
                                            }
                                        });
                                        // #endif
                                }
                            })
                            .catch(err => {
                                console.log('err', err);
                                this.$showToast(err.msg || err || '创建订单失败', 'error');
                                // this.$router.go(-1);
                            });
                    })
                    .catch(err => {
                        console.log('err', err);
                    });
            },
            getTraceId(){
                let that = this;
                let traceId = '';
                return new Promise((resolve, reject) => {
                    wx.checkBeforeAddOrder({
                      success (res) { 
                          traceId = res.data.traceId;
                          // console.log('获取分享id', traceId)
                          resolve(traceId);
                      },
                      fail (err) { 
                          reject(err);
                          // console.log('获取分享id失败', err)
                      }
                    })
                });
            },
            getScene() {
                let that = this;
                let opt = wx.getEnterOptionsSync();
                this.scene = opt.scene;
            },
            getCartInfo(cartId, inviterId) {
                activityOrderConfirm({
                        cartId,
                        inviterId
                    })
                    .then(res => {
                        const {
                            data
                        } = res;
                        console.log('确认订单返回', data)
                        this.activityInfo = data.activityInfo;
                        if (this.activityInfo.is_apply) {
                            if (this.activityInfo.registrationInfo.musical_instrument.length >= 1) {
                                for (let i = 0; i < this.activityInfo.registrationInfo.musical_instrument
                                    .length; i++) {
                                    this.activityInfo.registrationInfo.musical_instrument[i].content = '';
                                    this.activityInfo.registrationInfo.musical_instrument[i].isCheck = false;
                                    this.arr.push(this.activityInfo.registrationInfo.musical_instrument[i])
                                }
                            }
                        }
                        let obj = {};
                        for (let key in this.activityInfo.channel_list) {
                            obj[key] = this.activityInfo.channel_list[key];
                        };
                        this.occupaArr = Object.keys(obj).map(val => ({
                            name: obj[val],
                            isCheck: false,
                            content: ''
                        }));
                        // console.log('this.occupaArr----', this.occupaArr)
                        this.activityOrderNoteStatus = data.activityInfo.activity_order_notes;
                        this.activityOrderNotes = data.activityInfo.activity_order_notes ? data.activityInfo
                            .activity_order_notes : '请填写备注信息（150个字）';
                        this.agreementReadtxt = data.activityInfo.text_agreement;
                        this.cartInfo = data.cartInfo;
                        this.orderKey = data.orderKey;
                        this.priceGroup = data.priceGroup;
                        this.userInfo = data.userInfo;
                        this.couponCodeRatio = !!data.couponCodeRatio;
                        this.computedPrice();
                        this.totalNum = data.cartInfo.map(item => item.cart_num).reduce((prev, curr) => {
                            return prev + curr

                        }, 0)
                    })
                    .catch(err => {
                        console.log('加载订单数据失败', err)
                        // this.$showToast('加载订单数据失败', 'error');
                        // return this.$navigator(-1);
                    });
            },
        },
        mounted() {
            const token = this.$storage.get('token');
            this.videoConfig = {
                url: `${VUE_APP_API_URL}/upload/video`,
                header: {
                    ['Authori-zation']: 'Bearer ' + token
                }
            }
            console.log('this.videoConfig', this.videoConfig)
        },
        onLoad(options) {
            const {
                cartId,
                inviterId
            } = options;
            if (inviterId && inviterId != 'undefined') {
                this.inviterId = Number(inviterId);
            }
            console.log('邀请人id', this.inviterId)
            this.getCartInfo(cartId, this.inviterId)
            // console.log('process.env.NODE_ENV---',process.env.NODE_ENV)
        },
        onShow() {
            this.getScene()
        }
    }
</script>

<style lang="scss" scoped>
    .xmask {
        width: 200rpx;
        height: 200rpx;
        margin: 40vh auto;
        background: rgba($color: #000000, $alpha: 0.5);
        color: #fff;
        border-radius: 20rpx;

        .absolute {
            width: 40rpx;
            top: -10rpx;
            right: -10rpx;
        }
    }

    .address {
        width: 710rpx;
        background: #ffffff;
        border-radius: 30rpx;
        box-shadow: 0px 0px 40rpx 0px rgba(107, 127, 153, 0.20);
        margin: 20rpx auto;
        padding: 36rpx 28rpx 64rpx 36rpx;

        .info {
            font-size: 24rpx;
            font-weight: 400;
            text-align: left;
            color: #666666;

            view {
                margin-bottom: 12rpx;
            }

            text {
                font-size: 28rpx;
                color: #333333;
                font-weight: 500;
            }
        }

        .block {
            width: 26rpx;

            image {
                width: 100%;
            }
        }

        .tips {
            margin-top: 20rpx;
            padding-top: 20rpx;
            border-top: 4rpx solid #e2e6ec;
        }
    }

    .wrap {
        width: 710rpx;
        background: #ffffff;
        border-radius: 30rpx;
        box-shadow: 0px 0px 40rpx 0px rgba(107, 127, 153, 0.20);
        margin: 20rpx auto;
        // padding: 30rpx 40rpx;
        box-sizing: border-box;
        overflow: hidden;
        border: 4rpx solid #fff;

        .title {
            width: 100%;
            height: 100rpx;
            line-height: 100rpx;
            background: #f2f5f8;
            font-size: 32rpx;
            font-family: PingFang SC, PingFang SC-Bold;
            font-weight: 700;
            text-align: left;
            color: #646464;
            padding: 0 40rpx;
            box-sizing: border-box;
        }

        .info {
            .info-box {
                width: 100%;
                box-sizing: border-box;
                overflow: auto;
                border-bottom: 2rpx solid #eaeaea;
                padding-bottom: 32rpx;

                .info-box-left {
                    float: left;
                    width: 134rpx;
                    height: 112rpx;
                    border-radius: 30rpx;
                }

                .info-box-right {
                    float: left;
                    width: 472rpx;
                    padding-left: 20rpx;
                    box-sizing: border-box;

                    .name {
                        font-size: 32rpx;
                        font-family: PingFang SC, PingFang SC-Bold;
                        font-weight: 700;
                        text-align: left;
                        color: #333333;
                        margin-bottom: 14rpx;
                    }

                    .des {
                        font-size: 24rpx;
                        font-family: PingFang SC, PingFang SC-Regular;
                        font-weight: 400;
                        text-align: left;
                        color: #666666;
                    }

                    .pre {
                        width: 100%;
                        font-size: 24rpx;
                        font-family: SF Pro Text, SF Pro Text-Medium;
                        font-weight: 500;
                        text-align: right;
                        color: #3e3e3e;
                        margin-top: 22rpx;

                        .pre-left {
                            float: left;
                        }

                        .pre-right {
                            float: right;
                        }
                    }
                }
            }

            .info-box-foot {
                width: 100%;
                padding-top: 40rpx;
                padding-bottom: 52rpx;

                .info-box-foot-left {
                    float: left;
                    width: 500rpx;
                    font-size: 28rpx;
                    font-family: PingFang SC, PingFang SC-Medium;
                    font-weight: 500;
                    text-align: left;
                    color: #333333;

                    .info-box-foot-left-text {
                        font-size: 24rpx;
                        font-family: SF Pro Text, SF Pro Text-Medium;
                        font-weight: 500;
                        text-align: left;
                        color: #3e3e3e;
                        margin-left: 10rpx;

                    }
                }

                .info-box-foot-right {
                    float: right;
                    width: 120rpx;
                    height: 40rpx;
                    line-height: 40rpx;
                    text-align: center;
                    border: 2rpx solid #ff5656;
                    border-radius: 14rpx;
                    font-size: 20rpx;
                    font-family: PingFang SC, PingFang SC-Regular;
                    font-weight: 400;
                    text-align: center;
                    color: #ff5656;
                }

            }

            .list {
                .item {
                    font-size: 24rpx;
                    font-family: PingFang SC, PingFang SC-Regular;
                    font-weight: 400;
                    text-align: left;
                    color: #333333;
                    margin-bottom: 28rpx;

                    .name {
                        flex: 1;
                    }

                    .num {
                        font-size: 28rpx;
                        font-weight: 700;
                        width: 80rpx;
                        text-align: center;
                    }
                }
            }

            .result {
                border-top: 2px solid #e2e6ec;
                border-bottom: 2px solid #e2e6ec;
                margin: 44rpx 0 40rpx 0;
                padding: 44rpx 0 78rpx 0;
                font-size: 28rpx;
                font-family: PingFang SC, PingFang SC-Medium;
                font-weight: 500;
                text-align: left;
                color: #333333;
                line-height: 38rpx;

                .tips {
                    margin-top: 28rpx;
                    font-size: 24rpx;
                    font-weight: 400;
                    color: #ff5656;
                }
            }
        }


    }

    .concats {
        width: 100%;
        background: #ffffff;
        border-radius: 0px 0px 70rpx 70rpx;
        box-shadow: 0px 0px 30rpx 30rpx rgba(107, 127, 153, 0.30);
        padding: 36rpx 40rpx 100rpx;
        box-sizing: border-box;
        margin-top: 52rpx;
        margin-bottom: 60rpx;

        .concat-title {
            width: 100%;
            overflow: auto;
            margin-bottom: 54rpx;

            .concat-title-left {
                float: left;
                font-size: 32rpx;
                font-family: PingFang SC, PingFang SC-Semibold;
                font-weight: 600;
                text-align: left;
                color: #646464;
                margin-top: 10rpx;
            }

            .concat-title-right {
                float: right;
                width: 152rpx;
                height: 60rpx;
                line-height: 60rpx;
                font-size: 24rpx;
                font-family: PingFang SC, PingFang SC-Regular;
                font-weight: 400;
                text-align: center;
                color: #646464;
                border-radius: 30rpx;
                background-color: #eaeaea;
            }
        }

        .concat-item {
            width: 100%;
            padding-top: 34rpx;
            overflow: auto;


            .concat-item-left {
                width: 100%;
                font-size: 24rpx;
                font-family: PingFang SC, PingFang SC-Regular;
                font-weight: 400;
                text-align: left;
                color: #666666;
                .title {
                    color: #d2d2d2;
                    font-size: 22rpx;
                    margin-left: 8rpx;
                }
            }

            .concat-item-right {
                width: 100%;
                font-size: 32rpx;
                height: 82rpx;
                line-height: 82rpx;
                font-family: SF Pro Text, SF Pro Text-Regular;
                font-weight: 400;
                text-align: left;
                color: #000;
                border-bottom: 2rpx solid #d2d2d2;
            }

            .channel-box {
                width: 100%;
                overflow: auto;

                &.relative {
                    position: relative;
                }

                .channel-box-musical {
                    float: left;
                    width: 50%;
                    margin-top: 20rpx;
                    padding-right: 45rpx;
                    box-sizing: border-box;
                    &.qita {
                        width: 100%;
                    }

                    .channel-box-musical-left {
                        float: left;
                        margin-top: 6rpx;

                        .sex-radios-box-img {
                            float: left;
                            width: 44rpx;
                            height: 44rpx;
                            margin-right: 16rpx;
                        }

                    }

                    .channel-box-musical-right {

                        float: left;
                        width: 224rpx;
                        &.qita {
                            width: 560rpx;
                        }
                        .title {
                            font-size: 24rpx;
                            font-family: PingFang SC, PingFang SC-Regular;
                            font-weight: 400;
                            text-align: left;
                            color: #d2d2d2;

                            &.active {
                                color: #50506f;
                            }
                        }

                        .input {
                            float: right;
                            width: 150rpx;
                            border-bottom: 2rpx solid #d2d2d2;
                            padding: 0 4rpx;
                            box-sizing: border-box;
                            &.qita {
                                width: 490rpx;
                            }
                        }
                    }
                }

                .sex-radios-box {
                    float: left;
                    width: 172rpx;
                    margin-top: 20rpx;

                    &.call {
                        width: 33%;
                    }

                    &.qita {
                        width: 100%;
                    }

                    &.input {
                        width: 500rpx;
                        margin-top: -4rpx;
                        margin-left: 8rpx;
                        border-bottom: 2rpx solid #d2d2d2;
                        font-size: 24rpx;
                    }

                    &.input2 {
                        width: 432rpx;

                    }

                    .sex-radios-box-img {
                        float: left;
                        width: 44rpx;
                        height: 44rpx;
                        margin-right: 16rpx;
                    }

                    .sex-radios-box-title {
                        float: left;
                        font-size: 24rpx;
                        font-family: PingFang SC, PingFang SC-Regular;
                        font-weight: 400;
                        text-align: left;
                        color: #d2d2d2;
                        margin-top: 4rpx;

                        &.active {
                            color: #50506f;
                        }
                    }
                }

                .questionnaire-wrap_pay-textarea {
                    width: 100%;
                    height: 120rpx;
                }


                &.relative {
                    position: relative;
                }




                .videourl-box {
                    width: 100%;
                    height: 76rpx;
                    line-height: 76rpx;
                    margin-top: 20rpx;

                    .videourl-box-left {
                        float: left;
                        width: 520rpx;
                        margin-top: 12rpx;
                    }

                    .videourl-box-right {
                        float: right;
                        width: 60rpx;
                        height: 40rpx;
                        margin-top: 12rpx;
                        background-color: #50516a;
                        border-radius: 24rpx;

                        .videourl-box-right-image {
                            display: block;
                            margin: 8rpx auto;
                            width: 25rpx;
                            height: 25rpx;
                        }
                    }
                }

                .channel-box-video {
                    position: relative;
                    width: 200rpx;
                    height: 200rpx;
                    border-radius: 12rpx;
                    margin-top: 28rpx;
                    background-color: #f4f4f4;
                    overflow: auto;
                    border: 2rpx dashed #d2d2d2;

                    .item {}

                    .upload-video {
                        width: 66rpx;
                        height: 64rpx;
                        margin: 68rpx auto;
                    }

                    video {
                        width: 200rpx;
                        height: 200rpx;
                        border-radius: 12rpx;
                    }

                    .icon-del-box {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        position: absolute;
                        top: 0;
                        right: 0;
                        height: 48rpx;
                        width: 48rpx;
                        border-radius: 50%;
                        background-color: #ff5656;
                        z-index: 2;
                    }
                }

                .channel-box-musical {
                    float: left;
                    width: 50%;
                    height: 56rpx;
                    line-height: 56rpx;
                    margin-top: 12rpx;

                    .left {
                        float: left;
                    }

                    .right {
                        float: left;
                        width: 160rpx;
                        padding-left: 20rpx;
                        box-sizing: border-box;
                        border-bottom: 2rpx solid #9999995e;
                        margin-left: 14rpx;
                    }
                }

                &.border-b2 {
                    border-bottom: 2rpx solid #d2d2d2;
                    padding-bottom: 24rpx;

                }

                .learning {
                    width: 100%;
                    overflow: auto;
                    margin-top: 20rpx;
                    margin-bottom: 16rpx;

                    .learning-content {
                        float: left;
                        width: 500rpx;
                        // border-bottom: 2rpx solid #d2d2d2;
                    }

                    .learning-radio {
                        float: left;
                    }

                    .learning-add {
                        float: right;
                        width: 150rpx;
                        height: 40rpx;
                        line-height: 40rpx;
                        text-align: center;
                        font-size: 24rpx;
                        font-family: PingFang SC, PingFang SC-Medium;
                        font-weight: 500;
                        text-align: center;
                        color: #d2d2d2;
                        background-color: #50516a;
                        border-radius: 24rpx;
                    }
                }

                .learning-box {
                    width: 100%;
                    overflow: auto;


                    .learning-box-item {
                        width: 100%;
                        height: 60rpx;
                        line-height: 60rpx;
                        overflow: auto;

                        .learning-box-item-left {
                            float: left;
                            width: 500rpx;
                        }

                        .learning-box-item-del {
                            float: right;
                            width: 50rpx;
                            height: 50rpx;
                        }
                    }
                }

                .channel-item {
                    float: left;
                    width: 33%;
                    margin-top: 12rpx;
                }
            }

        }

    }

    .wrap_pay {
        width: 710rpx;
        opacity: 1;
        background: #ffffff;
        border-radius: 30rpx;
        box-shadow: 0px 0px 40rpx 0px rgba(107, 127, 153, 0.20);
        margin: 0 auto;
        overflow: auto;
        border: 4rpx solid #ffffff;

        // padding: 64rpx 40rpx;
        .wrap-pay-item {
            width: 100%;
            padding: 30rpx 40rpx;
            box-sizing: border-box;
        }

        .title {
            width: 100%;
            height: 100rpx;
            line-height: 100rpx;
            background: #f2f5f8;
            font-size: 32rpx;
            font-family: PingFang SC, PingFang SC-Bold;
            font-weight: 700;
            text-align: left;
            color: #646464;
            padding: 0 40rpx;
            box-sizing: border-box;
        }

        &.relative {
            margin-bottom: 30rpx;
            padding: 40rpx;
        }

        .wish-title {
            width: 100%;
            text-align: left;
            font-size: 32rpx;
            font-weight: bold;
            color: #333333;
            margin-bottom: 30rpx;
        }


        .wish-submit {
            width: 220rpx;
            text-align: center;
            font-size: 26rpx;
            height: 60rpx;
            line-height: 60rpx;
            color: #fff;
            background-color: #0081ff;
            margin: 0 auto;
            border-radius: 10rpx;
        }

        .divider {
            width: 100%;
            height: 2rpx;
            background-color: #000;
            margin-bottom: 24rpx;
        }

        .wish-item {
            width: 100%;
            font-size: 26rpx;
            margin-bottom: 50rpx;
            overflow: hidden;

            .wish-item-title {
                float: left;
                width: 180rpx;
                font-size: 26rpx;
                padding-top: 10rpx;
                box-sizing: border-box;

                &.title2 {
                    width: 280rpx;
                    color: #666;
                }

                &.width100 {
                    width: 100%;
                }
            }

            .wish-item-add {
                float: left;
                width: 100%;
                font-size: 26rpx;
                margin: 16rpx 0;

                .wish-item-add-left {
                    float: left;
                }

                .wish-item-add-right {
                    float: right;
                    width: 100rpx;
                    height: 50rpx;
                    line-height: 50rpx;
                    background-color: #0081ff;
                    color: #fff;
                    text-align: center;
                    border-radius: 12rpx;
                }
            }

            .wish-item-experience {
                float: left;
                width: 100%;
                font-size: 26rpx;

                .wish-item-experience-con {
                    float: left;
                    width: 170rpx;
                    height: 50rpx;
                    border-radius: 8rpx;
                    padding: 0 20rpx;
                    box-sizing: border-box;
                    border: 2rpx solid #9999995e;
                    border-radius: 8rpx;
                    margin-right: 12rpx;

                    &.time {
                        width: 144rpx;
                    }
                }

                .wish-item-experience-timeRadio {
                    float: left;
                }

                .wish-item-experience-ok {
                    float: right;
                    width: 100rpx;
                    height: 50rpx;
                    line-height: 50rpx;
                    background-color: #0081ff;
                    color: #fff;
                    text-align: center;
                    border-radius: 12rpx;
                }
            }

            .wish-item-experiencebox {

                .wish-item-experienceitem {
                    float: left;
                    width: 100%;
                    height: 56rpx;
                    line-height: 56rpx;
                    font-size: 26rpx;
                    margin-top: 10rpx;

                    .wish-item-experienceite-left {
                        float: left;
                        width: 240rpx;
                    }

                    .wish-item-experienceite-cen {
                        float: left;
                    }

                    .wish-item-experienceite-right {
                        float: right;
                        width: 50rpx;
                        height: 50rpx;
                        margin-right: 20rpx;
                    }
                }
            }

            .wish-item-centen {
                float: left;
                width: 400rpx;

                &.margin-t12 {
                    margin-top: 12rpx;
                }

                &.width100 {
                    width: 100%;
                }

                &.relative {
                    position: relative;
                }

                .wish-item-centen-item {
                    float: left;
                    width: 50%;
                    height: 56rpx;
                    line-height: 56rpx;

                    .left {
                        float: left;
                    }

                    .right {
                        float: left;
                        width: 160rpx;
                        padding-left: 20rpx;
                        box-sizing: border-box;
                        border-bottom: 2rpx solid #9999995e;
                        margin-left: 14rpx;
                    }
                }

                .input {
                    height: 56rpx;
                    border-radius: 8rpx;
                    padding: 0 20rpx;
                    box-sizing: border-box;

                    &.age {
                        border: 2rpx solid #9999995e;
                        border-radius: 8rpx;
                    }
                }

                .textarea {
                    border: 2rpx solid #9999995e;
                    background-color: #f9f9f9;
                    border-radius: 8rpx;
                    box-sizing: border-box;
                    width: 100%;
                    height: 150rpx;
                    border-radius: 8rpx;
                    padding: 20rpx;
                    margin: 0 0 20rpx;
                    font-size: 24rpx;

                    &.relative {
                        margin: 0;
                        padding: 20rpx 20rpx 40rpx;
                    }
                }

                .wish-item-centen-text {
                    position: absolute;
                    right: 20rpx;
                    bottom: 12rpx;
                }
            }
        }


        .item {
            width: 100%;
            font-size: 24rpx;
            color: #666666;
            box-sizing: border-box;

            input,
            textarea {
                box-sizing: border-box;
                width: 100%;
                height: 150rpx;
                border-radius: 3rpx;
                padding: 20rpx;
                margin: 20rpx 0;
                font-size: 24rpx;
            }

            textarea {
                padding: 20rpx 0;
            }

            input {
                height: auto;
            }
        }

        .methods {
            margin-top: 30rpx;

            image {
                width: 44rpx;
                height: 44rpx;
            }

            .tip {
                text {
                    padding: 0 10rpx;
                    color: #999999;
                }
            }
        }
    }

    .footer {
        @include fixed_footer(110rpx);
        background: $uni-bg-color;

        .footer_l {
            width: calc(100% - 400rpx);
            text-align: center;

            font-size: 32rpx;
            font-weight: bold;

            text {
                color: #ff5656;
            }
        }

        .footer_r {
            width: 400rpx;
            height: 112rpx;
            background: #ff5656;
            border-radius: 30rpx 0px 0px 0px;
            color: #ffffff;
            font-weight: bold;
            font-size: 32rpx;
            text-align: center;
            line-height: 112rpx;
        }
    }

    .agreement {
        width: 100%;
        height: 500rpx;
        background-color: #fff;
    }

    .icon-del-box {
        /* #ifndef APP-NVUE */
        display: flex;
        /* #endif */
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 0;
        right: 0;
        height: 48rpx;
        width: 48rpx;
        border-radius: 50%;
        background-color: #ff5656;
        z-index: 2;
    }

    .rotate {
        position: absolute;
        transform: rotate(90deg);
    }

    .questionnaire {
        width: 100%;
        height: auto;

        .questionnaire-title {
            width: 100%;
            text-align: left;
            font-size: 32rpx;
            font-weight: bold;
            color: #333333;
            margin-bottom: 20rpx;
            padding: 0 30rpx;
            box-sizing: border-box;
        }

        .questionnaire-wrap_pay {
            position: relative;
            width: 710rpx;
            opacity: 1;
            background: #ffffff;
            border-radius: 30rpx;
            box-shadow: 0px 0px 40rpx 0px rgba(107, 127, 153, 0.20);
            margin: 0 auto 20rpx;
            padding: 40rpx;


        }

    }

    .u-radio__label {
        margin-right: 16rpx !important;
    }

    .questionnaire-wrap_pay-text {
        position: absolute !important;
        right: 0rpx !important;
        bottom: 20rpx !important;
        font-size: 32rpx;
        font-family: SF Pro Text, SF Pro Text-Regular;
        font-weight: 400;
        text-align: left;
        color: #d2d2d2;

        .red {
            color: #ff5656;
        }
    }
</style>
