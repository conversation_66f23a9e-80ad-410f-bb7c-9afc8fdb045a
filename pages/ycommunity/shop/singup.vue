<template>
    <view class="singup">
        <view class="show flex">
            <view class="show_l">
                <image :src="detail.activityInfo.image" mode="widthFix"></image>
            </view>
            <view class="show_r">
                <view class="name">{{detail.activityInfo.name}}</view>
                <view class="flex flex_between">
                    <view class="price">
                        <!-- #ifndef MP-TOUTIAO -->
                        ￥{{detail.activityInfo.price}}
                        <!-- #endif -->
                    </view>
                    <template v-if="detail.activityInfo.status===0 && detail.activityInfo.limit_crowdfunding_number===0">
                        <view class="num">剩余名额：不限制</view>
                    </template>
                    <template v-else-if="detail.activityInfo.status=== 1 &&  detail.activityInfo.limit_signup_number===0">
                        <view class="num">剩余名额：不限制</view>
                    </template>
                    <template v-else>
                        <view class="num">剩余名额：
                            <text v-if="detail.activityInfo.status===0">{{crowdfundingNumbers}}</text>
                            <text v-else>{{signupNumbers}}</text>
                            名
                        </view>
                    </template>

                </view>
            </view>
        </view>
        <view class="attr" v-if="detail.ActivityAttr&&detail.ActivityAttr.length&&!isDefault">
            <view class="list">
                <view class="item" v-for="(item, indexw) in detail.ActivityAttr" :key="indexw">
                    <view class="title">{{ item.attr_name }} </view>
                    <view class="listn acea-row row-middle">
                        <view class="itemn" :class="item.index  === itemn.attr ? 'on' : ''"
                            v-for="(itemn, indexn) in item.attr_value" v-if="itemn.attr!=='默认'"
                            @click="tapAttr(indexw, itemn.attr)" :key="indexn">
                            {{ itemn.attr }}
                        </view>
                    </view>
                </view>
            </view>
            <button type="default" @click="setOk" style="background-color: #ff5656;">确认</button>
        </view>
        <view class="has_attr" v-if="hasCheck.length">
            <view>已选择</view>
            <view v-for="(item,index) in hasCheck" :key="index" class="flex flex_align_center flex_between item">
                <view class="has_attr_l">{{item.attr}}</view>
                <view class="flex has_attr_r flex_align_center flex_between">
                    <view style="width: 170rpx;" class="flex ">
                        <u-button shape="circle" size="mini" @click="reduce(index)" :disabled="item.num<2">-</u-button>
                        <input type="text" v-model.number="item.num" disabled @input="inputChange(index)" />
                        <u-button shape="circle" size="mini" @click="plus(index)" :disabled="item.num>item.stock">+
                        </u-button>
                    </view>
                    <u-button type="primary" size="mini" @click="removeCheck(index,item.num)">删除</u-button>
                </view>
            </view>
        </view>
        <view class="result">
            <view>
                共报名：{{totalNum}} 人
            </view>
            <!-- #ifndef MP-TOUTIAO -->
            <view class="">
                总计金额：{{totalMoney}} 元
            </view>
            <template v-if="detail.activityInfo.status===0">
                <view class="">
                    活动目前报名中，须支付定金：{{payMoney}} 元
                </view>
                <view class="tips">
                    说明:报名成功后请补齐报名尾款，否则视为放弃活动名额， 截止日{{detail.activityInfo.end_time}}
                </view>
            </template>
            <!-- #endif -->
        </view>
        <view class="active_footer relative">
            <view class="absolute flex flex_align_center flex_between">
                <view class="flex flex_l flex_align_center">
                    <view class="item" @click="goPages('/pages/tabBar/index/index',false,'switchTab')">
                        <view>
                            <image src="@/static/images/community/home.png" mode="">
                            </image>
                        </view>
                        <view class="font_size20">首页</view>
                    </view>
                    <!-- #ifdef MP-WEIXIN -->
                    <view class="item"
                        @click="openWeChat">
                        <view>
                            <image src="@/static/images/community/chat.png" mode=""></image>
                        </view>
                        <view class="font_size20">客服</view>
                    </view>
                    <!-- #endif -->
                    <!-- #ifndef MP-WEIXIN -->
                    <view class="item"
                        @click="goPages(`/pages/user/CustomerList?id=${did}&type=0&scence=community`,true)">
                        <view>
                            <image src="@/static/images/community/chat.png" mode=""></image>
                        </view>
                        <view class="font_size20">客服</view>
                    </view>
                    <!-- #endif -->
                </view>
                <view class="btn ">
                    <view class="btn_r" @click="goSingUp">
                        立即报名</view>
                </view>
            </view>
        </view>
        <x-authorize></x-authorize>
        <wrapVip />
    </view>
</template>

<script>
    import wrapVip from '@/components/yuanshi/wrap-vip';
    import {
        activityDetail,
        activityCartAdd,
        activityOrderCreate,
    } from '@/api/community';
    import {
        mul,
        add
    } from '@/utils/bc';
    import {
        WX_KEFU_Link,
        WX_ENTERPRISE_Link
    } from '@/config.js';
    import {
        authNavigator,
        openWeChatCustomerService
    } from '@/utils/common.js';
    export default {
        components: {
            wrapVip
        },
        data() {
            return {
                detail: {
                    activityInfo: {
                        image: ''
                    }
                },
                hasCheck: [],
                productValue: [],
                totalMoney: 0,
                totalNum: 0,  //购买总数
                payMoney: 0,
                did: 0,
                isDefault: false,
                singleQuota: 0 ,//限制购买总数 0 不限制
                inviterId:0
            }
        },
        computed: {
            crowdfundingNumbers() {
                const {
                    limit_crowdfunding_number,
                    crowdfunding_numbers
                } = this.detail.activityInfo
                console.log('limit_crowdfunding_number',limit_crowdfunding_number)
                console.log('crowdfunding_numbers',crowdfunding_numbers)
                let a = limit_crowdfunding_number - crowdfunding_numbers;
                return a > 0 ? a : 0;
            },
            signupNumbers() {
                const {
                    limit_signup_number,
                    signup_numbers
                } = this.detail.activityInfo
                let a = limit_signup_number - signup_numbers;
                return a > 0 ? a : 0;
            }
        },
        methods: {
            openWeChat(){
                openWeChatCustomerService(WX_KEFU_Link,WX_ENTERPRISE_Link,true,this.detail.activityInfo.name,'pages/ycommunity/shop/singup.html?id='+ this.did,this.detail.activityInfo.image)
            },
            getEvaluateDetail(did) {
                activityDetail(did)
                    .then(res => {
                        const {
                            ActivityAttr,
                            ActivityValue,
                            activityInfo
                        } = res.data
                        this.detail = res.data;
                        this.productValue = ActivityValue;
                        this.singleQuota = activityInfo.single_quota;
                        if (ActivityAttr.length === 1 && ActivityAttr[0].attr_values[0] === '默认') {
                            let check = ActivityValue['默认'];
                            this.isDefault = true;
                            this.hasCheck.push({
                                attr: '默认',
                                num: 1,
                                stock: check.stock,
                                price: check.price,
                                dp_price: check.dp_price,
                                ot_price: check.ot_price,
                                unique: check.unique,
                                quota: check.quota
                            });
                            this.countMoney()
                        }
                    })
                    .catch(err => {
                        console.log(err)
                        this.$navigator(-1);
                    });

            },
            tapAttr: function(indexw, indexn) {
                let that = this;
                that.$set(that.detail.ActivityAttr[indexw], 'index', indexn);
            },
            removeCheck(index, num) {
                this.hasCheck.splice(index, 1);
                this.countMoney()
            },
            setOk() {
                if (this.detail.activityInfo.status === 0 && this.crowdfundingNumbers < 1 || this.detail.activityInfo
                    .status === 1 && this.signupNumbers < 1) {
                    return this.$showToast('无可用名额')
                }
                let value = this
                    .getCheckedValue()
                    .sort()
                    .join(',');
                if (value) {
                    let unCheckIndex = -1;
                    this.detail.ActivityAttr.forEach((item, index) => {
                        if (!item.index) {
                            return unCheckIndex = index
                        }
                    })
                    if (unCheckIndex > -1) {
                        return this.$showToast(this.detail.ActivityAttr[unCheckIndex].attr_name + '未选中属性')
                    }
                    if (this.singleQuota > 0) {
                        if (this.totalNum < this.singleQuota) {
                            console.log('setOk', this.totalNum, this.singleQuota, this.crowdfundingNumbers)
                        } else {
                            return this.$showToast('单次买票总数限制：' + this.singleQuota)
                        }
                    }
                    let hasCheck = this.hasCheck.map(item => item.attr);
                    if (hasCheck.indexOf(value) < 0) {
                        const check = this.detail.ActivityValue[value];
                        this.hasCheck.push({
                            attr: value,
                            num: 1,
                            stock: check.stock,
                            price: check.price,
                            ot_price: check.ot_price,
                            dp_price: check.dp_price,
                            unique: check.unique,
                            quota: check.quota
                        });
                        this.countMoney()
                    } else {
                        return this.$showToast('已添加')
                    }
                } else {
                    return this.$showToast('未选中属性')
                }

            },
            getCheckedValue: function() {
                let productAttr = this.detail.ActivityAttr;
                let value = [];
                for (let i = 0; i < productAttr.length; i++) {
                    for (let j = 0; j < productAttr[i].attr_value.length; j++) {
                        if (productAttr[i].index === productAttr[i].attr_value[j].attr) {
                            value.push(productAttr[i].attr_value[j].attr);
                        }
                    }
                }
                return value;
            },
            //加
            plus(index) {
                let item = this.hasCheck[index];
                const {
                    singleQuota,
                    totalNum
                } = this;
                if (item.num < item.stock) {
                    if (singleQuota > 0) {
                        let q = singleQuota - totalNum;
                        q = q > 0 ? q : 1;
                        if (totalNum < singleQuota) {
                            this.hasCheck[index].num++;
                        } else {
                            this.$showToast('单次买票总数限制：' + singleQuota)
                        }

                    } else {
                        this.hasCheck[index].num++;
                    }
                } else {
                    return this.$showToast('库存不足')
                }


                this.countMoney();
            },
            //减
            reduce(index) {
                console.log('index', index)
                let item = this.hasCheck[index];
                if (item.num > 1) {
                    this.hasCheck[index].num--;
                    this.totalNum--;
                }
                this.countMoney()
            },
            inputChange(index) {
                const {
                    singleQuota,
                    totalNum
                } = this;
                let {
                    num,
                    stock
                } = this.hasCheck[index];
                if (num < 1) {
                    this.hasCheck[index].num = 1;
                    num = 1;
                }
                if (singleQuota > 0) {
                    let q = (singleQuota - totalNum) + 1;
                    if (num > q) {
                        this.hasCheck[index].num = q;
                        this.$showToast('单次买票总数限制：' + singleQuota + '--' + q)
                    } else {
                        if (num > stock) {
                            this.hasCheck[index].num = stock;
                            this.$showToast('当前库存' + stock)
                        }
                    }
                } else {
                    if (num > stock) {
                        this.hasCheck[index].num = stock;
                        this.$showToast('当前库存' + stock)
                    }
                }
                this.$set(this.hasCheck[index], 'num', this.hasCheck[index].num);

                console.log('inputChange', index, this.hasCheck[index].num)
                this.countMoney()
            },
            countMoney: function() {
                let totalMoney = 0,
                    payMoney = 0,
                    totalNum = 0;
                let array = this.hasCheck;
                for (let i = 0; i < array.length; i++) {
                    const check = array[i],
                        num = check.num;
                    console.log(check)
                    totalNum = totalNum + Number(num);
                    totalMoney = add(totalMoney, mul(num, check.price));
                    payMoney = add(payMoney, mul(num, check.dp_price));
                }
                this.totalMoney = Number(totalMoney);
                this.payMoney = Number(payMoney);
                this.totalNum = totalNum;
            },
            goSingUp() {
                if (this.detail.activityInfo.status > 1) {
                    return this.$showToast('报名已截止，如有疑问请联系官方客服')
                }
                if (this.detail.activityInfo.status === 0 && this.crowdfundingNumbers < 1 || this.detail.activityInfo
                    .status === 1 && this.signupNumbers < 1) {
                    return this.$showToast('无可用名额')
                }
                let select = this.hasCheck.map((item) => {
                        return {
                            [item.unique]: item.num
                        }
                    }),
                    attrUnique = {};
                if (select.length) {
                    select.forEach((item) => {
                        attrUnique = Object.assign(attrUnique, item);
                    })
                }
                let obj = {
                    activityId: this.did,
                    new: 1,
                    attrUnique
                }
                activityCartAdd(obj).then(res => {
                    this.goPages('/pages/ycommunity/shop/submit?cartId=' + res.data.cartId + '&inviterId=' + this.inviterId)
                }).catch(err => {
                    this.$showToast(err.msg || err)
                })
            },
            goPages(path, type, pathType) {
                if (type) {
                    this.$authNavigator(path);
                } else {
                    this.$navigator(path, pathType);
                }
            },
        },
        onLoad(option) {
            const {
                id,
                inviterId
            } = option;
            this.did = id;
            this.inviterId = inviterId;
            this.getEvaluateDetail(id);
        },
        onShow() {

        },
    }
</script>

<style lang="scss" scoped>
    image {
        width: 100%;
        height: 100%;
    }

    .singup {
        padding: 60rpx;
    }

    .show {
        .show_l {
            width: 134rpx;
            height: 112rpx;

            image {
                border-radius: 30rpx;
            }
        }

        .show_r {
            padding: 0 0 0 20rpx;
            flex: 1;

            .name {
                font-size: 32rpx;
                font-weight: 700;
                text-align: left;
                color: #333333;
                @include show_line;
                margin-bottom: 12rpx;
            }

            .price {
                @include font_size;
                font-weight: 500;
                text-align: left;
                color: #3e3e3e;
            }

            .num {
                background: #fc5656;
                border-radius: 8rpx;
                padding: 4rpx 16rpx;
                font-size: 26rpx;
                font-family: PingFang SC, PingFang SC-Medium;
                font-weight: 500;
                text-align: left;
                color: #ffffff;
                line-height: 38rpx;
                letter-spacing: 0px;
            }
        }
    }

    .attr {

        margin-top: 60rpx;

        .list {
            max-height: 450rpx;
            overflow: auto;

            .item {
                .title {
                    font-size: 28rpx;
                    color: #333;
                    margin-bottom: 22rpx;
                }

                .listn {

                    // padding: 0 30rpx 0 16rpx;
                    .itemn {
                        border: 1px solid #bbb;
                        font-size: 26rpx;
                        color: #282828;
                        padding: 12rpx 30rpx;
                        margin: 0 16rpx 16rpx 0;

                        border-radius: 24rpx;

                        &.on {
                            color: #fc5656;
                            border: 2rpx solid #ff5656;
                        }
                    }
                }
            }
        }

        button {
            width: 616rpx;
            height: 80rpx;
            line-height: 80rpx;
            background: #666666;
            border-radius: 40rpx;
            color: #fff;
            margin: 80rpx 0;
        }
    }

    .has_attr {
        border-top: 2rpx solid #e2e6ec;
        border-bottom: 2rpx solid #e2e6ec;
        padding: 40rpx 0 78rpx 0;
        margin-top: 20rpx;

        .item {
            margin-top: 28rpx;
        }

        .has_attr_l {}

        .has_attr_r {
            width: 270rpx;

            input {
                margin: 0 10rpx;
            }
        }
    }

    .result {
        margin-top: 44rpx;
        font-size: 28rpx;
        font-family: PingFang SC, PingFang SC-Medium;
        font-weight: 500;
        text-align: left;
        color: #333333;
        line-height: 38rpx;

        .tips {
            margin-top: 28rpx;
            font-size: 24rpx;
            font-weight: 400;
            color: #ff5656;
        }
    }

    .active_footer {
        @include fixed_footer(112rpx);
        background: #f7f8fa;
        box-shadow: 0px 0px 40px 0px rgba(107, 127, 153, 0.20);

        .flex_l {
            width: calc(100% - 400rpx);
            padding: 0 60rpx;

            .item {
                // width: calc((100% - 400rpx) / 2);
                width: 50%;
                text-align: center;
                font-size: 24rpx;

                color: #3e3e3e;

                image {
                    width: 40rpx;
                    height: 40rpx;
                }
            }
        }

        .btn {
            width: 400rpx;
            height: 112rpx;
            line-height: 112rpx;
            text-align: center;

            border-radius: 30rpx 0px 0px 0px;
            font-size: 32rpx;
            background: #666666;
            color: #ffffff;
            font-weight: bold;

            .btn_r {
                background: #ff5656;
                border-radius: 30rpx 0px 0px 0px;
            }
        }
    }
</style>
