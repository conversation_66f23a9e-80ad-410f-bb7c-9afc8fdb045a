<template>
    <view class="admin-box">
        <view class="subscribe-btn">
            我的线上面试预约
        </view>
        <view class="subscribe-icon" >
            <image v-if="mainStatus" class="subscribe-icon-img" src="../../../static/images/yuanshi/check.png" mode=""></image>
            <view class="subscribe-icon-title">
                {{mainStatus?'已预约成功':'暂无预约'}}
            </view>
        </view>
        <block v-for="(item, index) in list1" :key="index" v-if="item.status != 7">
            <view class="subscribe-content">
                <view class="subscribe-content-top">
                    <view class="time">
                        预约时间：{{item.appointment_time}}
                    </view>
                    <view class="item">
                        <text class="title">咨询电话</text><text class="content">18301092661</text>
                    </view>
                    <view class="item">
                        <text class="title">工作时间</text><text class="content">9:00 - 18:00</text>
                    </view>
                </view>
                <view class="subscribe-content-bot">
                    <view class="btn" @click="editAppoint(item.id)">
                        更改面试时间
                    </view>
                    <view class="btn" @click="goAgentAppoint">
                        代人预约
                    </view>
                    <view class="btn" @click="goUserInterviewsCancel(1,item.id,index)">
                        取消预约
                    </view>
                </view>
            </view>
        </block>
        <view class="subscribe-btn agent" v-if="list2.length > 0 && list2status">
            我的代预约
        </view>
        <block v-for="(item, index) in list2" :key="index">
        <view class="subscribe-content agent" v-if="item.status != 7">
            
                <view class="subscribe-content-top agent" >
                    <view class="time">
                        预约时间：{{item.appointment_time}}
                    </view>
                    <view class="agenttext">
                        <view class="agenttext-left">
                            <view class="item">
                                <text class="title">联系人</text><text class="content">{{item.marks[0]}}</text>
                            </view>
                            <view class="item">
                                <text class="title">联系电话</text><text class="content">{{item.marks[1]}}</text>
                            </view>
                        </view>
                        <view class="agenttext-right" @click="goUserInterviewsCancel(2,item.id,index)">
                            取消预约
                        </view>
                    </view>
                </view>
            
        </view>
        </block>
    </view>
</template>

<script>
    import {
        eventAdd,
        eventProgramList,
        eventOrderList,
        userInterviewsCancel,
        userDetails,
        userInterviewsmyList
    } from '@/api/community';
    
    export default {
        props: {},
        data: function() {
            return {
                mainStatus:false, // 预约状态
                appointId:0, // 分享给用户的预约记录id
                did:0, // 活动id
                userappId:0 ,// 用户预约成功记录id
                userAppdata:{},
                list1:[],
                list2:[],
                list2status:false
            };
        },
        mounted: function() {},
        onLoad(options) {
            console.log('options',options)
            const {
                userappId,
                appointId,
                did
            } = options;
            this.userappId = userappId;
            this.appointId = appointId;
            this.did = did;
        },
        onShow() {
            // this.getUserDetails() // 获取面试预约详情
            this.getUserInterviewsmyList(1) // 获取我的面试预约列表
            this.getUserInterviewsmyList(2) // 获取我的面试预约列表
        },
        methods: {
            // 修改预约时间
            editAppoint(userappId){
                this.goPages('/pages/ycommunity/shop/userSubscribe?appointId=' + this.appointId + '&did=' + this.did + '&type=1' + '&userappId=' + userappId + '&isgetlist=1',false,'redirectTo')
            },
            goAgentAppoint(){
                this.goPages('/pages/ycommunity/shop/agentAppoint?appointId=' + this.appointId + '&did=' + this.did)  
            },
            // 获取用户面试记录详情
            getUserDetails(){
                let data = {
                    id: this.userappId
                }
                userDetails(data)
                    .then(res => {
                        console.log('获取预约详情成功', res)
                        this.userAppdata = res.data;
                        this.getUserInterviewsmyList(1) // 获取我的面试预约列表
                        this.getUserInterviewsmyList(2) // 获取我的面试预约列表
                    })
                    .catch(err => {
                        console.log('获取预约详情失败', err)
                    });
            },
            getUserInterviewsmyList(type) {
                let data = {
                    type: type,
                    schedule_id:this.appointId,
                    limit: '',
                    page: ''
                }
                userInterviewsmyList(data)
                    .then(res => {
                        if(type == 1){
                            this.list1 = res.data;
                            for(let i =0; i < this.list1.length;i++){
                                if(this.list1[i].status != 7){
                                    this.mainStatus = true
                                }
                            }
                            console.log('获取用户面试预约记录', this.list1)
                        }
                        if(type == 2){
                            this.list2 = res.data;
                            console.log('获取代约记录', this.list2)
                            for(let i= 0; i< this.list2.length;i++){
                                this.list2[i].marks = this.list2[i].mark.split(",");
                                if(this.list2[i].status != 7){
                                    this.list2status = true;
                                }
                            }
                            console.log('获取代约记录', this.list2)
                        }
                    })
                    .catch(err => {
                        console.log('获取用户面试预约记录失败', err)
                        // this.$navigator(-1);
                    });
            },
            
            // 用户取消预约
            goUserInterviewsCancel(type,id,index){
                let that = this;
                let data = {
                    id: id,
                    status:7 
                }
                userInterviewsCancel(data)
                    .then(res => {
                        console.log('取消预约成功', res)
                        if(res.status == 200){
                            this.$showToast('取消预约成功');
                            if(type == 1){
                                this.list1[index].status = 7
                                this.goPages('/pages/ycommunity/shop/detail?id=' + this.did)
                            }
                            if(type == 2){
                                this.list2[index].status = 7
                            }
                            // that.$navigator(-1);
                        }
                    })
                    .catch(err => {
                        console.log('取消预约失败', err)
                    });
            },
            
            goPages(path, type, pathType) {
                if (type) {
                    this.$authNavigator(path);
                } else {
                    this.$navigator(path, pathType);
                }
            }
        }
    };
</script>
<style scoped lang="scss">
    .admin-box {
        width: 100%;
        padding: 40rpx 20rpx;
        box-sizing: border-box;
        overflow: auto;
        
        .subscribe-btn {
            width: 688rpx;
            height: 100rpx;
            line-height: 100rpx;
            background: #d4e8fd;
            border: 2rpx solid #3399ff;
            border-radius: 40rpx;
            font-size: 32rpx;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: center;
            color: #3399ff;
            margin: 0 auto;
            &.agent {
                margin-bottom: 32rpx;
            }
        }
        .subscribe-icon {
            width: 100%;
            overflow: auto;
            .subscribe-icon-img {
                display: block;
                width: 120rpx;
                height: 120rpx;
                margin: 66rpx auto 0rpx;
                box-sizing: border-box;
            }
            .subscribe-icon-title {
                width: 100%;
                font-size: 28rpx;
                font-family: PingFang SC, PingFang SC-Bold;
                font-weight: 700;
                text-align: left;
                color: #50506f;
                text-align: center;
                margin-bottom: 48rpx;
                margin-top: 24rpx;
            }
        }
        .subscribe-content {
            width: 710rpx;
            min-height: 376rpx;
            background: #ffffff;
            border-radius: 30rpx;
            box-shadow: 0 0 40rpx 0 rgba(107,127,153,0.20); 
            margin: 0 auto 60rpx;
            padding: 48rpx 40rpx;
            box-sizing: border-box;
            &.agent {
                margin-bottom: 16rpx;
                min-height: 250rpx;
            }
            
            .subscribe-content-top {
                width: 100%;
                border-bottom: 2rpx solid #eaeaea;
                padding-bottom: 22rpx;
                box-sizing: border-box;
                
                &.agent {
                    border-bottom: 0;
                    .agenttext {
                        width: 100%;
                        overflow: auto;
                        
                        .agenttext-left {
                            float: left;
                        }
                        .agenttext-right {
                            float: right;
                            width: 152rpx;
                            height: 60rpx;
                            line-height: 60rpx;
                            background: #eaeaea;
                            border-radius: 24rpx;
                            text-align: center;
                            font-size: 24rpx;
                            font-family: PingFang SC, PingFang SC-Regular;
                            font-weight: 400;
                            text-align: center;
                            color: #50506f;
                            margin-top: 20rpx;
                        }
                    }
                    
                }
                
                .time {
                    width: 100%;
                    font-size: 32rpx;
                    font-family: PingFang SC, PingFang SC-Regular;
                    font-weight: 400;
                    text-align: left;
                    color: #50506f;
                    margin-bottom: 32rpx;
                }
                .item {
                    width: 100%;
                    font-size: 24rpx;
                    font-family: PingFang SC, PingFang SC-Regular;
                    font-weight: 400;
                    text-align: left;
                    color: #333333;
                    overflow: auto;
                    margin-bottom: 10rpx;
                    
                    .title {
                        float: left;
                        margin-right: 32rpx;
                    }
                    .content {
                        float: left;
                    }
                }
            }
            .subscribe-content-bot {
                width: 100%;
                padding-top: 32rpx;
                overflow: auto;
                
                .btn {
                    float: left;
                    height: 60rpx;
                    line-height: 60rpx;
                    text-align: center;
                    background: #eaeaea;
                    border-radius: 24rpx;
                    font-size: 24rpx;
                    font-family: PingFang SC, PingFang SC-Regular;
                    font-weight: 400;
                    text-align: center;
                    color: #50506f;
                    padding: 0 24rpx;
                    margin-right: 20rpx;
                }
            }
        }
        
        
        
    }
</style>
