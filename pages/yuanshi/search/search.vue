<template>
	<view class="search">
		<view class="logo">
			<xSearch v-model="keywords" @search="search" :fixed="false" :btn="true" :history="true" :focus="false" type="search"
				:clear="true" @change="change" @focus="pannel = true"></xSearch>
		</view>
		<view class="relative">
			<view class="result absolute" @click="pannel = false">
				<view class="block" v-if="result.evaluated&&result.evaluated.length">
					<view class="tab flex flex_align_center flex_between">
						<view class="title">
							<view>已测评活动</view>
							<view class="line"></view>
						</view>
						<view class="more" @click="showMore('evaluated_more')">展开 <u-icon
								:name="result.evaluated_more?'arrow-down':'arrow-right'" color="#333333" size="24">
							</u-icon>
						</view>
					</view>
					<view class="wrap" :class="{active:result.evaluated_more}">
						<view v-for="(item, index) in result.evaluated" :key="item.id"
							@click="goPages(item,'evaluated')" class="relative item">
							<view class=" bg">
								<image :src="item.image + ossResize" mode="aspectFill"></image>
							</view>
							<view class="item_b absolute flex flex_column flex_between">
								<view class="item_t">
									<view class="name">{{ item.name }}</view>
									<view class="money">￥ {{ item.price }}</view>
								</view>
								<view class="item_b">
									<!-- <view class="b_l flex flex_align_center">
										<view class="span">
										
										</view>
										<view class="num font_size20">
											
										</view>
									</view> -->
								</view>
							</view>

						</view>
					</view>
				</view>
				<view class="block" v-if="result.not_evaluated&&result.not_evaluated.length">
					<view class="tab flex flex_align_center flex_between">
						<view class="title">
							<view>待测评活动</view>
							<view class="line"></view>
						</view>
						<view class="more" @click="showMore('not_evaluated_more')">展开 <u-icon
								:name="result.not_evaluated_more?'arrow-down':'arrow-right'" color="#333333" size="24">
							</u-icon>
						</view>
					</view>
					<view class="wrap" :class="{active:result.not_evaluated_more}">
						<view v-for="(item, index) in result.not_evaluated" :key="item.id"
							@click="goPages(item,'evaluated')" class="relative item">
							<view class=" bg">
								<image :src="item.image + ossResize" mode="aspectFill"></image>
							</view>
							<view class="item_b absolute flex flex_column flex_between">
								<view class="item_t">
									<view class="name">{{ item.name }}</view>
									<view class="money"
										v-if="(item.status === 1 || item.status === 4) && item.vip_price">￥
										{{ item.price }}
									</view>
								</view>
								<view class="item_b">
									<!-- <view class="b_l flex flex_align_center">
									<view class="span">
									
									</view>
									<view class="num font_size20">
										
									</view>
								</view> -->
								</view>
							</view>

						</view>
					</view>
				</view>
				<view class="block" v-if="result.activity&&result.activity.length">
					<view class="tab flex flex_align_center flex_between">
						<view class="title">
							<view>活动</view>
							<view class="line"></view>
						</view>
						<view class="more" @click="showMore('activity_more')">展开 <u-icon
								:name="result.activity_more?'arrow-down':'arrow-right'" color="#333333" size="24">
							</u-icon>
						</view>
					</view>
					<view class="wrap" :class="{active:result.activity_more}">
						<view v-for="(item, index) in result.activity" :key="item.id" @click="goPages(item,'activity')"
							class="item relative">
							<view class=" bg">
								<image :src="item.image + ossResize" mode="aspectFill"></image>
							</view>
							<view class="item_b absolute flex flex_column flex_between">
								<view class="item_t">
									<view class="name">{{ item.name }}</view>
									<view class="money">￥ {{ item.price }}</view>
								</view>
								<view class="item_b">
									<view class="b_l flex flex_align_center">
										<view class="span">
											<!-- <text v-if="item.status === 0">众筹中</text> -->
                                            <text v-if="item.status === 0">报名中</text>
											<text v-else-if="item.status === 1">报名中</text>
											<text v-else-if="item.status === 2">活动举行中</text>
											<text v-else-if="item.status === 3">活动结束</text>
											<text v-else-if="item.status === 4">活动已取消</text>
										</view>
										<view class="num font_size20">
											<text v-if="item.limit_numbers>0">
												限制人数：{{item.limit_numbers}}
											</text>
										</view>
									</view>
								</view>

							</view>
						</view>
					</view>
				</view>
				<view class="block" v-if="result.product&&result.product.length && isToutiaoPay">
					<view class="tab flex flex_align_center flex_between">
						<view class="title">
							<view>商品</view>
							<view class="line"></view>
						</view>
						<view class="more" @click="showMore('product_more')">展开 <u-icon
								:name="result.product_more?'arrow-down':'arrow-right'" color="#333333" size="24">
							</u-icon>
						</view>
					</view>
					<view class="wrap" :class="{active:result.product_more}">
						<view v-for="(item, index) in result.product" :key="item.id" @click="goPages(item,'product')"
							class="item relative">
							<view class=" bg">
								<image :src="item.image + ossResize" mode="aspectFill"></image>
							</view>
							<view class="item_b absolute flex flex_column flex_between">
								<view class="item_t">
									<view class="name">{{ item.store_name }}</view>
									<view class="money">￥
										{{ item.price }}
									</view>
								</view>
								<view class="item_b">
									<view class="b_l flex flex_align_center">
										<!-- 			<view class="span">
				
									</view> -->
										<view class="num font_size20">
											销量：{{item.sales}}
										</view>
									</view>
								</view>

							</view>

						</view>
					</view>
				</view>
				<view class="block" v-if="result.special&&result.special.length">
					<view class="tab flex flex_align_center flex_between">
						<view class="title">
							<view>课程</view>
							<view class="line"></view>
						</view>

						<view class="more" @click="showMore('special_more')">展开 <u-icon
								:name="result.special_more?'arrow-down':'arrow-right'" color="#333333" size="24">
							</u-icon>
						</view>
					</view>
					<view class="wrap" :class="{active:result.special_more}">
						<view v-for="(item, index) in result.special" :key="item.id" @click="goPages(item,'special')"
							class="item relative">
							<view class=" bg">
								<image :src="item.image + ossResize" mode="aspectFill"></image>
							</view>
							<view class="item_b absolute flex flex_column flex_between">
								<view class="item_t">
									<view class="name">{{ item.title }}</view>
									<view class="money">￥
										{{ item.money }}
									</view>
								</view>
								<view class="item_b">
									<view class="b_l flex flex_align_center">
										<view class="span">
											<text>共{{item.total_count || 1}}节</text>
										</view>
										<view class="num font_size20">
											浏览量：{{item.browse_count}}
										</view>
									</view>
								</view>

							</view>

						</view>
					</view>
				</view>
				<view v-if="!isShow" class="noresult">
					<view class="text_center res">{{keywords ? '~~换个搜索条件试试~~':'~请输入搜索条件~~'}}</view>
					<!-- <view class="tab flex flex_between flex_align_end">
						<view class="title">
							<view>新评推荐</view>
							<view class="line"></view>
						</view>
					</view>
					<xList :arr="recommendList" :btn="true" :vip="false"></xList> -->
				</view>
			</view>
		</view>
		<x-authorize :isAuto="false" @login="init"></x-authorize>
	</view>
</template>

<script>
	import xLogo from '@/components/x-logo/x-logo.vue';
	import xSearch from '@/components/x-search/x-search.vue';
	import xList from '@/components/yuanshi/x-list.vue';
	import {
		ossImgParams
	} from '@/utils/oss.js';
	import {
		yIndex
	} from '@/api/yuanshi/public';
	import {
		searchLabel,
		searchGo
	} from '@/api/yuanshi/public.js';
	import {
		reportSearch,
		reportSearchLabel,
		reportSearchResultClick
	} from '@/utils/ReportAnalytics.js';
    import {
        IS_TOUTIAO_PAY
    } from '@/config.js';
	export default {
		components: {
			xLogo,
			xList,
			xSearch
		},
		data() {
			return {
                isToutiaoPay:IS_TOUTIAO_PAY,
				show: 1,
				value: '45622',
				keywords: '',
				pannel: false,
				recommendList: [],
				pannelList: [],
				result: {
					evaluated: [],
					not_evaluated: [],
					special: [],
					product: [],
					activity: [],
					wish: []
				},
				ossResize: ossImgParams({
					w: 710,
					h: 300,
					m: 'fill',
					q: 80
				})
			};
		},
		computed: {
			isShow() {
				const {
					special,
					product,
					activity,
					wish,
					evaluated = [],
					not_evaluated = [],
				} = this.result;

				return (evaluated.length || not_evaluated.length || special.length || product.length || activity.length ||
					wish.length) ? true : false;

			}
		},
		methods: {
			showMore(type) {
				this.result[type] = !this.result[type];
			},
			init() {
				this.recommendList = [];
				this.page = {
					page: 1,
					limit: 10,
					more: true
				};
				yIndex().then(res => {
					this.recommendList = res.data.new_comment_recommend;
				});
			},
			goPages(item, type) {
				let page = '';
				switch (type) {
					case 'evaluated':
						if (item.type === 1) {
							page = '/pages/yuanshi/wish/detail?wid=' + item.id + '&pid=' + item.product_id;
						} else {
							page = '/pages/yuanshi/evaluate/detail?wid=' + item.id + '&pid=' + item.product_id;
						}
						break;
					case 'wish':
						page = '/pages/yuanshi/wish/detail?wid=' + item.id + '&pid=' + item.product_id;
						break;
					case 'activity':
						page = '/pages/ycommunity/shop/detail?id=' + item.id;
						break;
					case 'product':
						page = '/pages/shop/GoodsCon?id=' + item.id;
						break;
					case 'special':
						page = '/pages/yknowledge/course/detail?sid=' + item.id;
						break;
				}

				reportSearchResultClick({
					click_name: item.name
				});

				console.log(page)
				this.$navigator(page);
			},
			labelClick(item, check, idx, idx1) {
				this.$set(this.pannelList[idx].label_group[idx1], 'is_checked', !check);
				console.log(this.pannelList[idx].label_group[idx1]);
			},
			pannelClick(typ) {
				if (typ) {
					this.search(this.keywords);
				} else {
					this.handleLabel(0);
				}
			},
			handleLabel(type) {
				// 0 清空标签 1选中转数组
				let arr = [];
				this.pannelList.forEach((item, index) => {
					item.label_group.forEach((item1, index1) => {
						if (type === 0) {
							item1.is_checked = false;
						} else if (type === 1) {
							if (item1.is_checked) {
								arr.push(item1.name);
							}
						}
					});
				});
				if (type === 1) {
					return arr.toString();
				}
			},
			change(val) {
				this.keywords = val;
			},
			search(val) {
				this.keywords = val;
				this.$loadingToast('搜索中')
				let labels = this.handleLabel(1);
				reportSearch({
					search_key: val
				});
				reportSearchLabel({
					label_name: labels
				});
				searchGo({
					keyword: val,
					labels: labels
				}).then(res => {
					this.$hideLoading()
					const {
						special,
						product,
						activity,
						wish,
						evaluated = [],
						not_evaluated = [],
					} = res.data;
					this.result = {
						special_more: false,
						product_more: false,
						activity_more: false,
						wish_more: false,
						evaluated_more: false,
						not_evaluated_more: false,
						...res.data
					};

					if (!this.isShow) {
						this.$showToast('换个搜索条件试试');
					}

				});
				this.pannel = false;
				// this.$emit('search', val);
			}
		},
		onLoad(option) {
			const {value=''} = option;
			if(value){
				this.search(value)
			}
			// this.init();
			// searchLabel().then(res => {
			// 	this.pannelList = res.data;
			// });
		}
	};
</script>
<style lang="scss">
	page {
		background: #ffffff;
	}
</style>
<style scoped lang="scss">
	.search {
		.logo {
			padding: 60rpx 40rpx;
			// height: 200rpx;
			// background: #ffffff;
			// position: relative;
			// z-index: 9999;
			// padding-bottom: 70rpx;
			// box-shadow: 0px 15px 30px -30rpx rgba(107, 127, 153, 0.3);
			// border-radius: 0px 0px 70rpx 70rpx;
		}

		.result {
			// top: 60rpx;
			padding: 0rpx 40rpx;
			min-height: calc(100vh - 410rpx);

			.block {
				margin-bottom: 60rpx;

				.tab {
					margin-bottom: 10rpx;
				}

				.title {
					padding-left: 20rpx;

					.line {
						width: 100%;
						height: 8rpx;
						background: linear-gradient(90deg, #ff5a73 0%, #ffa969 100%);
						border-radius: 2rpx;
					}
				}

				.more {
					color: #333;
					font-size: 24rpx;
					font-weight: 400;
					color: #333333;
					line-height: 40rpx;
				}

				.wrap {
					max-height: 240rpx;
					overflow: hidden;
					transition: max-height 0.5s linear;

					&.active {
						max-height: 1300rpx;
						overflow: scroll;
					}

					.item {
						padding: 0;
						height: 240rpx;
						margin-bottom: 20rpx;

						.bg {
							height: 100%;

							image {
								border-radius: 30rpx;

								&:after {
									content: '';
									width: 100%;
									height: 100%;
									position: absolute;
									left: 0;
									top: 0;
									border-radius: 30rpx;
									background: linear-gradient(270deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.46) 100%);
									// filter: blur(2px);
								}
							}
						}

						.absolute {
							padding: 24rpx 26rpx 24rpx 30rpx;
							top: 0;
							height: 100%;
							color: #fff;

							.item_t {
								.name {
									font-size: 28rpx;
									font-weight: bold;
								}

								.money {
									font-size: 24rpx;
									font-weight: 400;
									margin-top: 6rpx;
								}

							}

							.item_b {
								.b_l {
									.span {
										padding: 0 10rpx;
										height: 36rpx;
										border-radius: 14rpx;
										line-height: 33rpx;
										text-align: center;
										// font-size: 22rpx;
										color: #ffffff;
										background: #ff5656;

										text {
											@include font_size(22);
										}

										&.no {
											background: #d2d2d2;
										}
									}
								}
							}
						}
					}
				}
			}

			.noresult {
				// text-align: center;
				margin: 0 auto 60rpx auto;
				color: #666666;
				.res {
					padding: 200rpx 0;
					font-size: 24rpx;
					color: #ccc;
				}
				.tab {
					margin-bottom: 40rpx;

					.title {
						font-size: 32rpx;
						font-weight: bold;
						color: #333333;
						padding-left: 12rpx;

						.line {
							width: 132rpx;
							height: 8rpx;
							background: linear-gradient(90deg, #ff5a73 0%, #ffa969 100%);
							border-radius: 2rpx;
						}
					}

					.more {
						color: #999999;
					}
				}
			}
		}
	}
</style>
