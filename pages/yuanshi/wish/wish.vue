<template>
	<view class="wish">
		<view class="nav">
			<view class="logo"><image src="@/static/images/yuanshi/ysxc.png" mode="widthFix"></image></view>
			<xLogo :disabled="true" :logo="false"></xLogo>
		</view>
		<view class="banner">
			<xBanner :arr="banner"></xBanner>
			<view class="btn">
				<view class="flex flex_between relative">
					<button :disabled="disabled" class="flex flex_around" @click="btnClick">
						<view class="flex flex_align_center">
							<view class="icon">
								<image src="@/static/images/yuanshi/xcup.png" mode="widthFix"></image>
							</view>
							<!-- <text>想测</text> -->
							<view class="xc flex flex_align_center">
								<image src="@/static/images/yuanshi/xc.png" mode="widthFix"></image>
							</view>
						</view>
					</button>
					<view class="say_feeling" @click="goPages('/pages/yuanshi/wish/info')"></view>
				</view>
				
				<view class="txt ">
					<!-- <text class="font_size20">~ 没找到你要的测评报告就点这儿 ~</text> -->
				</view>
			</view>
		</view>
		<view class="wrap">
			<xTab @tabClick="tabClick" :active="-1" :reCancel="true" :arr="tabArr"></xTab>
			<xList :btn="true" :arr="wishList" type="wish"></xList>
			<view v-if="!page.more"><uniLoadMore status="noMore"></uniLoadMore></view>
		</view>
		<x-authorize  @login="loginReload" :isAuto="false"></x-authorize>
	</view>
</template>

<script>
import xLogo from '@/components/x-logo/x-logo.vue';
import xBanner from '@/components/yuanshi/x-banner.vue';
import xTab from '@/components/yuanshi/x-tab.vue';
import xList from '@/components/yuanshi/x-list.vue';
import { wishIndex, wish, wishNum } from '@/api/yuanshi/wish.js';
export default {
	props: {
		value: {
			type: '',
			default: ''
		}
	},
	components: {
		xLogo,
		xBanner,
		xList,
		xTab
	},
	data() {
		return {
			disabled: false,
			wishList: [],
			banner: [],
			page: {
				page: 1,
				limit: 10,
				more: true
			},
			requestLoading:false,
			tabArr: [{ label: '已测评', num: 10, status: 1 }, { label: '待测评', num: 10, status: 2 }],
			tabStatus: 0
		};
	},
	methods: {
		goPages(path, type) {
			this.$navigator(path, type);
		},
		tabClick(item, type) {
			this.page = {
				page: 1,
				limit: 10,
				more: true
			};
			this.wishList = [];
			if (type) {
				this.tabStatus = 0;
			} else {
				this.tabStatus = item.status;
			}
			this.getWish();
		},
		btnClick() {
			this.goPages('/pages/yuanshi/wish/make');
			this.disabled = true;
		},
		getWish() {
			if (!this.page.more || this.requestLoading) return;
			this.requestLoading = true;
			const {page,limit} = this.page;
			wish({ page: page, limit:limit, status: this.tabStatus })
				.then(res => {
					// if(res.data.length>this.wishList.length){
					// 	this.getWishNum();
					// }
					
					this.requestLoading = false;
					this.wishList = this.wishList.concat(res.data);
					this.page.more = res.data.length ===limit;
					this.page.page++;
				})
				.catch(err => {
					this.requestLoading = false;
					this.$navigator(-1);
				});
		},
		getWishNum(){
			wishNum().then(res => {
				this.tabArr = [{ label: '已测评', num: res. data.evaluated, status: 1 }, { label: '待测评', num: res.data.not_evaluated, status: 2 }];
			});
		},
		loginReload(){
			this.wishList=[];
			this.page={
				page: 1,
				limit: 10,
				more: true
			};
			this.getWish()
		},
		init() {
			this.getWish();
			this.getWishNum();
			wishIndex().then(res => {
				this.banner = res.data.banner;
			});
			
		}
	},
	mounted() {},
	onShow() {
		this.disabled = false;
		this.init();
		this.setTabBarIndex(1); //index为当前tab的索引
	},
	onLoad(option) {},
	onReachBottom() {
		this.getWish();
	}
};
</script>

<style scoped lang="scss">
.wish {
	image {
		width: 100%;
		height: 100%;
	}
	.nav{
		.logo {
			padding: 52rpx 0 40rpx 40rpx;
			image {
				width: 350rpx;
				// height: 146rpx;
			}
		}
	}
	.banner {
		// margin: 35rpx 0 0rpx 0;
		margin: 20rpx 0 0rpx 0;
		.btn {
			margin-top: -10rpx;
			padding: 0 50rpx 0 46rpx;

			button {
				width: 582rpx;
				// margin: 0 auto;
				height: 100rpx;
				// line-height: 100rpx;
				
				background: #FF5656;
				border-radius: 40rpx;
				font-weight: 400;
				color: #ffffff;
				font-size: 32rpx;
				.icon {
					margin-right: 13rpx;
					width: 42rpx;
					height: 42rpx;
					image {
						width: 42rpx;
					height: 42rpx;
					}
				}
				.xc{
					height: 40rpx;
					image{
						width: 391rpx;
						height: 40rpx;
					}
				}
			}
			.say_feeling{
				&::after{
					top: 0;
					right: 0;
					transform: scale(0.55);
				}
			}
			.txt{				
				color: #999999;
				text-align: center;
				padding: 16rpx 0 36rpx 0;
			}
		}
	}
	.wrap {
		padding: 0 40rpx;
		min-height: calc(100vh - 120rpx);
	}
}
</style>
