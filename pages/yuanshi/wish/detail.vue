<template>
	<view class="active">
		<eSwiper :arr="detail.wishInfo.image" :info="detail.wishInfo" type="wish"></eSwiper>
		<view class="main">
			<view class="wish_title padding">
				<view class="flex flex_between">
					<view class="title">
						{{ (detail.wishInfo.status === 1 || detail.wishInfo.status === 4 )? '恭喜心愿达成！期待你的心愿造福全人类！' : '这货目前还是个空想，速速分享让全人类帮你达成心愿吧！' }}
					</view>
					<!-- 已完成 -->
					<view class="image" v-if="detail.wishInfo.status === 1 || detail.wishInfo.status === 4"><image src="@/static/images/yuanshi/finsh.png" mode="widthFix"></image></view>
					<view class="image" @click="goPages('/pages/yuanshi/wish/make?type=edit&id=' + detail.wishInfo.id)" v-else-if="detail.is_my && detail.wishInfo.status != 3">
						<!-- <view class="image" @click="goPages('/pages/yuanshi/wish/make?type=edit&id='+detail.wishInfo.id,true)" > -->
						<image src="@/static/images/yuanshi/set.png" mode="widthFix"></image>
						<view><text class="font_size20">修改</text></view>
					</view>
				</view>
				<view class="flex flex_align_center flex_between">
					<template v-if="detail.wishInfo.status === 1 || detail.wishInfo.status === 4">
						<button
							class="flex_line_height "
							style="width: 100%;"
							@click="goPages('/pages/yuanshi/evaluate/detail?wid=' + detail.wishInfo.id + '&pid=' + detail.wishInfo.product_id)"
						>
							点击查看
						</button>
					</template>
					<template v-else>
						<button class="flex_line_height btn_l" open-type="share">
							<view class="flex">
								<image src="@/static/images/yuanshi/share2.png" mode="widthFix"></image>
								<text>分享</text>
							</view>
						</button>
						<button class="flex_line_height btn_r" @click="btnClick(detail.is_want_to_test)">{{ detail.is_want_to_test ? '已想测' : '想测+1' }}</button>
					</template>
				</view>
			</view>
			<view class="padding show_title ">
				<view class=" flex flex_align_center flex_between">
					<view class="title">活动简介</view>
					<!-- 		<view class="icon_more">
						查看更多
					</view> -->
				</view>
				<view class="desc">{{ detail.wishInfo.desc || '等待添加' }}</view>
			</view>
			<template v-if="detail.want_testInfo&&detail.want_testInfo.length">
			<view class="bottom_line"></view>
			<view class="want_go">
				<view class="nav flex flex_align_center flex_between">
					<view class="title">这些人也想测</view>
					<button type="default" class="go flex_line_height" @click="btnClick(detail.is_want_to_test)">
						<text>{{ detail.is_want_to_test ? '已想测' : '想测+1' }}</text>
					</button>
				</view>
				<view class="wrap ">
					<scroll-view scroll-x style="white-space:nowrap;" :scroll-with-animation="true">
						<view class="item" v-for="(item, index) in detail.want_testInfo" :key="item.uid" @click="goPages('/pages/yuanshi/user/home?id=' + item.uid)">
							<image :src="item.avatar" mode="aspectFill"></image>
							<view class="name">{{ item.nickname }}</view>
							<view class="btn flex_line_height" @click.stop="goFollow(item.is_follow, item.uid, index)">{{ item.is_follow ? '已跟随' : '跟随' }}</view>
						</view>
					</scroll-view>
				</view>
			</view>
			</template>
			<template v-if="detail.surrounding_activity&&detail.surrounding_activity.length">
			<view class="bottom_line"></view>
			<view class="goods "><xRecom title="附近推荐" :arr="detail.surrounding_activity" :ids="mixinsParam" :line="false" :more="false"></xRecom></view>
			</template>
			<view class="bottom_line"></view>
			<view class="message" :class="{ minHeight: minHeight }">
				<view class="mess_tab flex flex_between">
					<view class="mess">
						评论区
						<text class="font_size20">{{ detail.messageCount || 0 }} 评论</text>
					</view>
					
					<view class="flex flex_align_center">
					<!-- 	<view class="write item flex_line_height" :class="{ active: isOnlyMy }" @click="onlyShowMy()">
							<text>{{ isOnlyMy ? '查看全部' : '只看我的' }}</text>
						</view> -->
							<view class="write flex_line_height" @click="showInput(1)"><text>我来评论</text></view>
					</view>
					
				
				</view>
				<view class="message_wrap padding">
					<view class="comment">
						<xComment ref="xComment" :handle="true" @success="sendSuccess" :ctype="1" :rid="mixinsParam.wishId" :time="false" :info="messageInfo"></xComment>
					</view>
				</view>
			</view>
		</view>
		<x-authorize @login="login"></x-authorize>
	</view>
</template>

<script>
import { SHARE_ID } from '@/config.js';
import readyToPay from '@/mixins/readyToPay';
import yuanshiDetail from '@/mixins/yuanshiDetail';
// #ifdef H5
import { isWeixin } from '@/utils/validate.js';
import { openShareAll } from '@/utils/wechat/share.js';
// #endif
import { uniSelectorQueryInfo } from '@/utils/uni_api.js';
import xRecom from '@/components/yuanshi/x-recom.vue';
import xComment from '@/components/x-comment/x-comment/x-comment';
import { wishDetail, wishWant, wishUnWant } from '@/api/yuanshi/wish.js';
import { yComment, yCommentAdd,yCommentMe } from '@/api/yuanshi/public.js';
import { userFollow, userUnFollow, commentLike, commentUnLike } from '@/api/yuanshi/user.js';
import {reportWishDetail} from '@/utils/ReportAnalytics.js'
export default {
	mixins: [readyToPay, yuanshiDetail],
	components: {
		xComment,
		xRecom
	},
	data() {
		return {
			detail: {
				wishInfo: {
					desc: ''
				}
			},
			mixinsParam: {
				wishId: 12,
				productId: 76
			},
			messageInfo: [],
			requestLoading: false,
			page: {
				page: 1,
				limit: 20,
				more: true
			},
			shareConfig: {},
			isOnlyMy:false,
			minHeight:false
			
		};
	},
	methods: {
		onlyShowMy(typ = false) {
			this.messageInfo = [];
			this.page = {
				page: 1,
				limit: this.page.page,
				more: true
			};
			if (!typ) {
				this.isOnlyMy = !this.isOnlyMy;
			}
			if (this.isOnlyMy) {
				this.$refs.xComment.inputBlur();
			}
			this.getComment();
		},
		showInput(type = 1) {
			let wishInfo = this.detail.wishInfo;
			this.$refs.xComment.showInput(type,{wish_id: wishInfo.id});
		},
		sendSuccess() {
			this.detail.messageCount++;
			this.$refs.xComment.inputBlur();
		},
		share() {},
		goPages(path, auth) {
			if (auth) {
				this.$authNavigator(path);
			} else {
				this.$navigator(path);
			}
		},
		btnClick(is_want_to_test) {
			let id = this.detail.wishInfo.id;
			if (is_want_to_test) {
				wishUnWant(id).then(res => {
					this.detail.is_want_to_test = false;
					this.$showToast('已取消');
				});
			} else {
				wishWant(id).then(res => {
					this.detail.is_want_to_test = true;
					this.$showToast('想测+1');
				});
			}
		},
		goFollow(type, id, idx) {
			if (this.$store.state.userInfo.uid === id) {
				return this.$showToast('不能对自己进行相关操作');
			}
			if (type) {
				userUnFollow(id).then(res => {
					this.detail.want_testInfo[idx].is_follow = false;
					this.$showToast('已取消');
				});
			} else {
				userFollow(id).then(res => {
					this.$showToast('跟随成功');
					this.detail.want_testInfo[idx].is_follow = true;
				});
			}
		},
		getWishDetail(wid) {
			wishDetail(wid).then(res => {
				res.data.surrounding_activity.map((item, index) => {
					item.yw_index = item.yw_index ? item.yw_index.toFixed(1) : '0.0';
				})
				
				
				reportWishDetail({ id: wid,wish_name:res.data.wishInfo.name, uid: this.$store.state.userInfo.uid || 0 });
				
				this.detail = res.data;
				this.$updateTitle(res.data.wishInfo.name);
				// this.messageInfo = res.data.messageInfo;
				let data = res.data.wishInfo;
				this.shareConfig = {
					desc: data.desc,
					title: data.name,
					link: '/pages/yuanshi/wish/detail?wid=' + this.mixinsParam.wishId + '&pid=' + this.mixinsParam.productId,
					imgUrl: data.image[0]
				};
				// #ifdef H5
				openShareAll(this.shareConfig);
				// #endif
			}).catch(err => {
					this.$navigator(-1);
				});;
		},
		getComment() {
			if (!this.page.more || this.requestLoading) return;
			this.requestLoading = true;
			
			let obj = {
					related_id: this.mixinsParam.wishId, type: 1, page: this.page.page, limit: this.page.limit
				},
				req = '';
			if (this.isOnlyMy) {
				req = yCommentMe(obj);
			} else {
				req = yComment(obj);
			}
		
			req.then(res => {
				
				if (!this.minHeight) {
					this.minHeight = res.data.length ? true : false;
				}
				
					this.messageInfo = this.messageInfo.concat(res.data);
					this.page.more = res.data.length === this.page.limit;
					this.page.page++;
					this.requestLoading = false;
				})
				.catch(err => {
					this.requestLoading = false;
				});
		},
		login() {
			const { wishId, productId } = this.mixinsParam;
			this.getWishDetail(wishId);
			this.messageInfo = [];
			this.page = {
				page: 1,
				limit: 20,
				more: true
			};
			this.getComment();
		}
	},
	onPageScroll(e) {
		this.$refs.xComment.inputBlur();
	},
	mounted() {},
	onLoad(option) {
		const { wid, pid=0 } = option;
		this.mixinsParam = {
			wishId: wid,
			productId: pid
		};
		this.getWishDetail(wid);
		this.getComment();
	},
	onPullDownRefresh() {
		this.stopPullRefresh(1000);
	},
	// #ifdef MP
	onShareAppMessage() {
		if (this.shareConfig) {
			return {
				title: this.shareConfig.title,
				imageUrl: this.shareConfig.imgUrl,
				path: this.shareConfig.link,
                templateId: SHARE_ID
			};
		}
	},
	onShareTimeline() {
		if (this.shareConfig) {
			return {
				title: this.shareConfig.title,
				imageUrl: this.shareConfig.imgUrl,
				query: this.shareConfig.link.split('?')[1]
			};
		}
	},

	// #endif
	onReachBottom() {}
};
</script>
<style>
page {
	background: #f7f7f9;
}
</style>
<style scoped lang="scss">
.bottom_line {
	height: 2rpx;
	background: #e2e6ec;
	margin: 0 48rpx;
}
button {
	width: 300rpx;
	margin: 30rpx auto;
}
.active {
	image {
		width: 100%;
		height: 100%;
	}
	.icon_more {
		font-size: 24rpx;
		color: #555555;
		.iconfont {
			font-size: 24rpx;
			color: #555555;
		}
	}
	.padding {
		padding: 32rpx 36rpx 32rpx 34rpx;
		margin: 0 16rpx 20rpx 16rpx;
		background: #fff;
	}
	.main {
		position: absolute;
		width: 100%;
		margin-top: -18rpx;
		.wish_title {
			border-radius: 16rpx;
			background: #fff;
			margin: 0 16rpx 20rpx 16rpx;
			padding-bottom: 34rpx;
			.title {
				max-width: 500rpx;
				font-weight: bold;
				font-size: 52rpx;
				@include show_line(3);
			}
			.image {
				text-align: center;
				color: #999999;
				image {
					width: 52rpx;
					height: 52rpx;
				}
			}
			button {
				height: 100rpx;
				background: #ff5656;
				opacity: 1;
				border-radius: 40rpx;

				color: #ffffff;
				font-size: 24rpx;
				image {
					width: 30rpx;
					margin-right: 12rpx;
				}
				&.btn_l {
					width: 410rpx;
				}
				&.btn_r {
					width: 200rpx;
				}
			}
		}
		.show_title {
			font-weight: 400;
			background: none;
			.title {
				font-size: 32rpx;

				font-weight: bold;
				color: #333333;
			}

			.desc {
				margin: 30rpx 0;
				@include show_line(5);
			}
		}
		.want_go {
			padding: 0 0 70rpx 48rpx;
			.nav {
				padding-right: 48rpx;
				.title {
					color: #333333;
					font-weight: bold;
					font-size: 32rpx;
				}
				.go {
					width: 148rpx;
					height: 60rpx;
					border: 2rpx solid #ff5656;
					border-radius: 24rpx;
					margin: 60rpx 0 40rpx 0;
					color: #ff5656;
					font-size: 24rpx;
				}
			}

			.wrap {
				.item {
					display: inline-block;
					&:not(:last-child) {
						padding-right: 8rpx;
					}
					image {
						width: 120rpx;
						height: 120rpx;
						border-radius: 50%;
					}
					.name {
						text-align: center;
						margin: 16rpx 0;
						color: #999999;
						font-weight: bold;
						font-size: 28rpx;
						width: 120rpx;
						@include show_line;
					}
					.btn {
						// width: 94rpx;
						height: 40rpx;
						border: 2rpx solid #d2d2d2;
						border-radius: 14rpx;
						margin: 0 auto;
						color: #999999;
						padding: 0 12rpx;
					}
				}
			}
		}
		.goods {
			padding: 70rpx 0 70rpx 48rpx;
		}
		.message {
			margin-top: 80rpx;
			min-height: 500rpx;
			&.minHeight {
				min-height: 100vh;
			}
			.mess_tab {
				// width: 688rpx;
				margin: 30rpx 40rpx;
				.mess {
					// margin-left: 40rpx;
					font-size: 28rpx;

					color: #333333;
					font-weight: bold;
					text {
						padding: 0 16rpx;

						font-weight: 400;
						color: #999999;
					}
				}
				.write {
					width: 148rpx;
					height: 60rpx;
					border: 2rpx solid #ff5561;
					background: #ff5656;
					color: #fff;
					border-radius: 24rpx;
					text {
						display: inline-block;
						font-size: 24rpx;
					}
					&.item {
						color: #ff5656;
						background: none;
						margin-right: 20rpx;
						&.active {
							border: 2rpx solid #50506f;
							color: #50506f;
						}
					}
				}
			}
			.message_wrap {
				// max-height: 800rpx;
				// overflow-y: scroll;
				background: none;
				.comment {
				}
			}
		}
	}
}
</style>
