<template>
	<view class="make">
		<form @submit="submit" ref="form">
			<view class="item">
				<view class="label">想测项目名称（必填）</view>
				<view class="input"><input type="text" v-model="form.name" name="name" /></view>
			</view>
			<view class="item">
				<view class="label">地址（必填）</view>
				<view class="input"><input type="text" v-model="form.position" name="position" /></view>
			</view>
	<!-- 		<view class="item">
				<view class="label">时间 (必填)</view>
				<view class="input">
					<picker name="date" mode="date" :value="form.date" :start="startDate" :end="endDate" @change="bindDateChange">
						<input type="text" v-model="form.date" :disabled="true" />
					</picker>
				</view>
			</view> -->
			<view class="item">
				<view class="label">想测原因</view>
				<view class="input textarea"><xTextarea height="200rpx" name="desc" v-model="form.desc" @input="input"></xTextarea></view>
			</view>
			<view class="item">
				<view class="label">图片上传</view>
				<view class="image">
					<xImageUpload ref="xImageUpload" :num="6" width="200rpx" height="200rpx" borderRadius="26rpx" :show="3" margin="0 30rpx 20rpx 0" @chooseImage="chooseImage" @delImg="chooseImage">
						<view class="icon flex flex_around flex_align_center"><image src="@/static/images/yuanshi/upload.png" mode="widthFix"></image></view>
					</xImageUpload>
				</view>
			</view>
			<view class="item">
				<view @click="check = !check">
					<xProtocol v-model="check"></xProtocol>
				</view>
			</view>
			<button form-type="submit" :disabled="disabled">确认上传</button>
		</form>
	</view>
</template>

<script>
import xTextarea from '@/components/x-textarea/x-textarea.vue';
import xProtocol from '@/components/x-protocol/x-protocol';
import xImageUpload from '@/components/x-image-upload/x-image-upload';
import { uploadImg } from '@/utils/upload.js';
import { wishAdd,wishEdit, wishDetail } from '@/api/yuanshi/wish.js';
import{reportWishMake} from '@/utils/ReportAnalytics.js';
import { regHref } from '@/utils/validate.js';
export default {
	props: {
		value: {
			type: '',
			default: ''
		}
	},
	components: { xTextarea, xImageUpload ,xProtocol},
	data() {
		const currentDate = this.getDate({
			format: true
		});
		return {
			form: {
				date: currentDate,
				desc: '',
				name: '',
				position: '',
				image: []
			},
			check: true,
			disabled: false
		};
	},
	computed: {
		startDate() {
			return this.getDate('start');
		},
		endDate() {
			return this.getDate('end');
		}
	},
	methods: {
		goPages(path, type) {
			this.$navigator(path, type);
		},
		chooseImage(arr) {
			this.form.image = arr;
		},
		input(val) {
			// console.log(val);
			// this.desc = val;
		},
		submit(e) {
			let val = e.detail.value;
			if (!val.name.length) {
				return this.$showToast('请确定活动名称');
			}
			if (!val.position.length) {
				return this.$showToast('请添加位置描述');
			}
			if (!this.form.image.length) {
				return this.$showToast('请添加活动图片');
			}
			if (!this.check) {
				return this.$showToast('请勾选协议');
			}
			this.disabled = true;
			if (this.makeType === 'edit') {
				val.id=this.wishInfo.id;
				if (this.form.image === this.copyImage) {
					val.image = this.form.image;
					this.wishEdit(val);
				} else {
					let arr = [],
						arr1 = [];
					this.form.image.forEach((item, index) => {
						if (regHref(item)) {
							arr.push(item); //https
						} else {
							arr1.push(item);
						}
					});
					
					if (arr1.length) {
						uploadImg(arr1)
							.then(res => {
								val.image = [...res, ...arr];
								this.wishEdit(val);
							})
							.catch(err => {
								console.log('图片上传失败', err);
								this.$showToast('图片上传失败-' + err);
							});
					} else {
						val.image = this.form.image;
						this.wishEdit(val);
					}
					
					// uploadImg(this.form.image)
					// 	.then(res => {
					// 		console.log(res);
					// 		val.image = res;
					// 		this.wishEdit(val);
					// 	})
					// 	.catch(err => {
					// 		this.$showToast('图片上传失败-'+err);
					// 		this.disabled = false;
					// 	});
				}
			} else {
				uploadImg(this.form.image)
					.then(res => {
						// console.log(res);
						val.image = res;
						this.wishAdd(val);
					})
					.catch(err => {
						this.$showToast('图片上传失败-'+err);
						this.disabled = false;
					});
			}
		},
		wishAdd(val) {
			wishAdd(val)
				.then(res => {
					// this.$showToast('想测愿望单已经提交审核，您可在【我的】-【我的想测】中关注审核进度');
					let _this = this;
					this.$showModal('提示','想测愿望单已经提交审核，您可在【我的】-【我的想测】中关注审核进度',{
						complete:function(){
							_this.$navigator(-1);
						}
					});
					this.disabled = false;
					this.$refs.xImageUpload.imgList = [];
					Object.keys(this.form).forEach((item, index) => {
						this.form[item] = '';
					});
				})
				.catch(err => {
					this.$showToast('添加失败-'+err);
					this.disabled = false;
				});
		},
		wishEdit(val){
			wishEdit(val)
				.then(res => {
					this.$showToast('编辑成功');
					this.disabled = false;
					this.$refs.xImageUpload.imgList = [];
					Object.keys(this.form).forEach((item, index) => {
						this.form[item] = '';
					});
					
					const history = getCurrentPages();
					if (history.length > 1) {
						let beforePage = history[history.length - 2];
						beforePage.$vm.getWishDetail(val.id);
						
						this.$navigator(-1);
					}
					
					
					
				})
				.catch(err => {
					this.$showToast('编辑失败-'+err);
					this.disabled = false;
				});
		},
		bindDateChange: function(e) {
			this.form.date = e.target.value;
		},
		getDate(type) {
			const date = new Date();
			let year = date.getFullYear();
			let month = date.getMonth() + 1;
			let day = date.getDate();

			if (type === 'start') {
				year = year - 2;
			} else if (type === 'end') {
				year = year + 2;
			}
			month = month > 9 ? month : '0' + month;
			day = day > 9 ? day : '0' + day;
			return `${year}-${month}-${day}`;
		}
	},
	mounted() {},
	onLoad(option) {
		const { type = 'write',id } = option;
		this.makeType = type;
		if (type === 'edit') {
			// 编辑
			wishDetail(id).then(res => {
				let wishInfo = res.data.wishInfo;
				Object.keys(this.form).forEach((item, index) => {
					this.form[item] = wishInfo[item] ;
				});
				this.wishInfo = res.data.wishInfo;
				this.copyImage = wishInfo.image;
				this.$refs.xImageUpload.imgList = wishInfo.image;
			});
		}else{
			reportWishMake({uid:this.$store.state.userInfo.uid})
		}
	},
	onShow() {
		this.disabled = false
	}
};
</script>

<style scoped lang="scss">
.make {
	padding:60rpx 40rpx;
	image {
		width: 100%;
		height: 100%;
	}
	form {
		.item {
			margin-bottom: 46rpx;
			.label {
				font-size: 24rpx;
				font-weight: 400;
				line-height: 34rpx;
				color: #666666;
				padding:0 0 20rpx 20rpx;
			}
			.input {
				border: 2rpx solid rgba(0, 0, 0, 0);
				box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
				border-radius: 26rpx;
				padding: 26rpx 40rpx;
				background: #ffffff;
			
				&:not(.textarea){
					height: 80rpx;
					display: flex;
					align-items: center;
				}
				input{
					font-size: 24rpx;
					width: 100%;
				}
			}
			.image {
				.icon {
					width: 100%;
					height: 200rpx;
					background: #f4f4f4;
					border: 2rpx solid #d2d2d2;
					border-radius: 26rpx;
					image {
						width: 66rpx;
					}
				}
			}
		}
		button {
			height: 100rpx;
			line-height: 100rpx;
			background: #ff5656;

			font-size: 24rpx;

			color: #ffffff;
			border-radius: 40rpx;
		}
	}
}
</style>
