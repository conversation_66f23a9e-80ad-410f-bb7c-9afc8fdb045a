<template>
	<view class="show_vip">
		<view class="des">
			<view class="nav flex flex_align_end">
				<image src="@/static/images/yuanshi/logo.png" mode="widthFix"></image>
				<view>想测说明：</view>
			</view>
			<view class="list" style="width: 100%;overflow: hidden;">
				<view class="item font_size20" v-for="(item, index) in memberDesc">{{ item }}</view>
			</view>
			<button :disabled="disabled" class="flex flex_around" @click="btnClick">
				<view class="flex flex_align_center">
					<view class="icon">
						<image src="@/static/images/yuanshi/xcup.png" mode="widthFix"></image>
					</view>
					<!-- <text>想测</text> -->
					<view class="xc flex flex_align_center">
						<image src="@/static/images/yuanshi/xc.png" mode="widthFix"></image>
					</view>
				</view>
			</button>
		</view>
		<view class="show">
			<view class="vip" v-if="recommendWantToTest.length">
				<view class="nav flex flex_align_center flex_between">
					<view class="title">
						大家都想测
						<view class="line"></view>
					</view>
					<!-- <view class="more"><text class="font_size20">查看更多</text></view> -->
				</view>
				<view class="list"><xList :arr="recommendWantToTest" :btn="true"  type="wish"></xList></view>
			</view>
		</view>
	</view>
</template>
<script>
import xList from '@/components/yuanshi/x-list.vue';
import { vipExclusive, memberDesc,recommendWantToTest } from '@/api/yuanshi/public.js';
export default {
	components: {
		xList
	},
	data() {
		return {
			recommendWantToTest: [],
			disabled: false,
			page: {
				page: 1,
				limit: 10,
				more: true
			},
			requestLoading: false,
			memberDesc: []
		};
	},
	methods: {
		btnClick() {
			this.goPages('/pages/yuanshi/wish/make');
			this.disabled = true;
		},
		goPages(path) {
			this.$navigator(path);
		},

		getRecommendWantToTest() {
			if (!this.page.more || this.requestLoading) return;
			this.requestLoading = true;
			recommendWantToTest({ page: this.page.page, limit: this.page.limit })
				.then(res => {
					this.requestLoading = false;
					this.recommendWantToTest = this.recommendWantToTest.concat(res.data);
					this.page.more = res.data.length === this.page.limit;
					this.page.page++;
				})
				.catch(err => {
					this.requestLoading = false;
				});
		}
	},
	onShow() {
		this.disabled = false;
		this.getRecommendWantToTest();
		memberDesc().then(res => {
			if (res.data.want_to_test_desc.length) {
				this.memberDesc = res.data.want_to_test_desc;
			}
		});
	},
	onReachBottom() {
		this.getRecommendWantToTest();
	}
};
</script>
<style lang="scss">
page {
	background: $uni-bg-color;
}
</style>
<style scoped lang="scss">
.show_vip {
	.des {
		padding: 52rpx 40rpx;
		min-height: 650rpx;
		.nav {
			font-size: 24rpx;
			color: #333333;
			margin-bottom: 50rpx;
			image {
				width: 140rpx;
				height: 68rpx;
				margin-right: 20rpx;
			}
		}
		.font_size20 {
			transform-origin: left;
			width: 118%;
			color: #999999;
		}
		button {
			margin: 70rpx auto 58rpx auto;
			height: 100rpx;
			// line-height: 100rpx;
			background: #ff5656;
			border-radius: 40rpx;
			font-weight: 400;
			color: #ffffff;
			font-size: 32rpx;
			.icon {
				margin-right: 13rpx;
				width: 42rpx;
				height: 42rpx;
				image {
					width: 42rpx;
				height: 42rpx;
				}
			}
			.xc{
				height: 40rpx;
				image{
					width: 456rpx;
					height: 40rpx;
				}
			}
		}
	}
	.show {
		padding: 0 36rpx;
		.vip {
			.nav {
				.title {
					padding-left: 16rpx;
					font-size: 32rpx;

					font-weight: bold;

					color: #333333;
					.line {
						width: 100%;
						height: 8rpx;
						background: linear-gradient(90deg, #ffa969 0%, #ff5a73 100%);
						border-radius: 2rpx;
					}
				}
				.more {
					color: #999999;
				}
			}
			.list {
				padding-top: 40rpx;
			}
		}
	}
}
</style>
