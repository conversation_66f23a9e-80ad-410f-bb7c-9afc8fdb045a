<template>
	<view class="lotteryDraw">
		<view>感谢你的参与</view>
		<view class="title">活动奖品领取流程</view>

		<view class="progress acea-row ">
			<view :class="{ active: progress < 4 }">
				<view class="num">1</view>
				<view class="label">调查与反馈</view>
			</view>
			<view class="acea-row row-middle line"><view class="line"></view></view>
			<view :class="{ active: progress > 1 && progress < 4 }">
				<view class="num">2</view>
				<view class="label">抽取奖品</view>
			</view>
			<view class="acea-row row-middle line"><view class="line"></view></view>
			<view :class="{ active: progress > 2 }">
				<view class="num">3</view>
				<view class="label">领取</view>
			</view>
		</view>
		<!-- 问卷调查环节 -->
		<view v-if="progress === 1" class="content">
			<view class="title">调查问卷</view>
			<view class="radio">
				<view class="issue">您从哪得知乌托邦活动消息? (单选)</view>
				<view class="answer"><QA type="radio" :arr="questions" :isTextarea="true" @change="groupChange" @input="inputChange"></QA></view>
			</view>
			<view class="checkbox">
				<view>多选</view>
				<view class="answer"><QA type="checkbox" :arr="questions1" :isTextarea="true" @change="groupChange" @input="inputChange"></QA></view>
			</view>

			<view class="image">
				<view class="issue">你最满意的展区是？</view>
				<xImageUpload :num="3" width="100rpx" height="100rpx"></xImageUpload>
			</view>
		</view>

		<!-- 抽奖环节 -->
		<view v-if="progress === 2" class="choujiang">
			<view class="title">抽奖品</view>
			<view class="wrap">
				<view class="evaluate1" v-for="(item, index) in evaluteData" :key="index" :class="['item' + index, { active: active === index }]">{{ item.name }}</view>
			</view>

			<view class="btn choujiangBtn " @click="play">抽奖</view>
			<view class="show" v-show="isFinsh">
				<template v-if="isEvalute">
					<view>抽中奖品</view>
				</template>
				<template v-else>
					<view>谢谢参与</view>
				</template>
			</view>
		</view>
		<view v-if="progress === 3" class="draw">
			<view class="draw_status">
				<view class="title">奖品</view>
				<view class="acea-row row-middle image">
					<image src="@/static/images/bargainBg.jpg" mode="aspectFill"></image>
					<view>中奖奖品名称</view>
				</view>
				<view class="status">领取状态： 未领取</view>
			</view>
			<view class="draw_way">
				<view class="title">领取方式</view>
				<view class="acea-row tab">
					<view @click="drawWay = 1" :class="{ active: drawWay === 1 }">现场领取</view>
					<view @click="drawWay = 2" :class="{ active: drawWay === 2 }">物流派送</view>
				</view>
				<view v-if="drawWay === 1">
					<view>请询问现场工作人员</view>
					<view class="acea-row row-bottom">
						<image src="" mode="" alt="二维码"></image>
						<view>领取核验二维码</view>
					</view>
				</view>
				<view v-if="drawWay === 2">
					邮寄地址：
					<textarea :value="drawAddress" placeholder="" />
				</view>
			</view>

			<view class="okBtn btn">确认领取</view>
		</view>

		<view class="lotteryDrawBtn btn" @click="nextProgress" v-if="progress < 3 && showBtn">下一步</view>
		<x-authorize></x-authorize>
	</view>
</template>

<script>
// #ifdef H5
import { isWeixin } from '@/utils/validate.js';
import { openShareAll } from '@/utils/wechat/share.js';
// #endif
import xImageUpload from '@/components/x-image-upload/x-image-upload';
import QA from '@/components/yuanshi/QA';
import { SHARE_ID } from '@/config.js';
export default {
	components: { xImageUpload, QA },
	data() {
		return {
			progress: 3,
			current: null,
			questions: [{ value: 1, name: '朋友圈' }, { value: 2, name: '路过看展' }, { value: 3, name: '源未公众号' }, { value: 4, name: '其它' }],
			questions1: [{ value: 1, name: '朋友圈' }, { value: 2, name: '路过看展' }, { value: 3, name: '源未公众号' }, { value: 4, name: '其它' }],
			showBtn: true,
			// 抽奖环节
			evaluteData: [], //奖品数组
			total: 5, //轮盘转的总圈数
			tj: 0, //当前轮盘圈数
			active: 0, //当前所在位置
			speed: 100, //抽奖跑动的初始速度
			timer: null,
			evalute: 6, //抽中的奖品,
			runNum: 0,
			isFinsh: false, //抽奖动作完成
			isEvalute: true, //是否中奖
			isJoin: false, //是否已经参与过活动

			// 领奖环节
			drawWay: 1,
			drawAddress: ''
		};
	},
	watch: {
		isJoin(a, b) {
			this.showBtn = a;
			console.log(a, b);
		}
	},
	methods: {
		goPages(path, type) {
			this.$navigator(path, type);
		},
		groupChange(type, mark, value) {},
		inputChange(type, mark, value) {},
		nextProgress() {
			this.progress++;
			if(this.progress===2){
				this.initChouJian()
			}
		},

		play() {
			// if (this.isJoin) {
			// 	return this.$showToast('已参与过活动');
			// }
			this.init();
			this.timer = setTimeout(this.start, this.speed);
		},
		start() {
			this.active = this.tj % this.evaluteData.length; //轮盘效果

			// this.active = Math.floor(Math.random() * 9); //随机效果

			this.tj++;
			if (this.tj < this.runNum - 9) {
				this.timer = setTimeout(this.start, this.speed);
			} else if (this.tj >= this.runNum - 9 && this.tj < this.runNum + this.evalute) {
				this.speed += (this.tj - this.runNum + 9) * this.total;
				this.timer = setTimeout(this.start, this.speed); //继续执行抽奖滚动
			}
			if (this.tj >= this.runNum + this.evalute) {
				console.log('抽奖动作完成', this.active);
				this.isFinsh = true;
				this.isJoin = true;
				// if(this.evalute>7){
				// 	this.active = null;
				// 	this.active = 4;
				// 	this.isEvalute = false
				// }else{

				// }
				clearTimeout(this.timer);
			}
		},
		init() {
			this.tj = 0;
			this.isEvalute = true;
			// this.active=0;
			this.speed = 100;
			this.timer = null;
			this.runNum = this.total * 9;
		},
		initChouJian() {
			// 初始化抽奖相关 数据
			this.evaluteData = [
				{ value: '1', name: '奖品1' },
				{ value: '2', name: '奖品2' },
				{ value: '3', name: '奖品3' },
				{ value: '4', name: '谢谢参与 4' },
				{ value: '5', name: '奖品5' },
				{ value: '6', name: '奖品6' },
				{ value: '7', name: '奖品7' },
				{ value: '8', name: '奖品8' },
				{ value: '9', name: '奖品9' }
			];
			this.evalute = 6; //根据返回数据已知 中奖商品
			this.showBtn = this.isJoin;
			this.isEvalute = true; //根据返回数据已知是否中奖
			
			uni.onNetworkStatusChange(function(res) {
				if (res.isConnected) {
				} else {
					this.$showToast('无网络连接，请稍后再试');
				}
			});
		}
	},
	mounted() {},
	onLoad(options) {
		const { type } = options;
		this.progress = parseInt(type);
	},
	onShow() {
		if (this.progress === 2) {
			
			this.initChouJian()
			
		}
	},
	onPullDownRefresh() {
		this.stopPullRefresh(1000);
	},
	onReachBottom() {},
	// #ifdef MP
	onShareAppMessage() {
		return {
			title: '',
			imageUrl: '',
			path: '',
            templateId: SHARE_ID
		};
	}
	// #endif
};
</script>

<style scoped lang="scss">
$width: 660rpx;
$item: calc(#{$width} / 3);
@mixin position($left: 0rpx, $top: 0rpx) {
	left: $left;
	top: $top;
}
.lotteryDraw {
	padding: 30rpx;
	.title {
		font-weight: bolder;
		border-left: 10rpx solid #000;
		padding-left: 20rpx;
		font-size: 30rpx;
		margin: 30rpx 0;
	}
	.btn {
		width: 400rpx;
		margin: 80rpx auto;
		text-align: center;
		height: 80rpx;
		line-height: 80rpx;
		background: #00aaea;
		color: #fff;
		font-weight: bold;
	}
	.progress {
		> view {
			min-width: 100rpx;
			text-align: center;
		}
		.num {
			width: 50rpx;
			height: 50rpx;
			line-height: 50rpx;
			border: 1px solid red;
			border-radius: 50%;
			margin: 0 auto 20rpx auto;
		}
		.line {
			height: 50rpx;
			> view {
				width: 150rpx;
				height: 2rpx;
				background: #000;
			}
		}
		.active {
			> view {
				background: #00bfff;
			}
		}
	}
	.content {
		.issue {
			font-weight: bold;
			font-size: 30rpx;
			padding: 20rpx 0;
			&:before {
				content: '';
				display: inline-block;
				width: 10rpx;
				height: 10rpx;
				background: #000;
				vertical-align: middle;
				border-radius: 50%;
				margin-right: 14rpx;
			}
		}
		.answer {
			width: 300rpx;
			margin-left: 100rpx;
		}
	}

	.choujiang {
		.wrap {
			position: relative;
			margin: 80rpx auto;
			width: $width;
			height: $width;
			.evaluate1 {
				position: absolute;
				width: $item;
				height: $item;
				text-align: center;
				line-height: $item;
				background: #ccc;
				@for $i from 0 through 8 {
					&.item#{$i} {
						@if $i<3 {
							@include position(calc(#{$i} * #{$item}), 0);
						} @else if $i<6 {
							@include position(calc((#{$i} - 3) * #{$item}), $item);
						} @else {
							@include position(calc((8 - #{$i}) * #{$item}), calc(2 * #{$item}));
						}
					}
				}
				&.active {
					background: red !important;
				}
				&.play {
					background: #c0c0c0;
					@include position($item, $item);
				}
			}
		}
		.choujiangBtn {
		}
	}

	.draw {
		.image {
			image {
				width: 300rpx;
				height: 300rpx;
			}
		}
		.draw_way {
			.tab {
				margin-bottom: 30rpx;
				> view {
					padding: 20rpx 40rpx;
					&.active {
						background: #ccc;
					}
				}
			}
			image {
				width: 300rpx;
				height: 300rpx;
			}
			textarea {
				margin-top: 20rpx;
				border: 1px solid red;
				padding: 20rpx;
				height: 200rpx;
			}
		}
		.okBtn {
		}
	}

	.lotteryDrawBtn {
	}
}
</style>
