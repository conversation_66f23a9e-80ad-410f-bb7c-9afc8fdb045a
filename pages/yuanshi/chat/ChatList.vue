<template>
    <view class="chat">
        <!-- 		<view class="search">
			<view class="title font_size20">活动搜索</view>
			<xSearch :fixed="false" type="x" @search="search"></xSearch>
		</view> -->
        <view class="tab">
            <xTab @tabClick="tabClick" :arr="navlist"></xTab>
        </view>
        <view class="list">
            <view class="item flex " v-for="item in list" :key="item.id" @click="goPages(item)">
                <template v-if="chatType === 0">
                    <view class="item_l relative">
                        <image :src="item.avatar" mode="aspectFill"></image>
                        <view class="dot absolute" v-if="item.recording.remind === 0"></view>
                    </view>
                    <view class="item_r">
                        <view class="name flex flex_between">
                            <text>{{ item.nickname }}</text>
                            <text class="time font_size20">{{ item.time }}</text>
                        </view>
                        <view class="des">
                            <view class="msg" v-if="item.noSend">
                                <text class="red">[ 草稿 ]</text>
                                {{ item.noSend.content }}
                            </view>
                            <view class="msg" v-else-if="item.failSend">
                                <text class="red">[ 发送失败 ]</text>
                                {{ item.failSend.msn }}
                            </view>
                            <view class="msg" v-else-if="item.recording">{{ item.recording.msn }}</view>
                        </view>
                    </view>
                </template>
                <template v-else>
                    <!-- 		<view class="item_l relative">
						<image :src="item.avatar" mode="aspectFill"></image>
						<view class="dot absolute" v-if="item.remind===0"></view>
					</view> -->
                    <view class="item_r">
                        <view class="des txt">
                            <!-- <view class="dot" v-if="item.remind == 0"></view> -->
                            <view class="msg" :class="item.remind == 1?'gray':''">{{ item.msn }}</view>
                        </view>
                        <view class="name flex flex_between">
                            <!-- <view class="relative">
								<view class="dot absolute" ></view>
								<text class="txt">{{ item.nickname }}</text>
							</view> -->
                            <view class=""> </view>
                            <text class="time font_size20">{{ item.add_time }}</text>
                        </view>

                    </view>
                </template>
            </view>
        </view>
        <x-authorize :isHidden="true" @login="init()"></x-authorize>
    </view>
</template>
<script>
    import {
        messageList,
        systemMessageList,
        systemeeadingRrecord,
        systemMessageStatus
    } from '@/api/yuanshi/user';
    import storage from '@/utils/storage.js';
    import {
        CHAR_MESS,
        SEND_FAIL_MSG
    } from '@/config';
    import xSearch from '@/components/x-search/x-search.vue';
    import xTab from '@/components/yuanshi/x-tab2.vue';
    export default {
        data() {
            return {
                list: [],
                page: {
                    page: 1,
                    limit: 20,
                    more: true
                },
                chatType: 1,
                navlist: [{
                    label: '系统消息',
                    type: 1,
                    status:false
                },
                {
                    label: '用户消息',
                    type: 0,
                    status:false
                },]
            };
        },
        components: {
            xSearch,
            xTab
        },
        methods: {
            tabClick(item, type) {
                this.chatType = item.type;
                this.init();
            },
            init() {
                this.page = {
                    page: 1,
                    limit: 20,
                    more: true
                };
                this.list = [];
                this.getList();
            },
            getSystemMessageStatus() {
                let that = this;
                systemMessageStatus({}).then(res => {
                     if(res.data.system_message_number == 1){
                         this.navlist[0].status = true;
                         // console.log('系统是否有未读',this.navlist[0].status)
                     }
                     if(res.data.user_message_number == 1){
                         this.navlist[1].status = true;
                         // console.log('系统是否有未读',this.navlist[0].status)
                     }
                });
            },
            getList() {
                if (!this.page.more) return;
                if (this.chatType === 1) {
                    // 系统消息
                    systemMessageList({
                        page: this.page.page,
                        limit: this.page.limit
                    }).then(res => {
                        this.list = this.list.concat(res.data);
                        this.page.more = res.data.length < this.page.limit;
                        this.page.page++;
                   
                    });
                    
                } else {
                    // 好友消息
                    messageList({
                        page: this.page.page,
                        limit: this.page.limit
                    }).then(res => {
                        let data = res.data;
                        data.forEach((item, index) => {
                            let toUid = item.to_uid;
                            let noSend = storage.get(`${CHAR_MESS}chat${toUid}`),
                                failSend = storage.get(`${SEND_FAIL_MSG}${toUid}`);
                            if (noSend) {
                                item.noSend = JSON.parse(noSend);
                            }
                            if (failSend) {
                                failSend = JSON.parse(failSend);
                                item.failSend = failSend[failSend.length - 1];
                            }
                            if (item.recording) {
                                let recording = item.recording;
                                item.recording = recording[recording.length - 1];
                                item.recordLen = recording.length;
                            }
                        });
                        this.list = this.list.concat(data);
                        this.page.more = res.data.length < this.page.limit;
                        this.page.page++;
                    });
                }
                
            },
            goPages(item) {
                if (this.chatType === 0) {
                    this.$navigator('/pages/yuanshi/chat/ChatRoom?toUid=' + item.to_uid);
                    this.goreading(item.id)
                } else {
                    if (!item.url) {
                        return this.$showToast('地址不存在')
                    }
                    this.$linkpress(item.url)
                    this.goreading(item.id)
                }
            },
            goreading(id) {
                let that = this;
                let status;
                if(this.chatType == 0){
                    status = 1
                }
                if(this.chatType == 1){
                    status = 2
                }
                systemeeadingRrecord({
                    id: id,
                    type: status
                }).then(res => {
                     
                });
            },
            // // 检测系统是否有未读
            // getList1() {
            //     let that = this;
            //     if (!that.page.more) return;
            //     systemMessageList({
            //         page: that.page.page,
            //         limit: that.page.limit
            //     }).then(res => {
            //         let list1 = [];
            //         list1 = list1.concat(res.data);
            //         that.page.more = res.data.length < that.page.limit;
            //         that.page.page++;
            //         for (let i = 0; i < list1.length; i++) {
            //             if (list1[i].remind == 0) {
            //                 that.navlist[0].status = true;
            //             }
            //         }
            //     });
            //     // console.log('系统是否有未读',that.navlist[0].status)
            // },
            
        },
        mounted() {},
        onLoad() {
            // this.getList1()
        },
        onShow() {
            this.init();
            this.getSystemMessageStatus()
        },
        onReachBottom() {
            this.getList();
        }
    };
</script>
<style lang="scss">
    page {
        background: $uni-bg-color;
    }
</style>
<style scoped lang="scss">
    .chat {
        padding: 0 36rpx;

        .search {
            .title {
                color: #999999;
                padding: 20rpx;
            }
        }

        .tab {
            padding: 70rpx 0 0px 0;
        }

        .list {
            .item {
                padding: 28rpx 0;

                border-bottom: 2rpx solid #c0c0c1;

                .item_l {
                    margin-right: 28rpx;

                    image {
                        width: 100rpx;
                        height: 100rpx;
                        background: #d8d8d8;
                        border-radius: 50%;
                    }

                    .dot {
                        width: 24rpx;
                        height: 24rpx;
                        background: #ff5656;
                        border: 2rpx solid #ffffff;
                        border-radius: 50%;
                        top: 0;
                        right: 0;
                    }
                }

                .item_r {
                    flex: 1;

                    .name {
                        font-weight: bold;
                        color: #333333;
                        font-size: 32rpx;

                        .time {
                            font-weight: 400;
                            color: #999999;
                        }

                        .dot {
                            width: 24rpx;
                            height: 24rpx;
                            background: #ff5656;
                            border: 2rpx solid #ffffff;
                            border-radius: 50%;
                            top: 10rpx;
                            left: 0;
                        }
                    }

                    .txt {
                        // margin-left: 30rpx;
                    }

                    .des {
                        margin-top: 10rpx;
                        color: #999999;
                        font-weight: 400;

                        .msg {
                            word-break: break-all;
                            text-wrap: wrap;
                            font-size: 24rpx;
                            // padding: 10rpx 0 0 0;
                            color: #333333;
                            margin-left: 30rpx;

                            text {
                                padding: 0 10rpx 0 0;

                                &.red {
                                    color: red;
                                }
                            }
                        }
                        .gray {
                            color: #999;
                        }
                    }

                    .txt {
                        position: relative;

                        .dot {
                            position: absolute;
                            left: 0;
                            top: 0;
                            width: 24rpx;
                            height: 24rpx;
                            background-color: #ff5656;
                            border-radius: 50%;
                        }
                    }
                }
            }
        }
    }
</style>
