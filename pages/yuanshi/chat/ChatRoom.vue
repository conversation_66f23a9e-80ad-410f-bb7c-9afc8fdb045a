<template>
	<view class="chat_room" id="chatRroom">
		<view v-if="loadingStatus"><uni-load-more status="loading"></uni-load-more></view>
		<view class="chat" v-if="history.length > 0"><xChatWrap :arr="history" :all="true" v-model="toUid" :show="show"></xChatWrap></view>
		<!-- 未发送成功 -->
		<view class="chat" :style="{ marginTop: marginTop }" v-if="SendFailMsg.length > 0">
			<xChatWrap :arr="SendFailMsg" :all="false" :right="true" v-model="toUid" @againSendMsg="againSendMsg"></xChatWrap>
		</view>
		<xChat :adjustPosition="false" @send="sendMsg" :uid="toUid" :inputShow="true" desc="chat"  ref="xChat"></xChat>
		<x-authorize :isHidden="true" @login="init('d')"></x-authorize>
	</view>
</template>
<script>
import storage from '@/utils/storage.js';
import { VUE_APP_WS_URL, CHAR_MESS, SEND_FAIL_MSG } from '@/config';
import { messageRecord } from '@/api/yuanshi/user';
import { uniSelectorQueryInfo } from '@/utils/uni_api.js';
import xChat from '@/components/x-chat/x-chat';
import xChatWrap from '@/components/x-chat-wrap/x-chat-wrap';
import { checkLogin, debounce,initTime } from '@/utils/common.js';
export default {
	components: {
		xChat,
		xChatWrap
	},
	data: function() {
		return {
			time:initTime(),
			toUid: -1,
			page: 1,
			limit: 10,
			loading: false,
			loaded: false,
			history: [],
			scrollY: 0,
			socket: this.linkSocket(),
			socketOpen: false,
			socketLimit: 5,
			socketTj: 0,
			sendStatus: false, //消息发送状态
			networkStatus: true, //网络状态
			SendFailMsg: [],
			marginTop: '-150rpx',
			show: 10,
			loadingStatus: false
		};
	},
	watch: {
		networkStatus(a, b) {
			console.log('网络状态', a);
			if (!a) {
				this.$showToast('网络异常');
			} else {
				this.socket = this.linkSocket();
				if (this.history.length) {
					this.initWebsocket();
				} else {
					this.init();
				}
			}
		}
	},
	beforeDestroy() {},
	onLoad(options) {
        console.log('options',options)
		const { toUid = 0 } = options;
		this.toUid = Number(toUid);
        console.log('this.toUid',this.toUid)
		this.init();
	},
	mounted: function() {
		let _this = this;
		uni.getNetworkType({
			success: function(res) {
				console.log('networkType', res.networkType);
				_this.networkStatus = res.networkType === 'none' ? false : true;
			}
		});
		uni.onNetworkStatusChange(function(res) {
			console.log('onNetworkStatusChange', res);
			console.log(res.networkType);
			_this.networkStatus = res.isConnected;
		});
		let senFailMsg = storage.get(`${SEND_FAIL_MSG}${this.toUid}`);
		if (senFailMsg) {
			if (this.history.length) {
				this.marginTop = '0rpx';
			}
			let timer = setTimeout(()=>{
				_this.SendFailMsg = JSON.parse(senFailMsg);
				_this.$nextTick(function() {
					_this.scroll();
					clearTimeout(timer)
				});
			},800)
			
		}
	},
	onShow() {},
	destroyed() {},
	onPageScroll(e) {
		let _this = this;
		this.scrollY = e.scrollTop;
		if (e.scrollTop === 0 && !this.loaded && !this.loading) {
			this.getHistory();
			this.loadingStatus = true;
			let timer = setTimeout(() => {
				this.show = this.page * this.limit;
				this.loadingStatus = false;
				clearTimeout(timer);
			}, 1000);
		}
	},
	methods: {
		init() {
			this.page = 1;
			this.loading = false;
			this.loaded = false;
			this.history = [];
			this.getHistory();
			if (this.socket) {
				this.initWebsocket();
			} else {
				this.socket = this.linkSocket();
				this.initWebsocket();
			}
		},
		initWebsocket() {
			if (!checkLogin()) return null;
			let socket = this.socket,
				_this = this;
			socket.onOpen(res => {
				console.log('WebSocket连接已打开！');
				_this.socketOpen = true;
				_this.socketTj = _this.socketTj || 0;
				_this.timer = setInterval(function() {
					_this.socket.send({
						data: JSON.stringify({
							type: 'ping'
						})
					});
				}, 10000);
				_this.socket.send({
					data: JSON.stringify({
						type: 'login',
						data: _this.$store.state.token
					})
				});

				_this.socket.send({
					data: JSON.stringify({
						data: { id: _this.toUid },
						type: 'to_chat'
					})
				});
			});
			socket.onClose(res => {
				console.log('WebSocket 已关闭！', res);
				_this.socketOpen = false;
				clearInterval(_this.timer);
				_this.againSocket();

				// console.log('监听 WebSocket 连接关闭事件。', onClose)
			});
			socket.onError(res => {
				console.log('WebSocket连接打开失败，请检查！', res);
				clearInterval(_this.timer);
				_this.againSocket();
				_this.socketOpen = false;
			});
			socket.onMessage(res => {
				console.log('收到服务器内容：' + _this.history.length);
				_this.show ++;
				let data = JSON.parse(res.data);
				console.log(data);
				if (data.type === 'reply' || data.type === 'chat') {
					data.data.idx = _this.history.length;
					_this.history.push(data.data);
					
					_this.$nextTick(function() {
						_this.$set(_this, 'history', _this.history);
						_this.scroll(600);
					});
				}
			});
		},
		linkSocket() {
			return uni.connectSocket({
				url: VUE_APP_WS_URL,
				success: res => {
					console.log('res-', res);
				},
				fail: err => {
					console.log('err-', err);
				}
			});
		},
		againSocket() {
			let _this = this;
			if (!this.networkStatus) return;
			let timer = setTimeout(() => {
				console.log('重连', _this.socketTj);
				if (_this.socketTj < _this.socketLimit) {
					_this.socketTj++;
					_this.socket = _this.linkSocket();
					_this.initWebsocket();
				} else {
					_this.socketOpen = false;
					clearTimeout(timer);
				}
			}, 1000);
		},
		getHistory: debounce(function(type = 0) {
			console.log('type', type);
			let _this = this;
			if (this.loading || this.loaded) return;
			this.loading = true;
			messageRecord(this.toUid, { page: this.page, limit: this.limit })
				.then(({ data }) => {
					let len = this.page * this.limit;
					data.forEach((item, index) => {
						item.idx = len--;
					});
                    data = data.slice().reverse();
					this.history = data.concat(this.history);
					if (this.page === 1) {
						this.scroll();
					}
					this.page++;
					this.loading = false;
					this.loaded = data.length < this.limit;
				})
				.catch(err => {
					console.log(err);
					this.$showToast(err.msg || '加载失败');
				});
		}, 1000),
		againSendMsg(item, idx) {
			console.log(item)
			console.log(idx)
			if (!this.networkStatus) {
				return this.$showToast('网络异常');
			}
			this.sendMsg(item.msn, 1,false);
			this.SendFailMsg.splice(idx, 1);
			this.updateSendMsg();
		},
		updateSendMsg() {
			this.scroll(80);
			storage.set(`${SEND_FAIL_MSG}${this.toUid}`, JSON.stringify(this.SendFailMsg));
		},
		sendMsg(msn, type,chat=true) {
			let _this = this;
			_this.sendStatus = false;
			let obj = { msn, type, to_uid: _this.toUid,idx:_this.SendFailMsg.length };
			if(chat){
				this.$refs.xChat.clear()
			}
			if (!this.networkStatus) {
				_this.SendFailMsg.push(obj);
				_this.updateSendMsg();
				return this.$showToast('网络异常');
			}
			_this.socket.send({
				data: JSON.stringify({
					data: obj,
					type: 'chat'
				}),
				success(res) {
					console.log('successres', res);
					_this.$refs.xChat.clear()
					_this.sendStatus = true;
				},
				fail(err) {
					console.log('failerr', err);
					_this.SendFailMsg.push(obj);
					_this.updateSendMsg();
				}
			});
		},
		scroll(n = 999, t = 500) {
			uni.pageScrollTo({
				scrollTop: this.scrollY + n,
				duration: t
			});
		}
	}
};
</script>

<style scoped lang="scss">
.chat {
	padding: 1rpx 23rpx 0 30rpx;
	margin-bottom: 150rpx;
}
</style>
