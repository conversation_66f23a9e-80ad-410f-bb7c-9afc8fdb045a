<template>
	<view class="fans">
	<!-- 	<view class="search">
			<view class="title font_size20">活动搜索</view>
			<xSearch :fixed="false" type="x" @search="search"></xSearch>
		</view> -->
		<view class="tab"><xTab :active="activeIdx" :arr="tabArr" @tabClick="tabClick"></xTab></view>
		<view class="list ">
			<view class="item flex flex_align_center flex_between" v-for="(item, index) in list" :key="item.uid" @click="goPages('/pages/yuanshi/user/home?id=' + item.uid)">
				<view class="item_l flex ">
					<view class="l_l relative">
						<image :src="item.avatar" mode="aspectFill"></image>
						<!-- <view class="dot absolute"></view> -->
					</view>
					<view class="l_r">
						<view class="name">{{ item.nickname || '' }}</view>
						<view class="des flex flex_align_center">
							<text class="font_size20">跟随 {{ item.follows || 0 }}</text>
							<text class="line"></text>
							<text class="font_size20">粉丝 {{ item.fans || 0 }}</text>
						</view>
					</view>
				</view>
				<view class="item_r" @click.stop="goFollow(item.is_follow, item.uid, index)">
					<!-- <template v-if="type === 1"> -->
						<button class="flex_line_height" :class="{ active: item.is_follow }">{{ item.is_follow ? '取消跟随' : '点击跟随' }}</button>
					<!-- </template> -->
					<!-- <template v-else>
						<button class="flex_line_height active">取消跟随</button>
					</template> -->
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import xSearch from '@/components/x-search/x-search.vue';
import xTab from '@/components/yuanshi/x-tab.vue';
import { userFans, userFollowList, userFollow, userUnFollow ,getBaseNum} from '@/api/yuanshi/user.js';
export default {
	components: {
		xSearch,
		xTab
	},
	data() {
		return {
			list: [],
			type: 0,
			activeIdx:0,
			requestLoading:false,
			tabArr:[ { label: '我的跟随', num: 10, type: 2 },{ label: '我的粉丝', num: 10, type: 1 }],
			page: {
				page: 1,
				limit: 20,
				more: true
			}
		};
	},
	methods: {
		goPages(path, type) {
			this.$navigator(path, type);
		},
		tabClick(item) {
			this.list = [];
			this.page = {
				page: 1,
				limit: 20,
				more: true
			};
			this.type = item.type || 1;
			this.request();
		},
		goFollow(type, id, idx) {
			console.log('this.type',this.type)
			if (type) {
				userUnFollow(id).then(res => {
					if (this.type === 2) {
						// 仅在跟随列表删除
						this.list.splice(idx, 1);
					}else{
						this.$set(this.list[idx], 'is_follow', false);
					}
					this.tabArr[0].num = this.tabArr[0].num-1;
					this.$set(this.tabArr[0],'num',this.tabArr[0].num);
					this.$showToast('已取消');
				});
			} else {
				// if (this.type === 1) {
					userFollow(id).then(res => {
						this.$showToast('跟随成功');
						this.$set(this.list[idx], 'is_follow', true);
						this.tabArr[0].num = this.tabArr[0].num+1;
						this.$set(this.tabArr[0],'num',this.tabArr[0].num)
					});
				// }
			}
		},
		request() {
			if (this.type === 1) {
				this.getUserFans();
			} else {
				this.getUserFollowList();
			}
		},
		getUserFans() {
			if (!this.page.more || this.requestLoading) return;
			this.requestLoading = true;
			userFans({ page: this.page.page, limit: this.page.limit }).then(res => {
				this.requestLoading = false;
				this.list = this.list.concat(res.data);
				this.page.more = res.data.length === this.page.limit;
				this.page.page++;
			}).catch(err=>{
				this.requestLoading = false;
			});
		},
		getUserFollowList() {
			if (!this.page.more|| this.requestLoading) return;
			this.requestLoading = true;
			userFollowList({ page: this.page.page, limit: this.page.limit }).then(res => {
				this.list = this.list.concat(res.data);
				this.page.more = res.data.length === this.page.limit;
				this.page.page++;
				this.requestLoading = false;
			}).catch(err=>{
				this.requestLoading = false;
			});
		}
	},
	mounted() {},
	onLoad(option) {
		this.type  = option.type==='trends' ? 2 :  1;
		this.activeIdx = option.type==='trends' ? 0 : 1;
		this.$updateTitle(option.type==='trends' ? '我的跟随' :  '我的粉丝')
		this.request();
		getBaseNum().then(res=>{
			this.tabArr=[ { label: '我的跟随', num: res.data.follow, type: 2 },{ label: '我的粉丝', num: res.data.fans, type: 1 }]
		})
	},
	onReachBottom() {
		this.request();
	}
};
</script>
<style lang="scss">
page {
	background: $uni-bg-color;
}
</style>
<style scoped lang="scss">
.fans {
	padding: 0 36rpx;
	image {
		width: 100%;
		height: 100%;
	}
	.search {
		.title {
			color: #999999;
			padding: 20rpx;
		}
	}
	.tab {
		padding: 70rpx 0 0rpx 0;
	}
	.list {	
		.item {
			padding: 36rpx 0 32rpx 0;
			border-bottom: 2rpx solid #c0c0c1;
			.item_l {
				.l_l {
					margin-right: 28rpx;

					image {
						width: 100rpx;
						height: 100rpx;
						background: #d8d8d8;
						border-radius: 50%;
					}
					.dot {
						width: 24rpx;
						height: 24rpx;
						background: #ff5656;
						border: 2rpx solid #ffffff;
						border-radius: 50%;
						top: 0;
						right: 0;
					}
				}
				.l_r {
					.name {
						font-weight: bold;
						color: #333333;
						font-size: 32rpx;
					}
					.des {
						margin-top: 10rpx;
						color: #999999;
						font-weight: 400;
						.line{
							display: inline-block;
							width: 2rpx;
							background:#999999 ;
							height: 20rpx;
							margin:0 16rpx;
						}
					}
				}
			}
			.item_r {
				button {
					width: 148rpx;
					height: 60rpx;
					border: 2rpx solid #ff5656;
					color: #ff5656;
					font-size: 24rpx;
					border-radius: 24rpx;
					&.active {
						border-color: #666666;
						color: #666666;
					}
				}
			}
		}
	}
}
</style>
