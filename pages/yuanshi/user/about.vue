<template>
	<!-- #ifdef H5 -->
	<view class="about relative">
		<view class="bg absolute"><image :src="baseUrl + '/wximage/aboutBg.png'" mode="widthFix"></image></view>
		<view class="relative">
			<xLogo :search="false"></xLogo>
			<view class="wrap">
				<view class="item">
					<view class="title">关于着调儿</view>
					<view>着调儿是一个体验精神消费的乐园。我们希望回到原初，让用户心无旁骛的感受精神创造和体验的美好与乐趣。</view>
				</view>
				<view class="item">
					<view>在过往的人类历史中，精神需求从来没有被视为独立于物质需求的存在，而与物物交易共享同一套规则</view>
					<view>我们希望通过着调儿，真正回归和发现精神消费的逻辑与价值，进而给予精神消费者应有的权益和价值。</view>
				</view>
				<view class="item">
					着调儿是一个聚合精神消费产品的平台，平台便于用户根据不同层级需求和社交需求选择精神消费品，无限接近和满足用户的精神需求。同时，平台还推出用户公测模式来共同捍卫精神消费的权益和满足不断涌现的新需求。
				</view>
				<view class="line"></view>
				<view class="item">
					<view class="title">Slogan：着调儿——先于财富自由实现精神自由</view>
					<view>科学技术的发展，促使我们有更多时间制造孤独，同时也有更多的可能打败孤独。</view>
					<view>着调儿：先于财富自由实现精神自由</view>
				</view>
			</view>
		</view>
	</view>
	<!-- #endif -->
	<!-- #ifdef MP -->
	<web-view :src="baseUrl + '/yabout'"></web-view>
	<!-- #endif -->
</template>

<script>
import { VUE_APP_URL } from '@/config.js';
import xLogo from '@/components/x-logo/x-logo.vue';
export default {
	data() {
		return {
			baseUrl: VUE_APP_URL
		};
	},
	components: {
		xLogo
	},
	methods: {},
	mounted() {},
	onLoad(option) {}
};
</script>
<style lang="scss">
page {
	background: $uni-bg-color;
}
</style>
<style scoped lang="scss">
/* #ifdef H5 */
.about {
	// height: 100vh;
	.bg{
		width: 100%;
		height: 100%;
		top: -128rpx;
		image{
			width: 100%;
			height: 100%;
		}
	}
	.relative{
		padding: 60rpx 40rpx;
		height: 100%;
		overflow: scroll;
		.wrap {
			min-height: 600rpx;
			padding: 50rpx 20rpx;
			border-radius: 30rpx;
			color: #333333;
			line-height: 40rpx;
			font-size: 24rpx;
			.line {
				width: 366rpx;
				height: 34rpx;
				background: #fc5656;
				border-radius: 32rpx;
				margin-bottom: 40rpx;
			}
			.item {
				margin-bottom: 40rpx;
				.title {
					margin-bottom: 40rpx;
					font-size: 32rpx;
					font-weight: bold;
				}
			}
		}
	}
	
}
/* #endif */
</style>
