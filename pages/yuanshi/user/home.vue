<template>
	<view class="user">
		<xUsePannel @pannelClick="pannelClick" :info="detail" tagType="other"></xUsePannel>
		<view class="wrap">
			<!-- <xTab :arr="arr" @tabClick="tabClick"></xTab> -->
			<view class="">
				<!-- <view class="">{{arr[0].num}}</view> -->
				<view class="font_bold" style="padding-bottom:20rpx">Ta的关注 ( {{arr[0].num}} ) </view>
			</view>	
			<view class="list"><xList :arr="listArr" :status="tabType===1" :status1="tabType===0"  :attention="true" :btn="true"></xList></view>
		</view>
	</view>
</template>

<script>
import { SHARE_ID } from '@/config.js';
import xUsePannel from '@/components/yuanshi/x-user-pannel';
import xTab from '@/components/yuanshi/x-tab.vue';
import xList from '@/components/yuanshi/x-list.vue';
import { userIndexBase, userWishList,getAttention } from '@/api/yuanshi/user.js';
export default {
	props: {
		value: {
			type: '',
			default: ''
		}
	},
	components: {
		xUsePannel,
		xTab,
		xList
	},
	data() {
		return {
			pannelType: 'follow',
			detail: {},
			arr: [],
			listArr:[],
			shareInfo: {},
			requestLoading: false,
			page: {
				page: 1,
				limit: 20,
				more: true
			},
			tabType:0
		};
	},
	methods: {
		pannelClick(idx, type) {
			this.pannelType = type;
			console.log('xxxx', type);
		},
		tabClick(item, type) {
			if (type) {
			} else {
				this.requestLoading = false;
				this.page = {
					page: 1,
					limit: 20,
					more: true
				};
				this.listArr = [];
				this.tabType = item.type;
				this.request();
			}
		},
		request() {
			let type = this.tabType;
			let {page,limit} = this.page;
			if (!this.page.more || this.requestLoading) return;
			this.requestLoading = true;
			let req = '';
			if (type === 0) {
				// 我的关注
				req = getAttention({user_id:this.id, page: page, limit: limit})
			} else if (type === 1) {
				// 我的心愿
				req = userWishList({user_id:this.id, page: page, limit: limit});
			} else if (type === 2) {
				// 我的足迹
			}
			req.then(res => {
				this.requestLoading = false;
				this.listArr = this.listArr.concat(res.data);
				this.page.more = res.data.length === limit;
				this.page.page++;
			}).catch(err=>{
				this.requestLoading = false;
			});
			
		}
	},
	mounted() {},
	onLoad(option) {
		const { id } = option;
		// console.log('id')
		this.id = id;
		userIndexBase(id).then(res => {
			let data = res.data;
			this.detail = data;
			this.arr = [{ label: 'Ta的关注', num: data.attentions, type: 0 }, { label: 'Ta的心愿', num: data.wishs, type: 1 }];
			this.shareInfo = {
				image: data.avatar,
				title: data.nickname
			};
		});
		this.request()
	},
	onReachBottom() {
		this.request();
	},
	onShareAppMessage() {
		if (this.shareInfo) {
			return {
				title: this.shareInfo.title || '',
				imageUrl: this.shareInfo.image || '',
				path: '/pages/yuanshi/user/home?id=' + this.id + '&spid=' + this.$store.state.userInfo.uid,
                templateId: SHARE_ID
			};
		}
	}
};
</script>

<style scoped lang="scss">
.user {
	image {
		width: 100%;
		height: 100%;
	}

	.wrap {
		margin-top: 30rpx;
		border-radius: 16rpx 16rpx 0px 0px;
		min-height: 600rpx;
		padding: 52rpx 40rpx 0 40rpx;
		background: $uni-bg-color;
	}
}
</style>
