<template>
	<!-- #ifdef H5 -->
	<view class="protocol">
		<view class="nav flex flex_align_end">
			<image src="@/static/images/yuanshi/yshs1.png" mode="widthFix"></image>
			<view>着调儿卡用户协议</view>
		</view>
		<view class="desc relative">
			<view class="absolute">
				<view>
					本协议是您与着调儿的所有者北京源未文化发展有限公司之间就着调儿卡(以下简称VIP会员)所订立的契约。
					<text class="span">
						本站以字体加粗、下划线或其他合理方式提示您注意并请您重点阅读本协议中可能与您利益有重大关系的条款，包括免除或限制责任条款、法律适用和争议解决条款，请您在正式开通vip会员服务前，仔细阅读本协议，您点击”同意并继续”按钮后，本协议即构成对双方有约束力的法律文件。
					</text>
					当您选择“同意《着调儿vip会员用户协议》”)并完成VIP会员开通程序，或以着调儿允许的其他方式实际使用着调儿VIP会员服务时，即表示您自愿接受并遵守本协议;若您不同意本协议，或对本协议条款存在任何疑问，您可停止并退出着调儿VIP会员开通程序。
				</view>
				<view>第1条相关定义</view>
				<view>1.1着调儿VIP会员</view>
				<view>着调儿VIP会员是着调儿为了向用户提供更优质的购物体验推出的増值会员服务产品，努力为用户提供更好购物体验及服务。</view>
				<view>1.2着调儿VIP会员权益</view>
				<view>是指用户基于其着调儿VIP会员资格所享有的特殊权益，</view>
				<view>
					包括特定的购物、服务、增值等权益。您成为着调儿VIP会员后，可享受的权益以着调儿官方公布的信息为准。为更好地向着调儿VIP会员用户提供服务，着调儿有权基于自身业务发展需要调整全部或部分会员权益。若就
				</view>
				<view>
					前述权益调整，将提前7天在相应服务页面进行通知或公告。若公示期间，已开通的VIP用户对即将调整的VIP会员权益不接受，且开通后从未使用任何一项VIP会员权益（包括但不限于VIP专属的现金券/优惠券、购物折扣、消费积分抵现等VIP优惠形式），可向着调儿提出撤销VIP会员资格并返还开通费用。公示期后，着调儿发布的最新会员权益内容对您具有相同的合同效力。届时您可通过着调儿VIP会员页面查询最新的会员权益内容。
				</view>
				<view>第2条服务条款的确认和接纳</view>
				<view>2.1本站所提供的着调儿VIP会员的所有权、知识产权和运作权归着调儿所有。</view>
				<view>2.2用户点击同意本协议的，即视为用户确认自己同意接受着调儿VIP会员相关服务的条款，且同意按本协议内容履行，如产生用户相关责任的，同意承担相应法律责任。</view>
				<view>
					2.3如果您在18周岁以下，您只能在父母或监护人的监护参与下方能参与体验该服务。
					<text class="span">若您违反前述约定，则您及您的监护人应依照法律规定承担因此而导致的一切后果。</text>
				</view>
				<view>2.4着调儿保留在中华人民共和国大陆地区施行之法律允许的范围内独自决定拒绝服务、关闭用户账户、清除或编辑内容等相关权利。</view>
				<view><text class="span">2.5着调儿VIP会员开通后，您在已开通的服务期内中途主动取消服务或终止资格的，将不获得为开通本服务而支付费用的退还。</text></view>
				<view>第3条用户会员使用注意事项</view>
				<view>3.1着调儿VIP会员的有效期自您开通之日起至双方约定的到期日自动终止。</view>
				<view>
					<text class="span">
						3.2着调儿VIP会员服务仅限您本人使用，您不得以任何形式将您所享有的VIP会员服务转借、转让给他人使用，否则着调儿有权取消您VIP会员资格且不退还您已支付的会员费用。任何通过VIP会员账户发生的行为均视为您的个人行为，若发生问题需由您个人自行承担后果。
					</text>
				</view>
				<view>
					3.3您在提交开通着调儿VIP会员的申请时所提供本人手机号码等个人资料，需保证其真实、准确、合法、有效。如您的资料发生变更，您应及时进行更新。

					<text class="span">如若您的资料不合法、不真实、不准确、不详尽或不实时有效，由您自行承担因此引起的相应责任及后果，并且着调儿保留终止您VIP会员资格的权利。</text>
				</view>
				<view>
					3.4您承诺将合理使用享有的VIP会员服务，除为实现用户自身的会员权益外，不得利用VIP会员服务非法获利或从事非法活动。
					<text class="span">若因您不当行为致着调儿合理怀疑的或判定的，着调儿有权暂停或关闭用户该活动的权益，此外，用户需自行承担因此引起的相应责任及后果。</text>
				</view>
				<view>
					<text class="span">3.5您应妥善保管您的VIP会员帐户及密码，如发生任何泄漏、遗失、被盗、遭受侵权等行为，而该等行为并非着调儿法定过错导致，所有损失将由您自行承担。</text>
				</view>
				<view>
					<text class="span">
						3.6着调儿不对本站由第三方提供的商品、服务或信息作任何形式的担保，亦不承担任何形式的连带责任。如第三方发布的商品、服务或信息涉嫌侵犯您的合法权益，着调儿将在收到通知并核实情况属实后采取适当措施协助您维护自身合法权益。
					</text>
				</view>
				<view>第4条其他补充</view>
				<view>
					4.1根据国家法律法规变化及平台运营需要，着调儿有权以公告公示的方式进行不定期地制定、修改本协议及/或相关服务规则，暂停、取消和修改本协议条款。如发生协议与适用之法律相抵触时，则这些条款将完全按照法律规定重新定义，而其他条款继续有效。修改后的协议将提前7天要求进行公示，公示后协议即生效并代替原来协议。用户应及时关注不时发布的各项服务规则及本协议的变更。若用户继续使用本平台提供的服务，即视为同意更新后的协议。
				</view>
				<view>
					<text class="span">
						4.2如因不可抗力或其他本站无法控制的原因使平台服务无法及时提供或无法按本协议进行的，着调儿不承担违约责任，但着调儿应合理地尽力协助处理善后事宜。
					</text>
				</view>
				<view>
					<text class="span">4.3本协议之订立、生效、解释、修订、补充、终止、执行与争议解决均适用中华人民共和国大陆地区法律；如法律无相关规定的，参照商业惯例及/或行业惯例。</text>
				</view>
				<view>
					<text class="span">
						4.4如履行本协议中发生争议，双方友好协商解决；协商不成时，任何一方均可向着调儿的所有者北京源未文化发展有限公司所在地有管辖权的人民法院提起诉讼。
					</text>
				</view>
				<view style="height: 200rpx;"></view>
			</view>
		</view>
	</view>
	<!-- #endif -->
	<!-- #ifdef MP -->
	<web-view :src="baseUrl + '/yprotocol'"></web-view>
	<!-- #endif -->
</template>

<script>
import { VUE_APP_URL } from '@/config.js';
export default {
	data() {
		return {
			baseUrl: VUE_APP_URL
		};
	},
	methods: {},
	mounted() {},
	onLoad(option) {}
};
</script>

<style scoped lang="scss">
.protocol {
	padding: 60rpx;
	.nav {
		color: #333333;
		margin-bottom: 54rpx;
		image {
			width: 215rpx;
			height: 71rpx;
			margin-right: 20rpx;
		}
	}
	.desc {
		color: #999999;
		text {
			font-weight: bold;
			text-decoration: underline;
		}
		> view {
			font-size: 20rpx;
			transform: scale(0.83);
			width: 120%;
			height: 100%;
			line-height: 35rpx;
			transform-origin: left top;
		}
	}
}
</style>
