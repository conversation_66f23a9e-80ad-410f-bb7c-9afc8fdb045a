<template>
	<view>
		<xUsePannel @pannelClick="pannelClick" :info="detail"></xUsePannel>
		<view class="upgrade">
			<view class="nav flex flex_align_end">
				<image src="@/static/images/yuanshi/yshs1.png" mode="widthFix"></image>
				<view>用户等级规则说明：</view>
			</view>
			<view class="item">
				<view class=" font_size20" v-for="(item, index) in memberDesc">{{ item }}</view>
			</view>
		</view>
		<view class="task">
			<view class="nav flex flex_align_center ">
				<view class="title">
					用户等级任务
					<view class="line"></view>
				</view>
				<view class="title_r"><text class="font_size20">每月第一天刷新，完成任何一项即可升级。</text></view>
			</view>
			<view class="wrap active" v-if="!detail.vip" v-for="(item, index) in vipList" :key="item.id">
				<view class="item flex  flex_between">
					<view class="item_l">
						<view class="flex flex_align_center">
							{{ item.name }}
							<view class="span flex_line_height" @click="goPages('/pages/yuanshi/vip/equity')"><view class="font_size20">查看权益</view></view>
						</view>
						<view>{{ item.interests }}</view>
					</view>
					<view class="item_r flex">
						<view class="txt"><text class="font_size20">待完成</text></view>
						<image src="@/static/images/yuanshi/uncheck2.png"></image>
					</view>
				</view>
			</view>
			<view class="wrap wrap2">
				<view class="item flex flex_between" v-for="(item, index) in vipComplete" :key="item.id">
					<view class="item_l">
						<view>{{ item.name }}</view>
						<view >
							当前进度： <text class="num" v-if="item.task_type==='Likes'">
							
							目前单条最高已达 {{ item.new_number }} 个赞
							</text> 
							
							<text v-else>{{ item.new_number }} 条</text>
						</view>
					</view>
					<view class="item_r flex">
						<view class="txt">
							<text class="font_size20">{{ item.finish ? '已完成' : '待完成' }}</text>
						</view>
						<image src="@/static/images/yuanshi/check1.png" v-if="item.finish"></image>
						<image src="@/static/images/yuanshi/uncheck2.png" v-else></image>
					</view>
				</view>
				<view class="finish" v-if="task_list_finsh">恭喜本月升级任务已完成！</view>
			</view>
			<view class="wrap bg " v-if="detail.vip" v-for="(item, index) in vipList" :key="item.id">
				<view class="item flex  flex_between">
					<view class="item_l">
						<view class="flex flex_align_center">
							{{ item.name }}
							<view class="span flex_line_height" @click="goPages('/pages/yuanshi/vip/equity')"><view class="font_size20">查看权益</view></view>
						</view>
						<view>{{ item.interests }}</view>
					</view>
					<view class="item_r flex">
						<view class="txt"><text class="font_size20">已完成</text></view>
						<image src="@/static/images/yuanshi/check2.png"></image>
					</view>
				</view>
			</view>
		</view>
		<x-authorize @login="updateData"></x-authorize>
	</view>
</template>

<script>
import { user } from '@/api/yuanshi/user';
import { getVipInfo, getVipTask } from '@/api/user';
import { memberDesc } from '@/api/yuanshi/public';
import xUsePannel from '@/components/yuanshi/x-user-pannel';
import { VUE_APP_URL } from '@/config.js';
export default {
	data() {
		return {
			baseUrl: VUE_APP_URL,
			detail: {},
			vipList: [], //等级列表
			vipRequire: [], //等级要求
			vipComplete: [], //完成情况
			taskCount: 0, //任务数
			memberDesc: [],
			task_list_finsh:false
		};
	},
	components: {
		xUsePannel
	},
	methods: {
		goPages(path) {
			this.$navigator(path);
		},
		updateData() {
			user().then(res => {
				let data = res.data;
				this.detail = res.data;
			});
			memberDesc().then(res => {
				if (res.data.rules.length) {
					this.memberDesc = res.data.rules;
				}
			});
			getVipInfo().then(
				res => {
					let task_list = res.data.task.task_list;
					task_list.forEach((item,index)=>{
						if(item.finish){
							this.task_list_finsh = true
						}
					})
					this.vipList = res.data.list;
					this.vipRequire = res.data.task.list;
					this.vipComplete = res.data.task.task_list;
					this.taskCount = res.data.task.reach_count;
				},
				err => {
					this.$showToast(err.msg || err);
				}
			);
		}
	},
	mounted() {
		this.updateData();
		// getVipTask(that.vipList[that.activeIndex].id).then(
		//   res => {
		//     that.vipRequire = res.data.list;
		//     that.vipComplete = res.data.task;
		//     that.taskCount = res.data.reach_count;
		//   },
		//   err => {
		//     that.$dialog.message(err.msg || err);
		//   }
		// );
	},

	onLoad(option) {}
};
</script>
<style lang="scss">
page {
}
</style>
<style scoped lang="scss">
.upgrade {
	font-size: 24rpx;
	padding: 0 60rpx 60rpx 60rpx;
	.nav {
		font-size: 24rpx;
		color: #333333;
		margin: 58rpx 0 38rpx 0;
		image {
			width: 215rpx;
			height: 71rpx;
			margin-right: 20rpx;
		}
	}
	.item {
		min-height: 540rpx;
		width: 100%;
		overflow: hidden;
		.font_size20 {
			transform-origin: left;
			width: 117%;
			line-height: 34rpx;
			color: #999999;
		}
	}
}
.task {
	.nav {
		padding: 0 60rpx;
		margin-bottom: 40rpx;
		.title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333333;
			// width: 196rpx;
			margin-right: 20rpx;
			.line {
				width: 100%;
				height: 8rpx;
				background: linear-gradient(90deg, #ffa969 0%, #ff5a73 100%);
				border-radius: 2rpx;
			}
		}
		.title_r {
			width: calc(100% - 200rpx - 20rpx);
			overflow: hidden;
			.font_size20 {
				transform-origin: left;
				width: 112%;
				color: #999999;
			}
		}
	}
	.wrap {
		// width: 710rpx;
		margin: 30rpx 20rpx;
		background: #fcfdfd;
		box-shadow: 0px 0px 30rpx rgba(107, 127, 153, 0.3);
		border-radius: 30rpx;
		padding: 50rpx 46rpx;
		overflow: hidden;
		&.active {
			border: 2rpx solid #ff5656;

			box-shadow: 0px 0px 30rpx rgba(107, 127, 153, 0.3);
		}
		&.wrap2 {
			padding: 14rpx 0rpx  0 0;
			.item {
				margin: 0 10rpx;
				padding: 30rpx 36rpx;
				&:not(:last-child) {
					border-bottom: 2rpx solid #f0f0f0;
				}
				&:last-child{
					margin-bottom: 14rpx;
				}
			}
			.finish{
				background: #50506D;
				font-size: 24rpx;
				height: 104rpx;
				line-height: 104rpx;
				color: #fff;
				border: 4rpx solid #fff;
				padding-left:46rpx ;
				border-radius:0 0 30rpx 30rpx;
			}
		}
		.item {
			.item_l {
				.span {
					width: 120rpx;
					height: 38rpx;
					border: 2rpx solid #ff5656;
					border-radius: 14rpx;
					text-align: center;

					color: #ff5656;
					margin-left: 20rpx;
				}
				> view {
					&:first-child {
						color: #333333;
						font-size: 28rpx;
					}
					&:last-child {
						margin-top: 10rpx;
						color: #666666;
						font-size: 24rpx;
					}
				}
			}
			.item_r {
				.txt {
					margin-right: 10rpx;
					color: #999999;
				}
				image {
					width: 44rpx;
					height: 44rpx;
				}
			}
		}
		&.bg {
			border: 4rpx solid #ffffff;

			background: #f2f5f8;
			box-shadow: 0px 0px 30rpx rgba(107, 127, 153, 0.3);
			.item_l {
				.span {
				
					border: 2rpx solid #666666;

			
					color: #666666;
				}
				}
		}
	}
}
</style>
