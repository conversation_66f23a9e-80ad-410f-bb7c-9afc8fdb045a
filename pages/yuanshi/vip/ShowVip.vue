<template>
	<view class="show_vip">
		<vipCard></vipCard>
		<view class="show">
			<view class="vip" v-if="userInfo.vip">
				<view class="nav flex flex_align_center flex_between">
					<view class="title">
						着调儿卡专享价
						<view class="line"></view>
					</view>
					<!-- <view class="more"><text class="font_size20">查看更多</text></view> -->
				</view>
				<view class="list"><xList :arr="wishList"></xList></view>
			</view>
		</view>
	</view>
</template>
<script>
import xList from '@/components/yuanshi/x-list.vue';
import xProtocolVip from '@/components/x-protocol/x-protocol-vip';
import vipCard from '@/components/yuanshi/vip_card'
import { vipExclusive } from '@/api/yuanshi/public.js';
import { mapGetters } from 'vuex';
export default {
	components: {
		xList,
		xProtocolVip,
		vipCard
	},
	data() {
		return {
			wishList: [],
			page: {
				page: 1,
				limit: 10,
				more: true
			}
		};
	},
	computed: mapGetters(['userInfo']),
	methods: {
		goPages(path) {
			this.$navigator(path);
		},
		getVipExclusive() {
			if(!this.userInfo.vip) return
			if (!this.page.more || this.requestLoading) return;
			this.requestLoading = true;
			vipExclusive({ page: this.page.page, limit: this.page.limit }).then(res => {
				this.requestLoading = false;
				this.wishList = this.wishList.concat(res.data);
				this.page.more = res.data.length === this.page.limit;
				this.page.page++;
			}).catch(err => {
					this.requestLoading = false;
				});
		}
	},
	onShow() {
		this.getVipExclusive();
		console.log('ccc',this.userInfo)
	},
	onReachBottom() {
		this.getVipExclusive();
	}
};
</script>
<style lang="scss">
page {
	background: $uni-bg-color;
}
</style>
<style scoped lang="scss">
.show_vip {
	padding-top: 40rpx;
	.show {
		margin-top: 60rpx;
		padding: 0 36rpx;
		.vip {
			.nav {
				.title {
					padding-left: 16rpx;
					font-size: 32rpx;

					font-weight: bold;

					color: #333333;
					.line {
						width: 100%;
						height: 8rpx;
						background: linear-gradient(90deg, #ffa969 0%, #ff5a73 100%);
						border-radius: 2rpx;
					}
				}
				.more {
					color: #999999;
				}
			}
			.list {
				padding-top: 40rpx;
			}
		}
		button {
			width: 670rpx;
			height: 100rpx;
			margin: 0 auto;
			background: #ff5656;
			color: #ffffff;
			border-radius: 40rpx;
			.btn_t {
				font-weight: bold;
				font-size: 32rpx;
			}
			.btn_b {
				font-size: 24rpx;
			}
		}
	}
}
</style>
