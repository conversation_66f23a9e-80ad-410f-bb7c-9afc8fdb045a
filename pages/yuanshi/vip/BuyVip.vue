<template>
	<view class="buy_vip">
		<view class="nav flex flex_align_center flex_between">
			<view>开通{{ detail.name }}</view>
			<view class="image"><image src="@/static/images/yuanshi/vip.png" mode="widthFix"></image></view>
		</view>
		<view class="show">
			<view class="wrap">
				<view class="item flex flex_align_center flex_between">
					<view>实付款</view>
					<view>￥{{ detail.money }}</view>
				</view>
				<view class="item flex flex_align_center flex_between">
					<view>有效期</view>
					<view>{{ymd}}</view>
				</view>
				<view class="item">
					<view>支付方式</view>
					<!-- <view class="methods flex flex_align_center flex_between" @click="check=!check">
						<image src="@/static/images/yuanshi/wxpay.png" mode=""></image>
						<image src="@/static/images/yuanshi/check.png" mode="" v-if="check"></image>
						<image src="@/static/images/yuanshi/uncheck.png" mode="" v-else></image>
					</view> -->
					<view class="methods flex flex_align_center flex_between">
						<image src="@/static/images/yuanshi/wxpay.png" mode="widthFix"></image>
						<image src="@/static/images/yuanshi/check.png" mode="widthFix"></image>
					</view>
				</view>
			</view>
			<view class="protocol"><xProtocolVip></xProtocolVip></view>
		</view>
		<view class="footer acea-row row-middle row-between">
			<view>
				合计
				<text>￥{{ detail.money }}</text>
			</view>

			<button class="btn flex_line_height" @click="goPay">立即支付</button>
		</view>
	</view>
</template>
<script>
import { getVipInfo, buyVip } from '@/api/user';
import xProtocolVip from '@/components/x-protocol/x-protocol-vip';
import { mapGetters } from 'vuex';
import { initTime,getuserInfo } from '@/utils/common.js';
// #ifdef H5
import { isWeixin } from '@/utils/validate.js';
import { pay } from '@/utils/wechat/pay.js';
const _isWeixin = isWeixin();
// #endif

// #ifdef MP
const _isWeixin = true;
// #endif

export default {
	components: { xProtocolVip },
	data() {
		return {
			radio: 1,
			// #ifdef MP-WEIXIN
			from: 'routine',
			// #endif
			// #ifdef MP-TOUTIAO
			from: 'bytedance',
			// #endif
			// #ifdef H5
			from: _isWeixin ? 'weixin' : 'weixinh5',
			// #endif
			check: false,
			vipId: 1, //测试 0.01
			detail: {},
			ymd:'2020-12-24'
		};
	},
	computed: {
		...mapGetters(['userInfo'])
	},
	methods: {
		goPay() {
			this.tapBuy();
		},
		goBack(){
			getuserInfo().then(res=>{
				this.$navigator(-1);
			})
		},
		getInfo: function() {
			let that = this;
			getVipInfo().then(
				res => {
					that.vipList = res.data.list;
					that.detail = res.data.list[0];
					let time = (Date.parse(new Date())/1000+this.detail.valid_date*60*60*24);
					
					this.ymd = initTime(time*1000,'ymd');					
					console.log(this.ymd)
				},
				err => {
					this.$showToast(err.msg);
				}
			);
		},
		tapBuy() {
			let obj = {
					leveid: this.detail.id,
					paytype: 'weixin',
					from: this.from
				},
				_this = this;

			buyVip(obj)
				.then(res => {
					const data = res.data;
					let url = '/pages/yuanshi/vip/ShowVip';
					switch (data.status) {
						case 'ORDER_EXIST':
						case 'EXTEND_ORDER':
						case 'PAY_DEFICIENCY':
						case 'PAY_ERROR':
							_this.$showToast(res.msg);
							break;
						case 'SUCCESS':
							_this.$showToast(res.msg);
								_this.goBack();
							break;
						case 'WECHAT_H5_PAY':
							setTimeout(() => {
								location.href = data.result.jsConfig.web_url;
							}, 100);
							break;
						case 'WECHAT_PAY':
							// #ifdef H5
							pay(data.result.jsConfig).then(() => {
								_this.$showToast('支付成功');
								_this.goBack();
							});
							// #endif
							// #ifdef MP
							let jsConfig = res.data.result.jsConfig;
							uni.requestPayment({
								timeStamp: jsConfig.timestamp,
								nonceStr: jsConfig.nonceStr,
								package: jsConfig.package,
								signType: jsConfig.signType,
								paySign: jsConfig.paySign,
								success: function(res) {
									_this.$showToast('支付成功');
										_this.goBack();
								},
								fail: function(err) {
									_this.$showToast('取消支付');
								},
								complete: function(e) {
									//关闭当前页面跳转至订单状态
								}
							});
						// #endif
					}
				})
				.catch(err => {
					_this.$showToast(err || '支付失败');
				});
		}
	},
	onShow() {
		this.getInfo();
	}
};
</script>
<style lang="scss">
page {
	background: $uni-bg-color;
}
</style>
<style scoped lang="scss">
.buy_vip {
	.nav {
		padding: 40rpx 50rpx;

		color: #333333;
		font-size: 40rpx;
		font-weight: bold;
		image {
			width: 128rpx;
		}
	}
	.show {
		.wrap {
			width: 710rpx;
			margin: 0 auto;

			background: #ffffff;

			box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);

			border-radius: 30rpx;
			padding: 0 28rpx;
			.item {
				padding: 40rpx 0;
				font-weight: 400;
				color: #333333;
				font-size: 24rpx;
				&:not(:last-child) {
					border-bottom: 2rpx solid #e2e6ec;
				}
				.methods {
					margin-top: 30rpx;
					image {
						width: 44rpx;
						height: 44rpx;
					}
				}
			}
		}
	}
	.footer {
		@include fixed_footer(112rpx);

		background: #f7f8fa;
		font-size: 32rpx;
		> view {
			width: calc(100% - 400rpx);
			text-align: center;
			font-weight: 500;
			text {
				font-size: 34rpx;
				color: #ff5656;
			}
		}
		button {
			width: 400rpx;
			height: 112rpx;
			background: #ff5656;
			border-radius: 30rpx 0px 0px 0px;

			font-weight: bold;
			color: #ffffff;
		}
	}
}
</style>
