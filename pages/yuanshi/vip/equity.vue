<template>
	<view class="show_vip">
		<view class="des">
			<view class="nav flex flex_align_end">
				<image src="@/static/images/yuanshi/logo1.png" mode="widthFix"></image>
				<view>着调儿卡说明：</view>
			</view>
			<view class="list" style="width: 100%;overflow: hidden;">
			<view class="item font_size20" v-for="(item,index) in memberDesc">{{item}}</view>
			</view>
		</view>
		<vipCard :equity="false"></vipCard>
		<view class="show">		
			<view class="vip">
				<view class="nav flex flex_align_center flex_between">
					<view class="title">
						着调儿卡专享价
						<view class="line"></view>
					</view>
					<!-- <view class="more"><text class="font_size20">查看更多</text></view> -->
				</view>
				<view class="list"><xList :arr="wishList"></xList></view>
			</view>
		</view>
	</view>
</template>
<script>
import xList from '@/components/yuanshi/x-list.vue';
import vipCard from '@/components/yuanshi/vip_card'
import { vipExclusive,memberDesc } from '@/api/yuanshi/public.js';
export default {
	components: {
		xList,
		vipCard
	},
	data() {
		return {
			wishList: [],
			page: {
				page: 1,
				limit: 10,
				more: true
			},
			requestLoading:false,
			memberDesc:[]
		};
	},
	methods: {
		goPages(path) {
			this.$navigator(path);
		},
		
		getVipExclusive() {
			if (!this.page.more || this.requestLoading) return;
			this.requestLoading = true;
			vipExclusive({ page: this.page.page, limit: this.page.limit }).then(res => {
				this.requestLoading = false;
				this.wishList = this.wishList.concat(res.data);
				this.page.more = res.data.length === this.page.limit;
				this.page.page++;
			}).catch(err => {
					this.requestLoading = false;
				});
		}
		
	},
	onShow() {
		this.getVipExclusive();
		memberDesc().then(res=>{
			if(res.data.description.length){
				this.memberDesc = res.data.description
			}
		})
	},
	onReachBottom() {
		this.getVipExclusive();
	}
};
</script>
<style lang="scss">
page {
	background: $uni-bg-color;
}
</style>
<style scoped lang="scss">
.show_vip {
	.des {
		padding: 52rpx 40rpx;
		min-height: 420rpx;
		.nav {
			font-size: 24rpx;
			color: #333333;
			margin-bottom: 50rpx;
			image {
				width: 215rpx;
				height: 71rpx;
				margin-right: 20rpx;
			}
		}
		.font_size20 {
			transform-origin: left;
			width: 118%;
			color: #999999;
		}
	}
	.show {
		margin-top: 68rpx;
		padding: 0 36rpx;
		.vip {
			.nav {
				.title {
					padding-left: 16rpx;
					font-size: 32rpx;

					font-weight: bold;

					color: #333333;
					.line {
						width: 100%;
						height: 8rpx;
						background: linear-gradient(90deg, #ffa969 0%, #ff5a73 100%);
						border-radius: 2rpx;
					}
				}
				.more {
					color: #999999;
				}
			}
			.list {
				padding-top: 40rpx;
			}
		}
		
	}
}
</style>
