<template>
	<view class="wish">
		<view><xLogo :disabled="true"></xLogo></view>
		<view class="wrap">
			<xTab :arr="[{ label: '待测评', num: 10, type: 1 }, { label: '已测评', num: 10, type: 2 }, { label: '已入住', num: 10, type: 2 }]" @tabClick="tabClick"></xTab>
			<xList :btn="true" :arr="featuredList" ></xList>
			<view v-if="!page.more"><uniLoadMore status="noMore"></uniLoadMore></view>
		</view>
		<x-authorize @login="init"></x-authorize>
	</view>
</template>

<script>
import xLogo from '@/components/x-logo/x-logo.vue';
import xTab from '@/components/yuanshi/x-tab.vue';
import xList from '@/components/yuanshi/x-list.vue';
import { wish } from '@/api/yuanshi/wish.js';
import { yFeatured } from '@/api/yuanshi/public';
export default {
	props: {
		value: {
			type: '',
			default: ''
		}
	},
	components: {
		xLogo,
		xList,
		xTab
	},
	data() {
		return {
			disabled: false,
			featuredList: [],
			page: {
				page: 1,
				limit: 20,
				more: true
			}
		};
	},
	methods: {
		goPages(path, type) {
			this.$navigator(path, type);
		},
		tabClick(item) {},
		getFeatured(){
			if (!this.page.more) return;
			yFeatured({ page: this.page.page, limit: this.page.limit }).then(res => {
				this.featuredList = this.featuredList.concat(res.data);
				this.page.more = res.data.length === this.page.limit;
				this.page.page++;
			})
		}
	},
	mounted() {},
	onShow() {
		this.disabled = false;
		this.getFeatured();
	},
	onLoad(option) {},
	onReachBottom() {
		this.getFeatured();
	}
};
</script>

<style scoped lang="scss">
.wish {
	image {
		width: 100%;
		height: 100%;
	}
	.wrap {
		margin-top: 62rpx;
		padding: 0 36rpx;
		min-height: 800rpx;
	}
}
</style>
