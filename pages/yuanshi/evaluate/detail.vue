<template>
	<view class="active">
		<eSwiper :info="detail.storeInfo" :arr="detail.storeInfo.slider_image" @btnAttention="btnAttention"></eSwiper>
		<view class="main">
			<view class="wrap_title  ">
				<view class="flex flex_between flex_align_center ">
					<view class="flex flex_align_center">
						<view class="title">
							<view class="say_feeling flex relative" @click="goPages('/pages/yuanshi/evaluate/info?wid=' + mixinsParam.wishId + '&pid=' + mixinsParam.productId)"><image src="@/static/images/yuanshi/yszs.png" mode="widthFix" class="logo "></image></view>
							<view class="rate">
								<xProgress
									:diy="true"
									:isMove="false"
									:percent="(detail.yw_index / 5) * 100"
									:bg="require('@/static/images/yuanshi/star3.png')"
									:active="require('@/static/images/yuanshi/star4.png')"
									backgroundSize="35rpx 35rpx"
									:strokeWidth="35"
								></xProgress>
							</view>
						</view>
						<view class="num" v-if="detail.yw_index">{{ detail.yw_index }}</view>
					</view>
					<view class="btn" v-if="detail.official_comment.status">
						<button @click="goPages('/pages/webview/webview?url=' + detail.official_comment.storeInfo.url)">测评详情</button>
					</view>
				</view>
				<view class="label">
					<view class="label_t flex flex_align_center flex_around" @click="labelShow = !labelShow">
						<view class="line"></view>
						<view class="txt ">
							评分详情
							<text class="iconfont icon-xiangshang" v-if="labelShow"></text>
							<text class="iconfont icon-xiangxia" v-else></text>
						</view>
						<view class="line"></view>
					</view>
					<view class="label_b" :class="{ show: labelShow }">
						<view class="b_h"></view>
						<xLabel :arr="detail.more_evaluation_latitude"></xLabel>
						<view class="b_h1"></view>
					</view>
				</view>
			</view>
			<view class="ticket padding">
				<view class="nav flex flex_between">
					<view class="nav_l">
						<view class="name">{{ detail.storeInfo.store_name }}</view>
						<view class="time1" v-if="detail.storeInfo.evaluation_time">时间：{{ detail.storeInfo.evaluation_time }}</view>
					</view>
					<view class="nav_r"><button @click="normalBuy(false)">购买</button></view>
				</view>
				<view class="footer flex flex_align_center flex_between">
					<view class="flex flex_align_center">
						<button class="btn" @click="ChangeCartNumRule(false, 'normal')" :disabled="attrProductSelectCart_numNormal < 2">-</button>
						<view class="num">{{ attrProductSelectCart_numNormal }}</view>
						<button class="btn " @click="ChangeCartNumRule(true, 'normal')">+</button>
					</view>
					<view class="price flex flex_align_end">
						<view class="flex flex_align_center">
							<view class="txt">着调儿价</view>
							<view class="num1">￥{{ detail.storeInfo.price || 0 }}</view>
						</view>

						<view v-if="detail.storeInfo.ot_price" class="ot_price">
							￥
							<text>{{ detail.storeInfo.ot_price || 0 }}</text>
						</view>
					</view>
				</view>
			</view>
			<wrapVip></wrapVip>
			<view class="ticket padding vip_active" v-if="detail.storeSeckillInfo">
				<view class="nav flex flex_between">
					<view class="nav_l">
						<view class="name">抢！【仅限着调儿人等级会员】</view>
						<view class="time1 flex flex_align_center">
							<template v-if="vipActiveStatus === 2">
								<text class="time">距开始还剩</text>
								<CountDown
									:is-day="true"
									:tip-text="' '"
									:day-text="'天'"
									:hour-text="':'"
									:minute-text="':'"
									:second-text="' '"
									:datatime="parseInt(detail.storeSeckillInfo.isSeckillEnd.str_time)"
									styleAll="acticeSecill"
									@timeEnd="timeEnd"
								></CountDown>
							</template>
							<template v-else-if="vipActiveStatus === 1">
								<text class="time">距结束还剩</text>
								<CountDown
									:is-day="true"
									:tip-text="' '"
									:day-text="'天'"
									:hour-text="':'"
									:minute-text="':'"
									:second-text="' '"
									:datatime="parseInt(detail.storeSeckillInfo.isSeckillEnd.stop)"
									styleAll="acticeSecill"
									@timeEnd="timeEnd"
								></CountDown>
							</template>
							<template v-if="vipActiveStatus === 0">
								<view></view>
							</template>
						</view>
					</view>
					<view class="nav_r">
						<template v-if="vipActiveStatus === 1">
							<button @click="vipBuy(false)">{{ detail.storeSeckillInfo.isSeckillEnd.state }}</button>
						</template>
						<template v-else>
							<button disabled="true" class="disabled">{{ detail.storeSeckillInfo.isSeckillEnd.state }}</button>
						</template>
					</view>
				</view>
				<view class="footer flex flex_align_center flex_between">
					<view class="flex flex_align_center">
						<button class="btn" @click="ChangeCartNumRule(false, 'vip')" :disabled="attrProductSelectCart_numVip < 2 || vipActiveStatus != 1">-</button>
						<view class="num">{{ attrProductSelectCart_numVip }}</view>
						<button class="btn" @click="ChangeCartNumRule(true, 'vip')" :disabled="vipActiveStatus != 1">+</button>
					</view>
					<view class="price">￥{{ detail.storeSeckillInfo.storeInfo.price || 0 }}</view>
				</view>
				<view class="desc">
					<view>使用说明：</view>
					<view>{{ detail.storeSeckillInfo.storeInfo.info }}</view>
				</view>
			</view>
			<view class=" show_title ">
				<view class=" flex flex_align_center flex_between">
					<view class="title">活动简介</view>
					<view class="icon_more">
						<!-- 查看更多 -->
						<!-- <text class="iconfont icon-jiantou"></text> -->
					</view>
				</view>
				<!-- <view class="desc">{{ detail.storeInfo.store_info || '等待添加' }}</view> -->
				<view class="conter" v-if="detail.storeInfo.description">
					<u-parse :html="detail.storeInfo.description" :tag-style="parseStyle" @linkpress="$linkpress"></u-parse>
				</view>
			</view>
			<template v-if="detail.attentionInfo && detail.attentionInfo.length">
				<view class="bottom_line"></view>
				<view class="want_go">
					<view class="nav flex flex_align_center flex_between">
						<view class="title">这些人也感兴趣</view>
						<button type="default" class="go flex_line_height" @click="btnAttention">
							<text>{{ detail.storeInfo.is_follow ? '已关注' : '关注+1' }}</text>
						</button>
					</view>
					<view class="wrap ">
						<scroll-view scroll-x style="white-space:nowrap;" :scroll-with-animation="true">
							<view class="item" v-for="(item, index) in detail.attentionInfo" :key="item.uid" @click="goPages('/pages/yuanshi/user/home?id=' + item.uid,true)">
								<image :src="item.avatar" mode="aspectFill"></image>
								<view class="name">{{ item.nickname }}</view>
								<view class="btn flex_line_height" @click.stop="goFollow(item.is_follow, item.uid, index)">{{ item.is_follow ? '已跟随' : '跟随' }}</view>
							</view>
						</scroll-view>
					</view>
				</view>
			</template>
			<template v-if="detail.recommended_nearby && detail.recommended_nearby.length">
				<view class="bottom_line"></view>
				<view class="goods "><xRecom title="附近推荐" :arr="detail.recommended_nearby" :ids="mixinsParam" :line="false" :more="false"></xRecom></view>
			</template>
			<view class="bottom_line"></view>
			<view class="message" :class="{ minHeight: minHeight }">
				<view class="mess_tab flex flex_between">
					<view class="mess">
						测评区
						<text class="font_size20">{{ detail.evaluation_number || 0 }}测评</text>
					</view>
					<view class="flex flex_align_center">
						<view class="write item flex_line_height relative" :class="{ active: isOnlyMy }" @click="onlyShowMy()">
							<text>{{ isOnlyMy ? '查看全部' : '只看我的' }}</text>
							<view class="absolute tips" v-if="!isOnlyMy && tipsShow">
								<view class="txt">点此查看我的测评</view>
								<view class="flex flex_align_center flex_between txt1">
									<view class="flex flex_align_center" @click.stop="tipsShowCheckClick">
										<view class="image">
											<image src="@/static/images/yuanshi/check.png" mode="widthFix" v-if="tipsShowCheck"></image>
											<image src="@/static/images/yuanshi/uncheck.png" mode="widthFix" v-else></image>
										</view>
										<text>不再提示</text>
									</view>
									<view class="btn" @click.stop="tipsShow = false">确定</view>
								</view>
							</view>
						</view>
						<view class="write flex_line_height" @click="goPages('/pages/yuanshi/evaluate/submit?id=' + mixinsParam.wishId + '&pid=' + mixinsParam.productId)">
							<text>我来测评</text>
						</view>
					</view>
				</view>
				<template v-if="detail.all_evaluation_latitude && detail.all_evaluation_latitude.length">
					<view class="bottom_line"></view>
					<view class="mess_nav relative">
						<view class="nav flex flex_around">
							<view class="item" v-for="(item, index) in detail.all_evaluation_latitude" :key="index" @click="pannelClick(item, index)">
								<view class="num">{{ item.reviewers_number || 0 }}人测评</view>
								<view class="">
									<text class="font_size20">{{ item.cate_name }}</text>
									<text class="iconfont icon-xiangxia"></text>
								</view>
							</view>
						</view>
						<view class="absolute " :class="{ pannel_show: pannelShow }">
							<view class="pannel flex flex_wrap">
								<view class="item" :class="{ active: pannelInfoIdx === index }" v-for="(item, index) in pannelInfo" :key="index" @click="pannelInfoClick(item, index)">
									<text>{{ item.name }}</text>
									<text v-if="pannelInfo_is_scoring === 1">{{ item.scoring }}</text>
									<text v-else></text>
								</view>
								<view class="item">
									<text><!-- 控制距离 --></text>
								</view>
								<view class="item" @click="pannelInfoClick(null, pannelInfoIdx)" style="position: absolute;right:20rpx;bottom:16rpx"><text>清空筛选条件</text></view>
							</view>
						</view>
					</view>
				</template>
				<view class="bottom_line"></view>
				<view class="message_wrap " v-if="messageInfo.length" @click="pannelClick(null, pannelIdx)">
					<view class="item" v-for="(item, index) in messageInfo" :key="item.id">
						<view class="mess_avatar flex flex_align_center flex_between">
							<view class="avatar flex_align_center flex">
								<image :src="item.avatar"></image>
								<text>{{ item.nickname }}</text>
							</view>
							<view class="btn flex_line_height" @click="showInput(1, item)" v-if="!isOnlyMy">回复</view>
						</view>
						<view class="score flex flex_align_center">
							<view class="txt">评分</view>
							<view class="process flex flex_align_center">
								<view style="width: 137rpx;">
									<xProgress
										:diy="true"
										:isMove="false"
										:percent="(Number(item.score) / 5) * 100"
										:bg="require('@/static/images/yuanshi/star3.png')"
										:active="require('@/static/images/yuanshi/star4.png')"
										backgroundSize="29rpx 29rpx"
										:strokeWidth="29"
									></xProgress>
								</view>
								<view class="num">{{ item.score }}</view>
							</view>
						</view>
						<view class="label">
							<xLabel
								:arr="item.evaluation_latitude"
								:title="false"
								:score="true"
							></xLabel>
						</view>
						<view class="mess_des" v-if="item.content">
							<view v-for="(item1, index1) in item.content" :key="item1">
								<text>{{ item1 }}</text>
							</view>
						</view>
						<view class="mess_handle ">
							<view class="flex flex_align_center flex_between">
								<view class="time">{{ item.add_time }}</view>
								<view class="flex flex_align_center">
									<view class="item flex flex_align_center" @click="likeStars(item, index)">
										<image src="@/static/images/yuanshi/like_.png" mode="widthFix" v-if="item.is_like"></image>
										<image src="@/static/images/yuanshi/like.png" mode="widthFix" v-else></image>
										<text>{{ item.like_count || 0 }}</text>
									</view>
									<view class="item flex flex_align_center">
										<image src="@/static/images/yuanshi/comment.png" mode="widthFix"></image>
										<text>{{ item.comment_count || 0 }}</text>
									</view>
								</view>
							</view>
							<view class="time" style="margin-top: 10rpx;" v-if="item.store_name">
								体验店铺：{{ item.store_name || '' }}
							</view>
						</view>

						<view class="mess_image flex" v-if="item.image && item.image.length">
							<view class="nav rote180" @click="navClick('left')"><text class="iconfont icon-jiantou "></text></view>
							<view class="wrap ">
								<scroll-view scroll-x style="white-space:nowrap;" :scroll-with-animation="true" :scroll-left="scrollLeft" @scroll="scrollX">
									<view class="item" v-for="(item1, index1) in item.image" :id="index1 === 0 ? 'calcItem' : ''">
										<image :src="item1" mode="aspectFill" @click="previewImage(item.image, item1)"></image>
									</view>
								</scroll-view>
							</view>
							<view class="nav" @click="navClick('right')"><text class="iconfont icon-jiantou"></text></view>
						</view>

						<template v-if="isOnlyMy && (item.status === 2 || item.status === 3)">
							<view class="flex flex_align_center flex_between my_handle">
								<view class="txt">
									<text class="font_size20" v-if="item.status === 2">您的测评正在审核中…</text>
									<text class="font_size20" v-if="item.status === 3">您的测评审核不通过</text>
								</view>
								<view class="flex flex_align_center">
									<view
										class="btn flex_line_height"
										@click="goPages('/pages/yuanshi/evaluate/submit?type=edit&id=' + mixinsParam.wishId + '&pid=' + mixinsParam.productId + '&eid=' + item.id)"
									>
										编辑
									</view>
									<view class="btn flex_line_height" @click="delMyEvaluate(item, index)">删除</view>
								</view>
							</view>
						</template>
						<view class="comment">
							<xComment
								:ref="'xComment' + item.id"
								:ctype="2"
								:rid="item.id"
								:chat="false"
								@showInput="chatInput"
								@success="sendSuccess"
								:time="false"
								:tips="false"
								:info="item.comment_area"
							></xComment>
						</view>
					</view>
				</view>
				<view v-else class="text_center " style="color: #b7b7b7;padding: 20rpx 0;font-size: 24rpx;"><text class="">来添加第一条测评吧</text></view>
			</view>
		</view>

		<view class="active_footer relative" :class="{ hide: footerHidden }" :style="{ opacity: footerOpacity }">
			<view class="absolute flex flex_align_center flex_between">
				<view class="flex flex_l flex_align_center">
					<view class="item" @click="btnAttention">
						<view>
							<image src="@/static/images/yuanshi/love1.png" mode="" v-if="detail.storeInfo.is_follow"></image>
							<image src="@/static/images/yuanshi/love1_1.png" mode="" v-else></image>
						</view>
						<view class="font_size20">关注</view>
					</view>
                    <!-- #ifdef MP-WEIXIN -->
                    <view class="item" @click="openWeChat">
                    	<view><image src="@/static/images/yuanshi/kf.png" mode=""></image></view>
                    	<view class="font_size20">客服</view>
                    </view> 
                    <!-- #endif -->
					<!-- #ifndef MP-WEIXIN -->
					<view class="item" @click="goPages(`/pages/user/CustomerList?id=${mixinsParam.productId}&wid=${mixinsParam.wishId}&type=0`,true)">
						<view><image src="@/static/images/yuanshi/kf.png" mode=""></image></view>
						<view class="font_size20">客服</view>
					</view> 
					<!-- #endif -->
                    
				</view>
				<view class="btn flex">
					<view class="btn_l" @click="tapBuy">立即购买</view>
					<view class="btn_r" @click="goPages('/pages/yuanshi/evaluate/submit?id=' + mixinsParam.wishId + '&pid=' + mixinsParam.productId)">我来测评</view>
				</view>
			</view>
		</view>
		<Product-window ref="productWindow" @changeFun="changeFun" :attr="attr" :iSplus="true" :isQuota="attrProductSelectType === 'vip'"></Product-window>
		<x-authorize @login="updateData"></x-authorize>

		<xChat :adjustPosition="false" :placeholder="placeholder" :inputShow="inputShow" :uid="uid" @send="submitComment" desc="comment"></xChat>
	</view>
</template>

<script>
import readyToPay from '@/mixins/readyToPay';
import yuanshiDetail from '@/mixins/yuanshiDetail';

// #ifdef H5
import { isWeixin } from '@/utils/validate.js';
import { openShareAll } from '@/utils/wechat/share.js';
// #endif
import { uniSelectorQueryInfo } from '@/utils/uni_api.js';
import { initTime,
        authNavigator,
        openWeChatCustomerService
} from '@/utils/common.js';
import {
        WX_KEFU_Link,
        WX_ENTERPRISE_Link
    } from '@/config.js';
import xRecom from '@/components/yuanshi/x-recom.vue';
import xChat from '@/components/x-chat/x-chat';
import wrapVip from '@/components/yuanshi/wrap-vip';
import xComment from '@/components/x-comment/x-comment/x-comment';
import xLabel from '@/components/x-label/x-label.vue';
import xProgress from '@/components/x-progress/x-progress.vue';
import ProductWindow from '@/components/ProductWindow';
import CountDown from '@/components/CountDown';
import { yComment } from '@/api/yuanshi/public.js';
import { userFollow, userUnFollow } from '@/api/yuanshi/user.js';
import { reportEvaluateDetail } from '@/utils/ReportAnalytics.js';
import storage from '@/utils/storage.js';
import {
	evaluationLabels,
	evaluationDetail,
	evaluationComment,
	evaluationAttention,
	evaluationUnAttention,
	evaluationLike,
	evaluationUnLike,
	evaluationMe,
	evaluationDel
} from '@/api/yuanshi/evaluate.js';
export default {
	mixins: [readyToPay, yuanshiDetail],
	components: {
		xComment,
		xLabel,
		xRecom,
		xProgress,
		ProductWindow,
		CountDown,
		wrapVip,
		xChat
	},
	data() {
		return {
			detail: {
				storeInfo: {
					slider_image: [],
					is_follow: false
				},
				storeSeckillInfo: {
					isSeckillEnd: {},
					storeInfo: {}
				},
				official_comment: {}
			},
			attrProductSelectCart_numNormal: 1, //正常商品购买数量
			attrProductSelectCart_numVip: 1, //秒杀商品购买数量
			attrProductSelectType: 'normal',
			mixinsParam: {
				wishId: 12,
				productId: 76
			},
			messageInfo: [],
			opacity: 0.5,
			scrollLeft: 0,
			initCalcItemScroll: 0,
			scrollDetail: {},
			pannelShow: false,
			pannelInfo: [],
			pannelInfoIdx: -1, //my:编辑删除等操作
			pannelInfo_is_scoring: 0,
			pannelIdx: -1,
			footerOpacity: 0,
			requestLoading: false,
			page: {
				page: 1,
				limit: 20,
				more: true
			},
			footerHidden: false,
			uid: -1,
			placeholder: '',
			inputShow: false,
			keywords: '',
			minHeight: false,
			isOnlyMy: false,
			tipsShow: false,
			tipsShowCheck: false,
			labelShow: false
		};
	},
	computed: {
		vipActiveStatus() {
			if (this.detail.storeSeckillInfo) {
				// let stime = this.detail.storeSeckillInfo.start_time * 1000,
				// 	ntime = new Date().getTime(),
				// 	etime = this.detail.storeSeckillInfo.isSeckillEnd.stop * 1000;

				// console.log(this.detail.storeSeckillInfo)
				return this.detail.storeSeckillInfo.isSeckillEnd.status || 0;
				// if (ntime < stime) {
				// 	return 0; //未开始
				// } else if (ntime > etime) {
				// 	return 2; //已结束
				// } else {
				// 	return 1; //活动进行中
				// }
			}
		}
	},
	methods: {
        openWeChat(){
            openWeChatCustomerService(WX_KEFU_Link,WX_ENTERPRISE_Link)
        },
		tipsShowCheckClick() {
			this.tipsShowCheck = !this.tipsShowCheck;
			storage.set('tipsShowCheck', this.tipsShowCheck);
		},
		isTipsShow(){
			this.isOnlyMy = false;
			this.onlyShowMy(true);
			this.tipsShow = storage.get('tipsShowCheck') ? false : true;
		},
		ChangeCartNumRule(type, type1) {
			if (type) {
				// 加法
				if (type1 === 'normal') {
					this.attrProductSelectCart_numNormal = this.attrProductSelectCart_numNormal + 1;
				}
				if (type1 === 'vip') {
					this.attrProductSelectCart_numVip = this.attrProductSelectCart_numVip + 1;
				}
			} else {
				// 减法
				if (type1 === 'normal') {
					this.attrProductSelectCart_numNormal = this.attrProductSelectCart_numNormal > 1 ? this.attrProductSelectCart_numNormal - 1 : 1;
				}
				if (type1 === 'vip') {
					this.attrProductSelectCart_numVip = this.attrProductSelectCart_numVip > 1 ? this.attrProductSelectCart_numVip - 1 : 1;
				}
			}
			if (type1 === 'normal') {
			this.$set(this.attr.productSelect, 'cart_num', this.attrProductSelectCart_numNormal);
			}
			if (type1 === 'vip') {
				this.$set(this.attr.productSelect, 'cart_num', this.attrProductSelectCart_numVip);
			}
		},
		showInput(type = 1, item) {
			// console.log(this.$refs['xComment' + id]);
			const {id,nickname} = item;
			this.inputShow = true;
			this.footerHidden = true;
			this.xComment = this.$refs['xComment' + id][0];
			this.xComment.showInput(type, {wish_id:id,nickname},null, null);
		},
		pannelClick(item, index) {
			if (this.isOnlyMy && item) return this.$showToast('只看我的下不支持筛选');
			if (this.pannelIdx === index) {
				this.pannelIdx = -1;
				this.pannelShow = false;
			} else {
				this.pannelShow = true;
				this.pannelIdx = index;
			}
			if (item) {
				this.pannelInfoIdx = -1;
				this.pannelInfo_is_scoring = item.is_scoring;
				this.pannelInfo = item.label_group;
			}
		},
		pannelInfoClick(item, index) {
			if (this.pannelInfoIdx === index) {
				if (!item) {
					this.pannelInfoIdx = -1;
					this.keywords = '';
					this.pannelShow = false;
				} else {
					return;
				}
			} else {
				this.pannelInfoIdx = index;
				this.keywords = item.name;
			}
			this.messageInfo = [];
			this.page = {
				page: 1,
				limit: this.page.page,
				more: true
			};
			this.getComment();
		},
		onlyShowMy(typ = false) {
			this.messageInfo = [];
			this.page = {
				page: 1,
				limit: this.page.page,
				more: true
			};
			if (!typ) {
				this.isOnlyMy = !this.isOnlyMy;
			}
			if (this.isOnlyMy) {
				this.sendSuccess();
			}
			this.keywords = '';
			this.getComment();
		},
		chatInput({id,uid}, placeholder) {
			this.inputShow = true;
			this.footerHidden = true;
			this.uid = uid;
			this.xComment = this.$refs['xComment' + id][0];
			this.placeholder = placeholder;
		},
		submitComment(val) {
			this.xComment.submitComment(val);
		},
		sendSuccess() {
			this.inputShow = false;
			this.footerHidden = false;
			this.opacity = 1;
		},
		share() {},
		rightFixedHandle(type) {
			switch (type) {
				case 'share':
					this.share();
					break;
				case 'write':
					this.showInput(1);
					break;
				case 'top':
					uni.pageScrollTo({
						scrollTop: 0,
						duration: 300
					});
					break;
				default:
			}
		},
		likeStars(item, index) {
			let obj = { related_id: item.id, type: 2 };
			if (item.is_like) {
				evaluationUnLike(obj).then(res => {
					this.messageInfo[index].like_count--;
				});
			} else {
				evaluationLike(obj).then(res => {
					this.messageInfo[index].like_count++;
				});
			}
			this.messageInfo[index].is_like = !item.is_like;
		},
		goPages(path, type, pathType) {
            if (type) {
                this.$authNavigator(path);
            } else {
                this.$navigator(path, pathType);
            }
		},
        
		navClick(type) {
			let initCalcItemScroll = this.initCalcItemScroll,
				oldScrollLeft = this.scrollDetail.scrollLeft || 0;
			if (type === 'left') {
				if (oldScrollLeft > 0) {
					this.scrollLeft = oldScrollLeft > initCalcItemScroll ? oldScrollLeft - initCalcItemScroll : 0;
				}
			} else {
				this.scrollLeft = oldScrollLeft + initCalcItemScroll;
			}
		},
		scrollX(e) {
			this.scrollDetail = e.detail;
		},

		getEvaluateDetail(wid, pid) {
			this.$set(this.attr, 'productAttr', []);
			evaluationDetail({ wish_id: wid, product_id: pid })
				.then(res => {
					this.$refs.productWindow.initAttr();
					res.data.recommended_nearby.map((item, index) => {
						item.yw_index = item.yw_index ? item.yw_index.toFixed(1) : '0.0';
					});
					let storeSeckillInfo = res.data.storeSeckillInfo;
					res.data.yw_index = res.data.yw_index.toFixed(1);
					this.detail = res.data;
					reportEvaluateDetail({ id: wid, pid: pid, store_name: res.data.storeInfo.store_name, uid: this.$store.state.userInfo.uid || 0 });
					this.$set(this, 'storeInfo', res.data.storeInfo);
					this.$set(this.attr, 'productAttr', res.data.productAttr);
					this.$set(this, 'productValue', res.data.productValue);
					this.$updateTitle(res.data.storeInfo.store_name);
					
					
					this.shareConfig = {
						desc: res.data.storeInfo.store_name,
						title: res.data.storeInfo.store_name,
						link: '/pages/yuanshi/evaluate/detail?wid=' + this.mixinsParam.wishId + '&pid=' + this.mixinsParam.productId,
						imgUrl: res.data.storeInfo.slider_image[0]
					};
					// #ifdef H5
					openShareAll(this.shareConfig);
					// #endif
					
					
					this.DefaultSelect();
				})
				.catch(err => {
					this.$navigator(-1);
				});

			this.getComment();
		},
		normalBuy(init) {
			// init 是否仅初始化
			if (this.attrProductSelectType === 'vip') {
				this.attrProductSelectType = 'normal';
				this.mixinsParam.secKillId = null;
			}
			let detail = this.detail;
			this.$set(this, 'storeInfo', detail.storeInfo);
			this.$set(this.attr, 'productAttr', detail.productAttr);
			this.$set(this, 'productValue', detail.productValue);
			this.DefaultSelect();
			if (!init) {
				this.footerOpacity = 1;
				this.tapBuy();
			}
		},
		vipBuy(init) {
			console.log('this.attrProductSelectType', this.attrProductSelectType);
			if (this.attrProductSelectType === 'normal') {
				let userInfo = this.$store.state.userInfo;
				// if(!userInfo.vip&&){
				// 	return this.$showToast('仅限着调儿人等级会员')
				// }
				let { membership_level, membership_condition } = this.detail.storeSeckillInfo.storeInfo;
				membership_level = parseInt(membership_level);
				membership_condition = parseInt(membership_condition);

				if (membership_level === 0) {
					// 免费会员
					if (membership_condition === 0 || userInfo.level > membership_condition || userInfo.level === membership_condition) {
						// 允许参与活动
					} else {
						if (membership_condition === 1) {
							return this.$showToast('仅限小嘢人等级购买');
						} else if (membership_condition === 2) {
							return this.$showToast('仅限着调儿人等级购买');
						}
					}
				} else {
					//付费会员
					if (!userInfo.vip) {
						return this.$showToast('仅限付费会员购买');
					}
				}
				this.attrProductSelectType = 'vip';
			}

			let storeSeckillInfo = this.detail.storeSeckillInfo;
			let storeInfo = storeSeckillInfo.storeInfo;
			// storeInfo.store_name = storeInfo.store_name ? storeInfo.store_name : storeInfo.title;

			console.log('storeInfo', storeInfo);
			console.log('storeSeckillInfo', storeSeckillInfo);
			this.$set(this, 'storeInfo', storeInfo);
			this.mixinsParam.secKillId = storeInfo.id;
			this.$set(this.attr, 'productAttr', storeSeckillInfo.productAttr);
			this.$set(this.attr.productSelect, 'cart_num', this.attrProductSelectCart_numVip);
			this.$set(this, 'productValue', storeSeckillInfo.productValue);
			this.footerHidden = false;
			this.DefaultSelect();
			this.footerOpacity = 1;
			this.tapBuy();
		},
		getComment() {
			if (!this.page.more || this.requestLoading) return;
			this.requestLoading = true;
			let obj = {
					wish_id: this.mixinsParam.wishId,
					product_id: this.mixinsParam.productId,
					page: this.page.page,
					limit: this.page.limit,
					keywords: this.keywords
				},
				req = '';
			if (this.isOnlyMy) {
				req = evaluationMe(obj);
			} else {
				req = evaluationComment(obj);
			}

			req
				.then(res => {
					let storeInfo = res.data.storeInfo;
					if (!this.minHeight) {
						this.minHeight = storeInfo.length ? true : false;
					}
					storeInfo.forEach((item, index) => {
						if (typeof item.content === 'string') {
							item.content = [item.content];
						}
					});
					this.messageInfo = this.messageInfo.concat(res.data.storeInfo);
					this.page.more = res.data.storeInfo.length === this.page.limit;
					this.page.page++;
					this.requestLoading = false;
				})
				.catch(err => {
					console.log(err);
					this.requestLoading = false;
				});
		},
		btnAttention() {
			let product_id = this.mixinsParam.productId,
				is_follow = this.detail.storeInfo.is_follow,
				follow_test_number = this.detail.storeInfo.follow_test_number;
			console.log('关注相关操作');
			if (is_follow) {
				evaluationUnAttention(product_id).then(res => {
					this.detail.storeInfo.is_follow = false;
					this.detail.storeInfo.follow_test_number = follow_test_number > 1 ? follow_test_number - 1 : 0;
					this.$showToast('已取消');
				});
			} else {
				evaluationAttention(product_id).then(res => {
					this.detail.storeInfo.is_follow = true;
					this.detail.storeInfo.follow_test_number = follow_test_number + 1;
					this.$showToast('关注成功');
				});
			}
		},
		goFollow(type, id, idx) {
			if (this.$store.state.userInfo.uid === id) {
				return this.$showToast('不能对自己进行相关操作');
			}
			if (type) {
				userUnFollow(id).then(res => {
					this.detail.attentionInfo[idx].is_follow = false;
					this.$showToast('已取消');
				});
			} else {
				userFollow(id).then(res => {
					this.$showToast('跟随成功');
					this.detail.attentionInfo[idx].is_follow = true;
				});
			}
		},
		previewImage(urls, current) {
			uni.previewImage({
				urls: urls,
				current: current //地址需为https
			});
		},

		updateData() {
			const { wishId, productId } = this.mixinsParam;
			this.getEvaluateDetail(wishId, productId);
			this.messageInfo = [];
			this.page = {
				page: 1,
				limit: this.page.page,
				more: true
			};
			this.getComment();
		},
		timeEnd() {
			const { wishId, productId } = this.mixinsParam;
			this.getEvaluateDetail(wishId, productId);
		},
		delMyEvaluate(item, index) {
			let _this = this;
			this.$showModal('提示', '是否要删除', {
				success: function(res) {
					console.log(res);
					if (res.confirm) {
						evaluationDel(item.id)
							.then(res => {
								_this.$showToast('删除成功');
								_this.messageInfo.splice(index, 1);
							})
							.catch(err => {
								_this.$showToast('删除失败');
							});
					}
				},
				fail: function() {}
			});
		}
	},
	onPageScroll(e) {
		this.inputShow = false;
		this.footerHidden = false;
		this.opacity = (e.scrollTop / 400).toFixed(1);
		if (e.scrollTop > 200) {
			this.footerOpacity = (e.scrollTop / 400).toFixed(1);
		} else {
			this.footerOpacity = 0;
		}
	},
	mounted() {
		let _this = this;
		uniSelectorQueryInfo('#calcItem', this)
			.then(res => {
				_this.initCalcItemScroll = res.width;
			})
			.catch(err => {});
	},
	onLoad(option) {
		const { wid = 1, pid = 76, type } = option;
		this.mixinsParam = {
			wishId: wid,
			productId: pid
		};
		if (type === 'pj') {
			let timer = setTimeout(() => {
				this.goPages('/pages/yuanshi/evaluate/submit?id=' + wid + '&pid=' + pid);
			}, 800);
		}
	},
	onShow() {
		const wid = this.mixinsParam.wishId;
		const pid = this.mixinsParam.productId;
		this.getEvaluateDetail(wid, pid);
		// if(this.detail.storeInfo.id){
		// 	console.log('dddddddddddddddd',this.detail)
		// 	console.log('dddddddddddddddd',this.attrProductSelectType)
		// 		this.normalBuy(true);

		// }
	},
	onPullDownRefresh() {
		this.stopPullRefresh(1000);
	},
	onReachBottom() {
		this.getComment();
	},
	// #ifdef MP
	onShareAppMessage() {
		return {
			title: this.shareConfig.title,
			imageUrl: this.shareConfig.imgUrl,
			path: this.shareConfig.link
		};
	},
	onShareTimeline() {
		if (this.shareConfig) {
			return {
				title: this.shareConfig.title,
				imageUrl: this.shareConfig.imgUrl,
				query: this.shareConfig.link.split('?')[1]
			};
		}
	}
	// #endif
};
</script>
<style>
page {
	background: #f7f7f9;
}
</style>
<style scoped lang="scss">
.bottom_line {
	height: 2rpx;
	background: #e2e6ec;
	margin: 0 48rpx;
}
button {
	width: 300rpx;
	margin: 30rpx auto;
}
.active {
	image {
		width: 100%;
		height: 100%;
	}
	.icon_more {
		font-size: 24rpx;

		color: #999999;
		.iconfont {
			font-size: 24rpx;
			color: #555555;
		}
	}
	.padding {
		padding: 32rpx 36rpx 32rpx 34rpx;
		margin: 0 20rpx 20rpx 20rpx;
		background: #fff;
	}
	.main {
		position: absolute;
		width: 100%;
		margin-top: -18rpx;
		.wrap_title {
			border-radius: 16rpx;
			background: #fff;
			margin: 0 16rpx 30rpx 16rpx;
			// padding-bottom: 34rpx;
			padding: 50rpx 0 0rpx 33rpx;
			.title {
				width: 170rpx;
				.logo {
					width: 144rpx;
					height: 44rpx;
					margin-bottom: 10rpx;
				}
				.say_feeling{
					&::after{
						right: -12rpx;
						transform: scale(0.32);
					}
				}
			}
			.num {
				margin-left: 28rpx;
				font-size: 124rpx;
				font-weight: bold;
				color: #ff5656;
				height: 152rpx;
				line-height: 152rpx;
			}
			.btn {
				padding-right: 18rpx;
				button {
					width: 148rpx;
					height: 60rpx;
					border-radius: 24rpx;
					line-height: 56rpx;
					color: #666666;
					font-size: 24rpx;
					border: 2rpx solid #d2d2d2;
				}
			}
			.label {
				.label_t {
					margin-top: 30rpx;
					margin-left: -33rpx;
					.line {
						height: 2rpx;
						width: 270rpx;
						background: #d2d2d2;
					}
					.txt {
						width: calc(100% - 540rpx);
						text-align: center;
						font-size: 24rpx;
						color: #50506f;
						.iconfont {
							font-size: 24rpx;
							padding-left: 16rpx;
						}
					}
				}
				.label_b {
					// padding-top: 40rpx;
					width: calc(100% + 4rpx);
					max-height: 40rpx;
					overflow: hidden;
					transition: max-height 0.25s;
					.b_h {
						height: 50rpx;
					}
					.b_h1 {
						height: 60rpx;
					}
					&.show {
						max-height: 800rpx;
						overflow: scroll;
					}
				}
			}
		}
		.ticket {
			margin-bottom: 20rpx;
			border-radius: 30rpx;
			padding: 30rpx;
			.nav {
				.nav_l {
					margin-right: 30rpx;
					.name {
						font-size: 28rpx;
						font-weight: bold;
						color: #333333;
					}
					.time1 {
						margin-top: 12rpx;
						font-size: 24rpx;
						font-weight: 400;
						color: #666666;
						.time {
							color: #ff3d3d !important;
						}
					}
				}
				.nav_r {
					button {
						width: 108rpx;
						height: 60rpx;
						line-height: 56rpx;
						border: 2rpx solid #ff5656;
						border-radius: 24rpx;
						font-size: 24rpx;
						font-weight: 400;
						color: #ff5656;
						margin: 0;
					}
				}
			}
			.footer {
				margin-top: 40rpx;
				.btn {
					padding: 0;
					width: 40rpx;
					height: 40rpx;
					line-height: 40rpx;
					font-size: 24rpx;
					color: #808080;
					border-radius: 50%;
					&::after {
						border: 2rpx solid #d2d2d2;
						border-radius: 50%;
					}
				}
				.num {
					width: 60rpx;
					text-align: center;

					font-weight: bold;
					color: #333333;
					font-size: 28rpx;
				}
				.price {
					font-weight: 500;
					.txt {
						border-radius: 12rpx;
						// background: #ff5656;
						// color: #ffffff;

						color: #999999;
						font-size: 26rpx;
						height: 44rpx;
						line-height: 44rpx;
						padding: 0 6rpx;
					}
					.num1 {
						margin: 0 4rpx;
						color: #3e3e3e;
						font-size: 32rpx;
					}
					.ot_price {
						font-size: 24rpx;

						color: #999999;
						text {
							text-decoration: line-through;
						}
					}
				}
			}
			&.vip_active {
				border: 4rpx solid #ff5656;
				margin-top: 40rpx;
				margin-bottom: 0rpx;
				.nav {
					.nav_l {
						.time {
							color: #ff5656;
						}
					}
					.nav_r {
						button {
							width: 152rpx;
							background: #ff5656;

							color: #ffffff;
							&.disabled {
								color: #666666;
								background: #d2d2d2;

								border: none;
							}
						}
					}
				}
				.footer {
					.price {
						color: #ff5656;

						font-weight: 500;
						font-size: 40rpx;
					}
				}
				.desc {
					border-top: 2rpx dashed #999;
					color: #999;
					font-size: 24rpx;
					view {
						&:first-child {
							margin: 20rpx 0 10rpx 0;
						}
					}
				}
			}
		}

		.show_title {
			font-weight: 400;
			background: none;
			margin-top: 70rpx;
			padding: 0 64rpx 0 48rpx;
			.title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333333;
			}

			.desc {
				margin: 40rpx 0 60rpx 0;
				@include show_line(5);
			}
			.conter {
				margin: 40rpx 0 60rpx 0;
			}
		}
		.want_go {
			padding: 70rpx 0 70rpx 48rpx;
			.nav {
				.title {
					color: #333333;
					font-weight: bold;
					font-size: 32rpx;
				}
				.go {
					width: 148rpx;
					height: 60rpx;
					border: 2rpx solid #ff5656;
					border-radius: 24rpx;
					margin: 0 64rpx 0 0;
					color: #ff5656;
					font-size: 24rpx;
				}
			}

			.wrap {
				margin-top: 50rpx;
				.item {
					display: inline-block;
					&:not(:last-child) {
						padding-right: 8rpx;
					}
					image {
						width: 120rpx;
						height: 120rpx;
						border-radius: 50%;
					}
					.name {
						text-align: center;
						margin: 16rpx 0;
						color: #999999;
						font-weight: bold;
						font-size: 28rpx;
						width: 120rpx;
						@include show_line;
					}
					.btn {
						width: 94rpx;
						height: 40rpx;
						border: 2rpx solid #d2d2d2;
						border-radius: 14rpx;
						margin: 0 auto;
						color: #999999;
					}
				}
			}
		}
		.goods {
			padding: 70rpx 0 70rpx 48rpx;
		}
		.message {
			margin-top: 70rpx;
			margin-bottom: 200rpx;
			min-height: 500rpx;
			&.minHeight {
				min-height: 100vh;
			}
			.bottom_line {
				height: 2rpx;
				background: #e2e6ec;
				margin: 0 30rpx;
			}
			.mess_tab {
				padding: 0rpx 48rpx 32rpx 48rpx;
				.mess {
					font-size: 32rpx;

					color: #333333;
					font-weight: bold;
					text {
						padding: 0 16rpx;

						font-weight: 400;
						color: #999999;
					}
				}
				.write {
					width: 148rpx;
					height: 60rpx;
					border: 2rpx solid #ff5561;

					background: #ff5656;
					color: #fff;
					border-radius: 24rpx;
					text {
						display: inline-block;
						font-size: 24rpx;
					}
					.tips {
						z-index: 999;
						width: 356rpx;
						height: 150rpx;
						background: #ffffff;
						border-radius: 30rpx;
						box-shadow: 0px 0px 30rpx 0px rgba(107, 127, 153, 0.3);
						text-align: left;
						color: #50506f;
						padding: 22rpx 50rpx;
						bottom: -185rpx;
						font-family: PingFang SC, PingFang SC-Regular;
						font-weight: 400;
						.txt {
							font-size: 32rpx;
							margin-bottom: 20rpx;
						}
						.txt1 {
							font-size: 24rpx;
							.image {
								margin-right: 10rpx;
								height: 36rpx;
								image {
									width: 36rpx;
									height: 36rpx;
								}
							}
							.btn {
								padding: 4rpx 26rpx;
								border: 2rpx solid #50506f;
								border-radius: 18rpx;
								transform: scale(0.82);
								transform-origin: right;
							}
						}
						&::before {
							position: absolute;
							content: '';
							z-index: 999999;
							width: 0rpx;
							height: 0rpx;
							top: -30rpx;
							right: 40%;
							// background: red;
							border-bottom: 40rpx solid #fff;
							border-left: 10rpx solid transparent;
							border-right: 30rpx solid transparent;
							transform: skew(35deg);
						}
					}
					&.item {
						color: #ff5656;
						background: none;
						margin-right: 20rpx;
						&.active {
							border: 2rpx solid #50506f;
							color: #50506f;
						}
					}
				}
			}
			.mess_nav {
				margin: 0 31rpx;
				padding: 20rpx 0 24rpx 0;
				// border-top: 2rpx solid #d8d8d8;
				// border-bottom: 2rpx solid #d8d8d8;
				.nav {
					.item {
						flex: 1;
						color: #333333;
						text-align: center;
						.num {
							color: #999999;
						}
						&:not(:last-child) {
							border-right: 2rpx solid #d8d8d8;
						}
						.iconfont {
							font-size: 24rpx;
							font-weight: bold;
						}
					}
				}
				.absolute {
					z-index: 9999;
					max-height: 0;
					top: 128rpx;
					overflow: scroll;
					background: #f4f4f4;
					box-shadow: 0px 0px 30rpx rgba(107, 127, 153, 0.3);
					border-radius: 0px 0px 50rpx 50rpx;
					transition: max-height 0.5s;
					&.pannel_show {
						max-height: 300rpx;
					}
					.pannel {
						padding: 20rpx;
						.item {
							min-width: 66rpx;
							// height: 44rpx;
							// line-height: 44rpx;
							margin: 0 8rpx 20rpx 0;
							padding: 8rpx 24rpx;
							border-radius: 16rpx;
							background: #ffffff;
							text {
								@include font_size;
								&:first-child {
									color: #999999;

									font-weight: 400;
								}
								&:last-child {
									font-weight: 500;
									color: #3e3e3e;
								}
							}
							&:nth-last-child(2) {
								margin-right: 164rpx;
								min-width: 0;
								padding: 8rpx 0;
								background: none;
							}
							&.active {
								border: 2rpx solid #50506f;
							}
							&:last-child {
								background: none;
								color: #50506f;
								border-radius: 16rpx;
								box-shadow: 0px 0px 20rpx rgba(107, 127, 153, 0.2);
								border: 2rpx solid #50506f;
							}
						}
					}
				}
			}
			.message_wrap {
				padding: 0 54rpx 0 48rpx;
				background: none;
				> view.item {
					padding-bottom: 20rpx;
					> view:not(.mess_avatar) {
						padding-left: 52rpx;
					}
					&:not(:last-child) {
						border-bottom: 2rpx solid #e2e6ec;
					}
				}
				.mess_avatar {
					margin: 50rpx 0 20rpx 0;
					padding-bottom: 14rpx;
					.avatar {
						image {
							width: 60rpx;
							height: 60rpx;
							border-radius: 50%;
							margin-right: 32rpx;
						}
						text {
							color: #101010;
							font-size: 28rpx;
						}
					}
					.btn {
						width: 108rpx;
						height: 50rpx;
						margin-left: 20rpx;
						border: 1px solid rgba(0, 0, 0, 0.078);
						border-radius: 32rpx;

						font-size: 24rpx;

						color: #666666;
					}
				}
				.score {
					margin-bottom: 40rpx;
					.txt {
						padding: 8rpx 16rpx;

						border-radius: 20rpx;
						border: 2rpx solid #ff5656;
						color: #ff5656;
						// letter-spacing: 12rpx;
						font-weight: 500;
						font-size: 28rpx;
						margin-right: 12rpx;
					}
					.process {
						// width: 276rpx;
						height: 58rpx;
						padding: 14rpx 20rpx;
						background: #ffffff;
						opacity: 1;
						border-radius: 20rpx;
						.num {
							margin-left: 14rpx;
							color: #ff5656;
							font-weight: bold;
							font-size: 40rpx;
						}
					}
				}
				.label {
					::deep.xlabel {
						> .wrap {
							margin-bottom: 36rpx;
							background: none;
							.flex {
								background: none;
							}
							.more {
								width: 60rpx;
								height: 60rpx;
								border-radius: 30rpx;
								text {
									width: 60rpx;
									height: 60rpx;
									border-radius: 30rpx;
								}
							}
						}
					}
				}
				.mess_des {
					margin-top: 42rpx;
					font-size: 24rpx;
					line-height: 36rpx;
					color: #101010;
					margin-bottom: 20rpx;
				}
				.mess_image {
					height: 148rpx;
					margin-bottom: 20rpx;
					margin-left: -20rpx;
					.nav {
						width: 20rpx;
						line-height: 148rpx;
						text-align: center;
						text {
							font-size: 24rpx;
							font-weight: bolder;
						}
					}
					.wrap {
						width: calc(100% - 40rpx);

						.item {
							display: inline-block;
							&:not(:last-child) {
								padding-right: 6rpx;
							}
							image {
								width: 136rpx;
								height: 136rpx;
							}
						}
					}
				}
				.my_handle {
					background: #fff;
					padding: 12rpx 10rpx 12rpx 82rpx;
					margin-bottom: 30rpx;
					border-radius: 32rpx;
					box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
					.txt {
						color: #ff5656;
					}
					.btn {
						width: 152rpx;
						height: 60rpx;
						background: #eaeaea;
						opacity: 1;
						border-radius: 24rpx;

						color: #50506f;
						font-size: 24rpx;
						margin-left: 8rpx;
					}
				}
				.mess_handle {
					font-size: 24rpx;

					color: #ff5656;
					margin: 32rpx 0 40rpx 0;
					.time {
						color: #999999;
					}
					.item {
						image {
							width: 24rpx;
							height: 24rpx;
						}
						text {
							padding: 0 16rpx;
							font-size: 24rpx;

							color: #ff5656;
						}
					}
				}
				.comment {
					padding-left: 0rpx !important;
					margin-left: 52rpx;
					background: #eaeaea;

					max-height: 600rpx;
					overflow-y: scroll;
				}
			}
		}
	}
	.active_footer {
		@include fixed_footer(112rpx);

		background: #f7f8fa;
		&.hide {
			display: none;
		}
		.flex_l {
			width: calc(100% - 400rpx);
			padding: 0 60rpx;
			.item {
				// width: calc((100% - 400rpx) / 2);
				width: 50%;
				text-align: center;
				font-size: 24rpx;

				color: #3e3e3e;
				image {
					width: 40rpx;
					height: 40rpx;
				}
			}
		}

		.btn {
			width: 400rpx;
			height: 112rpx;
			line-height: 112rpx;
			text-align: center;

			border-radius: 30rpx 0px 0px 0px;
			font-size: 32rpx;
			background: #666666;
			color: #ffffff;
			font-weight: bold;
			.btn_l {
				width: 50%;
			}
			.btn_r {
				width: 50%;
				background: #ff5656;
				border-radius: 30rpx 0px 0px 0px;
			}
		}
	}
}
</style>
