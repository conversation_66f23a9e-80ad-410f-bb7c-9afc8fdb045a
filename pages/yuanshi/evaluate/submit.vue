<template>
	<view class="submit ">
		<view class="fixed fixed_l">
			<view v-for="(item, index) in labelList" class="item " :class="{ active: floorIdx === index }" @click="scrollIntoView(index)">
				<view>{{ index + 1 }}</view>
			</view>
			<!-- 自建标签 -->
			<!-- <view class="item " :class="{ active: floorIdx === labelListLen }" @click="scrollIntoView(labelListLen)">
				<view>{{ labelListLen + 1 }}</view>
			</view> -->
			<view class="item total flex_line_height" :class="{ active: floorIdx === 'total' }" @click="scrollIntoView('total')">
				<image src="@/static/images/yuanshi/star5_.png" mode="widthFix" v-if="floorIdx === 'total'"></image>
				<image src="@/static/images/yuanshi/star5.png" mode="widthFix" v-else></image>
			</view>
		</view>
		<view class="fixed_r">
			<scroll-view scroll-y="true" @scroll="scroll" @scrolltolower="scrolltolower" :scroll-into-view="scrollToId" :scroll-with-animation="true" style="height:100vh;">
				<view v-for="(item, index) in labelList" :id="'label' + index" class="item item_c" :class="{ item_active: floorIdx === index || item.selected }">
					<view class="nav flex relative" @click="scrollIntoView(index)">
						{{ item.long_cate_name }} ({{ item.is_single === 1 ? '单选' : '可多选' }})
						<view class="skip" @click.stop="skip(index)" v-if="item.is_required === 0">跳过</view>
					</view>
					<view class="wrap">
						<view class="labels">
							<view v-for="(item1, index1) in item.label_group" class="relative" :class="{ active: item1.check }" @click="labelChange(item, index, item1, index1)">
								<text class="line1">{{ item1.name }}</text>
								<text class="absolute close" @click.stop="delDiyLabel(item, index, index1)" v-if="item1.diy"><text class="iconfont icon-guanbi3"></text></text>
							</view>
							<view @click="createDiyLabel(item, index)" v-if="item.is_other === 1"><text class="line1">其它</text></view>
						</view>
						<view class="scores" v-if="item.selected">
							<view class="tips flex flex_align_center">
								<view class="span"></view>
								<view class="txt">滑动星星，给出满意度评分</view>
							</view>
							<view :class="{ scores1: item.is_scoring === 0 }">
								<view v-if="item1.check" v-for="(item1, index1) in item.label_group" class="item1 flex flex_align_center flex_between">
									<text>{{ item1.name }}</text>
									<view style="height: 60rpx;" class="flex flex_align_center" v-if="item.is_scoring === 1">
										<view class="score">{{ Number(item1.scoring).toFixed(1) }}</view>
										<view style="width:220rpx;position: relative;">
											<xProgress
												:diy="true"
												:tips="true"
												:isMove="true"
												:percent="((Number(item1.scoring) < 1 ? 1 : Number(item1.scoring)) / 5) * 100"
												:bg="require('@/static/images/yuanshi/star6.png')"
												:active="require('@/static/images/yuanshi/star6_.png')"
												@move="progressMove"
												@end="progressEnd($event, item, index, item1, index1)"
												backgroundSize="44rpx 44rpx"
												:strokeWidth="44"
											></xProgress>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<!-- 自建标签 -->
				<!-- 	<view :id="'label' + labelListLen" class="item" :class="{ item_active: floorIdx === labelListLen || diyLabelArr.length }">
					<view class="nav" @click="scrollIntoView(labelListLen)">我来说点新鲜的</view>
					<view class="wrap">
						<view class="labels"><text @click="scrollIntoView(labelListLen, true)">+ 自建标签</text></view>
						<view class="scores flex" v-if="diyLabelArr.length">
							<view class="item1 relative" v-for="(item, index) in diyLabelArr" :key="index">
								<text style="margin:0 20rpx 20rpx 0" class="relative">{{ item.name }}</text>
								<view class="span absolute close" @click="diyLabelArr.splice(index, 1)"><i class="iconfont icon-guanbi3"></i></view>
							</view>
						</view>
					</view>
				</view> -->
				<view class="total item_c flex flex_align_center" id="total" @click="scrollIntoView('total')">
					<view class="total_l flex_line_height">
						<view :class="{ active: floorIdx === 'total' || percent > 0 }">
							评分
							<!-- <image src="@/static/images/yuanshi/zs.png" mode="widthFix"></image> -->
						</view>
					</view>
					<view class="total_r flex flex_align_center" :class="{ active: floorIdx === 'total' || percent > 0 }">
						<view style="position: relative;width:260rpx ;margin:0 24rpx 0 30rpx;">
							<view v-show="floorIdx === 'total' || percent > 0">
								<xProgress
									:diy="true"
									:isMove="false"
									:percent="percent"
									:bg="require('@/static/images/yuanshi/star3.png')"
									:active="require('@/static/images/yuanshi/star4.png')"
									backgroundSize="53rpx 53rpx"
									:strokeWidth="53"
								></xProgress>
							</view>
							<view v-show="floorIdx !== 'total' && percent === 0">
								<xProgress
									:diy="true"
									:isMove="false"
									:percent="percent"
									:bg="require('@/static/images/yuanshi/star7.png')"
									:active="require('@/static/images/yuanshi/star7.png')"
									backgroundSize="53rpx 53rpx"
									:strokeWidth="53"
								></xProgress>
							</view>
						</view>
						<view>{{ score }}</view>
					</view>
				</view>
				<view class="wrap_write item_c" @click="inputShow = false">
					<view class="name">详细评价：</view>
					<view class="wrap_text">
						<!-- 	<view class="title flex">
							<view class="span relative" v-for="(item, index) in commentLabel" :key="index">
								<text>{{ item }}</text>
								<text class="absolute close" @click="commentLabel.splice(index, 1)"><text class="iconfont icon-guanbi3"></text></text>
							</view>
						</view> -->
						<view class="textarea"><xTeatarea placeholder="体验感受&避坑指南" v-model="des" @input="inputTextarea" height="150rpx"></xTeatarea></view>
					</view>
				</view>
				<view class="upload item_c" @click="inputShow = false">
					<view class="title">图片上传</view>
					<xImageUpload
						ref="xImageUpload"
						:num="6"
						width="200rpx"
						height="200rpx"
						borderRadius="26rpx"
						:show="3"
						margin="0 16rpx 12rpx 0"
						@chooseImage="chooseImage"
						@delImg="chooseImage"
					>
						<view class="icon flex flex_around flex_align_center"><image src="@/static/images/yuanshi/upload.png" mode="widthFix"></image></view>
					</xImageUpload>
				</view>
				<view class="shop_list item_c" v-if="storeList.length">
					<view class="title">选择您所测评的店铺</view>
					<view>
						<view class="shop_item" v-for="(item, index) in storeList" :key="item.id" :class="{ active: storeIdx === index }" @click="storeChange(index)">
							<view class="flex flex_align_center">
								<view class="image"><image src="@/static/images/yuanshi/address.png" mode="widthFix"></image></view>
								<view>{{ item.name }}</view>
							</view>
						</view>
					</view>
				</view>
				<view class="footer item_c">
					<view @click="check = !check"><xProtocol v-model="check"></xProtocol></view>
					<button class="btn flex_line_height" :disabled="submitLoading" @click="submit"><text>确认上传</text></button>
				</view>
			</scroll-view>
		</view>
		<xChat :adjustPosition="false" ref="xChat" @send="createLabel" inputType="input" :inputShow="inputShow" :placeholder="placeholder" :maxlength="8" sendText="确定" desc="label" :isCash="false"></xChat>
		<x-authorize></x-authorize>
	</view>
</template>

<script>
// #ifdef H5
import { isWeixin } from '@/utils/validate.js';
import { openShareAll } from '@/utils/wechat/share.js';
// #endif
import xImageUpload from '@/components/x-image-upload/x-image-upload';
import xProtocol from '@/components/x-protocol/x-protocol';
import xProgress from '@/components/x-progress/x-progress.vue';
import xTeatarea from '@/components/x-textarea/x-textarea.vue';
import { uploadImg } from '@/utils/upload.js';
import { evaluationLabels, evaluationAdd, evaluationInfo, evaluationEdit, evaluationStoreList } from '@/api/yuanshi/evaluate.js';
import xChat from '@/components/x-chat/x-chat';
import { mapGetters } from 'vuex';
import { regHref } from '@/utils/validate.js';
import storage from '@/utils/storage.js';
import { SHARE_ID } from '@/config.js';
export default {
	components: {
		xImageUpload,
		xProgress,
		xTeatarea,
		xProtocol,
		xChat
	},
	data() {
		return {
			commentLabel: [],
			labelList: [],
			img: [],
			score: '0.0',
			percent: 0,
			des: '',
			pid: 0,
			check: true,
			str: '不能二次修改',
			isFirst: false, //是否是第一次修改分数
			floorIdx: 0,
			scrollToId: null,
			diyLabelArr: [],
			inputShow: false,
			submitLoading: false,
			placeholder: '最多8个字',
			isCreate: false, //是否自建标签
			storeList: [],
			storeIdx: -1,
			isSubmit:false
		};
	},
	computed: {
		...mapGetters(['userInfo']),
		labelListLen: function() {
			// 小程序里在非循环中,不能监听数据length改变
			return this.labelList.length;
		}
	},
	methods: {
		storeChange(index) {
			this.storeIdx = index;
			this.isEdit = true;
		},
		skip(index) {
			this.labelList[index]['selected'] = true;
			let idx = index + 1 > this.labelList.length ? 'total' : index + 1;
			this.scrollIntoView(idx);
		},
		createDiyLabel(item, index) {
			this.placeholder = item.cate_name + ' ( 最多8个字 )';
			this.scrollIntoView(index, true);
		},
		delDiyLabel(item, index, index1) {
			let label_group = item.label_group;
			label_group.splice(index1, 1);

			this.$set(this.labelList[index], 'label_group', label_group);
		},
		scrolltolower(e) {
			if (this.inputShow) {
				this.inputShow = false;
			}
		},
		scroll() {},
		scrollIntoView(index, type) {
			this.floorIdx = index;
			this.scrollToId = index === 'total' ? 'total' : `label${index}`;
			this.isEdit = true;
			if (type) {
				this.inputShow = true;
				this.$refs.xChat.inputFocus();
			} else {
				this.inputShow = false;
			}
			console.log(this.scrollToId);
		},

		goPages(path, type) {
			this.$navigator(path, type);
		},
		progressMove(percent, item, index, item1, index1) {
			this.isEdit = true;
		},
		inputTextarea() {
			this.isEdit = true;
		},
		progressEnd(percent, item, index, item1, index1) {
			let score = (5 * (percent / 100)).toFixed(1);
			this.$set(this.labelList[index].label_group[index1], 'scoring', score);
			this.computedScore();
		},
		// 标签分数改变时,计算总分
		computedScore() {
			let { labelList } = this,
				score = 0,
				tj = 0;
			labelList.forEach((item, index) => {
				if (item.selected) {
					item.label_group.forEach((item1, index1) => {
						let scoring;
						if (item1.check && item.is_scoring === 1) {
							tj++;
							score = score + Number(item1.scoring);
						}
					});
				}
			});

			if (tj > 0) {
				this.score = (score / tj).toFixed(1);
				this.percent = Number(((score / tj / 5) * 100).toFixed(1));
			}
		},
		// 过滤自建标签,但没有选择
		filterLabel() {
			let labelList = JSON.parse(JSON.stringify(this.labelList));
			labelList.forEach((item, index) => {
				if (item.is_other === 1) {
					let label_group = [];
					item.label_group.forEach((item1, index1) => {
						if (!item1.diy || (item1.diy && item1.check)) {
							label_group.push(item1);
						}
					});

					item.label_group = label_group;
				}
			});
			return labelList;
		},
		labelChange(item, index, item1, index1) {
			let label_group = this.labelList[index].label_group;
			this.scrollIntoView(index);
			this.isEdit = true;
			if (!item1.check) {
				this.labelList[index]['selected'] = true;
			} else {
				if (item.is_scoring === 1) {
					this.labelList[index]['selected'] = false;
				}
			}
			if (item1.scoring < 1 && item.is_scoring === 1) {
				// 保证最低分为1
				this.labelList[index].label_group[index1].scoring = 1;
			}
			if (item.is_single === 1) {
				// 单选
				label_group.forEach((item2, index2) => {
					if (index1 === index2) {
						// item2.check=!item2.check;
						this.$set(this.labelList[index].label_group[index2], 'check', !item1.check);
					} else {
						// item2.check = false;
						this.$set(this.labelList[index].label_group[index2], 'check', false);
					}
				});
			} else {
				// 多选
				this.$set(this.labelList[index].label_group[index1], 'check', !item1.check);
			}
			this.computedScore();
		},
		createLabel(val) {
			let index = this.floorIdx,
				item = this.labelList[index],
				label_group = item.label_group;
			this.isCreate = true;
			item['selected'] = true;
			let obj = {
				check: true,
				name: val,
				scoring: 1.0,
				diy: true
			};

			if (item.is_single === 1) {
				// 单选
				label_group.forEach((item2, index2) => {
					item2.check = false;
				});
			}

			label_group.push(obj);
			console.log(label_group);
			this.$set(this.labelList[index], 'label_group', label_group);
			
			let timer  = setTimeout(()=>{
				this.inputShow = false;
				clearTimeout(timer)
			},500)
			// this.diyLabelArr.push({ name: val });
		},
		chooseImage(arr) {
			this.isEdit = true;
			this.img = arr;
			// uploadImg(arr, false).then(res => {
			// 	this.sendMsg(res[0], 3);
			// });
		},
		submit() {
			const { score, diyLabelArr, des, img, commentLabel, isCreate } = this;
			let labelList = isCreate ? this.filterLabel() : this.labelList;
			this.inputShow = false;
			for (let i = 0; i < labelList.length; i++) {
				let item = labelList[i];
				if (!item.selected && item.is_required === 1) {
					this.$showToast(i + 1 + '必选项');
					this.scrollIntoView(i);
					return;
				}
			}
			if (Number(score) === 0) {
				return this.$showToast('评分不能为0，请修改');
			}
			if (!this.check) {
				return this.$showToast('请勾选协议');
			}
			let obj = {
				product_id: this.pid,
				store_id: this.storeList.length ? this.storeList[this.storeIdx].id : 0,
				score,
				scoring_label: labelList,
				self_built_label: diyLabelArr || [],
				content: [...commentLabel, des]
			};
			this.submitLoading = true;
			if (this.submitType === 'edit') {
				if (!this.isEdit) {
					this.submitLoading = false;
					return this.$showToast('未做修改');
				}
				obj.id = this.eid;
				if (img === this.copyImage) {
					obj.image = this.copyImage;
					this.evaluationHandle(obj, false);
				} else {
					let arr = [],
						arr1 = [];
					img.forEach((item, index) => {
						if (regHref(item)) {
							arr.push(item); //https
						} else {
							arr1.push(item);
						}
					});
					if (arr1.length) {
						uploadImg(arr1)
							.then(res => {
								obj.image = [...res, ...arr];
								this.evaluationHandle(obj, false);
							})
							.catch(err => {
								console.log('图片上传失败', err);
								this.$showToast('图片上传失败-' + err);
								this.submitLoading = false;
							});
					} else {
						obj.image = img;
						this.evaluationHandle(obj, false);
					}
				}
			} else {
				uploadImg(img)
					.then(res => {
						obj.image = res;
						this.evaluationHandle(obj, true);
					})
					.catch(err => {
						console.log('图片上传失败', err);
						this.$showToast('图片上传失败-' + err);
						this.submitLoading = false;
					});
			}
		},

		evaluationHandle(obj, typ) {
			let req = '';
			if (typ) {
				// 正常提交
				req = evaluationAdd(obj);
			} else {
				// 编辑
				req = evaluationEdit(obj);
			}
			req
				.then(res => {
					this.$showToast(typ ? '已提交审核' : '编辑成功');

					if (!typ) {
						const history = getCurrentPages();
						if (history.length > 1) {
							let beforePage = history[history.length - 2];
							console.log(beforePage);
							beforePage.$vm.onlyShowMy(true);
						}
					} else {
						const history = getCurrentPages();
						if (history.length > 1) {
							let beforePage = history[history.length - 2];
							beforePage.$vm.isTipsShow();
						}

						storage.remove(`EVALUATE_${this.pid}_${this.id}`);
					}

					this.submitLoading = false;
					this.isSubmit = true
					setTimeout(() => {
						this.$navigator(-1);
					}, 500);
				})
				.catch(err => {
					console.log('提交失败', err);
					this.$showToast('提交失败-' + err);
					this.submitLoading = false;
				});
		},
		open() {
			if (this.isFirst) {
				return this.$showToast(this.str);
			}
			this.$refs.popup.open();
		},
		async getEvaluationLabels(id) {
			evaluationLabels(id).then(res => {
				let is_check = false,
					label_group = res.data[0].label_group;
				// res.data[0]['selected'] = true;
				label_group.forEach((item, index) => {
					if (item.check) {
						is_check = true;
					}
				});
				if (!is_check) {
					if (res.data[0].label_group.length) {
						// res.data[0].label_group[0].check = true;
					}
				}
				this.labelList = res.data;
			});
		},
		getList(id, type, store_id) {
			let data = {
				latitude: '', //纬度
				longitude: '', //经度
				page: 1,
				limit: 10,
				product_id: id || this.pid
			};
			evaluationStoreList(data)
				.then(res => {
					this.storeList = res.data;
					if (res.data.length) {
						if (store_id && res.data.length > 1) {
							res.data.forEach((item, index) => {
								if (item.id === store_id) {
									this.storeIdx = index;
								}
							});
						} else {
							this.storeIdx = 0;
						}
					}
				})
				.catch(err => {
					this.$showToast(err.msg || err);
				});
		}
	},
	mounted() {
		this.percent = Number(((this.score / 5) * 100).toFixed(1));
	},
	onLoad(option) {
		const { pid, id, type = 'submit', eid } = option;
		this.submitType = type;
		this.isSubmit = false;
		this.pid = Number(pid);
		this.id = Number(id);
		if (type === 'edit') {
			this.isEdit = false;
			this.eid = eid;
			evaluationInfo(eid).then(res => {
				let storeInfo = res.data.storeInfo,
					content = storeInfo.content,
					score = Number(storeInfo.score),
					commentLabel = this.commentLabel, //标签数组
					labelList = storeInfo.scoring_label,
					span = [], //标签数组
					content1 = []; // 输入框内容
				this.copyImage = storeInfo.image;
				this.img = storeInfo.image;
				this.$nextTick(() => {
					this.$refs.xImageUpload.imgList = storeInfo.image;
				});
				content.forEach((item, index) => {
					if (commentLabel.includes(item, 0)) {
						span.push(item);
					} else {
						content1.push(item);
					}
				});
				labelList.forEach((item, index) => {
					item['selected'] = true;
				});
				this.labelList = labelList;
				this.diyLabelArr = storeInfo.self_built_label;
				this.score = score.toFixed(1);
				this.percent = Number(((score / 5) * 100).toFixed(1));

				this.commentLabel = span;
				this.des = content1.join(',');
				this.getList(pid, type, Number(storeInfo.store_id));
			});
		} else {
			let t = storage.get(`EVALUATE_${this.pid}_${this.id}`);
			console.log(`EVALUATE_${pid}_${id}`);
			if (t) {
				let storeInfo = JSON.parse(t),
					score = Number(storeInfo.score);
				if (storeInfo.labelList.length) {
					this.labelList = storeInfo.labelList;
					this.score = score.toFixed(1);
					this.percent = Number(((score / 5) * 100).toFixed(1));
					this.des = storeInfo.content;
					this.img = storeInfo.img;
					this.$nextTick(() => {
						this.$refs.xImageUpload.imgList = storeInfo.img;
					});
					this.getList(pid, type, storeInfo.store_id);
				} else {
					this.getEvaluationLabels(pid);
					this.getList(pid, type);
				}
			} else {
				this.getEvaluationLabels(pid);

				this.getList(pid, type);
			}
		}
	},
	onPageScroll(e) {},
	onReachBottom() {},
	onUnload() {
		if (this.submitType === 'submit' && !this.isSubmit) {
			const { score, diyLabelArr, des, img, commentLabel, isCreate, storeList, storeIdx, pid } = this;
			let labelList = isCreate ? this.filterLabel() : this.labelList;
			let obj = {
				score,
				store_id: storeList.length ? storeList[storeIdx].id : 0,
				labelList,
				content: des,
				img
			};
			storage.set(`EVALUATE_${this.pid}_${this.id}`, JSON.stringify(obj));
		}

		clearInterval(this.setInter);
	},
	onHide() {},
	// #ifdef MP
	onShareAppMessage() {
		return {
			title: '',
			imageUrl: '',
			path: '',
            templateId: SHARE_ID
		};
	}
	// #endif
};
</script>
<style lang="scss">
page {
	padding-bottom: 0rpx;
}
</style>
<style scoped lang="scss">
.submit {
	margin: 30rpx 0rpx 50rpx 0;
	// width: calc(100% - 20rpx);
	// overflow: hidden;
	background: none;
	.fixed_l {
		// padding-top: 30rpx;
		z-index: 999;
		left: 0;
		width: 86rpx;
		.item {
			position: relative;
			width: 68rpx;
			height: 86rpx;
			background: #ffffff;
			box-shadow: 0px 0px 20rpx rgba(107, 127, 153, 0.2);
			border-radius: 0px 30rpx 30rpx 0px;
			line-height: 86rpx;
			margin-bottom: 80rpx;
			text-align: center;
			font-size: 36rpx;
			color: #aca8b7;
			&.active {
				background: #50506f;
				width: 86rpx;
				color: #ffffff;
				&.total {
					background: #ff5656;
				}
			}
			&:not(:last-child) {
				&::after {
					content: '';
					position: absolute;
					top: 106rpx;
					height: 80rpx;
					margin-left: 34rpx;
					display: block;
					border: 2rpx dashed #50506f;
				}
			}
			&.total {
				image {
					width: 48rpx;
				}
			}
		}
	}
	.fixed_r {
		// margin-top: 30rpx;
		// width: calc(100% + 80rpx); //阴影位置
		.item_c {
			width: calc(100% - 94rpx - 20rpx);
			margin-left: 94rpx;
		}
		.item {
			background: #ffffff;
			box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
			min-height: 210rpx;
			margin-bottom: 30rpx;
			border-radius: 30rpx;
			overflow: hidden;
			.nav {
				padding: 0 30rpx;
				// border-radius: 30rpx 30rpx 0 0;
				background: #d2d2d2;
				color: #ffffff;
				font-weight: bold;
				font-size: 28rpx;
				height: 74rpx;
				line-height: 74rpx;
				.skip {
					position: absolute;
					right: 20rpx;
					top: 20rpx;
					padding: 0 26rpx;
					height: 40rpx;
					line-height: 40rpx;

					color: #d2d2d2;
					font-size: 24rpx;

					box-shadow: 0px 0px 20rpx rgba(107, 127, 153, 0.2);
					border-radius: 24rpx;
					background: #ffffff;
				}
			}
			.wrap {
				padding: 26rpx 20rpx 48rpx 20rpx;
				.labels {
					padding-bottom: 38rpx;
					width: calc(100% + 20rpx);
					> view {
						display: inline-block;
						border-radius: 24rpx;
						box-shadow: 0px 0px 20rpx rgba(107, 127, 153, 0.2);
						height: 60rpx;
						line-height: 60rpx;
						padding: 0 40rpx;
						margin: 0 20rpx 20rpx 0rpx;
						text-align: center;
						min-width: 104rpx;
						max-width: 272rpx;
						font-weight: 400;
						font-size: 24rpx;
						color: #d2d2d2;
						&.active {
							color: #d2d2d2;
						}
						.line1 {
							@include show_line;
						}
						.close {
							width: 32rpx;
							height: 32rpx;
							top: -20rpx;
							right: -16rpx;
							color: #666666;
						}
						// &:nth-last-child(2){
						// 	// margin-right: 0;
						// }
					}
				}
				.scores {
					padding: 32rpx 0 0rpx 0;
					border-top: 2rpx solid #e2e2e2;
					.scores1 {
						// display: flex;
						width: calc(100% + 20rpx);
						color: #d2d2d2;
						.item1 {
							display: inline-block;
							margin: 0 20rpx 20rpx 0;
						}
					}
					.tips {
						margin-bottom: 22rpx;
						.span {
							width: 30rpx;
							height: 34rpx;
							background: #50506f;
							border-radius: 0px 12rpx 12rpx 0px;
							box-shadow: 0px 0px 20rpx 0px rgba(107, 127, 153, 0.2);
							margin-right: 8rpx;
						}
						.txt {
							font-size: 24rpx;
							height: 34rpx;
							line-height: 34rpx;
							color: #50506f;
						}
					}
					.item1 {
						margin-bottom: 20rpx;
						text {
							@include show_line;
							display: inline-block;
							box-sizing: border-box;
							height: 60rpx;
							line-height: 56rpx;
							min-width: 104rpx;
							max-width: 280rpx;
							font-size: 24rpx;
							padding: 0 40rpx;
							text-align: center;
							border-radius: 24rpx;
							font-size: 24rpx;
							color: #50506f;
							border: 2rpx solid #50506f;
						}
						.score {
							font-size: 48rpx;
							font-family: PingFang SC, PingFang SC-Bold;
							font-weight: 700;
							color: #50506f;
							margin-right: 12rpx;
						}
						.close {
							width: 20rpx;
							top: -10rpx;
							right: 20rpx;
							color: #666666;
							background: #fff;
						}
					}
				}
			}
			&.item_active {
				.nav {
					background: #50506f;
					.skip {
						color: #50506f;
					}
				}
				.wrap {
					.labels {
						> view {
							color: #50506f;
							&.active {
								color: #d2d2d2;
							}
						}
					}
				}
			}
		}
		.total {
			height: 144rpx;
			line-height: 144rpx;
			background: #ffffff;
			border-radius: 30rpx;
			box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
			overflow: hidden;
			.total_l {
				width: 150rpx;
				> view {
					width: 150rpx;
					height: 96rpx;
					background: #d2d2d2;
					box-shadow: 0px 0px 20rpx rgba(0, 0, 0, 0.1);
					opacity: 1;
					border-radius: 4rpx 24rpx 24rpx 4rpx;
					text-align: center;
					line-height: 96rpx;

					font-weight: 500;
					font-size: 36rpx;
					color: #fff;
					image {
						width: 112rpx;
					}
					&.active {
						background: #ff5656;
					}
				}
			}
			.total_r {
				// padding: 40rpx 42rpx 40rpx 48rpx;

				color: #d2d2d2;
				font-weight: bold;
				font-size: 74rpx;
				&.active {
					color: #fc5656;
				}
			}
		}
	}
	.wrap_write {
		margin: 50rpx 0 40rpx 0;
		.name {
			font-size: 24rpx;
			color: #666666;
			padding: 0rpx 30rpx 20rpx 0rpx;
		}
		.wrap_text {
			padding: 30rpx;

			border-radius: 26rpx;

			box-shadow: 0px 0px 40rpx rgba(107, 127, 153, 0.2);
			background: #fff;
			.title {
				font-size: 24rpx;

				color: #eee;
				padding: 4rpx 0;
				.span {
					background: #666666;
					padding: 4rpx 15rpx;
					border-radius: 16rpx;
					margin-right: 20rpx;
					.close {
						width: 20rpx;
						top: -15rpx;
						right: 0rpx;
						color: #666666;
					}
				}
			}
			.textarea {
				background: #fff;
				padding: 20rpx 0;
			}
		}
	}
	.upload {
		.title {
			font-size: 24rpx;
			color: #666666;
			padding: 0rpx 30rpx 20rpx 0rpx;
		}
		.icon {
			width: 200rpx;
			height: 200rpx;
			background: #f4f4f4;
			border: 2rpx dashed #d2d2d2;
			border-radius: 26rpx;
			image {
				width: 66rpx;
			}
		}
	}
	.shop_list {
		margin-top: 50rpx;
		font-size: 24rpx;
		.title {
			color: #666;
		}
		.shop_item {
			margin-top: 20rpx;
			color: #333;
			padding: 30rpx;
			background: #ffffff;
			border-radius: 30rpx;
			border: 2rpx solid rgba(0, 0, 0, 0);
			box-shadow: 0px 0px 20rpx 0px rgba(107, 127, 153, 0.2);
			.image {
				height: 32rpx;
				image {
					width: 27rpx;
					height: 32rpx;
					margin-right: 20rpx;
				}
			}

			&.active {
				border: 2rpx solid #ff5656;
			}
		}
	}
	.footer {
		margin-top: 30rpx;
		padding: 16rpx 0;
		.txt {
			image {
				width: 42rpx;
				height: 39rpx;
				margin: 0 26rpx 0 22rpx;
			}
			.font_size20 {
				width: 118%;
				transform-origin: left;
				color: #999999;
			}
		}

		.btn {
			margin: 100rpx 0;
			width: 638rpx;
			height: 100rpx;

			background: #ff5656;
			border-radius: 40rpx;

			font-size: 24rpx;

			color: #ffffff;
		}
	}
}
</style>
