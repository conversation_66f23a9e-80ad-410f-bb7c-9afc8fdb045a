<template>
	<view class="show_vip">
		<view class="des">
			<view class="nav flex flex_align_end">
				<image src="@/static/images/yuanshi/yshs1.png" mode="widthFix"></image>
				<view>着调儿指数说明：</view>
			</view>
			<view class="list" style="width: 100%;overflow: hidden;">
				<view class="item font_size20" v-for="(item, index) in memberDesc">{{ item }}</view>
			</view>
			<button :disabled="disabled" class="flex flex_around" @click="btnClick">
				<view class="flex flex_align_center">
					<text>我来测评</text>
				</view>
			</button>
		</view>
		<view class="show">
			<view class="vip" v-if="recommendWantToTest.length">
				<view class="nav flex flex_align_center flex_between">
					<view class="title">
						大家都想测
						<view class="line"></view>
					</view>
					<!-- <view class="more"><text class="font_size20">查看更多</text></view> -->
				</view>
				<view class="list"><xList :arr="recommendWantToTestrecommendWantToTest" :btn="true"  type="wish"></xList></view>
			</view>
		</view>
	</view>
</template>
<script>
import xList from '@/components/yuanshi/x-list.vue';
import { vipExclusive, memberDesc,recommendWantToTest } from '@/api/yuanshi/public.js';
export default {
	components: {
		xList
	},
	data() {
		return {
			recommendWantToTest: [],
			disabled: false,
			page: {
				page: 1,
				limit: 10,
				more: true
			},
			requestLoading: false,
			memberDesc: []
		};
	},
	methods: {
		btnClick() {
			const {wishId,productId} = this.mixinsParam;
			this.goPages('/pages/yuanshi/evaluate/submit?id=' + wishId + '&pid=' + productId,'redirectTo')
			this.disabled = true;
		},
		goPages(path,type) {
			this.$navigator(path,type);
		},

		getRecommendWantToTest() {
			if (!this.page.more || this.requestLoading) return;
			this.requestLoading = true;
			recommendWantToTest({ page: this.page.page, limit: this.page.limit })
				.then(res => {
					this.requestLoading = false;
					this.recommendWantToTest = this.recommendWantToTest.concat(res.data);
					this.page.more = res.data.length === this.page.limit;
					this.page.page++;
				})
				.catch(err => {
					this.requestLoading = false;
				});
		}
	},
	onLoad(option) {
		const { wid = 1, pid = 76, type } = option;
		this.mixinsParam = {
			wishId: wid,
			productId: pid
		};
	},
	onShow() {
		this.disabled = false;
		this.getRecommendWantToTest();
		memberDesc().then(res => {
			if (res.data.ywindex_desc.length) {
				this.memberDesc = res.data.ywindex_desc;
			}
		});
	},
	onReachBottom() {
		this.getRecommendWantToTest();
	}
};
</script>
<style lang="scss">
page {
	background: $uni-bg-color;
}
</style>
<style scoped lang="scss">
.show_vip {
	.des {
		padding: 52rpx 40rpx;
		min-height: 100rpx;
		.nav {
			font-size: 24rpx;
			color: #333333;
			margin-bottom: 50rpx;
			image {
				width: 215rpx;
				height: 71rpx;
				margin-right: 20rpx;
			}
		}
		.font_size20 {
			transform-origin: left;
			width: 118%;
			color: #999999;
		}
		button {
			margin: 70rpx auto 58rpx auto;
			height: 100rpx;
			background: #ff5656;
			border-radius: 40rpx;
			font-family: PingFang SC, PingFang SC-Regular;
			font-weight: 400;
			color: #ffffff;
			font-size: 24rpx;
			
		}
	}
	.show {
		padding: 0 36rpx;
		.vip {
			.nav {
				.title {
					padding-left: 16rpx;
					font-size: 32rpx;

					font-weight: bold;

					color: #333333;
					.line {
						width: 100%;
						height: 8rpx;
						background: linear-gradient(90deg, #ffa969 0%, #ff5a73 100%);
						border-radius: 2rpx;
					}
				}
				.more {
					color: #999999;
				}
			}
			.list {
				padding-top: 40rpx;
			}
		}
	}
}
</style>
