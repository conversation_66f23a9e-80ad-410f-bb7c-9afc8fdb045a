<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
		<title>
			<%= htmlWebpackPlugin.options.title %>
		</title>
		<meta name="Copyright" content="helang">
        <script src="https://kf.arthorize.com/customerServer.js"></script>
		<script type="text/javascript">
			document.addEventListener('DOMContentLoaded', function() {
			    document.documentElement.style.fontSize = document.documentElement.clientWidth / 20 + 'px'
			})
		</script>
		<link rel="stylesheet" href="<%= BASE_URL %>static/index.css" />
	</head>
	<body>
		<noscript>
			<strong>Please enable JavaScript to continue.</strong>
		</noscript>
		<div id="app"></div>
		<!-- built files will be auto injected -->
	</body>
    <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.4.0.js"></script>
    <script type="text/javascript" src="https://js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.2.js"></script>
    <script>
    	  // 待触发 `UniAppJSBridgeReady` 事件后，即可调用 uni 的 API。
    	  document.addEventListener('UniAppJSBridgeReady', function() {
    		uni.webView.getEnv(function(res) {
    			console.log('当前环境：' + JSON.stringify(res));
    		});
    	  });
    </script>
</html>
