# 项目概述
本项目是基于Vue.js + Uni-app框架开发的多端应用，包含元识系统、商城、社区等核心功能模块。当前主要在HBuilderX工具环境下运行，未来将完善命令行运行方式。

## 目录结构说明
```
├── api/                  # 接口服务（按功能模块划分）
├── components/           # 公共组件库（含x-前缀业务组件）
├── pages/                # 页面模块（含tabBar导航）
├── store/                # Vuex状态管理
├── utils/                # 工具库（请求封装/校验/支付等）
├── uni_modules/          # Uni-app插件
├── static/               # 静态资源（字体/图片/CSS）
├── uniCloud-alipay/      # 支付宝云开发配置
├── custom-tab-bar/       # 自定义底部导航（小程序专用）
│
├── App.vue               # 应用入口组件
├── main.js               # 应用入口文件
├── manifest.json         # 跨端配置
├── pages.json            # 页面路由配置
├── uni.scss              # 全局样式变量
├── vue.config.js         # Vue构建配置
└── config.js             # 应用常量配置
```

## 运行调试说明
### 当前方式（HBuilderX）
1. **导入项目**：菜单 → 文件 → 导入 → 选择项目目录
2. **选择运行平台**：顶部工具栏选择目标平台（小程序/H5/App）
3. **启动调试**：菜单 → 运行 → 运行到模拟器
4. **查看日志**：控制台查看运行时日志

### 未来命令行方式（规划中）
```bash
# 安装依赖
npm install

# 启动H5开发环境
npm run dev:h5

# 构建微信小程序
npm run build:mp-weixin
```

## 常见问题说明
### 样式兼容问题
- **编译后`-webkit-box-orient: vertical`失效**  
  解决方案：在对应样式添加`/* autoprefixer: off */`注释
- **小程序背景图片限制**  
  需使用base64编码（<40k自动转换）或网络图片

### 组件使用限制
- **字节小程序**：不支持作用域插槽 → 使用解构插槽
- **动态插槽名**：小程序环境不支持 → 需静态命名
- **全局组件注册**：不支持在app.vue全局注册 → 需在页面内单独引入

### 跨端差异
- **H5**：支持完整Vue特性
- **小程序**：注意生命周期和API差异
- **App**：需原生插件支持特定功能

## 富文本链接实现示例
以下是课程详情页使用的自定义链接协议示例：

```html
<!-- 示例1：公众号跳转（通过webview中转） -->
<a href="/pages/webview/webview?url=https://mp.weixin.qq.com/s/3ztkZVNcJ0FhUmc3J0hr5w" target="_self">跳转公众号 👈</a>

<!-- 示例2：跨小程序跳转（自定义miniapp协议） -->
<a href="http://miniapp://wxb63203ca8ecbc8fc/pages/soundPage/soundPage?trackId=885815638&albumId=89105371">跳转其他小程序👈</a>

<!-- 示例3：复制链接功能 -->
<a href="https://mp.weixin.qq.com/s/3ztkZVNcJ0FhUmc3J0hr5w" target="_self">复制链接</a>
```

### 技术实现要点
1. **路由拦截**：在`/pages/webview/webview`页面处理URL中转逻辑
2. **协议注册**：在`manifest.json`中声明`miniapp`自定义协议
3. **复制功能**：需配合全局mixin实现点击事件监听
