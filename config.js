export const VUE_NAME = "着调儿"; //__UNI__92D9658
// api请求地址
// export const VUE_APP_API_URL = process.env.NODE_ENV === 'development' ?'https://dev.shop.arthorize.com/api' : 'https://shop.arthorize.com/api';// 'https://dashboard.arthorize.com'
export const VUE_APP_API_URL = 'https://shop.arthorize.com/api';// 正式数据请求地址
// export const VUE_APP_API_URL = 'https://dev.shop.arthorize.com/api';// 测试数据请求地址

//静态图片地址
// export const VUE_APP_URL = (process.env.NODE_ENV  === 'development' ?'https://dev.shop.arthorize.com' : 'https://shop.arthorize.com'); 
export const VUE_APP_URL = 'https://shop.arthorize.com';
// export const VUE_APP_URL = 'https://dev.shop.arthorize.com';

export const VUE_APP_WS_URL ='wss://scoket.arthorize.com/chat_message';

// 上报事件等,开发环境不上传数据
export const IS_DEV = process.env.NODE_ENV === 'development' ? true : false ;

// 要跳转小程序appid
export const APPID_YWYS = 'wxe95c67489c17edcf'; //源未艺术 

//首页地址 返回上一页找不到路径重定向首页
export const PATH_INDEX_URL = '/pages/tabBar/index/index'
// web-view跳转地址
export const SKIP_URL = 'https://ot.arthorize.com'
//授权回调地址
// export const REDIRECT_URI = 'https://shop.arthorize.com'; 

// 公众号中是否使用公众号授权登录 true公众号授权->auth界面下一步操作  false直接跳转login登录界面
export const WECHAT_LOGIN = true;
// 授权之后返回原有地址
export const WECHAT_AUTH_BACK_URL = 'WECHAT_AUTH_BACK_URL';

// 订阅消息模板缓存
export const SUBSCRIBE_MESSAGE = 'SUBSCRIBE_MESSAGE';

// 首页右上角提示
export const TIPS_SHOW = 'TIPS_SHOW';

//首页优惠券弹窗是否显示
export const HAS_COUPON_WINDOW = 'HAS_COUPON_WINDOW';

// export const SUBSCRIBE_MESSAGE = 'SUBSCRIBE_MESSAGE';
//缓存经度
export const LONGITUDE = 'LONGITUDE';
//缓存纬度
export const LATITUDE = 'LATITUDE';
//缓存位置
export const ADDRESS = 'ADDRESS';
// 是否造乐空间管理员
export const IS_MANAGE = 'IS_MANAGE';

//缓存地图key
export const MAPKEY = 'MAPKEY';

export const BACK_URL = "BACK_URL"

// 待发送消息 拼接uid
export const CHAR_MESS = "CHAR_MESS" 
// 发送失败消息列表 拼接uid
export const SEND_FAIL_MSG = "SEND_FAIL_MSG"

// 小程序是否开启虚拟支付内容(字节小程序需关闭)
// #ifndef MP-TOUTIAO
export const IS_TOUTIAO_PAY = true;
// #endif
// #ifdef MP-TOUTIAO
export const IS_TOUTIAO_PAY = false;
// #endif

//缓存客服token
export const KEFUTOKEN = 'KEFUTOKEN';
// 客服系统webscoket链接        
export const CHAT_WS_URL ='wss://kf.arthorize.com/ws';
// 客服系统相关api请求地址
export const KEFU_URL = 'https://kf.arthorize.com' ; 
// 字节小程序分享模板id
export const SHARE_ID = '5rhj9r9a53ph3huxac' ; 

// 微信小程序企业客服相关配置
// 客服链接
export const WX_KEFU_Link = "https://work.weixin.qq.com/kfid/kfc90e6ffaa45e43f15";
// 企业ID
export const WX_ENTERPRISE_Link = "ww27545fd04b88f7ec";
// #ifdef MP-WEIXIN
// ios虚拟支付开关
export const OPEN_IOS_PAYMENT = true;
// #endif
// #ifndef MP-WEIXIN
export const OPEN_IOS_PAYMENT = false;
// #endif

// 调试请求失败是否弹窗
export const DEBUG_POP = false; 