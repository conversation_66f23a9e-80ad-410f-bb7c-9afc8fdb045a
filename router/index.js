//路由 与 uni-simple-router 联用
import {RouterMount,createRouter} from 'uni-simple-router';
import store from "@/store";
import {
	autoAuth
} from '@/utils/common.js'
//初始化
const router = createRouter({
	platform: process.env.VUE_APP_PLATFORM,  
	routes: [...ROUTES.ROUTES_ARRAY]
});

// console.log(router)
//全局路由前置守卫
router.beforeEach((to, from, next) => {
	const {
		home,
		auth,
		nav,
		navigationBarTitleText
	} = to.meta;
	// 需要授权,token不存在 去登录
	document.title = navigationBarTitleText;
	// 由于使用跳转apis时已经鉴权,此处没有必要，当前项目存在跳出当前网址 客服
	// if (auth === true && !store.state.token) {
	// 	if (from.name === "login") return;
	// 	return autoAuth();
	// }
	home === false ? store.commit("HIDE_HOME") : store.commit("SHOW_HOME");
	next()
})
// 全局路由后置守卫
router.afterEach((to, from) => {
	const {
		home,
		auth,
		nav
	} = to.meta;
	nav === true ? store.commit("SHOW_NAV") : store.commit("HIDE_NAV");
})


export {
	router,
	RouterMount
}
